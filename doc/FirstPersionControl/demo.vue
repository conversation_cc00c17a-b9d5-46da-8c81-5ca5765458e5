<!-- html -->
<template>
  <el-button type="primary" @click="funpopShow">弹出面板</el-button>

  <div class="container" id="parentContainer">
    <MapControl
      MapControlName="mainMapControl"
      :PropStore="PropStore"
      :LayerStore="layerStore"
    ></MapControl>
  </div>
  <pop-panel
    v-if="popShow"
    :title="'测试面板'"
    :width="300"
    :height="350"
    @close="close"
  >
    <template #content>
      <FirstPersionControl
        MapControlName="mainMapControl"
        :PropStore="PropStore"
        :LayerStore="layerStore"
      ></FirstPersionControl>
    </template>
  </pop-panel>
</template>

<script setup lang="ts">

import { ref } from "vue";
import {
  MapControl,
  PopPanel,
  FirstPersionControl,
  mapType,
  InitMapControl,getOption
} from "../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";

/**第一步：导入图层参数*/
import {
  PropStore,
  _Onemap,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
} from "../layer";

PropStore.MapType = mapType.cesium;
if (_Onemap.MapType.value != mapType.cesium) _Onemap.setMapType(mapType.cesium);

/**  第二步：配置公共参数 */
const _Options = Object.assign(getOption(), {
  id: "mapviewerContainer",
  BASE_URL: "ThirdParty",
  mapType: mapType.cesium,
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
});
InitMapControl(_Onemap, {
  MapControlName: "mainMapControl",
  Options: _Options,
  Components: {
    MapLayerTreeVisible: false,
    SwitchMapVisible: false,
    PropertyVisible: false,
    MapToolsVisible: false,
    BottomInfoVisible: false,
  },
});
let popShow = ref(false);
const funpopShow = () => {
  popShow.value = true;
};
const close = () => {
  popShow.value = false;
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
