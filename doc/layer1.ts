import {
  mapType,
  serviceType,
  BaseMapType,
  IMapLayer,
  Utils,
  OnemapClass,
  Icons,
  DockType,
  // MapLayerStore,
  // GlobalPropStore,
  IToolItemType,
  ILayerType,
  //*****工具自组件 */
  Location,
  ViewRoamer,
  splitscreen,
  SceneLight,
  DrawTools,
  debug,
  DrawPanel,
  UnderGround,
  SunlightAnalysis,
  sceneset,
  ModedlLodSet,
  CacheSet,
  BlendColor,
  LayerStorage,
  PropStorage,
  OnemapEvent,
  Globalvariable,
} from "../packages/onemapkit";

import * as Cesium from "@onemapkit/cesium";
import { computed, markRaw } from "vue";
import axios from "axios";

// import * as base from "../packages/base/base";
export const _Onemap = new OnemapClass(mapType.cesium);

/** layerStore,ServiceUrl,BaseMapLayerUrl,layerCheckEvent,TerrainUrl
 * 在这统一实例化 MapLayerStore 在各个组件用法示例直接引用。
 */

export const PropStore: any = new PropStorage([
  {
    isLocalStorage: true,
    storagekey: "propertyVisible",
    variableType: 1,
    initStoragevalue: false,
  },
  {
    isLocalStorage: true,
    storagekey: "mousePosition",
    variableType: 2,
    initStoragevalue: {} /*IPosition类型*/,
  },
]);
export const layerStore: any = new LayerStorage([
  {
    variableType: 2, //变量类型
    storagekey: "CheckedLayerids", //变量名称
    isLocalStorage: true, //是LocationalStorage还是SessionLocalStorage
    initStoragevalue: [], //变量初值
  },
]) as any;
// export const mapextent ={
//   destination: Cesium.Rectangle.fromDegrees(
//     mapextent?.xmin || 105.9738504337227,
//     mapextent?.ymin || 21.624205573999646,
//     mapextent?.xmax || 110.55005559052246,
//     mapextent?.ymax || 24.67104274606849
//   ), //west, south, east, north
//   orientation: {
//     heading: Cesium.Math.toRadians(mapextent?.heading || 0.0), // 方向
//     pitch: Cesium.Math.toRadians(mapextent?.pitch || -90.0), // 倾斜角度
//     roll: mapextent?.roll || 0,
//   },
// }

/**
 * 如用内网的数据资源就设为 false ，想用本地自己的资源（或外网资源）就设为 true
 */
const isLocal = true;
/** mapdata\3dtiles\wuxiang2
 * ServiceUrl对象主要存放临时服务地址参数，一般是在 initMaplayers 中无法确定参数的情况下才在这里配置
 */
export const ServiceUrl = {
  model_XXJT: isLocal
    ? "proxmapdata/mapdata/max/xxjt/tileset.json"
    : "https://cesium.dev.nnric.net/mapdata/danti/xxjt005/tileset.json",
  TerrainUrl: isLocal
    ? "http://localhost/mapdata/terrain"
    : "https://cesium.dev.nnric.net/mapdata/terrain2",
  WuXiang3DTiles: "http://localhost/mapdata/3dtiles/wuxiang2/tileset.json",
};
//#region 天地图组合底图
const tdtimg_czm = [
  {
    layerid: "tdtimg_czm001",
    isBaseLayer: true,
    useMapModel: ["cesium"],
    visible: true,
    name: "影像底图(czm)",
    showInCatalog: true,
    layerType: ILayerType.BaseImageLayer,
    // 
    url:
      "http://{s}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w" +
      "&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
    serviceType: "tiandiMap",
    options: {
      mapoption: {
        layer: "img",
        // layer: 'cia',
        style: "default",
        format: "tiles",
        tileMatrixSetID: "w",
        // tileMatrixSetID: "c",
        tilingScheme:new Cesium.WebMercatorTilingScheme(),
        subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
      },
    },
  },
  // { 
  //   url:
  //     "http://t{s}.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w" +
  //     "&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
  //   serviceType: "tiandiMap",
  //   options: {
  //     mapoption: {
  //       layer: "vic",
  //       style: "default",
  //       format: "tiles",
  //       tileMatrixSetID: "w",
  //       subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
  //     },
  //   },
  // },
];
const tdtimg_arc = [
  {
    layerid: "tdtimg_czm001",
    isBaseLayer: false,
    useMapModel: [mapType.arcgis],
    visible: true,
    name: "影像底图(czm)",
    showInCatalog: true,
    layerType: ILayerType.ImageryLayer,
    url:
      "http://{subDomain}.tianditu.gov.cn/img_c/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c" +
      "&TileMatrix={level}&TileRow={row}&TileCol={col}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
    serviceType: "tiandiMap",
  },
  // {
  //   layerid: "tdtimg_czm001",
  //   isBaseLayer: false,
  //   useMapModel: [mapType.arcgis],
  //   visible: true,
  //   name: "影像底图注记(czm)",
  //   showInCatalog: true,
  //   layerType: ILayerType.BasePointLineLayer,
  //   url:
  //     "http://{subDomain}.tianditu.gov.cn/cia_c/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c" +
  //     "&TileMatrix={level}&TileRow={row}&TileCol={col}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
  //   serviceType: "tiandiMap",
  // },
];
const tdtvec_czm = [
  {
    //方法2
    layerid: "tdtvec_arc003",
    isBaseLayer: false,
    useMapModel: ["cesium"],
    visible: false,
    name: "矢量底图(czm)",
    showInCatalog: false,
    layerType: ILayerType.BaseImageLayer,
    url:
      "http://t{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w" +
      "&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
    serviceType: "tiandiMap",
    options: {
      mapoption: {
        layer: "vec",
        style: "default",
        format: "tiles",
        tileMatrixSetID: "w",
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
      },
    },
    /*
    url:
      "http://t{s}.tianditu.gov.cn/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w" +
      "&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
    serviceType: "tiandiMap",
    options: {
      mapoption: {
        layer: "vic",
        style: "default",
        format: "tiles",
        tileMatrixSetID: "w",
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
      },*/
  },
  // {
  //   layerid: "tdtvec_arc003",
  //   isBaseLayer: true,
  //   useMapModel: ["cesium"],
  //   visible: true,
  //   name: "矢量底图注记(czm)",
  //   showInCatalog: false,
  //   layerType: ILayerType.BasePointLineLayer,
  //   url:
  //     "http://t{s}.tianditu.gov.cn/cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w" +
  //     "&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
  //   serviceType: "tiandiMap",
  //   options: {
  //     mapoption: {
  //       layer: "vec",
  //       style: "default",
  //       format: "tiles",
  //       tileMatrixSetID: "w",
  //       subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
  //     },
  //   },
  // },
];
const tdtvec_arc = [
  {
    layerid: "tdtvec_arc003",
    isBaseLayer: true,
    useMapModel: [mapType.arcgis],
    visible: true,
    name: "矢量底图(arc)",
    showInCatalog: false,
    layerType: ILayerType.BaseImageLayer,
    url:
      "https://{subDomain}.tianditu.gov.cn/vec_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0" +
      "&LAYER=vec&STYLE=default&TILEMATRIXSET=c" +
      "&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=tile&tk=4267820f43926eaf808d61dc07269beb",
    serviceType: "tiandiMap",
  },
  // {
  //   layerid: "tdtvec_arc003",
  //   isBaseLayer: true,
  //   useMapModel: [mapType.arcgis],
  //   visible: true,
  //   name: "矢量底图注记(arc)1",
  //   showInCatalog: true,
  //   layerType: ILayerType.BasePointLineLayer,
  //   url:
  //     "https://{subDomain}.tianditu.gov.cn/cva_c/wmts?service=wmts&request=GetTile&version=1.0.0" +
  //     "&LAYER=cva&style=default&tileMatrixSet=c" +
  //     "&TileMatrix={level}&TileRow={row}&TILECOL={col}&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
  //   serviceType: "tiandiMap",
  // },
];
//#endregion

/** 原始的 MapLayer 参数数组 */
export const mapLayersUrl = [
  //@ts-ignore 测试数据
  {
    layerid: "08d9a80b-a21f-4d33-89123ab1-95c89663552f12",
    name: "测试数据",
    showInCatalog: true,
    children: [
      //@ts-ignore   测试Extent（cesium,arcGIS）
      {
        showInCatalog: true,
        isBaseLayer: false,
        layerid: "235532267890",
        visible: false,
        name: "测试Extent（cesium,arcGIS）",
        serviceType: serviceType.ArcgisDynamicLayer,
        layerType: ILayerType.PolylineLayer,
        isDynamicMapLayer: true,
        url: "http://localhost:6080/arcgis/rest/services/yusy/yusy_diji/MapServer",
        options: {
          mapoption: {
            rectangle: {
              west: 107.62122442800006,
              south: 22.168714009850042,
              east: 108.88951322460007,
              north: 23.784075189150066,
            },
          },
        },
      },
      //@ts-ignore ArcgisTilesMap底图1
      {
        layerid: "ArcgisTilesMap001",
        showInCatalog: true,
        isBaseLayer: false,
        name: "ArcgisTilesMap底图1",
        visible: false,
        layerType: ILayerType.ImageryLayer,
        isDynamicMapLayer: false,
        serviceType: "ArcgisTilesMap",
        subLayers: [
          {
            layerid: "ArcgisTilesMap001",
            showInCatalog: true,
            isBaseLayer: false,
            useMapModel: [mapType.cesium],
            name: "ArcgisTilesMap底图czm",
            visible: true,
            layerType: ILayerType.ImageryLayer,
            isDynamicMapLayer: false,
            url: "/proxhz/mapdata/arcmap/yusy_yusy_fcg_jiexianimagebuffer/{z}/{y}/{x}.png",
            serviceType: "ArcgisTilesMap",
            options: {
              mapoption: {
                minimumLevel: 1,
                maximumLevel: 16,
                rectangle: {
                  west: 107.5485312539593,
                  south: 21.691371951628508,
                  east: 108.50941363434033,
                  north: 22.013422846454546,
                },
              },
            },
          },
          {
            layerid: "ArcgisTilesMap001",
            showInCatalog: true,
            isBaseLayer: false,
            useMapModel: [mapType.arcgis],
            name: "ArcgisTilesMap底图arc",
            visible: true,
            layerType: ILayerType.ImageryLayer,
            isDynamicMapLayer: false,
            url: "/proxhz/mapdata/arcmap/yusy_yusy_fcg_jiexianimagebuffer/{z}/{y}/{x}.png",
            serviceType: "ArcgisTilesMap",
            options: {
              mapoption: {
                minimumLevel: 1,
                maximumLevel: 16, 
                spatialReference: {
                  wkid: 4490,
                },
                initialExtent: {
                  xmin: 107.50048713494024,
                  ymin: 21.675269406887207,
                  xmax: 108.55745775335939,
                  ymax: 22.029525391195847,
                },
                fullExtent: {
                  xmin: 107.5485312539593,
                  ymin: 21.691371951628508,
                  xmax: 108.50941363434033,
                  ymax: 22.013422846454546,
                },
              },
            },
          },
        ],
      },
      //@ts-ignore tdt影像+注记1AQ"
      {
        layerid: "tdtimg_czm001",
        showInCatalog: true,
        isBaseLayer: false,
        name: "tdt影像+注记U",
        visible: false,
        layerType: ILayerType.ImageryLayer,
        isDynamicMapLayer: false,
        serviceType: serviceType.tiandiMap,
        subLayers: [...tdtimg_czm, ...tdtimg_arc],
      },

      {
        layerid: "arcgisimg_czm001",
        showInCatalog: true,
        isBaseLayer: false,
        name: "esri服务",
        visible: false,
        layerType: ILayerType.ImageryLayer,
        isDynamicMapLayer: false,
        serviceType: serviceType.ArcgisMapImageLayer,
        subLayers: [
          {
            showInCatalog: true,
            isBaseLayer: false,
            layerid: "235532267890",
            visible: false,
            name: "esri服务",
            serviceType: serviceType.ArcgisMapImageLayer,
            layerType: ILayerType.ImageryLayer,
            isDynamicMapLayer: false,
            url: "http://localhost:6080/arcgis/rest/services/yusy/earthmap2000/MapServer",
              
          }
        ],
      },

      
    ],
  },
  //@ts-ignore 底图数据
  {
    layerid: "08d9a80b-a21f-4d33-89ab-95c89663552f123",
    name: "底图数据",
    children: [
      //@ts-ignore cesium地形 mapType.cesium
      {
        layerid: "czm_terrain",
        isBaseLayer: false,
        name: "cesium地形",
        useMapModel: [mapType.cesium],
        visible: false,
        showInCatalog: false,
        url: "http://localhost/mapdata/terrain",
        serviceType: serviceType.CesiumTerrain,
      }, 
      {
        layerid: "tdtvec_arc003",
        isBaseLayer: true,
        name: "tdt矢量+注记",
        layerType: ILayerType.BaseImageLayer,
        baseLayerType: BaseMapType.vector,
        useMapModel: [mapType.arcgis, mapType.cesium],
        visible: true,
        showInCatalog: false,
        subLayers: [
          // ...tdtimg_czm,
          ...tdtvec_czm,
          // {
          //   layerid: "tdtvec_arc003",
          //   isBaseLayer: true,
          //   useMapModel: [mapType.cesium],
          //   visible: true,
          //   name: "单张图片",
          //   showInCatalog: true,
          //   isDynamicMapLayer: false,
          //   layerType: ILayerType.BaseImageLayer,
          //   url: "/assets/earth.jpg",
          //   serviceType: serviceType.SingleImage,
          // },
          ...tdtvec_arc,
        ],
      },
      //@ts-ignore 2023年卫星影像
      {
        layerid: "2023年卫星影像basemapSatilite2023",
        isBaseLayer: true,
        baseLayerType: BaseMapType.satellite,
        useMapModel: [mapType.arcgis],
        visible: false,
        name: "2023年卫星影像",
        subLayers: [
          //@ts-ignore
          {
            layerid: "天地图影像aatdt_image",
            isBaseLayer: true,
            useMapModel: [mapType.arcgis],
            visible: false,
            name: "天地图影像",
            url:
              //@ts-ignore
              import.meta.env.VITE_TDT_HOST +
              "img_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=c&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=tile&tk=4267820f43926eaf808d61dc07269beb",
            serviceType: serviceType.tiandiMap,
          },
          //@ts-ignore
          {
            layerid: "08da4252-4375-4bb7-8f9a-47e20e4fd228",
            name: "2023年卫星影像",
            useMapModel: [mapType.arcgis],
            visible: false,
            serviceType: serviceType.ArcgisTileLayer,
            showInCatalog: true,
            url: "/onemap/proxy/basemap/08da4252-4375-4bb7-8f9a-47e20e4fd228/u-76935425/mapserver",
          },
          //@ts-ignore
          {
            layerid: "tdt_image_anno",
            isBaseLayer: true,
            useMapModel: [mapType.arcgis],
            visible: false,
            name: "天地图影像注记",
            url:
              //@ts-ignore
              import.meta.env.VITE_TDT_HOST +
              "cia_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=c&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=tile&tk=4267820f43926eaf808d61dc07269beb",
            serviceType: serviceType.tiandiMap,
          },
        ],
      },
      //电子地图11
      {
        layerid: "basemapVector1",
        isBaseLayer: false,
        baseLayerType: BaseMapType.vector,
        useMapModel: [mapType.cesium],
        visible: false,
        name: "电子地图11",
        showInCatalog: false,
        subLayers: [
          //@ts-ignore
          {
            layerid: "tdt_vector",
            isBaseLayer: false,
            useMapModel: [mapType.cesium],
            visible: false,
            name: "电子地图",
            showInCatalog: true,
            url: "http://{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=4267820f43926eaf808d61dc07269beb",
            serviceType: serviceType.tiandiMap,
          },
        ],
      },
    ],
  },
  // 三维数据
  {
    layerid: "08d9a7dc-4b01-4766-8e44-f69d3c9f1a4d54",
    name: "三维数据",
    showInCatalog: true,
    useMapModel: [mapType.cesium],
    children: [
      {
        layerid: "895bcac2-8e16-4154-9140-5250705b551f",
        isBaseLayer: false,
        name: "czm三维数据",
        showInCatalog: true,
        useMapModel: [mapType.cesium],
        visible: true,
        children: [
          //@ts-ignore
          {
            layerid: "10146e3e-1245-47b6-8780-99bdbc285ab7",
            name: "南宁市倾斜模型",
            useMapModel: [mapType.cesium],
            showInCatalog: true,
            layerOrder: 1,
            visible: false,
            isZoomTo: true,
            serviceType: serviceType.Cesium3DTiles,
            url: isLocal //http://localhost/mapdata/3dtiles/wuxiang2/tileset.json
              ? "https://cesium-develop.nnland.cn/mapdata/qingxie/tileset.json"
              : "https://cesium-develop.nnland.cn/mapdata/qingxie/tileset.json",
          },
        ],
      },
      {
        layerid: "895bcac2-8e16-4154-9140-5250705b551f",
        isBaseLayer: false,
        name: "hz三维数据",
        showInCatalog: true,
        useMapModel: [mapType.cesium],
        visible: true,
        children: [
          //@ts-ignore
          {
            layerid: "hzOfice10146e3e-1245-47b6-8780-99bdbc285",
            name: "hzOfice",
            useMapModel: [mapType.cesium],
            showInCatalog: true,
            layerOrder: 1,
            visible: true, 
            serviceType: serviceType.Cesium3DTiles,
            url: "http://localhost/mapdata/hzoffice/tileset.json",
          }, 
          {
            layerid: "hzOfice10146e3e-1245-47b6-812344780-99bdbc285",
            name: "hzOfhzfbxice",
            useMapModel: [mapType.cesium],
            showInCatalog: true,
            layerOrder: 1,
            visible: true, 
            serviceType: serviceType.Cesium3DTiles,
            url: "http://localhost/mapdata/hzfbx/tileset.json",
          },
          
          //@ts-ignore
          {
            layerid: "10146e3e-126786745-47b6-8780-99bdb",
            name: "hzOficeOSGB",
            useMapModel: [mapType.cesium],
            showInCatalog: true,
            layerOrder: 1,
            visible: false,
            isZoomTo: true,
            serviceType: serviceType.Cesium3DTiles,
            subLayers: [
              {
                layerid: "10146e3e-1245-47b611-8780-99bdb",
                name: "hzOficeOSGB",
                useMapModel: [mapType.cesium],
                showInCatalog: true,
                layerOrder: 1,
                visible: false,
                isZoomTo: true,
                serviceType: serviceType.Cesium3DTiles,
                url: "http://localhost/mapdata/hzofficeosgb/tileset.json",
              },
              {
                layerid: "10146e3e-1245-1147b6-80-99bdb",
                name: "hztest",
                useMapModel: [mapType.cesium],
                showInCatalog: true,
                layerOrder: 1,
                visible: false,
                isZoomTo: true,
                serviceType: serviceType.Cesium3DTiles,
                url: isLocal //http://localhost/mapdata/3dtiles/wuxiang2/tileset.json
                ? "https://cesium-develop.nnland.cn/mapdata/qingxie/tileset.json"
                : "https://cesium-develop.nnland.cn/mapdata/qingxie/tileset.json",
              },
            ],
          },
          //@ts-ignore
          
          {
            layerid: "10146e3e-1245-47b3336-80-99bdb1",
            name: "hztest",
            useMapModel: [mapType.cesium],
            showInCatalog: true,
            layerOrder: 1,
            visible: false,
            isZoomTo: true,
            serviceType: serviceType.Cesium3DTiles,
            url: "http://localhost/mapdata/hztest/tileset.json",
          },
        ],
      },
      {
        layerid: "895bcac2-8e16-4143-9140-5250705b551f",
        name: "arc三维数据",
        showInCatalog: true,
        useMapModel: [mapType.cesium],
        children: [
          //@ts-ignore
          {
            layerid: "10146e3e-1223-47b6-8780-99bdbc285ab7",
            name: "ArcGIS倾斜模型",
            useMapModel: [mapType.cesium],
            layerOrder: 1,
            visible: false,
            serviceType: serviceType.Arcgis3DTiles,
            url: isLocal
              ? "https://tiles.arcgis.com/tiles/z2tnIkrLQ2BRzr6P/arcgis/rest/services/Frankfurt2017_vi3s_18/SceneServer/layers/0"
              : "",
            legendUrl:
              "/dev-api/onemap/proxy/map/10146e3e-1233-47b6-8780-99bdbc285ab7/legend?f=json",
            options: {
              /*
               zoomparams:{},
               mapparams: {
                traceFetches: false,
                cesium3dTilesetOptions: {
                  skipLevelOfDetail: true,
                  debugShowBoundingVolume: false,
                },
              },
              isZoomTo: false,*/
              layerid: "10146e3e-1233-472b6-8780-99bdbc285ab7",
              serviceCatalogId: "ebfb88f8-4cb8-4177-a520-7495d21741f0",
              cid: "ebfb88f8-4cb8-4177-a520-7495d21741f0",
              name: "南宁市开发区界线",
              layerTagId: null,
              layerType: serviceType.ArcgisMapImageLayer,
              serviceType: "MapServer",
              featureType: "Polygons",
              coverageType: null,
              group: null,
              isAuthorized: true,
              coverage: "",
              coverageArray: [],
              orderIndex: 1,
              isQuery: true,
              querable: true,
              isAnalyst: false,
              createTime: "2023-06-26 18:48:02",
              enabledExports: [],
              enabledExternalExport: false,
            },
          },
        ],
      },
      {
        layerid: "895bcac2-8e16-4134-9140-5250705b551f",
        name: "super三维数据",
        useMapModel: [mapType.cesium],
        showInCatalog: true,
        children: [
          //@ts-ignore
          {
            layerid: "10146e3e-12333-47b6-8780-99bdbc285ab7",
            name: "超图倾斜模型",
            useMapModel: [mapType.cesium],
            layerOrder: 1,
            visible: false,
            serviceType: serviceType.Super3DTiles,
            url: isLocal ? "/assets/s3m/cbd.scp" : "/assets/s3m/cbd.scp",
            legendUrl:
              "/dev-api/onemap/proxy/map/10146e3e-1233-47b6-8780-99bdbc285ab7/legend?f=json",
            options: {
              /* mapparams: { },*/
              zoomparams: {
                destination: new Cesium.Cartesian3(
                  -2181968.890329965,
                  4385313.17843029,
                  4072712.8241634783
                ),
                orientation: {
                  heading: 3.1756648661534443,
                  pitch: -0.3715184468182904,
                },
              },
              //是否放大到加载数据区域
              isZoomTo: true,
              layerid: "10146e3e-1233-47b6-87480-99bdbc285ab7",
              serviceCatalogId: "ebfb88f8-4cb8-4177-a520-7495d21741f0",
              cid: "ebfb88f8-4cb8-4177-a520-7495d21741f0",
              name: "南宁市开发区界线",
              layerTagId: null,
              layerType: serviceType.ArcgisMapImageLayer,
              serviceType: "MapServer",
              featureType: "Polygons",
              coverageType: null,
              group: null,
              isAuthorized: true,
              coverage: "",
              coverageArray: [],
              orderIndex: 1,
              isQuery: true,
              querable: true,
              isAnalyst: false,
              createTime: "2023-06-26 18:48:02",
              enabledExports: [],
              enabledExternalExport: false,
            },
          },
        ],
      },
      {
        layerid: "895bcac2-8e16-418e-9122-5250705b551f",
        name: "白模数据",
        showInCatalog: true,
        useMapModel: [mapType.cesium],
        children: [
          //@ts-ignore
          {
            layerid: "10146e3e-1233-47b6-8712-99bdbc285ab7",
            name: "白模数据",
            useMapModel: [mapType.cesium],
            layerOrder: 1,
            visible: false,
            serviceType: serviceType.Cesium3DTiles,
            url: "/dev-api/onemap/proxy/map/10146e3e-1233-47b6-8780-99bdbc285ab7/u-76935425/MapServer",
            legendUrl:
              "/dev-api/onemap/proxy/map/10146e3e-1233-47b6-8780-99bdbc285ab7/legend?f=json",
            options: {
              layerid: "10146e3e-1233-47b6-87580-99bdbc285ab7",
              serviceCatalogId: "ebfb88f8-4cb8-4177-a520-7495d21741f0",
              cid: "ebfb88f8-4cb8-4177-a520-7495d21741f0",
              name: "南宁市开发区界线",
              layerTagId: null,
              layerType: serviceType.ArcgisMapImageLayer,
              serviceType: "MapServer",
              featureType: "Polygons",
              coverageType: null,
              group: null,
              isAuthorized: true,
              coverage: "",
              coverageArray: [],
              orderIndex: 1,
              isQuery: true,
              querable: true,
              isAnalyst: false,
              createTime: "2023-06-26 18:48:02",
              enabledExports: [],
              enabledExternalExport: false,
            },
          },
        ],
      },
    ],
  },
];

/** 将MapLayer参数赋值给 layerStore */
//@ts-ignore
layerStore.initTreeDataExecute(mapLayersUrl);
/**
 *数据初始加载范例
 */
export const initShowMapLayer = () => {
  const layers = Utils.getMapLayerByVisible(
    _Onemap.MapType.value,
    true,
    layerStore.MapLayers,
    true
  );
  let tmpLyrs: any = [];
  if (_Onemap.OldImageLayerCollection) {
    let oldlayers: any = [];
    (_Onemap.OldImageLayerCollection as any).forEach((lyr) => {
      oldlayers = [...oldlayers, ...lyr];
    });
    let newlayers: any = [];
    layers.forEach((itm: any) => {
      newlayers.push(itm.layerid);
    });

    oldlayers.forEach((itm: any) => {
      let idx = newlayers.findIndex((_itm) => _itm == itm);
      if (idx > -1) {
        let tmplayer = layers.find((_itm) => _itm.layerid == itm);
        tmpLyrs.push(tmplayer);
        newlayers.splice(idx, 1);
      }
    });

    newlayers.reverse();
    newlayers.forEach((itm: any) => {
      tmpLyrs.unshift(layers.find((_itm) => _itm.layerid == itm));
    });
  } else {
    tmpLyrs = layers;
  }
  tmpLyrs.forEach((layer: IMapLayer) => {
    if (OnemapEvent.LayerCheckServiceAPI) {
      OnemapEvent.LayerCheckServiceAPI(true, layer).then((res: IMapLayer) => {
        _Onemap.AddLayer(res);
      });
    } else {
      _Onemap.AddLayer(layer);
    }
  });
};

/**
 * 获取可见底图
 * @param { mapType } _MapType
 * @param { Array<IMapLayer>} _Layers  MapLayers
 * @returns MapLayers
 */
function getBaseMapLayers(_MapType: mapType, _Layers: any) {
  const baseLayers: Array<IMapLayer> = Utils.getBaseMapLayerByVisible(
    _MapType,
    true,
    _Layers
  );
  if (_MapType == mapType.cesium) {
    const singlemap = baseLayers.filter(
      (item: any) => item.serviceType === serviceType.SingleImage
    );
    if (singlemap?.length > 0) {
      return singlemap[0];
    } else {
      return baseLayers[0] || false;
    }
  }
}
/** 获取 默认的底图参数
 * @returns IMapLayer
 */
export const BaseMapLayerUrl = (): IMapLayer => {
  const baseLayers: Array<IMapLayer> = Utils.getBaseLayerByVisible(
    true,
    layerStore.MapLayers
  );
  console.log("=====baseLayers",baseLayers)
  if (baseLayers?.length > 0) {
    const singlemap = baseLayers.filter(
      (item: any) => item.serviceType != serviceType.SingleImage
    );
    if (singlemap?.length > 0) {
      return singlemap[0];
    } else {
      return baseLayers[0] || false;
    }
  } else {
    throw new Error("底图参数BaseMapLayer为undefined");
  }
};
export const initShowLayerUrl = (): IMapLayer => {
  const baseLayers: Array<IMapLayer> = layerStore.LayerTreeDataSource(
    (node: IMapLayer) => {
      return (
        (node?.isBaseLayer == undefined || node?.isBaseLayer == false) &&
        node.visible &&
        (node.useMapModel === undefined ||
          node.useMapModel?.some((item: any) => item == _Onemap.MapType.value))
      );
    }
  );

  const singlemap = baseLayers.filter(
    (item: any) => item.serviceType === serviceType.SingleImage
  );
  if (singlemap?.length > 0) {
    return singlemap[0];
  } else {
    return baseLayers[0] || false;
  }
};

export const CustomTools: Array<IToolItemType> = [
  //1.基础工具 1.1定位
  {
    index: 2,
    name: "Location",
    label: "定位",
    icon: Icons.IconHTML.locate,
    component: markRaw(Location),
    groupname: "基础工具",
    Dock: DockType.center,
    isActive: false,
    isQuick: true,
    isFix: true,
    defaultSize: {
      width: 400,
      height: 250,
    },
  },
  //2.基础工具 2.1分屏
  {
    index: 3,
    name: "splitscreen",
    label: "分屏",
    icon: Icons.IconHTML.clear,
    component: markRaw(splitscreen),
    isLayerStore: true,
    isPropStore: true,
    groupname: "基础工具",
    Dock: DockType.center,
    paramType: 0,
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 400,
      height: 320,
    },
  },
  //2.基础工具 2.2 绘制
  {
    index: 4,
    name: "DrawTools",
    label: "绘制",
    icon: Icons.IconHTML.draw,
    component: markRaw(DrawTools),
    groupname: "基础工具",
    Dock: DockType.center,
    paramType: 0,
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 550,
      height: 220,
    },
  },
  //3.三维工具 3.1景观亮化
  {
    index: 6,
    name: "SceneLight",
    label: "景观亮化",
    icon: Icons.IconHTML.clear,
    component: markRaw(SceneLight),
    groupname: "三维工具",
    Dock: DockType.center,
    paramType: "SceneLight",
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 750,
      height: 720,
    },
    useMapModel: [mapType.cesium],
  },
  //3.三维工具 3.2三维漫游
  {
    index: 6,
    name: "ViewRoamer",
    label: "三维漫游",
    icon: Icons.IconHTML.clear,
    component: markRaw(ViewRoamer),
    groupname: "三维工具",
    Dock: DockType.center,
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 400,
      height: 220,
    },
  },
  //3.三维工具 3.3地下模式
  {
    index: 6,
    name: "UnderGround",
    label: "地下模式",
    icon: Icons.IconHTML.clear,
    component: markRaw(UnderGround),
    isPropStore: true,
    groupname: "三维工具",
    Dock: DockType.center,
    paramType: 2,
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 320,
      height: 130,
    },
  },
  //3.三维工具 3.4日照分析
  {
    index: 6,
    name: "SunlightAnalysis",
    label: "日照分析",
    icon: Icons.IconHTML.clear,
    component: markRaw(SunlightAnalysis),
    isPropStore: true,
    groupname: "三维工具",
    paramType: 2,
    Dock: DockType.center,
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 600,
      height: 200,
    },
  },
  //4.参数设置 4.1调试工具
  {
    index: 7,
    name: "debug",
    label: "调试工具",
    icon: Icons.IconHTML.clear,
    component: markRaw(debug),
    groupname: "参数设置",
    Dock: DockType.center,
    useMapModel: [mapType.cesium],
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 280,
      height: 150,
    },
  },
  //4.参数设置 4.2存储管理
  {
    index: 7,
    name: "CacheSet",
    label: "存储管理",
    icon: Icons.IconHTML.clear,
    groupname: "参数设置",
    component: markRaw(CacheSet),
    paramType: 2,
    Dock: DockType.center,
    useMapModel: [mapType.cesium],
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 400,
      height: 260,
    },
  },
  //4.参数设置 4.3三维场景设置
  {
    index: 7,
    name: "sceneset",
    label: "三维场景设置",
    icon: Icons.IconHTML.clear,
    groupname: "参数设置",
    component: markRaw(sceneset),
    Dock: DockType.center,
    paramType: 2,
    useMapModel: [mapType.cesium],
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 360,
      height: 210,
    },
  },
  //4.参数设置 4.4精度设置
  {
    index: 7,
    name: "ModedlLodSet",
    label: "精度设置",
    icon: Icons.IconHTML.clear,
    groupname: "参数设置",
    component: markRaw(ModedlLodSet),
    paramType: 2,
    Dock: DockType.center,
    useMapModel: [mapType.cesium],
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 350,
      height: 150,
    },
  },
  //4.参数设置 4.5三维场景设置
  {
    index: 7,
    name: "BlendColor",
    label: "模型颜色校正",
    icon: Icons.IconHTML.clear,
    groupname: "参数设置",
    component: markRaw(BlendColor),
    Dock: DockType.center,
    paramType: 2,
    useMapModel: [mapType.cesium],
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 420,
      height: 260,
    },
  },
];
/** 业务系统实现的图层加载前的事件,勾选后,会触发该事件,
 * 业务系统可以在该事件中实现自己的逻辑,该函数为异步函数,入参为图层的初始配置,返回值为修改后的图层配置
 * @param layer
 */
export const layerCheckEvent = async (
  isCheack: Boolean,
  layer: IMapLayer
): Promise<IMapLayer> => {
  console.log("ysy ==== layer = ", layer);
  // 读取接口获取图层配置信息
  try {
    for (let item of layer?.subLayers ?? []) {
      // @ts-ignore
      try {
        item.options = item.options || ({ customProperty: null } as any);
        const res = await axios.get(
          //@ts-ignore
          `${
            //@ts-ignore
            import.meta.env.VITE_SERVICE_PREFIX
          }/onemap/api/gis/server/info?id=${item.layerid}`
        );
        let dt =
          res && res.data && res.data.data && res.data.data.attribute
            ? res.data.data.attribute
            : null;
        if (dt) {
          //@ts-ignore
          item.options.customProperty = JSON.parse(dt);
        }
      } catch (error) {
        console.log(error);
      }

      // 如果获取配置失败，并且服务类型为arcgis的服务，则自己请求图层数据，获取图层的属性信息
      if (
        item.options.customProperty != null &&
        item.serviceType?.toLocaleLowerCase().includes("arcgis")
      ) {
        try {
          const res = await axios.get(
            //@ts-ignore
            `${item.url}?f=json`
          );

          let dt = res.data;
          // 获取子图层配置信息
          if (dt && dt.layers) {
            for (let layerItem of dt.layers) {
              try {
                const res2 = await axios.get(
                  //@ts-ignore
                  `${item.url}/${layerItem.id}?f=json`
                );
                if (res2 && res2.data) {
                  Object.assign(
                    dt.layers.find((x) => x.id == layerItem.id),
                    res2.data
                  );
                }
              } catch (error) {
                console.log(error);
              }
            }

            item.options.customProperty = dt;
          }
        } catch (error) {
          console.log(error);
        }
      }
    }
  } catch (error) {
    console.log(error);
  }
  return layer as any;
};
export const layerCheckEvent2 = async (
  isCheack: boolean,
  layer: IMapLayer
): Promise<IMapLayer> => {
  //这里写业务处理函数
  // ((layer.options ||{}) as any).customProperty = "customproperty";
  layer.options =
    layer.options ||
    ({
      customProperty: {},
    } as any);
  // 读取接口获取图层配置信息
  try {
    for (let item of layer.subLayers ?? []) {
      // @ts-ignore
      const res = await axios.get(
        //@ts-ignore
        `${import.meta.env.VITE_SERVICE_PREFIX}/onemap/api/gis/server/info?id=${
          item.layerid
        }`
      );
      let dt =
        res && res.data && res.data.data && res.data.data.attribute
          ? res.data.data.attribute
          : null;
      if (dt) {
        //@ts-ignore
        item.options.customProperty = JSON.parse(dt);
      }
    }
  } catch (error) {
    console.log(error);
  }
  return layer as any;
};
