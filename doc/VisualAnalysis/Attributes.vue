<template>
  <utable :fileds="fileds" :tabledata="tabledata"></utable>
</template>

<script setup>
import utable from "/packages/table/table.vue";

const fileds = [
  {
    field: "attr",
    title: "参数",
    align: "center",
  },
  {
    field: "type",
    title: "类型",
    align: "center",
  },
  {
    field: "red",
    title: "说明",
    align: "center",
    width: "350px",
  },
  {
    field: "sel",
    title: "可选值",
    align: "center",
  },
  {
    field: "def",
    title: "默认值",
    align: "center",
  },
];
const tabledata = [
  {
    attr: "viewer",
    type: "Cesium.Viewer",
    red: "Cesium场景的Viewer对象",
    def: "",
  },
  {
    attr: "start",
    type: "Boolean",
    red: "是否绘制天际线",
    sel: "true",
    def: "false",
  },
  {
    attr: "color",
    type: "string",
    red: "天际线颜色",
    sel: "十六进制颜色值",
    def: "#FF0000",
  },
  {
    attr: "width",
    type: "Number",
    red: "默认宽度",
    sel: "1-5",
    def: "1",
  },
];
</script>

<style lang="scss" scoped>

</style>
