<template> 
  <utable   :tabledata="tabledata"></utable>
</template>

<script setup>
import {utable} from "../../packages/onemapkit";

const tabledata = [
{
    fldName: "MapRealtimeEvent",
    fldType: `{
      Interval: Number;
      MapEvent: 回调函数（Function）;
    }`,
    fldDescribe:
      "地图实时变化的回调函数，Interval为节流参数，MapEvent(MapViewer:Viewer, location:IPosition)为回调函数",
    fldOption: "",
    fldDefault: "Interval默认值为400",
  },
  {
    fldName: "MapReadyEvent",
    fldType: `回调函数（Function）`,
    fldDescribe: "Viewer实例化完成之后的回调函数",
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "SwitchMapEvent",
    fldType: `回调函数（Function）`,
    fldDescribe: "地图切换时的回调函数",
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "LayerCheckEvent",
    fldType: `Function`,
    fldDescribe:
      "图层加载前的事件,由业务系统自己实现,通过属性方式传递至目录树组件",
    fldOption: "",
    fldDefault: "",
  },
];
 
</script>

<style lang="scss" scoped></style>
