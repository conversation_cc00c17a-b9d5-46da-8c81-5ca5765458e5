<template> 
  <utable :fileds="fileds" :tabledata="tabledata"></utable>
</template>

<script setup>
import {utable} from "../../packages/onemapkit"; 

const fileds = [
  {
    field: "attr",
    title: "参数",
    align: "center",
  },
  {
    field: "type",
    title: "类型",
    align: "center",
  },
  {
    field: "red",
    title: "说明",
    align: "center",
    width: "350px",
  },
  {
    field: "sel",
    title: "可选值",
    align: "center",
  },
  {
    field: "def",
    title: "默认值",
    align: "center",
  },
];
const tabledata = [
  {
    attr: "viewer",
    type: "Cesium.Viewer",
    red: "Cesium场景的Viewer对象",
    def: "",
  },
  {
    attr: "lights",
    type: "Object[]",
    red: "默认添加的光源",
    def: "[]",
  },
];
</script>

<style lang="scss" scoped></style>
