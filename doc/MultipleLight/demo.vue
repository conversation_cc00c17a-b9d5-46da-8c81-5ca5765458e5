<!-- html -->
<template>
  <el-button type="primary" @click="funpopShow">弹出面板</el-button>
  <pop-panel
    v-if="popShow"
    :title="'测试面板'"
    :width="434"
    :height="600"
    @close="close"
  >
    <template #content>
      <MultipleLight
        ref="MultipleLightRef"
        :PropStore="PropStore"
        :LayerStore="layerStore"
        :lights="lights"
      ></MultipleLight>
    </template>
  </pop-panel>
  <div class="container" id="parentContainer">
    <MapControl 
      MapControlName="mainMapControl"
      :PropStore="PropStore"
      :LayerStore="layerStore"
      @MapReadyEvent="MapReadyEvent"
    ></MapControl>
  </div>
</template>

<script setup lang="ts">

import { ref } from "vue";
import {
  MapControl,
  PopPanel,
  MultipleLight,
  mapType,
  type IPosition,
  InitMapControl,
  getOnemap,getOption
} from "../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";
/**第一步： 导入图层参数*/
import {
  _Onemap,
  layerStore,PropStore,
  ServiceUrl,
  BaseMapLayerUrl,
  initShowMapLayer,
} from "../layer";

/**
 * 第二步：配置公共参数
 */
const Options = Object.assign(getOption(), {
  BASE_URL: "ThirdParty",
  mapType: mapType.cesium,
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
});
InitMapControl(_Onemap, {
  MapControlName: "mainMapControl",
  Options: Options,
  Components: {
    MapLayerTreeVisible: false,
    SwitchMapVisible: false,
    MapToolsVisible: false,
    BottomInfoVisible: false,
    PropertyVisible: false,
  },
});
//鼠标坐标需要在界面上实时显示，因此需要做数据劫持
//@ts-ignore

//MapControl 组件的地图加载完毕的回调方法,是获取MapViewer的一种方式
let lightset = null;

// 默认进入页面的光源
const lights = [
  {
    color: "#ffffff",
    directionPoint: "",
    innerConeDegrees: 10,
    intensity: 100,
    lightType: "pointLight",
    name: "点光源1",
    outerConeDegrees: 45,
    position: "108.38982790268098,22.71079116498716,130",
    radius: 80,
  },
  {
    color: "#ff0000",
    directionPoint: "108.38980119878254,22.711189912686685,117.33",
    innerConeDegrees: 10,
    intensity: 100,
    lightType: "spotLight",
    name: "聚光灯1",
    outerConeDegrees: 45,
    position: "108.38964079542149,22.711201933841057,126.08",
    radius: 80,
  },
  {
    color: "#00ff00",
    directionPoint: "",
    innerConeDegrees: 10,
    intensity: 100,
    lightType: "pointLight",
    name: "点光源2",
    outerConeDegrees: 45,
    position: "108.3898611308499,22.711635960813965,130",
    radius: 80,
  },
];
let popShow = ref(false);
const funpopShow = () => {
  _Onemap.MapViewer.flyTo(lightset);
  popShow.value = true;
  // return popShow.value;
};
const close = () => {
  popShow.value = false;
};

function MapReadyEvent(_cesiumViewer: any) {   
  const cesiumViewer = getOnemap(_cesiumViewer).MapViewer; 
  initShowMapLayer();
  cesiumViewer.scene.requestRenderMode = false;
  const tilesetPromise = Cesium.Cesium3DTileset.fromUrl(ServiceUrl.model_XXJT);

  tilesetPromise.then((tileset) => {
    console.log("import.meta.env.isLocal =", tileset);
    cesiumViewer.scene.primitives.add(tileset);
    cesiumViewer.flyTo(tileset);
  });

  // 时间初始化
  cesiumViewer.clock.currentTime.dayNumber = 2460074;
  cesiumViewer.clock.currentTime.secondsOfDay = 17482.129938176193;
 
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
