<template>
  <utable :fileds="fileds" :tabledata="tabledata"></utable>
</template>

<script setup>
import {utable} from "../../packages/onemapkit";

const fileds = [
  {
    field: "attr",
    title: "参数",
    align: "center",
  },
  {
    field: "type",
    title: "类型",
    align: "center",
  },
  {
    field: "red",
    title: "说明",
    align: "center",
    width: "350px",
  },
  {
    field: "sel",
    title: "可选值",
    align: "center",
  },
  {
    field: "def",
    title: "默认值",
    align: "center",
  },
];
const tabledata = [
  {
    attr: "type",
    type: "String",
    red: "按钮类型",
    sel: "default / primary / success / danger / warning / dashed / text / custom",
    def: "default",
  },
  {
    attr: "size",
    type: "String",
    red: "按钮尺寸",
    sel: "default / medium / small / mini",
    def: "default",
  } 
];
</script>

<style scoped></style>
