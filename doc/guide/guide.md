#  指南

<br />

### 本组件是基于 vue3.2 开发，旨在秉承数据驱动视图，力求更小的体积、更快的相应、更强的升级拓展性、更完美的支持、更友好的渲染。请阅读以下内容。
<br />
  
## packages包中为项目封装组件

## doc包为项目的说明文档包
- ### install 组件安装方法文档 
- ### guide 相关约定规范指南文档
- ### updatelog 更新日志文档
- ### 其他文件夹为项目封装组件文档

 
 ## src包中个文件夹说明

 - ### AppMap 为移动demo示例项目
 - ### OneMap 为一张图demo示例项目
 - ### 其他为项目的辅助文档 

## 分支规范

- ### 代码请保持在 *dev* 分支，禁止操作 *main* 分支。
- ### 组件文档需要提供 *md* 文件，包括（使用方式、演示、配置项、事件 API、插槽说明...）。

## 目录内容描述

 ### 1 在packages下添加组件文件（如：yusy）
- #### 1.1 在yusy下添加组件和脚本
- #####    1、yusy.vue 组件对象
- #####    2、yusy.ts 脚本对象
- #####    3、iyusy.ts 接口对象 

 ####  1.2 在doc下添加组件文档文件（必须与packages内一样，如：yusy）
- #####    1、yusy.md 组件说明文档对象
- #####    2、yusy.vue 组件使用例子
- #####    3、Attributes.vue 组件属性接口说明 
- #####    4、Eventage.vue 组件事件接口说明 
- #####    5、Functions.vue 组件方法接口说明 

 ####  1.3 在src下配置路由（/src/router/pages/pages.ts）
- #####    1、所有组件均在 "/mapcomponent" 分支
- #####    2、所有组件均在 "index、name、path" 必填项， "component、children" 必填项，
- #####    3、所有组件必须归类到一个组件集下  

 <br />  

```javascript
├─package.json 
├─tsconfig.json
├─tsconfig.node.json
├─vite.config.ts           -- vite配置文件
├─packages                 -- 组件库目录 
|    ├─env.d.ts            -- 类型声明文件
|    ├─onemapkit.ts        -- 组件库打包入口文件
|    ├─utils               -- 组件库公共方法存放目录
|    |   └utils.ts         -- 一些组件库的公共方法
|    ├─CesiumMap           -- CesiumMap组件
|    |     ├─CesiumMap.vue -- CesiumMap组件内容
|    |     ├─index.ts      -- CesiumMap组件导出入口
|    |     └─imap.ts       -- CesiumMap组件的参数/事件定义
├─doc                      -- 组件库文档工程目录（使用vuepress构建） 
|  ├─guide                 -- 组件库文档快速上手的内容
|  |   ├─updatelog.md
|  |   ├─guide.json        -- 组件库文档快速上手中的目录信息
|  |   ├─install.md 
|  |   ├─table 
|  |   |   ├─table.md      -- 组件说明文档对象
|  |   |   ├─demo1.vue
|  |   |   ├─Attributes.vue  -- 组件属性接口说明
|  |   |   ├─Eventage.vue  --组件事件接口说明 
|  |   |   ├─Functions.vue --组件方法接口说明   
|  ├─build                   
|  |    ├─build.js         --编译脚本  
|  ├─src                   
|  |    ├─AppMap           --移动应用示例 
|  |    ├─OneMap           --一张图应用示例 
|  |    ├─views            --项目组件集
 
``` 
<br />
<br />

## 文档集成规范

- ### 组件目录下新增 doc  文件夹用于文档编写。
- ###  doc 文件夹下提供 ***.md ，一个组件最多只允许一个 *.md* 文件。
- ###  doc 文件同级存放  demo.vue  文件， demo可以多个。
- ### 然后在 doc.md 文件顶部使用  setup  语法糖的方式引入  demo  文件，使用组件的形式展示。  
- ### 代码预览文件为 src/views/Preview.vue，同时在  doc.md  文件以组件的形式引入  Preview.vue  ，接收两个*props*参数， compName  为组件目录名（建议与路由名保持一致）， demoName  为要展示的  demo  文件名，例如：
  ```javascript
  //doc.md
  <script setup>
    import demo1 from './demo1.vue'; 
    import demo2 from './demo2.vue'; 
    import preview from '/src/views/preview.vue'
  </script>

  <template>
      <div class="componetnsBox">
        <demo1/>
      </div>
      <preview compName="button" demoName="demo1"/>
      <div class="componetnsBox">
        <demo2/>
      </div>
      <preview compName="button" demoName="demo2"/>
  </template>

  
  ```

  <br />

  ## 组件开发规范

- ### 通过在 *packages* 目录下创建组件目录结构，包含测试代码、入口文件、文档。
- ### 组件入口文件必须以 *index.ts* 命名，并包含 *install* 方法,参考代码：
  ```javascript
  import ubutton from "./index.vue";
  ubutton.install = (app) => {
    app.component(ubutton.name || "ubutton", ubutton);
  };
  export default ubutton;
  ```
  <br/>
- ### *packages* 文件夹下 *onemapkit.ts* 作为整体入口文件，须包含所有组件  

  <br/>
- ### 任何组件禁止使用三方依赖库。
- ### 组件内如果依赖了其他组件，需要在当前组件内引入。

## 代码规范

- ### 避免过多的 html 代码累赘，秉承数据驱动视图思想，简化使用繁琐度，提供更强的拓展性。




