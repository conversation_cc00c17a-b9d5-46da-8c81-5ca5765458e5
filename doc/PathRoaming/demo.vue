<!-- html -->
<template>
  <el-button type="primary" @click="funpopShow">弹出面板</el-button>
  <pop-panel
    v-if="popShow"
    :title="'测试面板'"
    :width="434"
    :height="600"
    @close="close"
  >
    <template #content>
      <PathRoaming
        ref="PathRoamingRef"
        :PropStore="PropStore"
        :LayerStore="layerStore"
        :positions="positions"
        :pathColor="pathColor"
      ></PathRoaming>
    </template>
  </pop-panel>

  <div class="container" id="parentContainer">
    <MapControl
      :PropStore="PropStore"
      :LayerStore="layerStore"
      :MapReadyEvent="MapReadyEvent"
    ></MapControl>
  </div>
</template>

<script setup lang="ts">

import { ref } from "vue";
import {
  MapControl,
  PopPanel,
  PathRoaming,
  mapType,
  getOnemap,
  InitMapControl,
} from "../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";

/**第一步： 导入图层参数 */
import {
  _Onemap,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  initShowMapLayer,
} from "../layer";

if (_Onemap.MapType.value != mapType.cesium)
  _Onemap.setMapType(_Onemap.MapType.value);

const Options = {
  id: "mapviewerContainer",
  BASE_URL: "ThirdParty",
  mapType: mapType.cesium,
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
};
InitMapControl(_Onemap, {
  MapControlName: "mainMapControl",
  Options: Options,
  Components: {
    MapLayerTreeVisible: false,
    SwitchMapVisible: false,
    PropertyVisible: false,
    MapToolsVisible: false,
    BottomInfoVisible: false,
  },
});
//MapControl 组件的地图加载完毕的回调方法,是获取MapViewer的一种方式
const MapReadyEvent = (_cesiumViewer: any, is3D: any) => {
  const cesiumViewer = getOnemap(_cesiumViewer).MapViewer;

  initShowMapLayer();
  cesiumViewer.scene.requestRenderMode = false;
  const tilesetPromise = Cesium.Cesium3DTileset.fromUrl(ServiceUrl.model_XXJT);
  tilesetPromise.then((tileset) => {
    console.log("import.meta.env.isLocal =", tileset);
    cesiumViewer.scene.primitives.add(tileset);
    cesiumViewer.flyTo(tileset);
  });
};

// 默认进入页面的信息
const positions = [
  new Cesium.Cartesian3(
    -1857026.1017532153,
    5586082.971652261,
    2447227.439032078
  ),
  new Cesium.Cartesian3(
    -1857115.2500020594,
    5586054.846864679,
    2447219.865996624
  ),
  new Cesium.Cartesian3(
    -1857131.4501692026,
    5586041.760966572,
    2447237.300022894
  ),
  new Cesium.Cartesian3(
    -1857136.6656788485,
    5586025.592045486,
    2447269.473582927
  ),
  new Cesium.Cartesian3(
    -1857127.212887699,
    5586009.091585838,
    2447313.1813296555
  ),
  new Cesium.Cartesian3(
    -1857117.7021584085,
    5585983.959249556,
    2447376.7349041156
  ),
];
const pathColor = "#ff00ff";

let popShow = ref(false);
const funpopShow = () => {
  popShow.value = true;
};
const close = () => {
  popShow.value = false;
  if (_Onemap.MapType.value == mapType.cesium)
    _Onemap.MapViewer.scene.requestRenderMode = true;
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
