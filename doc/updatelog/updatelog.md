#### ***更新日志写在前面，确保最新更新的在最前面***

<br />

### 2023.11.13 
- 修改Onemap实现类的销毁机制、地图实时信息回调、获取三维当前视域范围
<br />

### 2023.11.12 
- 增加定位工具，arcgis3坐标转换
<br /> 

### 2023.11.10 
- 修改了cesiummap\arcgismap 初始化类别参数，修改了Onemap底层，规范函数名称，实现类销毁方法
<br />

### 2023.11.9 
- 修复ViewPointRoaming组件关闭相机控制锁死的问题
- 修改定位工具地图为Map，调整onemap中的定位点功能
<br />

### 2023.11.8 
- 新增ViewPointRoaming视点漫游组件，新增ViewPointRoaming视点漫游组件，导出MultipleLight多光源组件
- arcgis3地图加载时,加载图层和全幅改为Onemap方法
- 集成Arcgis3方法，调整天地图加载
<br />
 
### 2023.11.7 
- Onemap接口逻辑修改、Store逻辑修改
- 调整arcgis 3的加载，天地图改为通用图层加载方式
- 修改通用props
<br />

### 2023.11.6
- 新增MultipleLight多光源组件  
<br />

### 2023.11.3 
- 修复CesiumMap警告，增加getViewer方法获取viewer对象
-  CesiumMap新增ready事件
- MaplayersStore 
<br />

### 2023.11.2 
- 化底层存储技术框架
<br />

### 2023.11.1 
- 优化自定义工具，自定义工具持久化
<br />

### 2023.10.31 
- 自定义工具栏，优化底层技术框架，增加From组件，
- 优化底层接口技术方案，更多工具调整
<br />

### 2023.10.30 
- 修改资源目录面板
<br />

### 2023.10.29 
- 增加地图工具栏，修改一张图demo页面
<br />

### 2023.10.27 
- 二三维地图和切换控件
<br />

### 2023.10.26 
- 地图资源管理
<br />

### 2023.10.25 
- 增加图层勾选demo
<br />

### 2023.10.24 
- 资源面板组件框架，定位增加图上拾取功能，坐标转换
<br />

### 2023.10.23 
- 增加Arcgis4 定位面板及示例基本功能
<br />

### 2023.10.21 
- 增加地图底栏信息组件
<br />

### 2023.10.19
- 修改arcgis统一实现调用Onemap方法，增加arcgis 4版本通用方法
<br />
  
### 2023.10.18 
- 增加CesiumMap组件（Utils组件下）的地图加载与删除接口
<br />

### 2023.10.17
- 增加公共组件 弹出面板PopPanel
<br />

### 2023.10.17 
- 更新CesiumMap组件，Utils组件下的Onemap类的数据接口设计方案

<br />

###  2023.10.11 
- 原始代码框架调整

<br />

### 2023.10.08 
- 原始代码框架
