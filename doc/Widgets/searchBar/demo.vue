<template>
  <div class="container" id="parentContainer">
    <Map ref="MapControlRef" :LayerStore="layerStore" :PropStore="_PropStore" @MapReadyEvent="_MapReadyEvent" />
    <search-bar :Onemap="_Onemap" :PropStore="_PropStore" MapControlName="mainMapControl" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, provide, computed } from "vue";
import {
  Map,
  defaultParameter,
  InitMapControl,
  SearchBar,
} from "../../../packages/onemapkit";

import {
  _Onemap,
  PropStore as _PropStore,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  initShowMapLayer,
  CustomTools
} from "../../layer";


InitMapControl(_Onemap, {
	BASE_URL: "ThirdParty",
	MapControlName: "mainMapControl",
	BaseMapLayer: BaseMapLayerUrl(),
	TerrainUrl: "/dev-api/cesium/mapdata/08dcc357-5831-4d5d-8b9f-045c1e8de241",
	MapLayerTree: Object.assign(
		defaultParameter.defaultLayerContentTab,
		defaultParameter.defaultMapTreeTab,
		{
			// 是否显示详细底图面板
			basemap: {
				visible: true
			}
		}
	),
	DrawTools: defaultParameter.defaultEditToolItems,
	MapTools: {
		Toolset: CustomTools,
		CustomToolSize: {
			width: 400,
			height: 420,
		},
	},
});

const _MapReadyEvent = () => {
	initShowMapLayer();
};
//mounted
onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
