<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :Components="{
        MapLayerTree: {
          visible: false,
        },
        SwitchMapType: {
          visible: true,
        },
        MapTools: {
          visible: false,
        },
        BottomInfo: {
          visible: false,
        },
      }"
      :Options="Options"
      :MapRealtimeEvent="MapRealtimeEvent"
      :Onemap="_Onemap"
      :Store="{
        layers: layerStore,
        Props: PropStore,
      }"
    /> 
    <bottom-info :PositionInfo="positionInfo" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, provide, computed } from "vue";
import {
  Map,
  BottomInfo, 
  mapType, 
  OnemapClass,
} from "../../../packages/onemapkit"; 
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";

/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */

 import {
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
} from "../../layer";

/**
 * 第二步：配置公共参数
 */

//鼠标坐标需要在界面上实时显示，因此需要做数据劫持 
const _Onemap = new OnemapClass();
_Onemap.MapType.value = mapType.arcgis;
//@ts-ignore
const positionInfo: any = ref({});
// setInterval(() => {
//   positionInfo.value = {
//     x: Math.random() * 100,
//     y: Math.random() * 100,
//     z: Math.random() * 100,
//     Pitch: Math.random() * 100,
//     Heading: Math.random() * 100,
//   };
// }, 1000);
//MapControl 组件的地图实时回调方法
const MapRealtimeEvent = {
  Interval: 400,
  MapEvent: (obj, evg) => {
    positionInfo.value = evg;
    console.log("evg", evg);
  },
};

const MapControlRef = ref(null);

let MapViewer: Cesium.Viewer | undefined; 
//这里也可以获取MapViewer对象： Options.mapViewer
const Options = computed(() => {
  return Object.assign(getOption(), {
      id: "mapviewerContainer",
      BASE_URL: "ThirdParty",
      mapType: mapType.cesium, 
      BaseMapLayer: BaseMapLayerUrl(),
      TerrainUrl: ServiceUrl.TerrainUrl,
    });
});
//mounted
onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
