<template>
  <utable :isAttribute="false" :tabledata="tabledata"></utable>
</template>

<script setup>
import { ref } from "vue";
import { utable } from "../../../packages/onemapkit";
const fields = ref([]);
// import("../table/docProps.ts").then((res) => { fields.value = res.fields;  });

const tabledata = [
  {
    fldName: "MapReadyEvent",
    fldType: "Function",
    fldDescribe: `地图加载完毕事件,返回值：
    Promise<
    Array<{
      maplayer: Object | undefined;
      option: IMapLayer;
      layerid: string;
      success: boolean;
      type: string;
    }>
  >`,
    fldOption: ``,
    fldDefault: "",
  },
];
</script>

<style lang="scss" scoped></style>
