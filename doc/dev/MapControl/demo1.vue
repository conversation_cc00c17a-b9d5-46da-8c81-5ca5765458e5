<!-- html  
      :Onemap="_Onemap"-->
<template>
  <el-button type="primary" @click="funpopShow1">Open</el-button>
  <el-button type="primary" @click="funpopShow">Close</el-button>
  <div class="container" id="parentContainer">
    <MapControl
      ref="MapControlRef"
      :PropStore="PropStore"
      :LayerStore="layerStore"
      @MapClickEvent="_MapClickEvent"
      @MapReadyEvent="_MapReadyEvent"
    ></MapControl>
    <LayerContent
      :PropStore="PropStore"
      :LayerStore="layerStore"
      :MapTreeHeight="700"
    />

    <BottomInfo
      :isMouseStopEvent="_isMouseStopEvent"
      :PropStore="PropStore"
      :LayerStore="layerStore"
    />
    <PopPanel
      v-if="PropertyVisible"
      :title="'查询结果'"
      :width="450"
      :height="360"
      :top="20"
      :right="75"
      @close="Propertyclose"
    >
      <PropertyPanel></PropertyPanel>
    </PopPanel>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";

import {
  PopPanel,
  PropertyPanel,
  LayerContent,
  MapControl,
  type IPosition,
  BottomInfo,
  InitMapControl, 
  defaultParameter,
  base,
} from "../../../packages/onemapkit";
//第一步： 导入图层参数
import {
  _Onemap,
  PropStore,
  BaseMapLayerUrl,
  layerStore,
  ServiceUrl,
  CustomTools,
  initShowMapLayer,
} from "../../layer";
import * as Cesium from "@onemapkit/cesium";
const PropertyVisible = ref(false);
const Propertyclose = () => {
  PropertyVisible.value = false;
};
const MapControlRef = ref();
const mpositionInfo = ref<IPosition>({});

const _Options = {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  MapTools: {
    CustomTools: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
};
InitMapControl(_Onemap, _Options); 

// InitMapControl({
//   mapControlName: "mainMapControl",
//   onemap: _Onemap,
//   layerStore: layerStore,
//   propStore: PropStore,
//   options: _Options,
// });

const _MapClickEvent = (mapControlName: String, _point: any, _options: any) => {
  if (PropertyVisible.value == false) PropertyVisible.value = true;
  console.log("=======avg", mapControlName, _point, _options);
};
const _MapReadyEvent = (mapControlName: String) => {
  initShowMapLayer();
};
// const _MapRealtimeEvent = (mapControlName: String, position: any) => {
//   console.log("=======_MapRealtimeEvent", mapControlName, position);
//   mpositionInfo.value = position;
// };
const _isMouseStopEvent = ref(false);
//   if(props.isMouseStopEvent){
//     _Onemap.setMouseStopEvent(_MouseStopEvent,200);
//   }else{
//     _Onemap.removeMouseStopEvent(_MouseStopEvent);
//   }
const _MouseStopEvent = (avg: any) => {
  console.log("============123", avg);
};
const _MouseStopEvent1 = (avg: any) => {
  console.log("============456", avg);
};
let _MouseStop = undefined;
const funpopShow = () => {
  // PopPanelREFVisible.value = true;
  _Onemap.removeMapEventHandler(base.MouseEventType.MOUSE_STOP, {
    CallBackHandler: _MouseStop,
    eventName: "eventName20",
  });
};
const funpopShow1 = () => {
  console.log("============45&&&&&&&&&&&&&&&&&&&&&&&&===");
  // PopPanelREFVisible.value = true;
  _MouseStop = _Onemap.setMapEventHandler(base.MouseEventType.MOUSE_STOP, {
    isThrottle: true,
    delayTime: 300,
    eventName: "eventName20",
    CallBackHandler: _MouseStopEvent,
    // RealtimeHandler: _MouseStopEvent1,
  });
};

_Onemap.MapReadyHandler = () => {
  funpopShow1();
};
onMounted(() => {
  // MapControlRef.value.isPropertyVisible =false;
});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
