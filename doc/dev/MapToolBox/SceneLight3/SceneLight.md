<script setup>
import demo from './demo.vue' 
import Preview from '/src/views/Preview.vue' 
import Attributes from './Attributes.vue' 
import Function from './Function.vue' 


</script>

# SceneLight 多光源组件

### 三维场景中用于创建多光源的组件，包含点光源和聚光灯

## SceneLight 多光源组件的基本使用

  <demo/>
<Preview compName="SceneLight" demoName="demo" sourceCodePath="dev/MapToolBox/SceneLight/demo.vue"/>


## Attributes 参数

<Attributes/>

## Function 方法

<Function/>
