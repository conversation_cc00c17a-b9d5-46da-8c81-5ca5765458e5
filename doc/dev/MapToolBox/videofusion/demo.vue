<template>  
  <el-button type="primary" @click="funpopShow">弹出面板</el-button>
  <PopPanel
    v-if="popShow"
    :title="'测试面板'"
    :width="400"
    :height="450"
    index="1000"
    @close="close"
  > 
  </PopPanel>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :PropStore="PropStore"
      :LayerStore="layerStore"
    /> 
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";
import {
  Map, 
  InitMapControl,
  defaultParameter,PopPanel,getOptions
} from "../../../../packages/onemapkit";
import {
  _Onemap,
  PropStore,
  BaseMapLayerUrl,
  layerStore,
  ServiceUrl, 
} from "../../../layer";
  
const MapControlRef = ref(null); 
const _Options = {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl, 
  ToolQuickItem: [],
  MapDrawTools:defaultParameter.defaultEditToolItems
}; 
InitMapControl(_Onemap, _Options);

  
onMounted(async () => {}); 
let popShow = ref(false);
const close = () => {
  popShow.value = false;
};
const funpopShow = () => { 
  popShow.value = true;
  return popShow.value;
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
