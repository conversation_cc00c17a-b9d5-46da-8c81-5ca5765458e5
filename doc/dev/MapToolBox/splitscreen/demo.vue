<template>
  <el-button type="primary" @click="funsplitscreeen">打开分屏工具</el-button>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlREF"
      MapControlName="mainMapControl"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapReadyEvent="_MapReadyEvent"
    ></Map>
    <ToolsBox
      v-show="showMoreToolsPanel"
      ref="MapToolBoxRef"
      MapControlName="mainMapControl"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @ToolButotnClickEvent="_ToolButotnClickEvent"
    />
  </div>


  <Dialog
    v-if="showMoreToolsPanel"
    :title="'分屏工具'"
    :width="width"
    :height="height"
    :dock="DockType.center"
    :scrollbar="false"
    :onClose="ClickMorePanelEvent"
    isMaxmize="true"
    :top="0"
    :left="0"
    :right="0"
    :bottom="0"
  >
    <splitscreen MapControlName="mainMapControl" :PropStore="PropStore" :LayerStore="layerStore" />
  </Dialog>
</template>
<script lang="ts" setup>
import { onMounted, ref,   nextTick, markRaw } from "vue";
import layers from "../../../mock/layers.json";

import {
  ILayerType,
  DockType,
  Map,
  Dialog,
  PopPanel,
  splitscreen,
  ToolsBox,
  InitMapControl,
  OnemapEvent,
  Utils,
  IMapLayer,
  defaultParameter,
  LayerContainer,
  BaseMapView,
  LayerList,
  RecentList,
  LayerOrder,
	defaultLayerMoreList,
	LayerSearch,
	Legend,
} from "../../../../packages/onemapkit";

/**第一步：导入图层参数*/
import {
  _Onemap,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  CustomTools,
  PropStore,
	BaseMapLayers,
	initShowMapLayer,
} from "../../../layer";


localStorage.setItem("TokenName", "token");
const width =window.innerWidth-5
const height =window.innerHeight-5
/** 添加2024的图层目录 begin */
layerStore.Layers = [];
layerStore.MapLayers = [];
layerStore.initTreeDataExecute(layers);
const layerResPanelData: any = ref([
	{
		Control: LayerContainer, name: "LayerContainer", title: "图层目录", isDefault: true, Data: {
			BaseMapComponent: {
				Control: markRaw(BaseMapView),
				name: "BaseMapComponent",
				title: "底图面板",
				isDefault: true,
				Data: BaseMapLayers(_Onemap, layerStore)
			},
			TabComponents: [
				{ Control: markRaw(LayerList), name: "LayerList", title: "图层列表", isDefault: true, Data: [] },
				{ Control: markRaw(RecentList), name: "RecentList", title: "最近浏览", isDefault: false, Data: [] },
				{
					Control: markRaw(LayerOrder), name: "LayerOrder", title: "图层排序", isDefault: false, Data: {
						// 图层分组类型
						layerGroups: [
							{ label: ILayerType.LabelLayer, text: "标注图层" },
							{ label: ILayerType.PointLayer, text: "点图层" },
							{ label: ILayerType.PolylineLayer, text: "线图层" },
							{ label: ILayerType.PolygonLayer, text: "面图层" },
							{ label: ILayerType.ImageryLayer, text: "栅格图层" },
							{ label: ILayerType.OtherLayer, text: "其它图层" },
							{ label: ILayerType.ModelLayer, text: "三维图层" }
						]
					}
				}
			],
			MoreOperateList: [
				defaultLayerMoreList.favorites,
				defaultLayerMoreList.location,
				defaultLayerMoreList.opacity,
				defaultLayerMoreList.visibleScale
			]
		}
	},
	{ Control: markRaw(LayerSearch), name: "LayerSearch", title: "地图查询", isDefault: false, Data: [] },
	{ Control: markRaw(Legend), name: "Legend", title: "地图图例", isDefault: false, Data: [] },
]);

const baseMapLayerUrl = (): IMapLayer => {
	const basemaps = layerStore.MapLayers.filter((x: IMapLayer) => x.isBaseLayer && x.visible);

	if (basemaps && basemaps.length > 1) {
		return basemaps.find((x: IMapLayer) => x.layerid != "basemapEarth")
	}
	return basemaps[0]
};
/** 添加2024的图层目录 end */

InitMapControl(_Onemap, {
  MapControlName: "mainMapControl",
  BASE_URL: "ThirdParty",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: "/dev-api/cesium/mapdata/08dcc357-5831-4d5d-8b9f-045c1e8de241",
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});

const _MapReadyEvent = () => {
	initShowMapLayer();

	const initLocation = {
		xmax: 109.64409,
		ymax: 24.06955,
		xmin: 107.28664,
		ymin: 22.19061,
		heading: 0,
		pitch: -90,
		roll: 0,
		wkid: 4490,
		toolbar: "定位到南宁"
	}

	setTimeout(() => {
		_Onemap.gotoExtent(initLocation);
	}, 100)
};

let showMoreToolsPanel = ref(false);
//@ts-ignore 弹窗方案需要此变量
// let closeMoreTools: any;
const ClickMorePanelEvent = () => {
  if (showMoreToolsPanel.value == false) {
    showMoreToolsPanel.value = true;
  } else {
    showMoreToolsPanel.value = false;
    // closeMoreTools();
  }
};

const initShowMapLayerUrl = () => {
  const layers = Utils.getMapLayerByVisible(_Onemap.MapType.value, true, layerStore.MapLayers);
  layers.forEach((layer: IMapLayer) => {
    if (OnemapEvent.LayerCheckServiceAPI) {
      OnemapEvent.LayerCheckServiceAPI(true, layer).then((res: IMapLayer) => {
        _Onemap.AddLayer(res);
      });
    } else {
      _Onemap.AddLayer(layer);
    }
  });
};
const isShowMapToolBox = ref(true);
OnemapEvent.SplitSwitchVisible = (isVisible: any) => {
  isShowMapToolBox.value = isVisible;
};
const MapControlREF = ref();
const _ToolButotnClickEvent = (avg: any): any => {
  console.log("=====avg", avg);
  switch (avg.toolItem.name) {
    case "splitscreen":
      {
        isShowMapToolBox.value = !avg.state;
      }
      break;
  }
};

function funsplitscreeen() {
  showMoreToolsPanel.value = true;
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 260px);
  position: relative;
}
</style>
