<template>
  <div class="container" id="parentContainer">
    <Map
      :LayerStore="layerStore"
      :PropStore="_PropStore"
      :TipTools="[
        { Control: AttributeQuery, isPropStore: true, isLayerStore: false },
        { Control: mapswitch, isPropStore: false, isLayerStore: false },
        { Control: fullmap, isPropStore: true, isLayerStore: false },
        { Control: mapzoom, isPropStore: false, isLayerStore: false },
      ]"
      @MapReadyEvent="_MapReadyEvent"
    />
    <!-- 旧的 ubadge -->
    <ToolsBox
      :LayerStore="layerStore"
      :PropStore="_PropStore"
      @ToolButotnClickEvent="_ToolButotnClickEvent"
    />
    <LayerTreePop
      :LayerStore="layerStore"
      :PropStore="_PropStore"
      :MapTreeHeight="600"
    />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive } from "vue";
import {
  Map,
  ToolsBox,
  InitMapControl,
  type IPosition,
  LayerTreePop,
  OnemapEvent,
  debug,
  mapswitch,
  fullmap,
  mapzoom,
  AttributeQuery,
  defaultParameter,
  Popwindow,
} from "../../../packages/onemapkit";

import {
  PropStore as _PropStore,
  layerStore,
  _Onemap,
  ServiceUrl,
  CustomTools,
  initShowMapLayer,
  BaseMapLayerUrl,
} from "../../layer";

const _Options = {
  MapControlName: "mainMapControl",
  BASE_URL: "ThirdParty",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 330,
      height: 420,
    },
  },
  ToolQuickItem: [],
  MapDrawTools: defaultParameter.defaultEditToolItems,
};
InitMapControl(_Onemap, _Options);

const isShowMapToolBox = ref(true);
const _MapReadyEvent = (mapControlName: String) => {
  initShowMapLayer();
  console.log("=======mapControlName", mapControlName);
};
OnemapEvent.SplitSwitchVisible = (isVisible: any) => {
  isShowMapToolBox.value = isVisible;
};

const _ToolButotnClickEvent = (avg: any): any => {
  console.log("=====avg1", avg);
  switch (avg.toolItem.name) {
    case "splitscreen":
      {
        isShowMapToolBox.value = !avg.state;
      }
      break;
  }
};

//#region ******** Debug 工具 ********

const debugDock = reactive({ right: 100, top: 50 });
const _marginSize = (right: number, top: number) => {
  debugDock.right = right;
  debugDock.top = top;
};

//#endregion
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}

.checkbox-button {
  position: relative;
}

.checkbox-button button {
  padding-right: 30px; /* 留出足够空间给打勾 */
  position: relative;
}

.checkbox-button input[type="checkbox"] {
  opacity: 0; /* 使checkbox不可见 */
  position: absolute;
  top: 0;
  right: 0;
}

.checkbox-button input[type="checkbox"]:checked + button:after {
  content: "✔"; /* 打勾的字符 */
  position: absolute;
  top: 0;
  right: 0;
  font-size: 20px; /* 打勾的大小 */
  color: green; /* 打勾的颜色 */
}
</style>
