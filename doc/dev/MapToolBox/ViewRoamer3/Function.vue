<template> 
  <utable :fileds="fileds" :tabledata="tabledata"></utable>
</template>

<script setup>
import {utable} from "../../../../packages/onemapkit"; 

const fileds = [
  {
    field: "name",
    title: "方法名",
    align: "center",
  },
  {
    field: "parameters",
    title: "参数",
    align: "center",
  },
  {
    field: "return",
    title: "返回值",
    align: "center",
  },
  {
    field: "red",
    title: "说明",
    align: "center",
    width: "350px",
  }
];
const tabledata = [
  {
    name: "getViewPoints",
    red: '获取当前列表中的视点信息',
    parameters: "",
    return: "Object[]",
  },
  {
    name: "changeShowPrimitives",
    red: '更改视点范围显示状态',
    parameters: "(show: boolean)",
    return: "void",
  },
];
</script>

<style lang="scss" scoped></style>
