<template>
  <div class="container" id="parentContainer">
    <MapControl
      ref="MapControlRef"
      MapControlName="mainMapControl"
      :PropStore="PropStore"
      :LayerStore="layerStore"
    ></MapControl>
  </div>

  <PopPanel
    v-if="isShow"
    @close="popClose"
    title="量算工具"
    :width="200"
    :top="10"
  >
    <measure-panel :PropStore="PropStore" :LayerStore="layerStore" />
  </PopPanel>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import {
  mapType,
  MapControl, 
  MeasurePanel,
  PopPanel,
  InitMapControl,getOptions
} from "../../../../packages/onemapkit";

import * as CesiumTools from "@onemapkit/cesium-tools";
import {
  _Onemap, 
  ServiceUrl,
  BaseMapLayerUrl,
  layerStore,
} from "../../../layer";
 
_Onemap.setMapType(mapType.arcgis);
const MapControlRef = ref(null);
const _Options = Object.assign(getOptions(), {
  id: "mapviewerContainer",
  BASE_URL: "ThirdParty",
  mapType: mapType.cesium,
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
});
InitMapControl(_Onemap, {
  MapControlName: "mainMapControl",
  Options: _Options,
  // Components: {
  //   MapLayerTreeVisible: false,
  //   SwitchMapVisible: false,
  //   PropertyVisible: false,
  //   MapToolsVisible: false,
  //   BottomInfoVisible: false,
  // },
});

//弹窗开关
const isShow = ref(true);
const popClose = () => {
  isShow.value = false;
};

onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
