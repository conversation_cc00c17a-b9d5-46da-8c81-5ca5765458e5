<template>
  <div class="container" id="parentContainer">
    <Map ref="MapControlRef" :LayerStore="layerStore" :PropStore="PropStore" @MapReadyEvent="_MapReadyEvent"
      @MapClickEvent="MouseClickEvent" />
    <LayerResPanel :PropStore="PropStore" :LayerStore="layerStore" :TabComponents="layerResPanelData" />
    <!-- 地名地址搜索 -->
    <!-- <SearchBar v-if="PropStore" :Onemap="_Onemap" MapControlName="mainMapControl" :PropStore="PropStore" /> -->

    <!-- 区域搜索工具 -->
    <!-- <region-search :PropStore="PropStore" :Onemap="_Onemap" MapControlName="mainMapControl"
        :areaUrl="areaUrl" /> -->
    <!-- 工具栏 -->
    <!-- <ToolsBox v-show="isShowMapToolBox" MapControlName="mainMapControl" :LayerStore="layerStore" :PropStore="PropStore"
			:areaUrl="areaUrl" @ToolButotnClickEvent="_ToolButotnClickEvent" /> -->
    <MapToolBox v-show="isShowMapToolBox" MapControlName="mainMapControl" :LayerStore="layerStore"
      :PropStore="PropStore" :MapTools="CustomTools" />

    <!-- 点选 -->
    <PopPanel v-if="propertyVisible" @close="popClose" title="查询结果" :width="600" :height="372" :top="200" :right="75"
      :zindex="500">
      <el-button v-if="buttonExist" type="primary" size="small" style="position: absolute;top: 5px;left: 80px;"
        @click="simbleOrComposite">{{ isSimple ? '切换到综合查询模式' : '切换到简约查询模式' }}</el-button>
      <!-- <el-button v-if="buttonSetTip" type="primary" size="small" style="position: absolute;top: 5px;left: 18em;"
				@click="setTip">设置</el-button> -->
      <CommonPropertyBox MapControlName="mainMapControl" ref="PropertyInfoRef" :PropStore="PropStore"
        :LayerStore="layerStore" @showZrzPermission="showZrzPermission" @selectDataCallBack="selectDataCallBack">
        <el-button v-if="analysisBtnVisible" size="small" class="zrz-btn" type="primary" @click="graphicAnalysis()">空间分析
        </el-button>
        <el-tooltip content="将图形数据添加到导出列表中" placement="bottom" effect="light">
          <el-button v-if="analysisBtnVisible" size="small" class="zrz-btn" type="primary"
            @click="addGraphicForExport()">图形数据导出</el-button>
        </el-tooltip>
        <!-- <el-button
              v-if="fileConfig.isShow"
              size="small"
              class="zrz-btn"
              type="primary"
              @click="feilUpload(attributes)"
              >附件管理</el-button>
              <el-button
                size="small"
                class="zrz-btn"
                type="primary"
                v-if="zpBtnVisible"
                @click="showZPInfo(attributes)"
              >附件查看</el-button>
              <el-button
                v-for="(btn,index) in mapIdentifyButtons"
                :key="index"
                size="small"
                class="zrz-btn"
                type="primary"
                v-show="btn.visible"
                @click="btn.callback(currentData)"
              >{{btn.label}}</el-button> -->
      </CommonPropertyBox>

      <LayerAnalysisDialog ref="layerAnalysisPanel" :Onemap="_Onemap" :PropStore="PropStore" :LayerStore="layerStore" />
    </PopPanel>
    <!-- <PopPanel v-if="buttonSetTipDialog" title="设置查询项" @close="buttonSetTipDialog = false" :width="380" :height="90"
			:top="200" :right="75" :zindex="510">
			<span>请联系系统管理员配置综合查询模块</span>

		</PopPanel>  -->

    <!-- 图形导出 -->
    <GraphicForExport :Onemap=_Onemap :PropStore="PropStore" v-if="GraphicForExportShow"
      @setGraphicForExportShow="setGraphicForExportShow">
    </GraphicForExport>

    <!-- 底栏工具 -->
    <bottom-info v-if="PropStore" :PropStore="PropStore" :Onemap="_Onemap" ref="BottomInfoRef" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, markRaw, provide, nextTick, defineProps } from "vue";
import {
  Map,
  OnemapEvent,
  LayerResPanel,
  defaultParameter,
  InitMapControl,
  IMapLayer,
  LayerContainer,
  BaseMapView,
  LayerList,
  RecentList,
  LayerOrder,
  LayerSearch,
  Legend,
  ILayerType,
  defaultLayerMoreList,
  BottomInfo,
  PopPanel,
  GraphicForExport,
  PropertyInfo,
  MapToolBox,
  ToolsBox,
  CommonPropertyBox,
  serviceType,
  SearchBar,
  RegionSearch
} from "../../../../packages/onemapkit";
import request from "../../../../packages/utils/axios/";
import layers from "../../../mock/layers.json";
import atteachmentConfigs from "../../../mock/atteachmentConfigs.json";
import appConfigData from "../../../mock/config.json";

import { ElMessage } from "element-plus";
import LayerAnalysisDialog from "../../../../packages/Widgets/PropertyInfo/LayerAnalysisDialog.vue"
/**第一步：导入图层参数*/
import {
  layerStore,
  PropStore,
  ServiceUrl,
  _Onemap,
  initShowMapLayer,
  CustomTools,
  BaseMapLayers,
} from "../../../layer";

const props = defineProps({
  PropStore: {
    type: Object as any,
    default: null,
  },
});

localStorage.setItem("TokenName", "token");
PropStore.propertyVisible.value = true
PropStore.defaultQueryLayers = appConfigData.defaultQueryLayers

let queryServeTypes = [serviceType.ArcgisMapImageLayer, serviceType.ArcgisTileLayer, serviceType.ArcgisFeatureLayer]//支持点选查询的服务类型
let queryLayerTypes = [ILayerType.PointLayer, ILayerType.LabelLayer, ILayerType.PolygonLayer, ILayerType.PolylineLayer]//支持点选查询的要素类型
PropStore.clickQueryLayerType = queryServeTypes
PropStore.clickQueryFeatureType = queryLayerTypes
//--------------工具箱start--------------------/
const areaUrl = ref('')
const isShowMapToolBox = ref(true);

OnemapEvent.SplitSwitchVisible = (isVisible: any) => {
  isShowMapToolBox.value = isVisible;
};

const _ToolButotnClickEvent = (avg: any): any => {
  switch (avg.toolItem.name) {
    case "splitscreen":
      {
        isShowMapToolBox.value = !avg.state;
      }
      break;
  }
};

let currentPointOption: any
//-------------工具箱end---------------------/

/** 添加2024的图层目录 begin */
layerStore.Layers = [];
layerStore.MapLayers = [];
layerStore.initTreeDataExecute(layers as IMapLayer[]);
const layerResPanelData: any = ref([
  {
    Control: LayerContainer, name: "LayerContainer", title: "图层目录", isDefault: true, Data: {
      BaseMapComponent: {
        Control: markRaw(BaseMapView),
        name: "BaseMapComponent",
        title: "底图面板",
        isDefault: true,
        Data: BaseMapLayers(_Onemap, layerStore)
      },
      TabComponents: [
        { Control: markRaw(LayerList), name: "LayerList", title: "图层列表", isDefault: true, Data: [] },
        { Control: markRaw(RecentList), name: "RecentList", title: "最近浏览", isDefault: false, Data: [] },
        {
          Control: markRaw(LayerOrder), name: "LayerOrder", title: "图层排序", isDefault: false, Data: {
            // 图层分组类型
            layerGroups: [
              { label: ILayerType.LabelLayer, text: "标注图层" },
              { label: ILayerType.PointLayer, text: "点图层" },
              { label: ILayerType.PolylineLayer, text: "线图层" },
              { label: ILayerType.PolygonLayer, text: "面图层" },
              { label: ILayerType.ImageryLayer, text: "栅格图层" },
              { label: ILayerType.OtherLayer, text: "其它图层" },
              { label: ILayerType.ModelLayer, text: "三维图层" }
            ]
          }
        }
      ],
      MoreOperateList: [
        defaultLayerMoreList.favorites,
        defaultLayerMoreList.location,
        defaultLayerMoreList.opacity,
        defaultLayerMoreList.visibleScale
      ]
    }
  },
  { Control: markRaw(LayerSearch), name: "LayerSearch", title: "地图查询", isDefault: false, Data: [] },
  { Control: markRaw(Legend), name: "Legend", title: "地图图例", isDefault: false, Data: [] },
]);

const baseMapLayerUrl = (): IMapLayer => {
  const basemaps = layerStore.MapLayers.filter((x: IMapLayer) => x.isBaseLayer && x.visible);

  if (basemaps && basemaps.length > 1) {
    return basemaps.find((x: IMapLayer) => x.layerid != "basemapEarth") as IMapLayer
  }
  return basemaps[0] as IMapLayer
};
/** 添加2024的图层目录 end */

InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: baseMapLayerUrl(),
  TerrainUrl: "/dev-api/cesium/mapdata/08dd4590-c373-4513-87f8-1780112c5408",
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab,
    {
      // 是否显示详细底图面板
      basemap: {
        visible: true
      }
    }
  ),
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});
//const userInfo: any = globalPropStore.userInfo
const buttonSetTip = ref(false)
const simbleOrComposite = () => {
  isSimple.value = !isSimple.value
  console.log('isSimple.value', isSimple.value);

  // if (userInfo && userInfo.id) {
  // 	localStorage.setItem(`searchType_${userInfo.id}`, isSimple.value);
  // }
  if (!isSimple.value && !PropStore.defaultQueryLayers && buttonExist.value) {
    buttonSetTip.value = true
  } else {
    buttonSetTip.value = false
  }
  PropertyInfoRef.value.changeMode(isSimple.value, currentPointOption)
};

const buttonExist = ref(false)
const showZrzPermission = (data: any) => {
  console.log('获取后台权限', data);
  if (data.find((item: any) => item.code == "comprehensive_attribute")) {
    buttonExist.value = true;
    if (!isSimple.value && !PropStore.defaultQueryLayers) {
      buttonSetTip.value = true
    } else {
      buttonSetTip.value = false
    }
  } else {
    buttonExist.value = false;
    isSimple.value = false
    buttonSetTip.value = false;
  }
}
//楼盘表----------------//
const zrzParams = ref({
  zrzbdcdyh: "",
  zrzguid: "",
  permission: {}, // 权限，name有值 - 可以查自然幢，child数组中的值，代表可以显示的组件：单元信息、权利信息……
  buildingData: [], // 楼盘表数据
  currentNodeData: null, // 当前节点数据
} as any);

const showPanel = (layerdata: any) => {
  let data: any = layerdata.displayAttributes;
  console.log(layerdata)
  //globalPropStore.showBuildingTable = !globalPropStore.showBuildingTable;
  let one = data.find((x: any) => x.label === "自然幢GUID");
  let zrzguid = one ? one.value : "";
  let bdcdyh = data.find((x: any) => x.label === "不动产单元号");
  let chzt = data.find((x: any) => x.label === "测绘状态名称");

  let attributes = layerdata.attributes;
  let title = attributes.ZL2 == "Null" ? data.find((x: any) => x.label === "坐落").value : attributes.ZL2;
  zrzParams.value.buildingTitleSuffix = chzt.value + "绘";
  zrzParams.value.buildingTitle = title;
  zrzParams.value.buildingData = data;
  zrzParams.value.zrzbdcdyh = bdcdyh.value;
  zrzParams.value.zrzguid = zrzguid;

  //zrzParams.value.currentNodeData = currentNodeData;

}
const _MapReadyEvent = () => {
  initShowMapLayer();

  const initLocation = {
    xmax: 109.64409,
    ymax: 24.06955,
    xmin: 107.28664,
    ymin: 22.19061,
    heading: 0,
    pitch: -90,
    roll: 0,
    wkid: 4490,
    toolbar: "定位到南宁"
  }

  setTimeout(() => {
    _Onemap.gotoExtent(initLocation);
  }, 500)




  let mapIdentifyButtons = PropStore.getStorage('mapIdentifyButtons');
  if (mapIdentifyButtons && mapIdentifyButtons.value) {
    mapIdentifyButtons.value.push({
      label: '楼盘表',
      visible: false,
      callback: showPanel,
      functionName: 'showZRZInfo'
    })
  }

  let res = atteachmentConfigs
  if (res && res.list) {
    console.log('附件查看配置res', res);
    PropStore.setStorage({
      storagekey: "atteachmentConfigs",
      isLocalStorage: true,
      initStoragevalue: res.list,
      variableType: 1,
    })
    // PropStore.atteachmentConfigs = res.data.list
    console.log('PropStore', PropStore);
  }
};


const isSimple: any = ref(true)
//属性点选查询弹窗开关
const popClose = () => {
  propertyVisible.value = false;
};

const PropertyInfoRef = ref()
const propertyVisible = ref(false)
let lyList: any = []
let isDrawing = false;
const MouseClickEvent = (mapControlName: any, _point: any, _options: any) => {
  isDrawing = PropStore.getStorage("isDrawing").value;
  PropStore.setStorage({
    storagekey: "urlObj",
    isLocalStorage: true,
    initStoragevalue: urlObj,
    variableType: 1,
  });
  console.log('地图点击事件---------');
  console.log('layerStore', layerStore);

  if (!PropStore.propertyVisible.value) {
    return
  }

  if (isDrawing) {
    return
  }


  let Cesium3DList: any = [] //cesium的3维图层
  //勾选的图层

  for (const itId of layerStore.CheckedLayerids) {
    let ly = getLayerById(itId, layerStore.Layers)

    if (ly) {
      if (ly.serviceType == "Cesium3DTiles") {
        Cesium3DList.push(ly)
      } else if (queryServeTypes.includes(ly.serviceType) && queryLayerTypes.includes(ly.layerType)) {
        lyList.push(ly)
      }
    }
  }
  currentPointOption = {
    point: _point,
    option: _options
  }

  isSimple.value = true;
  propertyVisible.value = false
  setTimeout(() => {
    propertyVisible.value = true
    //如果没有选择图层，则进入地址查询模式
    if (lyList.length == 0) {
      buttonExist.value = false
    } else {
      buttonExist.value = true
    }
    nextTick(() => {
      console.log('PropertyInfoRef.value', PropertyInfoRef.value);
      if (PropertyInfoRef.value) {
        PropertyInfoRef.value.mapClickEvent(mapControlName, _point, _options)
      }
    });
  }, 1);


}
//遍历数组查找图层
const getLayerById = (id: string, arr: any) => {
  for (let i = 0; i < arr.length; i++) {
    const node = arr[i];
    if (id == node.layerid) {
      return node
    }
    if (node.children && node.children.length) {
      let foundNode: any = getLayerById(id, node.children)
      if (foundNode) {
        return foundNode
      }
    }
  }
  return null

}
//获取附件管理已上传的文件
const uploadPaneltableData: any = ref([])
const getTableData = async (uploadId: string) => {
  //测试数据
  let res = [
    {
      fileName: '测试',
      fileSuffix: 'zip',
      id: '1'
    }
  ]
  console.log('已上传的文件', res);
  uploadPaneltableData.value = res

};
//判断附件上传和删除是否成功
const returnResult: any = ref({
  deleteRes: false,
  fileUploadRes: false,
})
provide('returnResult', returnResult.value)



//上传附件管理文件
const FileUpload = async (row: any) => {
  //测试数据
  console.log('row', row);
  returnResult.value.fileUploadRes = true
};
const basePrefixUrl = '/dev-api';
const urlObj = {
  downFileUrl: `${basePrefixUrl}/api/map/gis/file/download/`,
  deleteFileUrl: `${basePrefixUrl}/api/map/gis/file/delete`,
  atteachmentListUrl: `${basePrefixUrl}/api/map/gis/file/dafile/list`,
  atteachmentReviewUrl: `${basePrefixUrl}/proxy/quanjing/DWGToOcf/index.html`,
  atteachmentDownUrl: `${basePrefixUrl}/api/map/gis/file/dafile/down`,
  atteachmentUploadUrl: `${basePrefixUrl}/api/map/gis/file`,
  lpbUrl: `${basePrefixUrl}/proxy/query/bdc/lpb?ZRZGUID=`,
  queryPermissionUrl: `${basePrefixUrl}/api/menu/btns`,
  layerConfigUrl: `${basePrefixUrl}/api/resource/map/info`,
  getGeoPointInfoUrl: `${basePrefixUrl}/proxy/map/search/GeoPoint`,
  getServerTreeUrl: `${basePrefixUrl}/api/resource/map/tree`,
  ggzProjectUrl: `${basePrefixUrl}/proxy/query/ggz/project`,
  stshowUrl: `${basePrefixUrl}/api/map/immovable/get`
}

let coordinate = ref()
const _MapClickEvent = async (mapControlName: any, _point: any, _options: any) => {
  console.log("=======avg", mapControlName, _point, _options);

};

const GraphicForExportShow = ref(false)
const setGraphicForExportShow = (val: boolean) => {
  GraphicForExportShow.value = val
}
onMounted(async () => {

})


const currentNodeBack = ref<any>(null);
const selectData = ref<any>(null);
const analysisBtnVisible = ref<boolean>(false);
const lpbBtnVisible = ref<boolean>(false);
const ggBtnVisible = ref<boolean>(false);
const ggzData = ref<any[]>([]);
const configId = ref<string | null>(null);

const selectDataCallBack = async (data: any) => {
  console.log('点选返回------------------data', data);
  const { node, row, currentNode } = data;
  if (row == null) {
    alert('请先选择图层')

  } else {
    currentNodeBack.value = currentNode;
    selectData.value = row;
    analysisBtnVisible.value = row.geometryType && row.geometryType == "esriGeometryPolygon";

    if (node) {
      const tag = node.parent.data.layerTagId;

      if (atteachmentConfigs.list && tag) {
        const attachmentConfig = atteachmentConfigs.list.find((item: any) => item.tagName === tag);
        if (attachmentConfig) {
          configId.value = attachmentConfig.id;
        }
      }

      console.log('图层标识码tag', tag);

      if (tag === "DQ_BDCZRZ" && row.attributes?.["不动产单元号"]) {
        lpbBtnVisible.value = true;
      } else {
        lpbBtnVisible.value = false;
      }

      if (row && row.STGUID) {
        try {
          const response = await request.get({
            url: `${urlObj.stshowUrl}`,
            params: { stGuid: row.STGUID },
            headers: { "Content-Type": "multipart/form-data" }
          });

          if (response.success && response.data) {
            const res = await request.get(`${urlObj.ggzProjectUrl}?code=${response.data.ghxkzh}`);
            if (res.Data) {
              ggzData.value = res.Data;
            } else {
              ggzData.value = [];
            }
            ggBtnVisible.value = true;
          }
        } catch (error) {
          console.error('请求失败:', error);
          // 可以在这里添加重试机制或用户提示
        }
      } else {
        ggBtnVisible.value = false;
      }
    }
  }
};

const layerAnalysisPanel: any = ref(null)
const graphicAnalysis = () => {
  layerAnalysisPanel.value.show(selectData.value);
}
const addGraphicForExport = () => {
  let list = PropStore.getStorage('GraphicForExportList')
  if (list) {
    let one = list.value.find((item: any) => {
      return (item.id == currentNodeBack.value.id)
    })
    if (!one) {
      list.value.push(currentNodeBack.value)
      PropStore.setStorage({
        storagekey: "GraphicForExportList",
        isLocalStorage: true,
        initStoragevalue: list.value,
        variableType: 1
      })
    } else {
      ElMessage.warning('添加图斑失败，该图斑已经存在列表中！')
    }
  } else {
    PropStore.setStorage({
      storagekey: "GraphicForExportList",
      isLocalStorage: true,
      initStoragevalue: [currentNodeBack.value],
      variableType: 1
    })
  }
  GraphicForExportShow.value = true
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 200px);
  position: relative;
}
</style>

<!-- <template>
    <div class="container" id="parentContainer">
      <Map ref="MapControlRef" :LayerStore="layerStore" :PropStore="_PropStore" @MapReadyEvent="_MapReadyEvent" @MapClickEvent="MouseClickEvent" />
      <PropertyInfo 
        MapControlName="mainMapControl" 
        ref="PropertyInfoRef" 
        :PropStore="_PropStore" 
        :LayerStore="layerStore" 
        @getTableData="getTableData"
        @FileUpload="FileUpload"
        @showZrzPermission="showZrzPermission"
        :coordinate="coordinate"
        :uploadPaneltableData="uploadPaneltableData"
      />
    </div>
</template>
  <script lang="ts" setup>
  import { onMounted, ref, provide } from "vue";
  import {
    PropertyInfo,
    InitMapControl,
    defaultParameter,
  } from "../../../../packages/onemapkit";
  
  // import * as CesiumTools from "@onemapkit/cesium-tools";
  import {
    _Onemap,
    layerStore,
    ServiceUrl,
    initShowMapLayer,
    BaseMapLayerUrl,
    CustomTools,
    PropStore as _PropStore,
  } from "../../../layer";
  
  const _MapReadyEvent = () => {
    initShowMapLayer();
  };
  
  const userInfo = localStorage.getItem('userInfo')
  
  //弹窗开关
  const PropertyInfoRef = ref(null) as any;
  
  const showZrzPermission = (data: any) => {
      console.log('获取后台权限',data);
  
  }
  _PropStore.setStorage({
		storagekey: "urlObj",
		isLocalStorage: true,
		initStoragevalue: '',
		variableType: 1,
	})

  InitMapControl(_Onemap, {
    BASE_URL: "ThirdParty",
    MapControlName: "mainMapControl",
    BaseMapLayer: BaseMapLayerUrl(),
    TerrainUrl: "/dev-api/cesium/mapdata/08dcc357-5831-4d5d-8b9f-045c1e8de241",
    MapLayerTree: Object.assign(
      defaultParameter.defaultLayerContentTab,
      defaultParameter.defaultMapTreeTab,
      {
        // 是否显示详细底图面板
        basemap: {
          visible: true
        }
      }
    ),
    DrawTools: defaultParameter.defaultEditToolItems,
    MapTools: {
      Toolset: CustomTools,
      CustomToolSize: {
        width: 400,
        height: 420,
      },
    },
  });

const MouseClickEvent = (mapControlName: any, _point: any, _options: any)=>{

}
  
  //获取附件管理已上传的文件
  const uploadPaneltableData:any = ref([])
  const getTableData = async (uploadId:string) => {
    //测试数据
    let res = [
      {
        fileName: '测试',
        fileSuffix: 'zip',
        id:'1'
      }
    ]
    console.log('已上传的文件',res);
    uploadPaneltableData.value = res
    
  };
  //判断附件上传和删除是否成功
  const returnResult:any = ref({
      deleteRes: false,
      fileUploadRes: false,
  })
  provide('returnResult',returnResult.value)
  
  
  
  //上传附件管理文件
  const FileUpload = async (row:any) => {
    //测试数据
    console.log('row',row);
    returnResult.value.fileUploadRes = true
  };
  //附件管理弹框的获取附件的url
  const downFileUrl = `https://onemap-pc.dev.nnric.net/api/gis/layer/file/download/`
  //附件管理弹框的删除附件的url
  const deleteFileUrl = `https://onemap-pc.dev.nnric.net/api/gis/layer/file/delete`
  //附件查看弹框获取列表url
  const atteachmentListUrl = `https://onemap-pc.dev.nnric.net/api/gis/layer/file/dafile/list`
  //附件查看弹框的“查看”按钮url
  const atteachmentReviewUrl = `https://onemap-pc.dev.nnric.net/proxy/quanjing/DWGToOcf/index.html`
  //附件查看弹框的“下载”按钮url
  const atteachmentDownUrl = `https://onemap-pc.dev.nnric.net/api/gis/layer/file/dafile/down`
  //附件上传
  const atteachmentUploadUrl = `https://onemap-pc.dev.nnric.net/api/gis/layer/file`
  //楼盘表
  const lpbUrl = `https://onemap-pc.dev.nnric.net/proxy/query/bdc/lpb?ZRZGUID=`
  //  获取权限url
  const queryPermissionUrl = `https://onemap-pc.dev.nnric.net/api/gis/menu/authlist?app=onemap_base`
  //  获取权限url
  const layerConfigUrl = `https://onemap-pc.dev.nnric.net/api/gis/server/info`

  
  let coordinate = ref()
  const _MapClickEvent =async (mapControlName: any, _point: any, _options: any) => {
    console.log("=======avg", mapControlName, _point, _options);
  
  };
  
  onMounted(async () => {

  });
  </script>
  <style lang="scss" scoped>
  .container {
    width: 100%;
    height: 700px;
    position: relative;
  }
  </style>
   -->