<!-- html -->
<template>
  <el-button type="primary" @click="funpopShow0">弹出面板(旧界面)</el-button>
  <el-button type="primary" @click="funpopShow">弹出面板（新界面）</el-button>
  <div class="container" id="parentContainer">
    <Map
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapReadyEvent="_MapReadyEvent"
    />
  </div>
  <Dialog
    v-if="showMoreToolsPanel0"
    :title="'漫游旧界面'"
    :width="550"
    :height="400"
    :dock="DockType.left"
    :margin="{
      top: 20,
      right: -38,
      left: 0,
      bottom: 0,
    }"
    :scrollbar="false"
    :onClose="ClickMorePanelEvent0"
  >
    <ViewRoamer ref="MultipleLight0Ref"></ViewRoamer>
  </Dialog>

  <Dialog
    v-if="showMoreToolsPanel"
    :title="'漫游新界面'"
    :width="590"
    :height="500"
    :minWidth="590"
    :dock="DockType.left"
    :margin="{
      top: 20,
      right: -38,
      left: 0,
      bottom: 0,
    }"
    :scrollbar="false"
    :onClose="ClickMorePanelEvent"
  >
    <ViewRoamer ref="MultipleLightRef"></ViewRoamer>
  </Dialog>
</template>

<script setup lang="ts">

import { ref } from "vue";
import {
  Map,
  Dialog,
  DockType,
  ViewRoamer, 
  mapType,
  InitMapControl,
  getOnemap,
  Utils,
  IMapLayer,
  OnemapEvent,
  defaultParameter,
} from "../../../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";

/**第一步： 导入图层参数*/
import {
  _Onemap,
  PropStore,
  ServiceUrl,
  BaseMapLayerUrl,
  layerStore,
  initShowMapLayer,
  CustomTools,
} from "../../../layer";

if (_Onemap.MapType.value != mapType.cesium) _Onemap.setMapType(mapType.cesium);

InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  MapTools: {
    CustomTools: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
  ToolQuickItem: [],
  MapDrawTools: defaultParameter.defaultEditToolItems,
});
const _MapReadyEvent = (mapControlName: String) => {
  initShowMapLayer();
  var boundingRectangle = Cesium.Rectangle.fromDegrees(
    PropStore.fullExtent.xmin,
    PropStore.fullExtent.ymin,
    PropStore.fullExtent.xmax,
    PropStore.fullExtent.ymax
  );
  _Onemap.MapViewer.camera.setView({
    destination: boundingRectangle,
  });
};
let viewer: Cesium.Viewer | undefined;

const showMoreToolsPanel0 = ref(false);
const funpopShow0 = () => {
  showMoreToolsPanel0.value = true;
  return showMoreToolsPanel0.value;
};
const ClickMorePanelEvent0 = () => {
  if (showMoreToolsPanel0.value == false) {
    showMoreToolsPanel0.value = true;
  } else {
    showMoreToolsPanel0.value = false;
    // closeMoreTools();
  }
};

const showMoreToolsPanel = ref(false);
const funpopShow = () => {
  showMoreToolsPanel.value = true;
  return showMoreToolsPanel.value;
};
const ClickMorePanelEvent = () => {
  if (showMoreToolsPanel.value == false) {
    showMoreToolsPanel.value = true;
  } else {
    showMoreToolsPanel.value = false;
    // closeMoreTools();
  }
};

//MapControl 组件的地图加载完毕的回调方法,是获取MapViewer的一种方式
let lightset = null;
function MapReadyEvent(_cesiumViewer: any) {
  const layers = Utils.getMapLayerByVisible(
    _Onemap.MapType.value,
    true,
    layerStore.MapLayers
  );
  layers.forEach((layer: IMapLayer) => {
    if (OnemapEvent.LayerCheckServiceAPI) {
      OnemapEvent.LayerCheckServiceAPI(true, layer).then((res: IMapLayer) => {
        _Onemap.AddLayer(res);
      });
    } else {
      _Onemap.AddLayer(layer);
    }
  });
  const cesiumViewer = getOnemap(_cesiumViewer).MapViewer;
  viewer = cesiumViewer;
  // 模型初始化
  const tilesetPromise = Cesium.Cesium3DTileset.fromUrl(ServiceUrl.model_XXJT);
  tilesetPromise.then((tileset) => {
    cesiumViewer.scene.primitives.add(tileset);
    //@ts-ignore
    lightset = tileset;
  });
  cesiumViewer.flyTo(lightset);
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
