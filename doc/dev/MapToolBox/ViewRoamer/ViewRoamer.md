<script setup>
import demo from './demo.vue' 
import Preview from '/src/views/Preview.vue' 
import Attributes from './Attributes.vue' 
import Function from './Function.vue' 


</script>

# ViewRoamer 三维场景漫游组件

#### 三维场景中用于用于创建连贯漫游动画的组件

<br/>

## 基本使用 

##### ViewRoamer 三维场景漫游组件

 <div >
  <demo/>
</div> 
<Preview compName="ViewRoamer" demoName="demo" sourceCodePath="dev/MapToolBox/ViewRoamer/demo.vue"/>
 

## Attributes 参数

<Attributes/>

## Function 方法

<Function/>

