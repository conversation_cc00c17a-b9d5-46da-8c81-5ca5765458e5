<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlREF"
      MapControlName="mainMapControl"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapReadyEvent="_MapReadyEvent"
    ></Map>
    <MapToolBox
      v-show="isShowMapToolBox"
      ref="MapToolBoxRef"
      MapControlName="mainMapControl"
      :LayerStore="layerStore"
      :PropStore="PropStore" 
    />
    <splitscreenPop MapControlName="mainMapControl" :LayerStore="layerStore" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed, nextTick } from "vue";

import * as CesiumTools from "@onemapkit/cesium-tools";
import {
  mapType,
  Map,
  splitscreenPop,
  InitMapControl,
  OnemapEvent,
  MapToolBox,type IMapLayer ,Utils,defaultParameter
} from "../../../../packages/onemapkit";

/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */
//导入图层参数
import {
  layerStore,PropStore,
  _Onemap,
  ServiceUrl,
  BaseMapLayerUrl,
  CustomTools,
} from "../../../layer";

// _Onemap.setMapType(mapType.cesium);
 
InitMapControl(_Onemap,  {
  MapControlName: "mainMapControl",
  BASE_URL: "ThirdParty",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});
const _MapReadyEvent =()=>{
  initShowMapLayerUrl();
}
const initShowMapLayerUrl = () => {
  const layers = Utils.getMapLayerByVisible(
    _Onemap.MapType.value,
    true,
    layerStore.MapLayers
  );
  layers.forEach((layer: IMapLayer) => {
    if (OnemapEvent.LayerCheckServiceAPI) {
      OnemapEvent.LayerCheckServiceAPI(true, layer).then((res: IMapLayer) => {
        _Onemap.AddLayer(res);
      });
    } else {
      _Onemap.AddLayer(layer);
    }
  });
};


const isShowMapToolBox = ref(true);
OnemapEvent.SplitSwitchVisible = (isVisible: any) => {
  isShowMapToolBox.value = isVisible;
};
 
const MapControlREF = ref(); 
 
//140px TitleHight
let treeHeight = ref(460);
const setContentVisible = () => {
  nextTick(() => {
    treeHeight.value =
    //@ts-ignore
      document.getElementById("user-panel")?.offsetHeight - 135;
  });
};
onMounted(async () => {});

const PopPanelREFVisible = ref(false);
function funsplitscreeen() {
  PopPanelREFVisible.value = true;
  MapControlREF.value.setComponentVisible(false, true);
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
