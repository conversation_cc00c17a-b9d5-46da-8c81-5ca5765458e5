<template>
  <utable :tabledata="tabledata"></utable>
</template>

<script lang="ts" setup>
import { utable } from "../../../packages/onemapkit";

const tabledata = [
  {
    fldName: "MapControlName",
    fldType: "String",
    fldDescribe: "地图组件类别属性",
    fldOption: "",
    fldDefault: `mainMapControl`,
  },
  {
    fldName: "LayerStore",
    fldType: "LayerStorage",
    fldDescribe: "数据源参数",
    fldOption: "",
    fldDefault: "null",
  },
  {
    fldName: "MapLayerData",
    fldType: ` Array<IMapLayer> ||
      Array<{ layerid: string; name: string; children: [] }>`,
    fldDescribe: "绑定的数据",
    fldOption: "true",
    fldDefault: "",
  },
  {
    fldName: "onMaplayerCheck",
    fldType: "(sender: any, avg: any) => any",
    fldDescribe: "图层Check事件函数",
    fldOption: "",
    fldDefault: "null",
  },
  {
    fldName: "LayerCheckServiceAPI",
    fldType: "(isCheck: any, avg: any) => any",
    fldDescribe: "图层加载外部处理函数",
    fldOption: "",
    fldDefault: "null",
  }, 
];
</script>

<style lang="scss" scoped></style>
