<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapReadyEvent="_MapReadyEvent"
    />
    <Popup
      :contentID="'parentContainer'"
      :caption-text="'图层资源'"
      :popupStyle="{
        top: `2px`,
        left: `2px`,
        position: `absolute`,
      }"
      :showCaptionArrow="_showCaptionArrow"
      :isIncontent="isInnerContent"
      :contentStyle="{
        top: isInnerContent ? `35px` : `40px`,
        left: isInnerContent ? `-1px` : `2px`,
        position: `absolute`,
      }"
      :isHeaderClickEnable="_isHeaderClickEnable"
      :CaptionClickHandler="_CaptionClickHandler"
    >
      <SimpleTree
        ref="mapTree"
        :LayerStore="layerStore"
        :MapLayerData="
          Utils.createNewTreeData(layerStore.Layers, (node) => {
            return (
              Utils.isUseMapModel(node, _Onemap.MapType.value) &&
              Utils.MapLayerFilter.MapLayers(node, true)
            );
          })
        "
      >
        <!-- :FilterText="[{label:111,value:1},{label:'三维',value:'A'}]" -->
        <!-- 也可以之定义，放入插槽 #layerFilter -->
        <!-- <template #layerFilter>
          <LayerFilter
            :onLayerFilter="inputEventHandler"
          ></LayerFilter>
          <div>qqq</div>
        </template> -->
      </SimpleTree>
    </Popup>
  </div>
</template>
<script lang="ts" setup>
import { ref, nextTick } from "vue";
import {
  Map,
  InitMapControl,
  SimpleTree,
  DockType,
  Dialog,
  Popup,
  Utils,
  LayerFilter,
} from "../../../packages/onemapkit";


/**第一步：导入图层参数*/
import {
  PropStore,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  _Onemap,
  initShowMapLayer,
} from "../../layer";

InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
});
const mapTree = ref();
const isCaptionClick = ref(true);
const _isHeaderClickEnable = ref(true);
const isInnerContent = ref(false);
const _showCaptionArrow = ref(true);
function _CaptionClickHandler(result: { popup: any; Header: any; Content: any }): any {
  nextTick(() => {
    isCaptionClick.value = !isCaptionClick.value;
    // _MapTreeHeight.value = result.Content.clientHeight - 80;
  });
}

const PopPanelREFVisible = ref(false);

const _MapReadyEvent = () => {
  initShowMapLayer();
};
const funpopShow = () => {
  PopPanelREFVisible.value = !PopPanelREFVisible.value;
};
function inputEventHandler(val) {
  console.log(val);
  // Access the mapTree reference and call MapLayerFilter
  if (mapTree.value) {
    mapTree.value.MapLayerFilter(val);
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 350px);
  position: relative;
}
</style>
