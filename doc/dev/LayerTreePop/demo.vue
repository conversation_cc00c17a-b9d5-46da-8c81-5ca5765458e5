<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapReadyEvent="_MapReadyEvent"
    />
    <LayerTreePop
      :LayerStore="layerStore"
      :BaseMapToolVisible="true"
      :PropStore="PropStore"
      :MapTreeHeight="550"
    />
  </div>
</template>
<script lang="ts" setup> 
import {
  Map,
  InitMapControl,
  LayerTreePop,
  defaultParameter,
} from "../../../packages/onemapkit";


/**第一步：导入图层参数*/
import {
  PropStore,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  _Onemap,
  initShowMapLayer,
} from "../../layer";

InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
});
const _MapReadyEvent = (avg1: any, avg2: any) => {
  console.log("yusy>BaseMapReady：avg1=", avg1, "， avg2=", avg2);
  initShowMapLayer();
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 350px);
  position: relative;
}
</style>
