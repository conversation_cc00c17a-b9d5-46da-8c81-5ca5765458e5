<template>
  <utable :fields="fields" :tabledata="tabledata"></utable>
</template>

<script setup>
import { ref } from "vue";
import { utable } from "../../../packages/onemapkit";
// import("../table/docProps.ts").then((res) => { fields.value = res.fields;  });

const tabledata = [
  {
    fldName: "MapControlName",
    fldType: "string",
    fldDescribe: "绑定的MapContro组件名称",
    fldOption: "可选",
    fldDefault: `mainMapControl`,
  },
  {
    fldName: "EsriObj",
    fldType: "arcgis对象",
    fldDescribe: "ArcgisJS3地图组件初始化对象",
    fldOption: "只读",
    fldDefault: "",
  },
  {
    fldName: "Arcgis3Tools",
    fldType: "Arcgis3Tools",
    fldDescribe: "Arcgis 3.x 通用方法",
    fldOption: "只读",
    fldDefault: "",
  },
  {
    fldName: "isImplNull",
    fldType: "boolean",
    fldDescribe: "OnemapClass类是否挂载对应地图模式的实现类",
    fldOption: "只读",
    fldDefault: "",
  },
  {
    fldName: "MapViewer",
    fldType: "Cesium.Viewer | map",
    fldDescribe:
      "实例化的地图对象，三维模式下就是Cesium.Viewer，二维模式就是Map",
    fldOption: "只读",
    fldDefault: "",
  },
  {
    fldName: "MapType",
    fldType: "ref(mapType)",
    fldDescribe: `该属性只是地图改变的响应式状态，不能作为OnemapClass的MapType属性赋值，
    否则无法正常实例化OnemapClass，赋值请用setMapType 或实例化时带mapType参数`,
    fldOption: "只读",
    fldDefault: "",
  },
  {
    fldName: "MaximumMemory",
    fldType: `CesiumTools.
    Cesium3DTilesMaximumTotalMemoryUsageControl`,
    fldDescribe: `
    设置三维Cesium的三维缓存大小`,
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "isMapReady",
    fldType: `boolean`,
    fldDescribe: `存放地图是否加载完毕状态参数`,
    fldOption: "",
    fldDefault: "false",
  },
  {
    fldName: "MapReadyHandler",
    fldType: `内部回调函数(Function)，不建议外部调用，可以用setMapReadyEvent方法重写函数实现`,
    fldDescribe: `在Map组件中调用`,
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "LayerCallbackCollection",
    fldType: "Map&lt;string, Map&lt;string, Function&gt;&gt;",
    fldDescribe: `地图加载异步事件的回调函数集合 `,
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "ImageryLayerClass",
    fldType: "ImageryLayerClass",
    fldDescribe: `地图服务加载顺序存储`,
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "ArcGisDynamicServices",
    fldType: "ArcGisDynamicServices",
    fldDescribe: `一种高性能加载动态地图的类`,
    fldOption: "只读",
    fldDefault: "",
  },
  {
    fldName: "OldImageLayerCollection",
    fldType: "Map&lt;string, Array&lt;string&gt;&gt;",
    fldDescribe: `地图服务加载顺序寄存储，在地图切换时存放已有的地图资源顺序`,
    fldOption: "",
    fldDefault: "",
  },
  {
    fldName: "DrawBrush",
    fldType: "",
    fldDescribe: `综合画图工具对象，具体在Arcgis和cesium里去实现`,
    fldOption: "",
    fldDefault: "",
  },
];
</script>

<style lang="scss" scoped></style>
