<script setup> 
import Preview from '/src/views/Preview.vue' 
import Attributes from './Attributes.vue'  
import Functions from './Functions.vue'  
</script>

# comstore 存储类

<br/>

## 基本用法

```
const PropStore: PropStorage = new PropStorage([
  {
    isLocalStorage: true,
    storagekey: "propertyVisible",
    variableType: 1,
    initStoragevalue: false,
  },
  {
    isLocalStorage: true,
    storagekey: "mousePosition",
    variableType: 2,
    initStoragevalue: {} /*IPosition类型*/,
  },
]);


const layerStore: LayerStorage = new LayerStorage([
  {
    variableType: 2, //变量类型
    storagekey: "CheckedLayerids", //变量名称
    isLocalStorage: true, //是LocationalStorage还是SessionLocalStorage
    initStoragevalue: [], //变量初值
  },
]) as any;
```

<br/>

## 参数说明

<Attributes/>
<br/>

## 方法

<Functions/>
<br/>
 
<div> 
</div>
