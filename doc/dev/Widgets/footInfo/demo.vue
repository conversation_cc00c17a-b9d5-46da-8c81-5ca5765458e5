<template>
  <div class="container" id="parentContainer">
    <MapControl
    ref="MapControlRef"
    MapControlName="mainMapControl"
      :LayerStore="layerStore"
      :PropStore="PropStore" 
      :MapReadyEvent="MapReadyEvent"
    />
    <BottomInfo :PropStore="PropStore" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref,  computed } from "vue";
import { MapControl, InitMapControl, mapType,BottomInfo, type IPosition ,defaultParameter} from "../../../../packages/onemapkit";

import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";

/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */
//导入图层参数
import { 
	_Onemap,
  ServiceUrl,layerStore,PropStore,
  BaseMapLayerUrl, 
  initShowMapLayer,CustomTools
} from "../../../layer"; 

/**
 * 第二步：配置公共参数
 */

//@ts-ignore 鼠标坐标需要在界面上实时显示，因此需要做数据劫持
const positionInfo: IPosition = ref({});
//MapControl 组件的地图实时回调方法
const MapRealtimeEvent = {
  Interval: 400,
  MapEvent: (obj, evg) => {
    //@ts-ignore
    positionInfo.value = event as IPosition;
  },
};
const MapControlRef = ref(null);
 

InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});
const MapReadyEvent = (obj: any, avg: any) => {
  initShowMapLayer()
};
//mounted
onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
