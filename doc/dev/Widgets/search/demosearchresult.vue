<template>
  <div
    style="
      position: relative;
      height: 410px;
      top: 3px;
      left: 10px;
      background-color: rgb(122, 157, 162);
    "
  >
    <!-- ---------------------------------------------
                     自定义搜索结果展示方式一   SearchResult      
      --------------------------------------------- -->
    <SearchResult
      :resultStyle="{
        top: '1px',
        left: `120px`,
        width: `400px`,
        height: `400px`,
        position: `relative`,
      }"
      :pageInfo="{
        keyword: _keyword,
        total: _TotalNum,
        pageSize: 10,
        pageCount: 5,
      }"
      @onQuery="_onQuery"
    >
      <!-- ---------------------------------------------

                     自定义搜索结果展示方式一   开始
      
      --------------------------------------------- -->
      <div
        style="
          width: 100%;
          height: calc(100% - 55px);
          background-color: rgb(250, 250, 250);
        "
      >
        <el-scrollbar style="background-color: rgb(250, 250, 250)">
          <ul id="resultUlid" style="padding-left: 0px">
            <li
              style="
                display: flex;
                padding-left: 0px;
                border-bottom: 1px solid #ebebeb; /* 这里可以调整颜色和样式 */
                padding: 3; /* 增加上下内边距 */
              "
              @mouseover="_pageMouseover($event, idx)"
              @mouseleave="_pageMouseleave($event, idx)"
              v-for="(item, idx) in CurrentResult"
              :id="item?.id"
              :key="idx"
            >
              <div
                style="
                  position: relative;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  width: 100px;
                  max-width: 100px;
                  padding-left: 0px;
                  /*pointer-events: none; 禁用鼠标事件  */
                "
              >
                <div
                  style="position: relative; height: 45px; z-index: 99"
                  class="image-container"
                >
                  <img
                    style="margin-top: 10px"
                    :src="Utils.getImage('LabelBlue', idx)"
                    alt="Red"
                  />
                </div>
                <el-button
                  type="info"
                  size="small"
                  style="z-index: 999"
                  @click.stop="resultItemIconClick(item)"
                  >自定义Icon{{ idx + 1 }}</el-button
                >
              </div>
              <div style="width: 100%; padding-left: 0px; z-index: 899">
                <h4 style="color: rgb(5, 5, 255)">
                  {{ item?.title }}
                </h4>
                <h4 style="color: rgb(37, 37, 37)">
                  <span style="color: rgb(56, 183, 14)">{{
                    item.prompt[0].admins[0].adminName
                  }}</span>
                  {{ item?.address }}
                </h4>
                <h4 style="color: rgb(255, 5, 159)">
                  <el-button
                    type="primary"
                    size="small"
                    style="z-index: 1999"
                    @click="resultItemTestClick(item)"
                    >自定义展示方式样例</el-button
                  >
                  {{ item?.lonlat }}
                </h4>
              </div>
            </li>
          </ul>
        </el-scrollbar>
      </div>
      <!-- ---------------------------------------------

                     自定义搜索结果展示方式一   结束
      
      --------------------------------------------- -->
    </SearchResult>

    <!-- ---------------------------------------------
                     自定义搜索结果展示方式二   SearchResult      
      --------------------------------------------- -->
    <SearchResult
      :resultStyle="{
        top: '1px',
        left: `600px`,
        width: `400px`,
        height: `400px`,
        position: `relative`,
      }"
      :pageInfo="{
        keyword: _keyword,
        total: _TotalNum,
        pageSize: 10,
        pageCount: 5,
      }"
    >
      <!-- ---------------------------------------------

                     自定义搜索结果展示方式二   开始
      
      --------------------------------------------- -->
      <div
        style="
          width: 100%;
          height: calc(100% - 55px);
          background-color: rgb(250, 250, 250);
        "
      >
        <el-scrollbar style="background-color: rgb(250, 250, 250)">
          <ul id="resultUlid" style="padding-left: 0px">
            <li
              style="
                display: flex;
                padding-left: 0px;
                border-bottom: 1px solid #ebebeb; /* 这里可以调整颜色和样式 */
                padding: 3; /* 增加上下内边距 */
              "
              v-for="(item, idx) in CurrentResult"
              :id="item?.id"
              :key="idx"
              @click="_pageMouseclick($event, item, idx)"
              @mouseover="_pageMouseover($event, idx)"
              @mouseleave="_pageMouseleave($event, idx)"
            >
              <div
                style="
                  position: relative;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  width: 100px;
                  max-width: 100px;
                  padding-left: 0px;
                  pointer-events: none; /* 禁用鼠标事件  */
                "
              >
                <div
                  style="position: relative; height: 45px"
                  class="image-container"
                >
                  <img
                    style="margin-top: 10px"
                    :src="Utils.getImage('LabelRed', idx)"
                    alt="Red"
                  />
                </div>
                <span style="color: rgb(109, 109, 109)">{{
                  item.prompt[0].admins[0].adminName
                }}</span>
              </div>
              <div style="width: 100%; padding-left: 0px; pointer-events: none">
                <h4 style="color: rgb(5, 5, 255)">
                  {{ item?.title }}
                </h4>
                <h4 style="color: rgb(37, 37, 37)">
                  {{ item?.address }}
                </h4>
                <h4 style="color: rgba(6, 176, 114, 0.865)">
                 地理坐标：{{ item?.lonlat }}
                </h4>
              </div>
            </li>
          </ul>
        </el-scrollbar>
      </div>
      <!-- ---------------------------------------------

                     自定义搜索结果展示方式二   结束
      
      --------------------------------------------- -->
    </SearchResult>

    <el-button @click="ShowSearchResultEvent"> 显示查询结果 </el-button>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch, h } from "vue";
import {
  SearchResult,
  Utils,
  type ITdtDataItemType,
  type ITdtResultType,
  mapType,
  MessageBox,
} from "../../../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";

import { _Onemap, PropStore } from "../../../layer";
const result = ref();
function resultItemIconClick(item) {
  MessageBox({
    props: {
      title: "自定义Icon",
      onConfirm: (_result: boolean) => {
        result.value = _result ? "选择的确定" : "选择的取消或关闭弹窗";
      },
    },
    content: JSON.stringify(item),
  });
}
const result2 = ref();
function resultItemTestClick(item) {
  MessageBox({
    props: {
      title: "自定义展示方式样例",
      onConfirm: (_result: boolean) => {
        result2.value = _result ? "选择的确定" : "选择的取消或关闭弹窗";
      },
    },
    content: item.address + "," + JSON.stringify(item.lonlat),
  });
}
 /** 显示查询结果 */
function ShowSearchResultEvent() {
  const newval = PropStore.getStorage("_SearchData").value;
  SearchData.value = newval;
  _TotalNum.value = PropStore.getStorage("_TotalNum").value;
  CurrentResult.value = SearchData.value.slice(0, 10);
}
/** 当前显示在结果栏中的poi */
const CurrentResult = ref<Array<ITdtDataItemType>>([]); /** 查询返回的总的poi */
/** 查询返回的总的poi */
let SearchData: any = ref();
// watch(
//   () => PropStore.getStorage("_SearchData").value,
//   (newval) => {
//     if (newval.length > 0) {
//       SearchData.value = newval;
//       _TotalNum.value = newval.length;
//       CurrentResult.value = SearchData.value.slice(0, 10);
//     }
//   }
// );
const _keyword = ref("样例");
const _extent = ref();
/** 存放总的查询到的条数 */
const _TotalNum = ref(0);
/** 存放点击POI的ID */
let clickItemID = ""; 

/** 点击分页栏上的按钮事件
 * @param option
 */
function _onQuery(option: {
  isQuery: boolean;
  //该分段中的开始搜索序号
  StartCount: number;
  //在分段中的顺序号，在搜索结果中从第几个开始取出来显示
  currentNum: number;
  eventType: string;
}) {
  console.log("_onQuery", option);
  if (option.isQuery) {
    const tmp: Promise<ITdtResultType> = _Onemap.QueryTdtPoi({
      keyword: _keyword.value,
      mapBound: _extent.value,
      start: option.StartCount,
    });
    tmp.then((data: ITdtResultType) => {
      SearchData.value = data.Data;
      CurrentResult.value = SearchData?.value.slice(
        option.currentNum,
        option.currentNum + 10
      ); 
      clickItemID = "";
    });
  } else {
    CurrentResult.value = SearchData?.value.slice(
      option.currentNum,
      option.currentNum + 10
    ); 
    clickItemID = "";
  }
}
//#region   收索结果列表的点击事件 返回最新点击Poi的ID
function _pageMouseclick(event: any, item: any, idx: number) {
  if (clickItemID == item.id) {
    return; //点击的同一记录 就返回
  }
  MessageBox({
    props: {
      title: "收索结果列表",
      onConfirm: (_result: boolean) => {
        result.value = _result ? "选择的确定" : "选择的取消或关闭弹窗";
      },
    },
    content: JSON.stringify(item),
  });
} 
//#endregion

//#region 收索结果列表的鼠标移进移出效果 LabelRed
function _pageMouseover(event: any, idx: number) {
  const listItem: HTMLDivElement = event.currentTarget.children[0].children[0];
  (listItem.children[0] as any).src = Utils.getImage("LabelRed", idx);
  (listItem.children[0] as any).style.marginTop = "5px";
}
function _pageMouseleave(event: any, idx: number) {
  if (clickItemID == event.currentTarget.id) {
    return;
  }
  const listItem: HTMLDivElement = event.currentTarget.children[0].children[0];
  (listItem.children[0] as any).src = Utils.getImage("LabelBlue", idx);
  (listItem.children[0] as any).style.marginTop = "10px";
}
//#endregion

onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  position: relative;
}
</style>
