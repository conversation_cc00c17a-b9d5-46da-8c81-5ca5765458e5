<template>
  <el-button type="primary" @click="openDialog">弹窗Dialog开关</el-button> 
  <Dialog
    v-if="showMoreToolsPanel"
    :title="'弹窗'"
    :width="550"
    :height="400"
    :dock="DockType.center"
    :scrollbar="false"
    :onClose="cloaseDialog"
  >
    这个是一个Dialog弹窗
  </Dialog>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { 
  DockType, 
  Dialog,
} from "../../../../packages/onemapkit";
 
  
let showMoreToolsPanel = ref(false); 
const cloaseDialog = () => {
  if (showMoreToolsPanel.value == false) {
    showMoreToolsPanel.value = true;
  } else {
    showMoreToolsPanel.value = false; 
  }
};
function openDialog() {
  showMoreToolsPanel.value = true;
} 
 
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
