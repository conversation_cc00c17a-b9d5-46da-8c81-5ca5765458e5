<!-- html -->
<template>
  <el-button type="primary" @click="funtestDialog">winPopup测试</el-button>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      :TipTools="[
        { Control: mapswitch, isPropStore: false, isLayerStore: false },
        { Control: fullmap, isPropStore: true, isLayerStore: false },
        { Control: mapzoom, isPropStore: false, isLayerStore: false },
      ]"
      @MapReadyEvent="_MapReadyEvent"
    ></Map>
    <MapToolBox
      MapControlName="mainMapControl"
      :LayerStore="layerStore"
      :PropStore="PropStore"
    />

    <!-- ---------------------------------------------------
       Popup与tdtSearch(包含Search、SearchResult两个组件)        
      -------------------------------------------------- -->

    <Popup  
      :contentID="popupcontentDivid"
      :popupStyle="{
        top: `2px`,
        left: `2px`,
        position: `absolute`,
      }"
      :showCaptionArrow="_showCaptionArrow"
      :isIncontent="isInnerContent"
      :contentStyle="{
        top: isInnerContent ? `35px` : `40px`,
        left: isInnerContent ? `-1px` : `2px`,
        position: `absolute`,
      }"
      :isHeaderClickEnable="_isHeaderClickEnable"
      :CaptionClickHandler="_CaptionClickHandler"
    >
      <template v-slot:caption>
        <tdtSearch
          MapControlName="mainMapControl"
          :PropStore="PropStore"
          :LayerStore="layerStore"
          :searchStyle="{
            top: `-1px`,
            left: isCaptionClick
              ? _showCaptionArrow
                ? `112px`
                : `36px`
              : `402px`,
            width: `287px`,
          }"
          :resultStyle="{
            top: '37px',
            left: isCaptionClick ? `-1px` : `402px`,
            width: `400px`,
            height: `400px`,
            position: `absolute`,
          }"
        ></tdtSearch>
      </template>
      <LayerTree
        ref="LayerTreeRef"
        :PropStore="PropStore"
        :LayerStore="layerStore"
        :MapTreeHeight="_MapTreeHeight"
      />
    </Popup>

    <BottomBar :PropStore="PropStore" />
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick, onMounted } from "vue";

import {
  LayerTree,
  MapToolBox,
  Map,
  defaultParameter,
  mapswitch,
  fullmap,
  mapzoom,
  Popup,
  InitMapControl,
  BottomBar,
  tdtSearch,
} from "../../../../packages/onemapkit";
import {
  _Onemap,
  PropStore,
  BaseMapLayerUrl,
  layerStore,
  ServiceUrl,
  CustomTools,
  initShowMapLayer,
} from "../../../layer";

const MapControlRef = ref();
const LayerTreeRef = ref();
InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});
const _MapReadyEvent = (avg1: any, avg2: any) => {
  console.log("======BaseMapReady：avg1=", avg1, "， avg2=", avg2);

  initShowMapLayer();
  _Onemap.fullExtent({
    xmin: 104.48964599996299,
    ymin: 20.211690999739915,
    xmax: 117.19277800030454,
    ymax: 26.385778000154612,
  });
};
const isInnerContent = ref(false);
const isCaptionClick = ref(false);
const _isHeaderClickEnable = ref(true);
const _showCaptionArrow = ref(true);
const _MapTreeHeight = ref(400);

function _CaptionClickHandler(result: {
  popup: any;
  Header: any;
  Content: any;
}): any {
  nextTick(() => {
    isCaptionClick.value = !isCaptionClick.value;
    _MapTreeHeight.value = result.Content.clientHeight - 80;
  });
}
function funtestDialog() {
  const cameraHeight = _Onemap.MapViewer.camera.positionCartographic.height;
  console.log(
    "==============cartographic 11",
    cameraHeight,
    _Onemap.MapViewer.camera
  );

  /*:PropStore="PropStore"
      :LayerStore="layerStore"
      :searchStyle="{
        top: `20px`,
        right: `60px`,
        width: `300px`,
      }"
      */
  // const frmPopup = winPopup({
  //   component: Popup,
  //   props: {
  //     contentID: popupcontentDivid,
  //     searchStyle: {
  //       top: `20px`,
  //       right: `60px`,
  //       width: `300px`,
  //     },
  //   },
  //   rootid: "mapviewerContainer",
  //   attrs: {},
  // });
}
const popupcontentDivid = "popupcontentDivid";
onMounted(() => {
  nextTick(() => {
    _MapTreeHeight.value =
      (document.getElementById(popupcontentDivid) as any).clientHeight - 80;
    // funtestDialog();
  });
});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
  background-color: rgb(255, 255, 255);
}

.image-container {
  position: relative;
  width: 30px; /* 根据实际图片大小调整 */
  height: 40px; /* 根据实际图片大小调整 */
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: opacity 0.3s;
}
.image-container .image-hover {
  position: absolute;
  opacity: 0; /* 默认隐藏 */
  transition: opacity 0.3s;
}
.image-container:hover .image-hover {
  opacity: 1; /* 悬停时显示 */
}
</style>
