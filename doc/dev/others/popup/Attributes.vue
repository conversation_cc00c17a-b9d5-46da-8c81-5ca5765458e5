<template>
  <utable   :tabledata="tabledata"></utable>
</template>

<script lang="ts" setup>
import { utable } from "../../../../packages/onemapkit";

const tabledata = [
  {
    fldName: "MapControlType",
    fldType: "mapType",
    fldDescribe: "地图组件类别属性",
    fldOption: "mapType",
    fldDefault: `{
              arcgis3="arcgis3",
              arcgis4="arcgis4",
              cesium="cesium",
              openlayer="openlayer",
            }`,
  },
  {
    fldName: "Options",
    fldType: "Option",
    fldDescribe: "地图组件初始化参数",
    fldOption:
      "由Arcgis的地图初始化参数，cesium地图初始化参数，以及本组件库自定义的 mapType / isFlyto / isReady / location 等参数",
    fldDefault: "{BASE_URL:''}",
  },
];
</script>

<style lang="scss" scoped></style>
