<template>
 
  
    <draggable :v-model="()=>{return tableData.filter((item)=>item.id !==1)} " tag="tbody">
      <template #item="{ element }">
        <tr :key="element.id">
          <td>{{ element.id }}</td>
          <td>{{ element.name }}</td>
          <td>{{ element.age }}</td> 
        </tr>
      </template>
    </draggable>
  </template>
  
  <script lang="ts" setup>
  import { ref } from 'vue'; 
  import draggable from 'vuedraggable';
  
  // 定义 tableData 类型
  interface TableRow {
    id: number;
    name: string;
    age: number;
  }
  
  const tableData = ref<TableRow[]>([
    { id: 1, name: '<PERSON>', age: 28 },
    { id: 2, name: '<PERSON>', age: 22 },
    { id: 3, name: '<PERSON>', age: 34 },
    { id: 4, name: '<PERSON>', age: 45 }
  ]);
  
 
  </script>
  
  <style scoped>
  /* 可以根据需要添加样式 */
  </style>
  