<template>
  <el-button type="primary" @click="openDialogFun">弹窗Dialog开关(函数方式)</el-button>
</template>
<script lang="ts" setup>
import { ref, h } from "vue";
import { Popwindow, DockType } from "../../../../packages/onemapkit";


let showMoreToolsPanel = ref(false);
let closeMoreTools: any;
const openDialogFun = () => {
  if (showMoreToolsPanel.value == false) {
    showMoreToolsPanel.value = true;
    /* 弹窗方案  */
    closeMoreTools = Popwindow({
      component: h("div", ["我是消息确认弹窗"]),
      nodeid: "maptoolsetdiv",
      attrs: {
        title: "消息确认弹窗",
        width: 500,
        height: 400,
        dock: DockType.center,
        margin: {
          top: 2,
          right: -40,
        },
        scrollbar: false,
        onClose: () => {
          showMoreToolsPanel.value = !showMoreToolsPanel;
        },
      },
    });
  } else {
    showMoreToolsPanel.value = false;
    closeMoreTools();
  }
};



//#region  测试：模拟一个异步操作的函数
function asyncOperation1() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("asyncOperation1");
      resolve("asyncOperation1");
    }, 30000);
  });
}
function asyncOperation33() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("asyncOperation33");
      resolve("asyncOperation33");
    }, 50000);
  });
}
function asyncOperation2(resultFromOp1: Promise<any>) {
  return new Promise((resolve, reject) => {
    resultFromOp1.then((data) => {
      console.log("======avg*********** " + data);
      resolve(" asyncOperation2 Result from resultFromOp1:" + data);
    });
  });
} 
function asyncOperation3(resultFromOp33: Promise<any>) {
  return new Promise((resolve, reject) => {
    resultFromOp33.then((data) => {
      console.log("======avg*********** " + data);
      resolve("asyncOperation3 Result from resultFromOp33:" + data);
    });
  });
} 
let pm1 = asyncOperation1();
let pm33 = asyncOperation33(); 
let pm2 = asyncOperation2(pm1);
let pm3 = asyncOperation3(pm33); 
Promise.all([pm2, pm3]).then((avg) => {
  console.log("======avg", avg);
});
//#endregion
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
