<template>
  <el-button type="primary" @click="openBadgeIcon">显示Badge</el-button
  ><el-button type="primary" @click="closeBadgeIcon">关闭Badge</el-button>
  <div style="display: flex; margin: 50px 10px; width: 100%">
    <ToolButton v-for="tool in tools" 
      ref="ToolButtonRef"
      :showBadge="isShowBadgeIcon"
      :icon="tool.icon"
      :tool="tool"
      :isErroBadge="isErroBadge"
      :label="tool.label"
      @onClick="ClickToolButotnItemEvent(tool)"
      @onBadgeClick="_BadgeClickEvent(tool)"
    /> 
  </div>
  <div style="">{{ resultinfo }}</div>
</template>
<script lang="ts" setup>
import { onMounted, ref, markRaw } from "vue";
import { ToolButton, type IToolItemType, Icons,DrawTools,Location, DockType } from "../../../../packages/onemapkit";

const ToolButtonRef=ref()
const resultinfo = ref("");
const isShowBadgeIcon = ref(false);
const isErroBadge =ref(true)
const label = ref("我是Badge按钮");
const openBadgeIcon = () => {
  isShowBadgeIcon.value = true;
};
const closeBadgeIcon = () => {
  isShowBadgeIcon.value = false;
};
const tools = ref([
  {
    index: 1,
    name: "Location",
    label: "定位",
    icon: Icons.IconHTML.locate,
    component: markRaw(Location),
    groupname: "基础工具",
    Dock: DockType.center,
    isActive: false,
    isQuick: true,
    isFix: true,
    defaultSize: {
      width: 400,
      height: 250,
    },
  }, {
    index:2,
    name: "DrawTools",
    label: "绘制",
    icon: Icons.IconHTML.draw,
    component: markRaw(DrawTools),
    groupname: "基础工具",
    Dock: DockType.center,
    paramType: 0,
    isActive: false,
    isQuick: false,
    isFix: false,
    defaultSize: {
      width: 550,
      height: 220,
    },
  },
]);

const ClickToolButotnItemEvent = (customTool: IToolItemType | any) => {
  if(isShowBadgeIcon.value){
    return;
  }
  resultinfo.value = `你点击按钮${customTool.label}`;
};
/** 工具箱中 Item 右上角的配置按钮事件 */
const _BadgeClickEvent = (prop: IToolItemType): any => {
  let tmp: any = ToolButtonRef.value.filter((itm: any) => itm.toolitem.name === prop.name)[0] || false;
   if(tmp == false)return;
   if(prop.isFix){

    resultinfo.value = `你点击的${prop.label}按钮为固定显示按钮`;
   }
  tmp.updateBadgeState(!tmp.isErroBadge); 
}; 
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
