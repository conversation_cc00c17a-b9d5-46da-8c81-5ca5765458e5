<template>
  <el-button type="primary" @click="openDialogFun">MessageBox弹窗</el-button>
  <div style="margin: 50px 10px">{{ result }}</div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { MessageBox } from "../../../../packages/onemapkit";

const result = ref();

const openDialogFun = () => {
  MessageBox({
    props: {
      title: "提示",
      onConfirm: (_result: boolean) => {
        result.value = _result ? "选择的确定" : "选择的取消或关闭弹窗";
      },
    },
    content: "是否清除所有绘制图形？",
  });
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100vh - 400px);
  position: relative;
}
</style>
