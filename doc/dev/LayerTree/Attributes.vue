<template>
  <div
    style="
      display: flex; /* 使用 Flexbox 布局 */
      flex-direction: column; /* 垂直排列子元素 */
      align-items: flex-start; /* 靠左对齐 */
      padding: 10px;
    "
  >
    <utable :fields="fields" :tabledata="LayerStorage"></utable>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { utable } from "../../../packages/onemapkit";
// import("../table/docProps.ts").then((res) => { fields.value = res.fields;  });

const LayerStorage = [
  {
    fldName: "MapControlName",
    fldType: "string",
    fldDescribe: "绑定的MapContro组件名称",
    fldOption: "可选",
    fldDefault: `mainMapControl`,
  },
  {
    fldName: "LayerTreeLabel",
    fldType: "String",
    fldDescribe: "参数值对象",
    fldOption: "",
    fldDefault: "图层资源",
  },
  {
    fldName: "LayerStore",
    fldType: "LayerStorage",
    fldDescribe: "数据源参数",
    fldOption: "",
    fldDefault: "null",
  },
  {
    fldName: "PropStore",
    fldType: "PropStorage",
    fldDescribe: "数据源参数",
    fldOption: "",
    fldDefault: "null",
  },
  {
    fldName: "MapTreeHeight",
    fldType: "Number",
    fldDescribe: "组件高度",
    fldOption: "",
    fldDefault: `400`,
  }, 
];
 
</script>

<style lang="scss" scoped></style>
