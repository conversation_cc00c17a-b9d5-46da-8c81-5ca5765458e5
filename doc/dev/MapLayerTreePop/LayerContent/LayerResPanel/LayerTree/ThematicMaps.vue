<template>
	<div id="thematic-view">
		<el-tree v-show="false" :data="treeData" show-checkbox node-key="id" :props="{
			label: 'text',
		}" ref="treeRef">
		</el-tree>
		<el-dialog v-model="uploadMaticBox" modal lock-scroll title="自定义专题" center width="800px" @open="openDialog"
			destroy-on-close>
			<el-form class="dialogfrom">
				<el-row>
					<el-col :span="19">
						<el-form-item label="专题名称">
							<el-input maxlength="20" placeholder="请输入专题名称" v-model="newMaticName" size="small">
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="5">
						<span class="table-tools" style="padding-left: 40px; line-height: 40px" @click="getLayer">获取当前地图设置
						</span>
					</el-col>
				</el-row>
				<el-col :span="24">
					<el-form-item label="底图图层">
						<el-select v-model="maticBaseMap" size="small" placeholder="请选择底图图层" style="width: 182px;">
							<el-option size="small" v-for="item in baseMapList" :key="item.layerid" :label="item.name" :value="item.layerid">
							</el-option>
						</el-select>
					</el-form-item>
					<div style="display: flex;">
						<el-form-item label="专题图层">
							<div class="dialogtree">
								<el-tree :data="TreeDataList" show-checkbox node-key="layerid" :props="props.propsExt?.TreeNodeProps"
									ref="treeRefDialog">
									<template #default="{ node }">
										<span class="custom-tree-node">
											<span :title="node.label">{{ node.label }}</span>
										</span>
									</template>
								</el-tree>
							</div>
						</el-form-item>
						<div>
							<el-row>
								<el-form-item label="专题类型" class="cameraitem" label-width="96px">
									<el-select style="width: 182px" v-model="switch23D" @change="changeSwaitch23D" placeholder="请输入专题类型"
										size="small">
										<el-option label='2D' value='2D'>
										</el-option>
										<el-option label='3D' value='3D'>
										</el-option>
									</el-select>
								</el-form-item>
							</el-row>

							<el-row>
								<el-form-item label="专题视角" class="cameraitem" label-width="96px">
									<el-switch v-model="cameraswitch" size="small">{{ cameraswitch }}
									</el-switch>
								</el-form-item>
							</el-row>

							<el-row v-if="cameraswitch">
								<el-form-item label="缩放级别" class="cameraitem" label-width="96px">
									<el-input maxlength="20" :disabled="switch23D == '3D'" placeholder="请输入缩放级别"
										v-model="cameraStatus.zoom" style="width: 182px;" size="small">
									</el-input>
								</el-form-item>
								<!-- <el-form-item v-if="cameraStatus.cameraPosition && _Onemap.MapType.value == 'cesium'" label="定位相机" class="cameraitem" label-width="96px">
                              <div>
                                <div v-for="item in Object.keys(cameraStatus.cameraPosition)" :key="item">{{ item }}：{{ cameraStatus.cameraPosition[item].toFixed(5) }}</div>
                              </div>
                            </el-form-item> -->
								<el-form-item label="罗盘方位" class="cameraitem" label-width="96px" v-if="switch23D != '2D'">
									<el-input maxlength="20" placeholder="请输入罗盘方位" style="width: 182px;" v-model="cameraStatus.heading"
										size="small">
									</el-input>
								</el-form-item>
							</el-row>
							<el-row v-if="cameraswitch">
								<el-form-item label="视角倾斜" class="cameraitem" label-width="96px" v-if="switch23D != '2D'">
									<el-input maxlength="20" placeholder="请输入视角倾斜" style="width: 182px;" v-model="cameraStatus.tilt"
										size="small">
									</el-input>
								</el-form-item>
							</el-row>

							<el-row v-if="cameraswitch">
								<el-form-item label="经度" class="cameraitem" label-width="96px">
									<el-input maxlength="20" placeholder="请输入经度" style="width: 182px;" v-model="cameraStatus.lon"
										size="small">
									</el-input>
								</el-form-item>
								<el-form-item label="纬度" class="cameraitem" label-width="96px">
									<el-input maxlength="20" placeholder="请输入纬度" style="width: 182px;" v-model="cameraStatus.lat"
										size="small">
									</el-input>
								</el-form-item>
							</el-row>
						</div>
					</div>
				</el-col>

			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button size="small" @click="uploadMaticBox = false">取消</el-button>
					<el-button size="small" type="primary" @click="addMaticList">提交</el-button>
				</span>
			</template>
		</el-dialog>
		<div class="table-tools-add" @click="openMaticBox"><span style="font-size:16px">✚</span>添加自定义专题</div>
		<el-table default-close-all :show-header="false" empty-text="暂无数据"
			:cell-style="{ paddingTop: '10px', paddingBottom: '10px' }" :data="maticList"
			:height="props.simbleBasemapVisible ? `calc(100vh - 456px)` : `calc(100vh - 295px)`" class="maticTable"
			style="width: 100%" ref="tableref" :style="{ 'max-height': '' + heightChangeStr + '' }">
			<el-table-column type="expand">
				<template #default="props">
					<div style="background: #f2f2f2; padding: 10px 20px 30px 20px">
						<p class="maticlayerlist" style="color: #009a00; font-size: 14px; text-indent: 0">
							<span>【底图】</span>
							{{ props.row.baseMapName }}
						</p>
						<el-checkbox-group v-model="checkList[props.row.id]">
							<div v-for="(item, index) in props.row.serviceDatas" :key="index" class="maticlayerlist">
								<el-checkbox :name="item.layerid" checked :label="item.name" />
							</div>
						</el-checkbox-group>
					</div>
				</template>
			</el-table-column>
			<el-table-column label="名称">
				<template v-slot="{ row }">
					<span>{{ row.name }}<span v-if="row.isCustom">(自定义)</span></span>
				</template>
			</el-table-column>
			<el-table-column label="操作" width="150">
				<template v-slot="{ $index, row }"><span class="table-tools" v-if="row.isCustom"
						style="color: red !important; margin-left: 15px" @click="deleteMatic($index, row)">
						移除
					</span>
					<span class="table-tools" @click="onMatic($index, row)">
						打开专题图层
					</span>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, ref, nextTick } from "vue";
import { getOption, mapType, Utils, mapEmits, getOnemap, type IMapLayer, OnemapEvent, serviceType } from "onemapkit";
import { ElMessage, ElMessageBox } from "element-plus";
import * as Cesium from "@onemapkit/cesium";
import arcgisScale from "./arcgisScale"

const props = defineProps({
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: false,
	},
	LayerStore: {
		type: Object as any,
		default: null,
	},
	PropStore: {
		type: Object as any,
		default: null,
	},
	baseMap: {
		type: Object,
		default: null,
	},
	simbleBasemapVisible: {//简约底图面板是否可见
		type: Boolean,
		default: true,
	},
	onMapLayerTreeCheck: {
		type: Function,
		default: null
	},
	propsExt: {
		type: Object
	}
});

const emits = defineEmits([...mapEmits, 'updateRecentList', 'checkoutFalseAll']);
// //调整组件高度
const heightChange = ref(0);
const heightChangeStr = ref("calc(100vh - " + heightChange.value + "px)");
const _inOptions = getOption(props.MapControlName);
const _Onemap = getOnemap(props.MapControlName);
const treeRef = ref();

const TreeDataList = ref(Utils.createNewTreeData(props.LayerStore.Layers, (node: any) => {
	return (
		Utils.MapLayerFilter.MapLayers(node, true) &&
		Utils.isUseMapModel(node, _Onemap.MapType.value)
	);
})
)
const baseMapList = ref();
const treeData = ref();
const maticList = ref();
const checkList: any = ref({});

// //删除单个专题
const deleteMatic = (_index: any, _row: any) => {
	ElMessageBox.confirm("是否删除该专题列表 ？", "提示", {
		confirmButtonText: "是",
		cancelButtonText: "否",
		customClass: "ElMessageBoxStyle",
		type: "warning",
	}).then(async () => {
		if (OnemapEvent.getMaticListAPI && OnemapEvent.getMaticListAPI.delete) {
			const res = await OnemapEvent.getMaticListAPI.delete(_row.id);
			console.log("删除专题：", res);
			if (res.code === 200) {
				getMaticMaps();
				ElMessage.success("自定义专题删除成功!");
			}
		} else {
			ElMessage.error("自定义专题删除失败，请联系管理员");
		}
	});
};

const newMaticName = ref()
const maticBaseMap = ref()
const uploadMaticBox = ref(false)
const cameraStatus = ref()
const switch23D = ref('2D');
const cameraswitch = ref(true);//自定义专题视角配置开关
const getCoordInfo = () => {
	console.log('_Onemap', _Onemap);

	if (_Onemap.MapType.value == mapType.cesium) {
		//获取zoom
		let Cartographic = (_Onemap.MapViewer as Cesium.Viewer).camera.positionCartographic
		let getzoom: any = arcgisScale.getZoomFromHeight(_Onemap.MapViewer, Cartographic.height, Cesium.Math.toDegrees(Cartographic.latitude))

		console.log('getzoom', getzoom);
		// //相机坐标
		// let cameraInfo = _Onemap.MapViewer.camera.position
		let cameraPosition = {
			x: Cesium.Math.toDegrees(Cartographic.longitude),
			y: Cesium.Math.toDegrees(Cartographic.latitude),
		}
		console.log('cameraPosition', cameraPosition);

		// 鼠标信息
		const mapPoint: any = props.PropStore.bottomInfoData.value.coordinates
		let data = {
			lat: cameraPosition.y.toFixed(5),
			lon: cameraPosition.x.toFixed(5),
			// z: Cartographic.height,
			tilt: mapPoint && mapPoint.pitch ? (mapPoint.pitch + 90).toFixed(2) : 0.00,
			heading: mapPoint && mapPoint.heading ? mapPoint.heading.toFixed(2) : 0.00,
			zoom: getzoom.toFixed(5)
		}
		cameraStatus.value = data;
	} else {
		if (_Onemap.MapViewer) {
			let center = _Onemap.MapViewer.extent.getCenter()
			console.log('getextent', center);
			cameraStatus.value = {
				lat: center.y.toFixed(5),
				lon: center.x.toFixed(5),
				zoom: _Onemap.MapViewer.getZoom(),
			};
		}
	}
}

const openMaticBox = () => {
	switch23D.value = _Onemap.MapType.value == 'arcgis' ? '2D' : '3D';
	treeData.value = handle3DNode(treeData.value, switch23D.value == '2D');
	newMaticName.value = null;
	maticBaseMap.value = _inOptions.BaseMapLayer.layerid !== "basemap" ? _inOptions.BaseMapLayer.layerid : "";
	uploadMaticBox.value = true;
	getCoordInfo()
};
const changeSwaitch23D = () => {
	treeData.value = handle3DNode(treeData.value, switch23D.value == '2D');
	let mapTypeValue: any = switch23D.value == '2D' ? 'arcgis' : 'cesium'
	TreeDataList.value = Utils.createNewTreeData(props.LayerStore.Layers, (node) => {
		return (
			Utils.MapLayerFilter.MapLayers(node, true) &&
			Utils.isUseMapModel(node, mapTypeValue)
		);
	})
};
const openDialog = () => {
	switch23D.value = _Onemap.MapType.value == 'arcgis' ? '2D' : '3D';
	treeData.value = handle3DNode(treeData.value, switch23D.value == '2D');
};
const expandnode = (node: any) => {
	node.expanded = true;
	if (node.parent) {
		expandnode(node.parent)
	}
}
const getLayer = () => {
	// maticBaseMap.value = activeBaseMap.value;
	treeRefDialog.value.setCheckedKeys(props.LayerStore.CheckedLayerids);
	for (let index = 0; index < props.LayerStore.CheckedLayerids.length; index++) {
		var node = treeRefDialog.value.getNode(props.LayerStore.CheckedLayerids[index]);
		if (node) {
			expandnode(node)
		}
	}

	getCoordInfo()
	switch23D.value = _Onemap.MapType.value == 'arcgis' ? '2D' : '3D'
};

const treeRefDialog: any = ref(null);
const addMaticList = async () => {
	if (treeRefDialog.value) {
		var checkeddiatree = treeRefDialog.value.getCheckedKeys(true);
	}
	let remark = "";
	if (cameraswitch.value) {
		if (switch23D.value == '2D') {
			cameraStatus.value.tilt = null
			cameraStatus.value.heading = null
		}
		remark = JSON.stringify(cameraStatus.value);
	}
	if (
		checkeddiatree.length != 0 &&
		newMaticName.value &&
		maticBaseMap.value
	) {
		if (OnemapEvent.getMaticListAPI && OnemapEvent.getMaticListAPI.add) {
			const res = await OnemapEvent.getMaticListAPI.add({
				name: newMaticName.value,
				baseMapId: maticBaseMap.value,
				order: 0,
				group: "",
				mapIds: checkeddiatree,
				remark: remark,
				metadata: null,
				analystIds: [],
			})
			console.log("添加专题：", res);
			if (res.code == 200) {
				uploadMaticBox.value = false;
				getMaticMaps();
				ElMessage.success("自定义专题添加成功!");
			} else {
				ElMessage.error("自定义专题添加失败!");
			}
		} else {
			ElMessage.error("自定义专题删除失败，请联系管理员");
		}
	} else {
		ElMessage.error("自定义专题内容不完整!");
	}
};

//获取地图配置信息
const getMapServices = async () => {
	if (props.PropStore.appConfigData && props.propsExt?.MapLayers) {
		baseMapList.value = props.propsExt?.MapLayers(true, props).basemap;
		console.log("treeData.value", treeData.value);
		console.log("baseMapList.value", baseMapList.value);
		getMaticMaps();
	}
};

const handle3DNode = (tree: any, status: boolean) => {
	if (!tree || !tree.length) {
		return [];
	}
	return tree.map((data: any) => {
		// 无权限的子节点禁止点击

		if (data.children && data.children.length) {
			data.disabled = true;
			data.children = handle3DNode(data.children, status);
		} else {
			if (data && data.visualType == "3D") {
				// 有子集的当前节点禁止点击
				data.disabled = status;
				data.checked = false;
			}
		}
		return data;
	});
};

//获取并更新专题列表
const getMaticMaps = async () => {
	if (OnemapEvent.getMaticListAPI && OnemapEvent.getMaticListAPI.get) {
		let res = await OnemapEvent.getMaticListAPI.get();
		if (res.length) {
			maticList.value = res
			buildMaticList(maticList.value);
		}
	}
	// }
};

const buildMaticList = (data: any) => {
	for (let i = 0; i < data.length; i++) {
		const element = data[i].id;
		if (checkList.value && !checkList.value[element]) {
			checkList.value[element] = [];
		}
	}
	nextTick(() => {
		for (let index = 0; index < data.length; index++) {
			//根据ID获取底图名称
			for (const key in baseMapList.value) {
				if (data[index].baseMapId == baseMapList.value[key].layerid) {
					data[index].baseMapName = baseMapList.value[key].name;
				}
			}

			//根据ID循环获取图层信息
			const element = data[index].mapIds;
			maticList.value[index].serviceDatas = [];
			for (let id of element) {
				const ly = props.LayerStore.MapLayers.find((x: IMapLayer) => x.layerid == id);
				if (ly) {
					ly.checked = true;
					maticList.value[index].serviceDatas.push(ly);
				}
			}
		}
	});
};
const tableref = ref();
// // 打开专题
const onMatic = (_index: any, row: any) => {
	console.log("row", row);
	console.log("baseMapList.value", baseMapList.value);
	tableref.value.toggleRowExpansion(row, true);
	let confirmstring = "是否取消勾选其他已选择图层,只显示专题图层？";
	let maticmapViewType = "2D";
	for (let index = 0; index < row.serviceDatas.length; index++) {
		const element = row.serviceDatas[index] as IMapLayer;
		if (element.useMapModel?.includes(mapType.cesium)) {
			maticmapViewType = "3D";
			if (element.serviceType == serviceType.Cesium3DTiles) {
				maticmapViewType = "3D";
			} else {
				maticmapViewType = "3D";
			}
		}
	}
	// 	//是否需要切换3D
	let switch3D =
		maticmapViewType != "2D" && _Onemap.MapType.value == mapType.arcgis;

	// 	//是否需要切换视角
	let hascamerasetting = false;
	let camera: any;
	if (row.remark) {
		try {
			camera = JSON.parse(row.remark);
		} catch (error) {
			console.log(error);
		}
	}
	if (camera && camera.zoom && camera.zoom != "") {
		hascamerasetting = true;
	}
	let servicedatachecked = [];
	let servicedataidschecked = [];
	for (let i = 0; i < checkList.value[row.id].length; i++) {
		if (
			row.serviceDatas.find((x: any) => {
				return x.text == checkList.value[row.id][i];
			})
		) {
			servicedatachecked.push(
				row.serviceDatas.find((x: any) => {
					return x.text == checkList.value[row.id][i];
				})
			);
			servicedataidschecked.push(
				row.serviceDatas.find((x: any) => {
					return x.text == checkList.value[row.id][i];
				}).id
			);
		}
	}

	// 	//是否需要清除图层
	let hasotherlayer = false;
	if (props.LayerStore.CheckedLayerids.length > 0) {
		hasotherlayer = true;
	}
	if (hasotherlayer && hascamerasetting && switch3D) {
		confirmstring =
			"您目前打开了其他图层，切换到" +
			row.name +
			"需要切换到三维视图并定位到专题的指定位置，然后需要您重新打开这些图层，是否继续？";
	}

	if (!switch3D && hascamerasetting && hasotherlayer) {
		confirmstring =
			"您目前打开了其他图层，切换到" +
			row.name +
			"将会定位到专题的指定位置，然后需要您重新打开这些图层，是否继续？";
	}
	if (!switch3D && !hascamerasetting && hasotherlayer) {
		confirmstring =
			"您目前打开了其他图层，切换到" +
			row.name +
			"后需要您重新打开这些图层，是否继续？";
	}

	if (switch3D && !hascamerasetting && hasotherlayer) {
		confirmstring =
			"您目前打开了其他图层，切换到" +
			row.name +
			"需要切换到三维视图，然后需要您重新打开这些图层，是否继续？";
	}
	if (switch3D && hascamerasetting && !hasotherlayer) {
		confirmstring =
			"切换到" +
			row.name +
			"需要切换到三维视图并定位到专题的指定位置，是否继续？";
	}

	if (switch3D && !hascamerasetting && !hasotherlayer) {
		confirmstring = "切换到" + row.name + "需要切换到三维视图，是否继续？";
	}
	if (!switch3D && hascamerasetting && !hasotherlayer) {
		confirmstring =
			"切换到" + row.name + "将会定位到专题的指定位置，是否继续？";
	}

	if (!switch3D && !hascamerasetting && !hasotherlayer) {
		confirmstring = "即将切换到" + row.name + "，是否继续？";
	}

	ElMessageBox.confirm(confirmstring, "提示", {
		distinguishCancelAndClose: true,
		customClass: "ElMessageBoxStyle",
		confirmButtonText: "是",
		cancelButtonText: "否",
	})
		.then(() => {
			if (switch3D) {
				if (_Onemap.MapType.value == mapType.arcgis) {
					console.log('切换三维场景')
					//用来切换3三维
					props.PropStore.setChangeToCesium(props.PropStore.ChangeToCesium.value + 1)
					nextTick(async () => {
						setTimeout(function () {
							if (props.LayerStore.CheckedLayerids.length) {
								emits('checkoutFalseAll')
							}
							addMaticLayer(row);
						}, 2000);
					})
				}

			} else {
				if (props.LayerStore.CheckedLayerids.length) {
					//先删除原来的图层
					emits('checkoutFalseAll')
				}
				setTimeout(function () {
					addMaticLayer(row);
				}, 1500);
			}
		})
		.catch((_action: any) => {
			console.log("action");
		});
};

const addMaticLayer = async (row: any) => {
	let camera: any;
	if (row.remark) {
		try {
			camera = JSON.parse(row.remark);
		} catch (error) {
			console.log(error);
		}
	}

	let cameragoto: any = {};
	if (camera && camera.tilt) {
		cameragoto.tilt = camera.tilt;
	}
	if (camera && camera.zoom) {
		cameragoto.zoom = camera.zoom;
	}
	if (camera && camera.lon && camera.lat) {
		cameragoto.center = [camera.lon, camera.lat];
	}
	if (camera && camera.heading) {
		cameragoto.heading = camera.heading;
	}
	//如果有视角，切换视角
	if (camera) {
		if (_Onemap.MapType.value === mapType.arcgis) {
			_Onemap.gotoPoint(
				{ y: camera.lat, x: camera.lon, wkid: 4490 },
				camera.zoom ? parseInt(camera.zoom) : 16,
				false
			);
		} else {
			if (camera.tilt >= 0 && camera.tilt !== null) {
				camera.tilt = camera.tilt - 90;
			}
			if (!camera.tilt && !camera.heading) {
				const mapPoint = props.PropStore.bottomInfoData.value.coordinates;
				camera.tilt = mapPoint.pitch
				camera.heading = parseFloat(mapPoint.heading).toFixed(2)
			}
			arcgisScale.goTo(_Onemap.MapViewer, camera.lon, camera.lat, camera.zoom, camera)
		}
	}
	let servicedatachecked: any[] = [];
	for (let i = 0; i < checkList.value[row.id].length; i++) {
		if (
			row.serviceDatas.find((x: any) => {
				return x.name == checkList.value[row.id][i];
			})
		) {
			servicedatachecked.push(
				row.serviceDatas.find((x: any) => {
					return x.name == checkList.value[row.id][i];
				})
			);
		}
	}

	// 底图切换
	const one = baseMapList.value.find((e: any) => e.layerid === row.baseMapId);
	if (one) {
		props.propsExt?.onChangeBaseMap(one);
	}

	for (let item of servicedatachecked) {
		let lyData = props.LayerStore.MapLayers.find((it: any) => {
			return it.layerid == item.layerid
		})
		if (props.onMapLayerTreeCheck && props.propsExt) {
			nextTick(() => {
				props.propsExt!.asyncTreeChecked(lyData.layerid, true);
				nextTick(() => {
					props.onMapLayerTreeCheck(lyData);
				})
			})
		}
	}
};

onMounted(() => {
	nextTick(() => {
		getMapServices();
		if (_Onemap.MapViewer) {
			getCoordInfo();
		}
	});
});
</script>

<style lang="scss" scoped>
@import "../css/LayerContent";

#thematic-view {
	padding-top: 6px;
	box-sizing: border-box;
}

.table-tools {
	margin-left: 5px;
	color: #1890ff !important;
	font-size: 12px;
	cursor: pointer;
	text-decoration: underline;
	float: right;
}

.table-tools-add {
	width: 94%;
	margin-left: 3%;
	height: 30px;
	line-height: 30px;
	text-align: center;
	background: #E3F2FD;
	color: #0091FF;
	font-size: 13px;
	cursor: pointer;
}

.dialogtree {
	height: calc(80vh - 330px);
	width: 400px;
	overflow: auto;
	border: 1px solid #ccc;
	border-radius: 5px;
}

:deep(.el-message-box__headerbtn) {
	top: 0;
	right: 0;
	width: 0;
	height: 0;
}

:deep(.el-dialog__header) {
	background: #fff;
}
</style>
<style lang="scss" scoped>
.ElMessageBoxStyle {

	.el-message-box__headerbtn {
		top: 10px;
		right: 15px;
		width: 10px;
		height: 10px;
	}
}
</style>