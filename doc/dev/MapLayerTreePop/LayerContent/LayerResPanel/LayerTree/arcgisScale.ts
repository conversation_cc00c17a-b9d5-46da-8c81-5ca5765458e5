import * as Cesium from "@onemapkit/cesium";

const defaultTileInfo = {
  lods: [],
} as any;

for (let i = 0; i < 40; i++) {
  defaultTileInfo.lods.push({
    level: i,
    scale: 590995186.1176 / Math.pow(2, i),
  });
}

export default class test {
  static goTo(
    viewer: Cesium.Viewer,
    longitude: number,
    latitude: number,
    zoom: number,
    camera?: any
  ) {
    const lods = defaultTileInfo.lods;
    if (zoom >= lods[0].level && zoom <= lods[lods.length - 1].level) {
      // zoom转scale
      let scale = 0;
      for (let i = 0; i < lods.length - 1; i++) {
        const curIndex = i;
        const nextIndex = i + 1;
        const curLevel = lods[curIndex].level;
        const nextLevel = lods[nextIndex].level;

        if (zoom >= curLevel && zoom <= nextLevel) {
          // 确定一下线性插值的程度
          const mixValue = (zoom - curLevel) / (nextLevel - curLevel);
          const curScale = lods[curIndex].scale;
          const nextScale = lods[nextIndex].scale;
          // 线性插值获取scale（防止传小数）
          scale = curScale + (nextScale - curScale) * mixValue;
          break;
        }
      }

      // 照搬ArcGIS的方法↓
      const o =
        viewer.scene.canvas.clientWidth / 2 / (viewer.scene as any).pixelRatio;
      const t = scale * Math.cos(Cesium.Math.toRadians(latitude));

      const fovX = (viewer.camera.frustum as any).fov;
      const height = o / ((96 * 39.37) / t) / Math.tan(fovX / 2);

      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
        orientation: {
          heading: camera.heading ? Cesium.Math.toRadians(Number(camera.heading)) : Cesium.Math.toRadians(0),
          pitch: Cesium.defined(camera.tilt) ? Cesium.Math.toRadians(Number(camera.tilt)) : Cesium.Math.toRadians(-90),
          roll: 0.0,
        },
      });
    } else {
      console.error("不支持该等级");
    }
  }

  static getZoomFromHeight(
    viewer: Cesium.Viewer,
    height: number,
    latitude: number
  ) {
    const o =
      viewer.scene.canvas.clientWidth / 2 / (viewer.scene as any).pixelRatio;

    const fovX = (viewer.camera.frustum as any).fov;

    const t = (96 * 39.37) / (o / (height * Math.tan(fovX / 2)));
    const scale = t / Math.cos(Cesium.Math.toRadians(latitude));

    const lods = defaultTileInfo.lods;
    let zoom;
    if (scale >= lods[lods.length - 1].scale && scale <= lods[0].scale) {
      for (let i = 0; i < lods.length - 1; i++) {
        const curIndex = i;
        const nextIndex = i + 1;
        const curScale = lods[curIndex].scale;
        const nextScale = lods[nextIndex].scale;

        if (scale >= nextScale && scale <= curScale) {
          // 确定一下线性插值的程度
          const mixValue = (scale - curScale) / (nextScale - curScale);
          const curLevel = lods[curIndex].level;
          const nextLevel = lods[nextIndex].level;
          // 线性插值获取scale（防止传小数）
          zoom = curLevel + (nextLevel - curLevel) * mixValue;
          break;
        }
      }

      return zoom;
    } else if (scale > lods[0].scale) {
      return lods[0].level;
    } else {
      return lods[lods.length - 1].level;
    }
  }
}
