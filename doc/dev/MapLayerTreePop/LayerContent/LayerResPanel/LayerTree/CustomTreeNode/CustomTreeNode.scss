@charset "UTF-8";
$primary-color: var(--primaryColor, #1890ff);

.custom-tree-node {
  width: 100%;
	min-height: 30px;
  display: flex;
  align-items: flex-start;
  position: relative;
  cursor: default;

  &__label {
    display: inline-block;
    //white-space: nowrap;
    padding: 3px 0;
    padding-left: 23px;
    //text-overflow: ellipsis;
    overflow: hidden;
    word-break: normal;
    white-space: pre-wrap;
    word-wrap: break-word;

    &.no-auth {
      color: #c7cbd4;
    }
  }

  &__more {
    position: absolute;
    right: 10px;
    font-size: 18px;
    margin-top: 0px;

    &:hover {
      color: #1890ff;
    }
  }

  .table-tools {
		position: absolute;
    right: 40px;
    top: 5px;
    width: 110px;
    height: 24px;

    .top {
      left: 0px;
      cursor: pointer;
    }

    .bottom {
      left: 25px;
      cursor: pointer;
    }

    .remove {
      left: 50px;
      cursor: pointer;
      color: red;
    }
  }
}

.more-operate-container {
  height: 30px;
  padding-bottom: 5px;
  border-bottom: solid 1px #e5e5e5;
  width: 100%;
  font-size: 12px;
  display: flex;
  align-items: center;

  .line {
    margin: 0 10px;
    text-align: center;
    color: $primary-color;
  }

  .btn-collect {
    color: #999;
    display: flex;
    align-items: center;

    .el-icon {
      font-size: 15px;
      margin-right: 3px;
    }

    &.is-active {
      color: #f7b500;
    }
  }

  .btn-quanxian,
  .btn-opacity {
    color: $primary-color;
    display: flex;
    align-items: center;
  }

  .btn-fullExtent,
  .btn-opacity {
    color: $primary-color;
    display: flex;
    align-items: center;
  }

  .iconfont {
    font-size: 22px;
  }
}

:deep(.el-popover) {
  border: 3px solid rgb(228, 229, 229);
}

.Scale {
  display: flex;
  flex-direction: column;
}
.scan-btn {
  display: flex;
  width: 100%;
  justify-content: center;
  padding-top: 5px;
  margin-top: 5px;
  border-top: 1px solid #ccc;
}

.opacity-panel {
	width: 100%;
	height: 100%;
	position: relative;

	.opacity-panel-close {
		width: 20px;
		height: 20px;
		position: absolute;
		right: 0px;
		top: 0px;
		padding: 0px;
		z-index: 99999;
		cursor: pointer;
	}
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:deep(.node-loading-icon) {
	animation: rotate 2s linear infinite;
}

.node-prefix-icon {
	display: flex;
  justify-content: center; /* 水平居中对齐图标 */
  align-items: center;     /* 垂直居中对齐图标 */ 

	.icon {
		width: 20px !important;
		height: 20px !important;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative !important;
		margin: 0 5px;           /* 图标之间的间距 */

		svg {
			width: 100%;
			height: 100%;
		}
	}
}