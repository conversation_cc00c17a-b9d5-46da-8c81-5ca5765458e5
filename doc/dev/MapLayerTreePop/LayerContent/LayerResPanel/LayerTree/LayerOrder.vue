<template>
	<div id="layerOrder" class="tab-recently-viewed-content">
		<div v-loading="tableLoading" element-loading-text="正在加载数据...">
			<div v-if="tableData.length" class="table-container">
				<Teleport to="#mapviewerContainer">
					<div ref="tipsRef" v-show="tipsVisible" class="tips">{{ tipsText }}</div>
				</Teleport>
				<el-table :key="timeStamp" :show-header="true" :style="{ 'max-height': '' + heightChangeStr + '' }"
					:row-class-name="tableRowClassName" :height="`calc(100vh - 270px)`" ref="tableRef"
					:cell-style="{ paddingTop: '6px', paddingBottom: '6px' }" :data="tableData"
					:row-key="(row: any) => { return row.layerid; }" :expand-row-keys="expandedRowKeys"
					@expand-change="expandOpen" style="width: 100%;">
					<el-table-column label="名称">
						<template v-slot="{ row }">
							<span>{{ row.name }}</span>
						</template>
					</el-table-column>
					<el-table-column label="" width="80">
						<template v-slot="{ row, $index }">
							<span class="table-tools" v-show="!row.type">
								{{ row.type }}
								<el-icon title="向上移" @click.stop="onSortUp(row, $index)">
									<el-icon-top />
								</el-icon>
								<el-icon title="向下移" @click.stop="onSortDown(row, $index)">
									<el-icon-bottom />
								</el-icon>
							</span>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div v-else>
				<el-empty description="暂无数据"></el-empty>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getOnemap, type IMapLayer } from "../../../../../../packages/onemapkit";
import { defineProps, onMounted, ref, nextTick, watch, onUnmounted, watchEffect } from "vue";
import Sortable from "sortablejs";
import { cloneDeep } from "lodash";

const props = defineProps({
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: false,
	},
	LayerStore: {
		type: Object,
		default: () => ({}),
		require: false,
	},
	PropsExt: {
		type: Object,
		default: () => ({}),
		require: false,
	},
	MoreOperateList: {
		type: Array,
		default: () => [],
		require: false,
	},
	Data: {
		type: Object,
		default: () => ({}),
		require: false,
	},
});

const _inOnemap = getOnemap(props.MapControlName);

const tableRef = ref();
const tipsRef = ref();

//#region 变量声明区

const tableLoading = ref(false);
const tableData = ref([] as any[]);
const tipsVisible = ref(false);
const tipsText = ref("");
const heightChangeStr = ref("");
const expandedRowKeys = ref([]);

// 重新渲染表格
const timeStamp = ref(0);

//#endregion


//#region 监听事件

watch(
	() => _inOnemap.isMapReady.value,
	(newVal: boolean) => {
		if (newVal) {
			bindEvent();
		}
	});

// 在tab页没有切换到这个组件时，tableRef.value总是为undefined
// 所以需要监听tableRef.value是否有值，一般在整个图层资源面板组件的生命周期
// 这个watchEffect只会触发一次，所以对性能没有影响
watchEffect(() => {
	if (tableRef.value) {
		let tb = tableRef.value.$el;
		if (tb) {
			tb.addEventListener("mousemove", (e: any) => {
				// tips位置
				let tips = tipsRef.value
				if (tips && tipsVisible.value) {
					tips.style.top = (e.y - 154) + "px";
					tips.style.left = (e.x - 334) + "px";

					if (e.y <= 0 && e.x <= 0) {
						tips.style.display = "none";
					} else {
						tips.style.display = "block";
					}
				}
			});

			tb.addEventListener("mouseleave", () => {
				const tips = tipsRef.value;
				if (tips && tipsVisible.value) {
					tips.style.display = "none";
				}
			});

			tb.addEventListener("mouseenter", () => {
				const tips = tipsRef.value;
				if (tips && tipsVisible.value) {
					tips.style.display = "block";
				}
			});
		}
	}
})

//#endregion


//#region 方法声明区

/**
 * 绑定图层添加/移除事件
 */
const bindEvent = () => {
	_inOnemap.ImageryLayerClass.addEventListener("update", (ImageLayerCollection: any) => {
		makeTableData(ImageLayerCollection);
	});
}

/**
 * 生成表格数据
 * @param ImageLayerCollection 图层集合
 */
const makeTableData = (ImageLayerCollection: any) => {
	const temp = props.Data.layerGroups.map((x: any) => ({
		id: x.label,
		name: x.text,
		value: ImageLayerCollection.get(x.label) || []
	}));
	
	let _layerGroups = [] as any[];
	for (let item of temp) {
		let _layers = props.LayerStore.MapLayers.filter((y: any) => y.layerType == item.id && item.value.includes(y.layerid));
		if (_layers.length > 0) {
			_layerGroups.push({
				layerid: item.id,
				name: item.name,
				type: "label"
			});
			_layers.reverse();
			for (let layer of _layers) {
				_layerGroups.push(cloneDeep(layer));
			}
		}
	}
	tableData.value = _layerGroups;

	// 绑定拖拽事件
	bindDrag();
}
const tableRowClassName = ({ row }: { row: any }) => {
	if (row && row.type && row.type == "label") {
		return "label-type";
	} else {
		return "label-layer";
	}
}

const expandOpen = () => {

}


const onSortUp = (row: any, index: number) => {
	if (index <= 0) {
		console.log("排序上移失败");
	} else {
		let temp = tableData.value[index - 1]
		if (temp && temp.type) {
			// 上层为标签，禁止移动
			console.log("排序上移失败")
			return
		}

		const upData = tableData.value[index - 1];
		if (upData.sort > tableData.value[index].sort) {
			console.log("排序上移失败");
		} else {
			//表格顺序移动
			tableData.value.splice(index - 1, 1);
			tableData.value.splice(index, 0, upData);

			//图层移动
			props.PropsExt.onSortUp(row);
		}
	}
}

const onSortDown = (row: any, index: number) => {
	console.log("排序下移", row, index)
	let temp = tableData.value[index + 1]
	if ((temp == undefined) || (temp && temp.type)) {
		// 下层为标签或者空，禁止移动
		console.log("排序上移失败")
		return
	}

	const downData = tableData.value[index + 1];
	if (downData) {
		if (downData.sort < tableData.value[index].sort) {
			console.log("排序下移失败");
		} else {
			//表格顺序移动
			tableData.value.splice(index + 1, 1);
			tableData.value.splice(index, 0, downData);

			//图层移动
			props.PropsExt.onSortDown(row);
		}
	} else {
		console.log("排序下移失败");
	}
}

let sortable: Sortable | null = null;
let beginItem: IMapLayer | any = null;  // 正在拖动的元素
let passItem: IMapLayer | any = null;  // 当前经过的元素

const bindDrag = () => {
	if (sortable) {
		sortable.destroy();
		sortable = null;
	}
	const tbody = document.querySelector("#layerOrder .el-table__body-wrapper tbody")
	if (tbody) {
		// 拖动排序
		sortable = new Sortable(tbody as HTMLElement, {
			sort: true,
			animation: 500,
			forceFallback: true,
			dragClass: ".label-layer",
			filter: ".more-operate-container .label-type",
			draggable: ".label-layer",
			onEnd: (evt: any) => {
				tipsVisible.value = false;

				nextTick(() => {
					// 如果图层类型不一样，则不进行移动	
					if (beginItem && passItem && beginItem.layerType != passItem.layerType) {
						timeStamp.value++;
						nextTick(() => {
							bindDrag();
						})
						return;
					}

					if (beginItem && passItem) {
						tableData.value.splice(evt.oldIndex, 1);
						tableData.value.splice(evt.newIndex, 0, beginItem)
						nextTick(() => {
							// 图层拖拽移动
							if (beginItem.layerid != passItem.layerid) {
								props.PropsExt.onSortDrag(beginItem, passItem)
							}
						})
					}
				})
			},
			onStart: (evt: any) => {
				tipsVisible.value = false
				tipsText.value = ""
				beginItem = tableData.value[evt.oldIndex]
				let tips = tipsRef.value
				if (tips) {
					tips.style.top = evt.originalEvent.y - 370 + "px";
					tips.style.left = evt.originalEvent.x + 5 + "px";
				}
			},
			onChange: (evt: any) => {
				// 判断是否匹配
				beginItem = tableData.value[evt.oldIndex] as IMapLayer;
				passItem = tableData.value[evt.newIndex] as IMapLayer | any;
				if (beginItem && passItem) {
					if (
						passItem.type
						|| (beginItem.layerType
							&& passItem.layerType
							&& (beginItem.layerType != passItem.layerType))) {
						tipsVisible.value = true
						tipsText.value = "无法移入其它分组"
					} else {
						tipsVisible.value = false
					}
				} else {
					tipsVisible.value = false
				}
				let tips = tipsRef.value
				if (tips) {
					// 获取当前鼠标坐标
					tips.style.top = (evt.originalEvent.y - 370) + "px"
					tips.style.left = (evt.originalEvent.x + 5) + "px"
				}
			},
		});
	}
}

//#endregion


//#region 生命周期
onMounted(() => {

});

onUnmounted(() => {
	_inOnemap.ImageryLayerClass.removeEventListener("update");
	const tb = tableRef.value;
	if (tb && tb.$el) {
		tb.$el.removeEventListener("mousemove");
		tb.$el.removeEventListener("mouseleave");
		tb.$el.removeEventListener("mouseenter");
	}
});

//#endregion


</script>

<style lang="scss" scoped>
$primary-color: #1890ff;

#layerOrder {
	user-select: none;
	padding: 10px 5px;

	.table {
		&-container {
			:deep(th) {
				padding: 6px 0;
			}
		}

		&-tools {
			float: right;
			display: flex;
			width: 65px;

			img {
				padding: 0px 4px;
				cursor: pointer;
				flex: 1;
				height: 14px;
				width: 14px;
				text-align: center;
				font-size: 18px;
			}

			i {
				cursor: pointer;
				flex: 1;
				text-align: center;
				font-size: 14px;
				padding: 0 7px;
			}
		}
	}

	/** 拖拽元素样式 begin */
	.drag-item {
		background: #000 !important;
		background-image: linear-gradient(#333, #999) !important;
		border: 1px solid #000 !important;
		border-radius: 5px;
	}


	:deep(.border-top) {
		position: relative;
	}

	:deep(.border-top td) {
		border-top: 1px solid #000 !important;
		padding-top: 20px !important;
		padding-bottom: 20px !important;
	}

	.label-layer-pre {
		position: absolute;
		left: -99999px;
	}

	:deep(.border-top .label-layer) {
		top: -15px;
		left: calc(50% + 10px);
		border-radius: 10px;
		padding: 0px 10px;
		font-weight: bold;
		background-color: #fff;
		border: 1px solid #000;
		text-align: center;
	}

	:deep(.border-top .label-layer) {
		top: -15px;
		border-radius: 10px;
		padding: 0px 10px;
		font-weight: bold;
		background-color: #fff;
		border: 1px solid #000;
		text-align: center;
		width: 140px;
		left: calc(50% - 40px);
	}

	:deep(.border-last td) {
		padding-bottom: 20px !important;
	}

	:deep(.el-table__row) {
		cursor: move;
	}

	/** 拖拽元素样式 end */

	.more-operate-container {
		height: 30px;
		width: 100%;
		font-size: 12px;
		display: flex;
		align-items: center;
		padding-left: 70px;

		.line {
			margin: 0 10px;
			text-align: center;
			color: $primary-color;
		}

		.btn-collect {
			cursor: pointer;
			color: #999;
			display: flex;
			align-items: center;

			.el-icon {
				font-size: 15px;
				margin-right: 3px;
			}

			&.is-active {
				color: #f7b500;
			}
		}

		.btn-quanxian,
		.btn-opacity {
			cursor: pointer;
			color: $primary-color;
			display: flex;
			align-items: center;
		}

		.btn-fullExtent,
		.btn-opacity {
			cursor: pointer;
			color: $primary-color;
			display: flex;
			align-items: center;
		}

		.iconfont {
			font-size: 22px;
		}
	}
}

:deep(.label-type) {
	cursor: auto !important;
	background-color: aliceblue !important;

	.el-table__cell:nth-child(1) {

		.cell {
			font-weight: bold !important;
			text-align: center !important;
		}
	}

	.el-table__cell:nth-child(2) {

		.cell {
			display: none !important;
		}
	}

	.el-table__cell:nth-child(3) {

		.cell {
			display: none !important;
		}
	}
}
</style>

<style lang="scss">
.tips {
	position: absolute;
	top: 0px;
	left: 0px;
	background-color: #fff;
	border: 1px solid #000;
	border-radius: 5px;
	padding: 2px 5px;
	text-align: center;
	white-space: nowrap;
	color: red;
	font-weight: bold;
	z-index: 99999;
}
</style>
