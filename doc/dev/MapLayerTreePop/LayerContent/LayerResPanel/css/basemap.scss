@charset "UTF-8";

.basemappanel {
    z-index: 2050;
    width: 100%;
    height: 150px;
    // overflow: auto;
    // display: flex;
}

.basemaprow1,
.basemaprow2,
.basemaprow3 {
    height: 100%;
    float: left;
    margin: 2%;
    width: 115px;
    font-family: "Microsoft YaHei";
    flex: 1;
}

.basemapbtn {
    font-size: 14px;
    border-radius: 2px;
    overflow: hidden;
    color: #333;
    cursor: pointer;
    width: 120px;
    height: 48px;
    margin: 2px 0;
    float: left;
}

.basemaprow1 .highlight {
    background: #ffa495 !important;
    color: #fff;
}

.basemaprow2 .highlight {
    background: #226cd3 !important;
    color: #fff;
}

.basemaprow3 .highlight {
    background: #81d1b5 !important;
    color: #fff;
}

.basemaprow1 .basemapbtn {
    background: #fff3e0;
}

.basemaprow2 .basemapbtn {
    background: #e3f2fd;
}

.basemaprow3 .basemapbtn {
    background: #e8f5e9;
}

.basemapbtn:hover {
    border-color: deepskyblue;
}

.maptitle {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: center;
    margin-top: 8px;
}

.resolution {
    text-align: center;
    font-size: 10px;
}

.highlightyear {
    color: red !important;
    text-decoration: underline !important;
}

.highlightTdt {
    color: #f56c6c !important;
    text-decoration: underline !important;
}

.years {
    color: #777;
    margin: 6px 0 0 6px;
    text-decoration: none;
    line-height: 18px;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
}