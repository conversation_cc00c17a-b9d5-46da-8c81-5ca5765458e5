@charset "UTF-8";

.layer-panel {
    background-color: #fcfafa;

    // &__title {
    //     background: #e1e4e6;
    //     padding: 0 10px;
    //     height: 40px;
    //     line-height: 40px;
    //     font-weight: 700;
    //     color: #535353;
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    //     cursor: pointer;
    // }


    &.fold {
        width: 200px;
        z-index: 5;

        .el-icon-transform {
            transition: 0.2s;
            transform-origin: center;
            transform: rotateZ(180deg);
        }
    }

    &__container {
        width: 100%;
        //height: calc(100vh - 325px);
    }
}

:deep(.curstom-icon) {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

:deep(.el-tabs__item) {
    height: 50px;
    line-height: 50px;
    color: #999;

    &.is-active {
        color: #1890ff;
    }

    &:hover {
        color: #1890ff;
    }
}

:deep(.el-tabs__nav-scroll) {
    // width: 248px;
    width: 355px;
    margin: 0 auto;
}

:deep(.el-tree-node__content) {
    height: auto !important;
}

:deep(label.el-checkbox) {
    margin-bottom: auto;
    margin-top: 4px;
}

:deep(.el-slider__button) {
    border-radius: 0;
    width: 10px;
}

.layer-tree-container {
    :deep(.el-tree-node__content) {
        display: flex;
        align-items: flex-start;
        padding-top: 2px;
        padding-bottom: 2px;
        //height: auto !important;

        >label.el-checkbox {
            margin-top: 6px;
            align-items: self-start;
        }
    }
}

// Cesium错误面板层级
.cesium-widget-errorPanel {
    z-index: 0;
}
