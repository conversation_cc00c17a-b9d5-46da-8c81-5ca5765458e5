<template>
	<!-- 1.基础底图 影像 -->
	<component
		v-if="_inOptions?.MapLayerTree?.basemap.visible && props.Data.BaseMapComponent && props.Data.BaseMapComponent.Control"
		:is="props.Data.BaseMapComponent.Control" :Basemap="props.Data.BaseMapComponent.Data" :PropsExt="propsExt" />

	<!-- 2.图层 -->
	<el-tabs class="layer-panel__container" v-model="(_inOptions?.MapLayerTree as any).initTabpane"
		@tab-change="tabChange">
		<!-- 2.1 图层目录-->
		<el-tab-pane v-if="_inOptions?.MapLayerTree?.tabLayers.visible" :label="_inOptions?.MapLayerTree?.tabLayers.Label"
			:name="_inOptions?.MapLayerTree?.tabLayers.name" key="tab1">
			<el-input v-model="propsExt.LayerFilterText.value" @input="searchKeyHandle" @clear="searchKeyClear" clearable
				placeholder="请输入图层名称关键字" prefix-icon="Search" style="margin-top: 1px">
			</el-input>
			<div id="maplayertreeid">
				<el-scrollbar height="100%">
					<el-tree ref="TreeRefComponent" class="treeRef" empty-text="暂无数据" show-checkbox node-key="layerid"
						@check="onMapLayerTreeCheck" @node-collapse="propsExt.nodeCollapse" @node-expand="propsExt.nodeExpand"
						:auto-expand-parent="true" :data="_Props.MapLayerTreedata" :props="propsExtClass.TreeNodeProps"
						:filter-node-method="propsExt.LayerFilterNode" :default-expanded-keys="propsExt._defaultExpandedKeys"
						:default-checked-keys="props.LayerStore.CheckedLayerids">
						<template #default="{ node, data }">
							<CustomTreeNode :tree-data="_Props.MapLayerTreedata" :node="node" :data="data" :more-operate="true"
								:Onemap="_inOnemap" @favoriteChange="favoriteChange" :props-ext="propsExt" />
						</template>
					</el-tree>
				</el-scrollbar>
			</div>
		</el-tab-pane>
		<!-- 2.4 专题地图-->
		<el-tab-pane v-if="_inOptions?.MapLayerTree?.tabSpecial.visible" :label="_inOptions?.MapLayerTree.tabSpecial.Label"
			:name="_inOptions?.MapLayerTree?.tabSpecial.name" key="tab4">
			<el-scrollbar height="100%">
				<ThematicMaps :LayerStore="props.LayerStore" :PropStore="props.PropStore" :MapControlName="props.MapControlName"
					:baseMap="_Props.basemap" :simbleBasemapVisible="_inOptions?.MapLayerTree?.basemap.visible"
					@checkoutFalseAll="checkoutFalseAll" :propsExt="propsExt" :onMapLayerTreeCheck="onMapLayerTreeCheck"
					:TreeDataList="_Props.MapLayerTreedata" />
			</el-scrollbar>
		</el-tab-pane>
		<!-- 2.2 我的收藏-->
		<el-tab-pane v-if="_inOptions?.MapLayerTree?.tabFavorites.visible && OnemapEvent.FavoritesAPI"
			:label="_inOptions?.MapLayerTree?.tabFavorites.Label" :name="_inOptions?.MapLayerTree?.tabFavorites.name"
			key="tab2">
			<div id="myfavorites">
				<el-scrollbar height="100%">
					<el-tree class="my-favorites" ref="FavRefComponent" empty-text="暂无数据" show-checkbox node-key="layerid"
						@check="onFavoritesTreeCheck" :data="propsExt.currentFavoritesTreeData.value"
						:props="propsExtClass.FavoritesTreeProps">
						<template #default="{ node, data }">
							<CustomTreeNode v-if="_inOnemap" :MapControlName="props.MapControlName" :node="node" :data="data"
								:more-operate="true" @favoriteChange="favoriteChange" :props-ext="propsExt"
								:components="props.Data.MoreOperateList" />
						</template>
					</el-tree>
				</el-scrollbar>
			</div>
		</el-tab-pane>
		<!-- 遍历组件 -->
		<el-tab-pane v-for="item in props.Data.TabComponents"
			:label="item.title" :name="item.name" :key="item.name">
			<component :is="markRaw(item.Control)" :LayerStore="props.LayerStore" :PropsExt="propsExt"
				:MapControlName="props.MapControlName" :components="props.Data.MoreOperateList" :Data="item.Data" />
		</el-tab-pane>
	</el-tabs>
</template>
<script lang="ts" setup>
import { onMounted, watch, ref, computed, nextTick, inject, defineEmits, markRaw } from "vue";
import CustomTreeNode from "./LayerTree/CustomTreeNode/CustomTreeNode.vue";
import request from "../../../../../packages/utils/axios/index";
import {
	getOnemap,
	getOption,
	mapType,
	OnemapEvent,
	defaultLayerMoreList,
	type IMapLayer,
} from "../../../../../packages/onemapkit";
import * as propsExtClass from "./LayerTreeProps";
import ThematicMaps from "./LayerTree/ThematicMaps.vue";
import { IFavoriteType } from "../Common";
import { globalPropStore } from "../index";
import { cloneDeep } from "lodash";
import { PropsClass } from "./LayerTreeProps";

// 0. 组件属性
const props = defineProps({
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: false,
	},
	LayerStore: {
		type: Object as any,
		default: null,
	},
	PropStore: {
		type: Object as any,
		default: null,
	},
	Data: {
		type: Object as any,
		default: {
			BaseMapComponent: null,
			TabComponents: [],
			MoreOperateList: [
				defaultLayerMoreList.favorites,
				defaultLayerMoreList.location,
				defaultLayerMoreList.opacity,
				defaultLayerMoreList.visibleScale
			]
		},
	},
});
let _inOnemap = getOnemap(props.MapControlName);
let _inOptions = getOption(props.MapControlName);

const emit = defineEmits(["tabChange"]);

const switchPanel = inject("switchPanel", ref(0));

const disableAll = ref(false);


const TreeRefComponent = ref();
const RTreeRefComponent = ref();
const FavRefComponent = ref();

const propsExt = new PropsClass(props.LayerStore, props.PropStore, TreeRefComponent, FavRefComponent, RTreeRefComponent);
propsExt._Onemap = _inOnemap;
propsExt._Options = _inOptions;

propsExt.currentBaseMapId.value = _inOptions.BaseMapLayer.layerid;


// 读取配置
propsExt.readLayerInfo();

//1.1 构建图层信息属性
let MapLayersNum = 0;
const _Props = computed(() => {
	console.log("执行次数：", ++MapLayersNum);
	return propsExt.MapLayers(false, props);
});

// 2. 图层树节点（Node）的数据
watch(propsExt.LayerFilterText, (val) => {
	TreeRefComponent.value!.filter(val);
});

watch(
	() => _inOnemap.isMapReady.value,
	() => {
		if (_inOnemap.isMapReady.value) {
			// RTreeRefComponent && RTreeRefComponent.value.filter(_inOnemap.MapType.value);
			props.LayerStore.MapType = _inOnemap.MapType.value;
			propsExt.currentBaseMapId.value = _inOptions.BaseMapLayer.layerid;

			if (propsExt.selectYear.value) {
				// 加载航片结合表
				propsExt.changeAPCT(propsExt.years.value.find((x: any) => x.name == propsExt.selectYear.value), true)
			}

			// 透明度处理
			if (_inOnemap.MapType.value == mapType.arcgis) {
				for (let id of _inOnemap.MapViewer.layerIds) {
					const ly = _inOnemap.MapViewer.getLayer(id);
					if (ly) {
						const op = parseFloat((propsExt.getOpacity(id)).toString()).toFixed(2);
						ly.setOpacity(parseFloat(op));
					}
				}

			} else if (_inOnemap.MapType.value == mapType.cesium) {
				for (let i = 0; i < _inOnemap.MapViewer.imageryLayers.length; ++i) {
					const ly = _inOnemap.MapViewer.imageryLayers.get(i);
					if (ly && ly.alpha && ly.layerid) {
						const op = parseFloat((propsExt.getOpacity(ly.layerid)).toString()).toFixed(2);
						ly.alpha = parseFloat(op);
					}
				}
			}
		}
	}
);

const onMapLayerTreeCheck = async (data: any) => {
	propsExt.onMapLayerTreeCheck(
		data
	);
};
const onFavoritesTreeCheck = async (data: any) => {
	propsExt.onFavoritesTreeCheck(
		TreeRefComponent.value,
		RTreeRefComponent.value,
		FavRefComponent.value,
		data
	);
	nextTick(() => {
		propsExt.asyncCheckAll(RTreeRefComponent.value);
	})
};

const checkoutFalseAll = () => {
	props.LayerStore.UploadLayerList.value = [];
	checkAllHandle(false);
};

/**
 * 获取收藏数据
 */
const getFavoritesData = async () => {
	if (OnemapEvent.FavoritesAPI && OnemapEvent.FavoritesAPI.get) {
		const favData = await OnemapEvent.FavoritesAPI.get();
		if (favData && favData.length > 0) {
			const data = favData.filter((x: any) => x.mapType == IFavoriteType.LayerService)
			for (let item of data) {
				// 更新目录树收藏状态
				const data1 = TreeRefComponent.value.getNode(item.layerid);
				if (data1 && data1.data) {
					data1.data.favId = item.favId
					data1.data.favorited = true
				}
			}

			return favData;
		}
	}

	return [];
}

// 获取航片结合表
const getAPCT = () => {
	const layer = props.LayerStore.MapLayers.find((x: IMapLayer) => x.layerTagId === propsExt.APCTTag);
	if (layer) {
		propsExt.APCTLayer = layer;

		request.get({ url: (layer.url ?? layer.subLayers[0].url) + "?f=json" }).then((res: any) => {
			if (res) {
				propsExt.years.value = res.layers;
			}
		}).catch((err: any) => {
			console.log(err)
		})
	}
}

const handleDisableNode = (tree: any) => {
	if (!tree || !tree.length) {
		return [];
	}
	return tree.map((data: any) => {
		const { children } = data;
		// 无权限的子节点禁止点击
		if (data.attributes) {
			data.disabled = !data.attributes.isAuthorized;
		}
		if (children && children.length) {
			// 有子集的当前节点禁止点击
			data.disabled = true;
			data.children = handleDisableNode(children);
		}
		return data;
	});
};

const favoriteChange = async ({ data, isFavorites }: { data: IMapLayer, isFavorites: boolean }) => {
	console.log("收藏更新：", data, isFavorites);
	const arrFavorites = await getFavoritesData();
	const favKeys = FavRefComponent.value.getCheckedKeys(true);
	console.log("favKeys: ", favKeys, arrFavorites)
	propsExt.currentFavoritesTreeData.value = arrFavorites;

	// 更新目录树收藏状态
	const data1 = TreeRefComponent.value.getNode(data.layerid);
	if (data1 && data1.data) {
		const keys = TreeRefComponent.value.getCheckedKeys(true);
		nextTick(() => {
			data1.data.favorited = isFavorites;
			nextTick(() => {
				TreeRefComponent.value.setCheckedKeys(keys, true);
			})
		})
	}

	// 最近浏览
	const data2 = RTreeRefComponent.value.getNode(data.layerid);
	if (data2 && data2.data) {
		nextTick(() => {
			data2.data.favorited = isFavorites;
		})
	}

	// 我的收藏
	nextTick(() => {
		const data3 = FavRefComponent.value.getNode(data.layerid);
		if (data3 && data3.data) {
			nextTick(() => {
				data3.data.favorited = isFavorites;
				nextTick(() => {
					FavRefComponent.value.setCheckedKeys(props.LayerStore.CheckedLayerids, true);
				})
			})
		} else {
			nextTick(() => {
				FavRefComponent.value.setCheckedKeys(props.LayerStore.CheckedLayerids, true);
			})
		}
	})
}

const checkAllHandle = (val: boolean) => {
	const data = cloneDeep(propsExt.currentBrowseMap.value);
	while (props.LayerStore.CheckedLayerids && (props.LayerStore.CheckedLayerids.pop() !== undefined));
	props.LayerStore.LedendData.value = [];	// 清空图例
	if (val) {
		try {
			disableAll.value = true;
			data.reverse();
			for (let one of data) {
				RTreeRefComponent.value.setChecked(one.layerid, true);
				propsExt.onRecentlyTreeCheck(RTreeRefComponent.value, one);
			}

			setTimeout(() => {
				disableAll.value = false;
			}, 2000)
		} catch (err: any) {
			disableAll.value = false;
		}
	} else {
		for (let one of data) {
			if (one.layerid) {
				_inOnemap.RemoveLayerById(one);
				propsExt.asyncTreeChecked(one.layerid, false);
			}
		}
	}
}

const tabChange = () => {
	switchPanel.value = new Date().getTime();
	emit("tabChange");
}

const resize = () => {
	let maplayertreeidHeight = 0;
	let myfavoritesHeight = 0;
	if (!_inOptions?.MapLayerTree?.basemap.visible) {
		maplayertreeidHeight = 147;
		myfavoritesHeight = 151;
	}

	const maplayertreeid = document.querySelector("#maplayertreeid .el-scrollbar") as any;
	const myfavorites = document.querySelector("#myfavorites .el-scrollbar") as any;
	const unfold = document.getElementById("unfold");
	if (unfold && maplayertreeid) {
		maplayertreeid.style.height = unfold.offsetHeight - (320 - maplayertreeidHeight) + "px";
	}
	if (unfold && myfavorites) {
		myfavorites.style.height = unfold.offsetHeight - (290 - myfavoritesHeight) + "px";
	}
}
propsExt.resize = resize;

/** 默认目录树展开节点 */
const defualtExpand = () => {
	for (let key of Object.getOwnPropertyNames(TreeRefComponent.value.store.nodesMap)) {
		const node = TreeRefComponent.value.store.nodesMap[key];
		if (propsExt._defaultExpandedKeys.includes(key)) {
			node.expanded = true;
		} else {
			node.expanded = false;
		}
	}
}

const searchKeyHandle = (val: string) => {
	if (val == "") {
		defualtExpand();
	}
}

const searchKeyClear = () => {
	defualtExpand();
}

/**
 * 更新在线服务图层
 * @param id 默认要勾选加载的图层id
 */
const updateService = async (id?: string) => {
	const arrFavorites = await getFavoritesData();
	propsExt.currentFavoritesTreeData.value = arrFavorites;

	// 如果有id，则勾选并加载图层
	if (id) {
		RTreeRefComponent.value.setChecked(id, true);
		const layer = propsExt.currentFavoritesTreeData.value.find((x: any) => x.id == id);
		if (layer) {
			_inOnemap.AddLayer(layer);
		}
	}
}

onMounted(async () => {
	console.log("图层目录onMounted");
	propsExt.setTreeRef(TreeRefComponent.value, RTreeRefComponent.value, FavRefComponent.value);
	if (propsExt.initEvent) propsExt.initEvent();	// 初始化图层加载

	globalPropStore.tdtConfig.labelId = propsExt.tdtIdConfig.ciaId;
	globalPropStore.tdtConfig.labelVisible = true;

	globalPropStore.tdtConfig.imageId = propsExt.tdtIdConfig.imgId;
	globalPropStore.tdtConfig.imageVisible = true;

	// 关联在线服务更新事件，以便在线服务工具添加图层时，更新我的收藏目录的图层
	// if (favoritesEvent) {
	// 	favoritesEvent.updateEvent = updateService;
	// }

	if (OnemapEvent.ChangeBaseMapAPI) {
		const newLayer = props.LayerStore.MapLayers.find((x: any) => x.layerid == propsExt.currentBaseMapId.value);
		OnemapEvent.ChangeBaseMapAPI(newLayer, null, propsExt);
	}

	getAPCT();
	updateService();
	// nextTick(() => {
	// 	const keys = RTreeRefComponent.value.getCheckedKeys() ?? [];
	// 	propsExt.isIndeterminate.value = keys.length > 0 && keys.length < propsExt.currentBrowseMap.value.length;
	// 	if (keys.length == propsExt.currentBrowseMap.value.length && propsExt.currentBrowseMap.value.length > 0) {
	// 		propsExt.checkAll.value = true;
	// 	} else {
	// 		propsExt.checkAll.value = false;
	// 	}
	// });

	nextTick(() => {
		resize();
	})
});

</script>
<style lang="scss" scoped>
@import "./css/basemap";
@import "./css/Layer";

.containertree {
	height: calc(100% - 50px);
}

.containerdiv {
	display: flex;
	flex-direction: column;
}

.childtree {
	flex: 1;
}

:deep(.el-tabs__nav-scroll) {
	width: auto !important;
	margin: 0 auto;
}

// :deep(.el-tabs__nav-wrap) {
//   margin: 0 auto;
//   height: 0px;
// }

// .el-tabs__header .is-top{
//   height: 0px;
// }
// .el-tabs__nav-wrap .is-top{
//   height: 0px;
// }
.layer-tree {
	//height: calc(100vh - 410px);
	width: 100%;
	overflow: auto;
}

:deep(.curstom-icon) {
	width: 20px !important;
	height: 20px !important;
	position: absolute;
	transform: translateY(-50%);
}

.recent-tree {
	width: 100%;
	margin-top: 12px;
	overflow: auto;
}

.recent-check {
	margin-left: 24px;
}

.recent-btn {
	position: absolute;
	z-index: 20;
	right: 10px;
	min-height: 20px;
	padding: 5px;
	margin-top: 5px;
}

:deep(.curstom-icon) {
	width: 20px !important;
	height: 20px !important;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
}

.basemapbtn {
	background: rgb(255, 243, 224);
	padding-bottom: 5px;
}

.resolution {
	text-align: center;
	font-size: 10px;
}

.highlightyear {
	color: red !important;
	text-decoration: underline !important;
}

.years {
	color: #777;
	float: left;
	margin: 6px 0 0 6px;
	text-decoration: none;
	line-height: 18px;
}

.basemapDiv {
	width: 35px;
	margin-right: 10px;
	margin-left: 20px;
}

.highlightTdt {
	color: #f56c6c !important;
	text-decoration: underline !important;
}

:deep(.node-loading) {
	.el-checkbox {
		display: none !important;
	}
}

#maplayertreeid {
	height: 100%;
	padding-top: 6px;
	box-sizing: border-box;
	position: relative;

	:deep(.el-checkbox) {
		margin-top: -3px !important;
	}

	:deep(.el-tree-node__expand-icon) {
		margin-top: -10px !important;
	}
}

#myfavorites {
	height: 100%;
	padding-top: 12px;
	box-sizing: border-box;

	:deep(.el-checkbox) {
		margin-top: 0px !important;
	}

	:deep(.node-prefix-icon) {
		margin-top: 2px !important;
	}
}

#myrecent {
	height: 100%;
	padding-top: 6px;
	box-sizing: border-box;

	:deep(.el-checkbox) {
		margin-top: 0px !important;
	}
}

:deep(.el-tabs__item) {
	padding: 0px 12px !important;
}
.layer-panel__container{
	position: relative;
    height: 100%;
    width: 415px;
}
</style>
