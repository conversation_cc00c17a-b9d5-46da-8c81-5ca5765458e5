import {
	type IMapLayer,
	OnemapEvent,
	Utils,
	BaseMapType,
	OnemapClass,
	mapType,
	ILayerType,
} from "../../../../../packages/onemapkit";
import { ref, nextTick, computed, type Ref } from "vue";
import { cloneDeep } from "lodash";
import { globalPropStore } from "../index";

/**
 * 构建图层信息属性
 * @param {boolean} [islayertree]  ture:获取笼统的basemap；false:按BaseMapType分类获取Aerial、Satellite、Vector
 * @param propStore  包含有LayerStore的Props属性
 * @returns {object} { basemap:[],MapLayerTreedata:[] } | {basemap:{Aerial:[],Satellite[],Vector[] },MapLayerTreedata:[] }
 */
export function MapLayers(
	islayertree: boolean,
	_LayerStore: any,
	_mapType: mapType
): any {
	if (islayertree) {
		return {
			basemap: Utils.getBaseMapLayers(_mapType, _LayerStore.MapLayers),
			MapLayerTreedata: Utils.createNewTreeData(_LayerStore.Layers, (node) => {
				return (
					Utils.MapLayerFilter.MapLayers(node, true) &&
					Utils.isUseMapModel(node, _mapType)
				);
			}),
			// MapLayerTreedata: Utils.getLayerTreeData(_mapType, _LayerStore.MapLayers),
		};
	} else {
		let tmpbaselayers: Array<IMapLayer> = Utils.getBaseMapLayers(
			_mapType,
			_LayerStore.MapLayers, true
		);
		return {
			basemap: {
				Aerial: tmpbaselayers.filter(
					(node) => node.baseLayerType === BaseMapType.aerial && node.showInCatalog
				),
				Satellite: tmpbaselayers.filter(
					(node) => node.baseLayerType === BaseMapType.satellite && node.showInCatalog
				),
				Vector: tmpbaselayers.filter(
					(node) => node.baseLayerType === BaseMapType.vector && node.showInCatalog
				),
			},
			MapLayerTreedata: Utils.createNewTreeData(_LayerStore.Layers, (node) => {
				return (
					Utils.MapLayerFilter.MapLayers(node, true) &&
					Utils.isUseMapModel(node, _mapType)
				);
			}),
			// MapLayerTreedata: Utils.getLayerTreeData(_mapType, _LayerStore.Layers),
		};
	}
}

// const urlObj:any = inject('urlObj')
export function setCheckedLayerid(
	layerStore: any,
	checked: boolean,
	data?: IMapLayer | any,
	//@ts-ignore
	handle?: any
) {
	//1.MapLayer
	let tmp = Utils.getMapLayerByLayerid(
		data.layerid as any,
		layerStore.MapLayers
	);

	if (tmp == undefined) return false;
	tmp.visible = checked;
	//2.CheckedLayerids
	const idx = layerStore.CheckedLayerids.indexOf(tmp?.layerid as any);
	if (idx > -1) {
		layerStore.CheckedLayerids.splice(idx, 1);
	}
	if (checked) {
		layerStore.CheckedLayerids.unshift(tmp?.layerid);
	}
}
export const TreeNodeProps = {
	key: "layerid",
	label: "name",
	children: "children",
	disabled: (data: any) => {
		return data?.children ? true : false;
	},
	isLeaf: (data: any) => {
		return data?.children ? false : true;
	},
	class: (data: any, _node: any): { [key: string]: boolean } | string => {
		if (data.loading) {
			return { 'node-loading': true };
		}
		return '';
	}
};
export const RecentlytreeProps = {
	key: "layerid",
	label: "name",
	children: "children",
	class: (data: any, _node: any): string => {
		return data.loading ? "node-loading" : "";
	}
};

export const FavoritesTreeProps = {
	key: "layerid",
	label: "name",
	children: "children",
	class: (data: any, _node: any): string => {
		return data.loading ? 'node-loading' : '';
	}
};

/**
 *
 * *********************************************************************************************
 *
 *
 */

/** 图层状态类型，包含图层ID，图层加载状态，图层勾选状态 */
export class PropsClass {
	public _Onemap: OnemapClass = undefined as any;
	public _Options: any = undefined as any;
	public LayerStore: any = undefined as any;
	public PropStore: any = undefined as any;
	public layerRef: any;	// 图层目录ref
	public favRef: any;	// 收藏ref
	public recentRef: any;	// 最近浏览ref
	constructor(_LayerStore?: any, _PropStore?: any, _layerRef?: any, _favRef?: any, _recentRef?: any) {
		this.LayerStore = _LayerStore;
		this.PropStore = _PropStore;
		this.layerRef = _layerRef;
		this.favRef = _favRef;
		this.recentRef = _recentRef;
	}

	public currentBaseMapId: any = ref();
	public _defaultExpandedKeys: Array<string> = [];
	public initEvent: Function | null = null; // 初始化图层加载，在instance.ts绑定

	public layerInfo: any = new Map();
	/** 获取透明度设置 */
	public getOpacity = (layerid: any): number => {
		const info = this.layerInfo.get(layerid);
		if (info) {
			return info.opacity;
		} else return 1;
	}

	/** 获取可见比例设置 */
	public getScale = (layerid: any): any => {
		const info = this.layerInfo.get(layerid);
		if (info) {
			return info.scale;
		} else return null;
	}

	/** 写入图层信息到缓存 */
	public saveLayerInfo = () => {
		// 保存到layerInfo到sessionId
		const layerInfo = Array.from(this.layerInfo);
		sessionStorage.setItem("layerInfo", JSON.stringify(layerInfo));
	}

	/** 读取图层信息 */
	public readLayerInfo = () => {
		// 读取layerInfo
		const layerInfo = sessionStorage.getItem("layerInfo");
		if (layerInfo) {
			const info = new Map(JSON.parse(layerInfo));
			this.layerInfo = info;
		}
	}
	_TreeRefComponent: any = null;
	_RTreeRefComponent: any = null;
	_FavRefComponent: any = null;

	public setTreeRef = (__TreeRefComponent: any, __RTreeRefComponent: any, __FavRefComponent: any) => {
		__TreeRefComponent && (this._TreeRefComponent = __TreeRefComponent);
		__RTreeRefComponent && (this._RTreeRefComponent = __RTreeRefComponent);
		__FavRefComponent && (this._FavRefComponent = __FavRefComponent);
	}

	/** 同步勾选按钮 */
	public asyncTreeChecked = (layerid: string, checked: boolean) => {
		nextTick(() => {
			this._TreeRefComponent && this._TreeRefComponent.setChecked(layerid, checked);
			this._RTreeRefComponent && this._RTreeRefComponent.setChecked(layerid, checked);
			this._FavRefComponent && this._FavRefComponent.setChecked(layerid, checked);

			nextTick(() => {
				if (this._TreeRefComponent.filterNodeMethod) {
					this._TreeRefComponent.filter(this.LayerFilterText.value)
				}
			})
		})
	}

	/** 同步全选按钮 */
	public asyncCheckAll = (RTreeRefComponent: any) => {
		nextTick(() => {
			// 同步是否全选状态
			if (!RTreeRefComponent) return;

			let hasLayer = 0;
			const checkKeys = RTreeRefComponent.getCheckedKeys();
			this.currentBrowseMap.value.forEach((x: any) => {
				if (checkKeys.includes(x.layerid)) {
					++hasLayer;
				}
			})
			if (hasLayer === 0) {
				this.checkAll.value = false;
				this.isIndeterminate.value = false;
			} else if (hasLayer < checkKeys.length) {
				this.checkAll.value = false;
				this.isIndeterminate.value = true;
			} else if (hasLayer === checkKeys.length) {
				this.checkAll.value = true;
				this.isIndeterminate.value = false;
			} else {
				this.checkAll.value = false;
				this.isIndeterminate.value = false;
			}
		})
	}

	//#region  1. 基础底图模块

	// 当前底图类型为航片的时候，航片结合表才显示
	public currentBaseType = ref(BaseMapType.aerial)
	/**
	 * 构建图层信息属性
	 * @param {boolean} [islayertree]  ture:获取笼统的basemap；false:按BaseMapType分类获取Aerial、Satellite、Vector
	 * @param propStore  包含有LayerStore的Props属性
	 * @returns {object} { basemap:[],MapLayerTreedata:[] } | {basemap:{Aerial:[],Satellite[],Vector[] },MapLayerTreedata:[] }
	 */
	public MapLayers(islayertree: boolean, propStore: any) {
		if (islayertree) {
			return {
				basemap: Utils.getBaseMapLayers(
					this._Onemap.MapType.value,
					propStore.LayerStore.MapLayers
				),
				MapLayerTreedata: Utils.createNewTreeData(
					propStore.LayerStore.Layers,
					(node) => {
						return (
							Utils.MapLayerFilter.MapLayers(node, true) &&
							Utils.isUseMapModel(node, this._Onemap.MapType.value)
						);
					}
				),
				//MapLayerTreedata: Utils.getLayerTreeData(this._Onemap.MapType.value,propStore.LayerStore.Layers),
			};
		} else {
			let tmpbaselayers: Array<IMapLayer> = Utils.getBaseMapLayers(
				this._Onemap.MapType.value,
				propStore.LayerStore.MapLayers
			);
			return {
				basemap: {
					Aerial: tmpbaselayers.filter(
						(node) => node.baseLayerType === BaseMapType.aerial && node.showInCatalog
					),
					Satellite: tmpbaselayers.filter(
						(node) => node.baseLayerType === BaseMapType.satellite && node.showInCatalog
					),
					Vector: tmpbaselayers.filter(
						(node) => node.baseLayerType === BaseMapType.vector && node.showInCatalog
					),
				},
				MapLayerTreedata: Utils.createNewTreeData(
					propStore.LayerStore.Layers,
					(node) => {
						return (
							Utils.MapLayerFilter.MapLayers(node, true) &&
							Utils.isUseMapModel(node, this._Onemap.MapType.value)
						);
					}
				),
				//MapLayerTreedata: Utils.getLayerTreeData(this._Onemap.MapType.value,propStore.LayerStore.Layers),
			};
		}
	}
	//1.2 底图切换方法
	public onChangeBaseMap(basemap: IMapLayer) {
		console.log("底图切换onChangeBaseMap")
		if (!basemap) return;
		this.currentBaseType.value = basemap.baseLayerType ?? BaseMapType.noBaseMap;
		// if (basemap.layerid == this._Options.BaseMapLayer.layerid) return;
		//1.删除旧的底图
		let oldLayer = cloneDeep(this._Options.BaseMapLayer);
		this._Onemap.RemoveLayerById(oldLayer);

		//2.添加新底图
		let newLayer = cloneDeep(basemap);

		// 设置新底图的天地图状态
		if (newLayer && newLayer.subLayers && newLayer.subLayers.length > 0) {
			// 影像
			let tdt_img = newLayer.subLayers.findIndex((x: any) => x.layerid == this.tdtIdConfig.imgId)
			if (tdt_img == -1) {
				tdt_img = newLayer.subLayers.findIndex((x: any) => x.layerid == this.tdtIdConfig.vecId);
			}

			if (!globalPropStore.tdtConfig.imageVisible && (tdt_img >= 0)) {
				newLayer.subLayers.splice(tdt_img, 1);
			}

			// 注记
			let tdt_cia = newLayer.subLayers.findIndex((x: any) => x.layerid == globalPropStore.tdtConfig.labelId);
			if (!globalPropStore.tdtConfig.labelVisible && (tdt_cia >= 0)) {
				newLayer.subLayers.splice(tdt_cia, 1);
			}
		}

		this._Options.BaseMapLayer = newLayer;
		this.currentBaseMapId.value = newLayer.layerid;

		if (OnemapEvent.ChangeBaseMapAPI) {
			OnemapEvent.ChangeBaseMapAPI(newLayer, oldLayer, this);
		}

		const result = this._Onemap.AddLayer(newLayer);
		result.then((layers: any) => {
			layers.reverse();
			for(let layer of layers){
				this._Onemap.setBottomIndex(layer.option);
			}

			// 如果底图类型是航片，且航片已经打开，自动根据航飞影像年份，切换到对应航片
			if (this.currentBaseType.value == BaseMapType.aerial && this.selectYear.value) {
				const one = this.years.value.find((item: any) => item.name == basemap.options.year);
				if (one) this.changeAPCT(one);
			} else {
				// 如果切换到非航片类型，清理航片
				this.selectYear.value = "";
				this.changeAPCT(null);
			}
		});
	}
	public UploadLayerList = ref<Array<IMapLayer>>([]);

	public setCheckedLayerid(
		checked: boolean,
		data?: IMapLayer | any,
		//@ts-ignore
		handle?: any
	) {
		if (!data) return;
		if (!data.layerid) return;

		//1.MapLayer
		let tmp = Utils.getMapLayerByLayerid(
			data.layerid as any,
			this.LayerStore.MapLayers
		);
		if (!tmp) {
			tmp = this.currentFavoritesTreeData.value.find((x: IMapLayer) => x.layerid == data.layerid);
		}

		if (tmp == undefined) return false;
		tmp.visible = checked;
		//2.CheckedLayerids
		const idx = this.LayerStore.CheckedLayerids.indexOf(
			tmp?.layerid as any
		);
		if (idx > -1) {
			this.LayerStore.CheckedLayerids.splice(idx, 1);
		}
		if (checked) {
			this.LayerStore.CheckedLayerids.unshift(tmp?.layerid);
		}
	}

	//#region 2. 图层树 TreeRefComponent ****************************************

	// 天地图id配置
	public tdtIdConfig: any = {
		imgId: "tdt_img",	// 天地图影像图
		vecId: "tdt_vec",	// 天地图电子地图矢量图
		ciaId: "tdt_cia",	// 天地图影像注记
		demId: "tdt_dem",	// 地形晕渲
		ctaId: "tdt_cta",	// 天地图地形注记
	};

	public APCTTag = "APCT";	// 航片结合表标识码

	// 最近浏览排序配置，倒序，最上面的在最底下
	public layerOrderConst = [
		ILayerType.ModelLayer,
		ILayerType.ImageryLayer,
		ILayerType.PolygonLayer,
		ILayerType.PolylineLayer,
		ILayerType.PointLayer,
		ILayerType.LabelLayer,
		ILayerType.TempLayer
	]

	/** 图层树过滤关键词 =>watch*/
	public LayerFilterText = ref("");
	/** 2.3 目录树过滤显示 */
	public LayerFilterNode(value: string, data: any): boolean {
		if (!value) return true;
		return data.name.includes(value);
	}
	//#region 2.4 目录树状态:节点是否展开  _defaultExpandedKeys
	//@ts-ignore
	public nodeCollapse = (data: any, node: any, nodeGroup: any) => {
		this._defaultExpandedKeys.splice(
			this._defaultExpandedKeys.indexOf(node.key),
			1
		);
	};
	//@ts-ignore
	public nodeExpand = (data: any, node: any, nodeGroup: any) => {
		this._defaultExpandedKeys.push(node.key);
	};
	//#endregion
	// 设置树节点勾选状态
	public setTreeChecked = (isChecked: boolean, data: any) => {
		this.treeNodehandle(isChecked, data)
	}

	/** 2.5 图层树选择事件 */
	public async onMapLayerTreeCheck(data: any) {
		const isChecked = this._TreeRefComponent.getCheckedKeys(true).includes(data.layerid);
		this.treeNodehandle(isChecked, data)
		
	}

	//处理树节点勾选事件
	public treeNodehandle = (isChecked: boolean, data: any) => {
		// 更新图层属性
		this.setCheckedLayerid(isChecked, data, "T");

		const treeNode = this._TreeRefComponent.getNode(data.layerid);
		const rtreeNode = this._RTreeRefComponent ? this._RTreeRefComponent.getNode(data.layerid) : null;
		const favNode = this._FavRefComponent && this._FavRefComponent.getNode(data.layerid);
		if (isChecked) {
			if (treeNode) {
				treeNode.data.isLeaf = true;
			}

			if (rtreeNode) {
				rtreeNode.data.isLeaf = true;
			}

			if (favNode) {
				favNode.data.isLeaf = true;
			}

			// 禁止用户对单个图层在短时间内快速的勾选取消
			setTimeout(() => {
				if (treeNode) {
					treeNode.data.isLeaf = false;
				}

				if (rtreeNode) {
					rtreeNode.data.isLeaf = false;
				}

			if (favNode) {
					favNode.data.isLeaf = false;
				}
			}, 2000)
		}

		const loadLayer = (result: any) => {
			result.then((layers: any) => {
				layers.forEach((x: any) => {
					const { layerid, maplayer, option } = x;
					if (maplayer && maplayer.layerid) {
						let op = "";
						if (this.layerInfo.get(layerid)) {
							op = parseFloat(this.getOpacity(layerid).toString()).toFixed(2);
						} else {
							op = option.options.customProperty.opacity;
						}
						this._Onemap.setLayerOpacity(option, parseFloat(op))
					}
				})
			}).catch(() => {
				this.setCheckedLayerid(false, data, "T");
				this.asyncTreeChecked(data.layerid, false);
			})
		}

		this.asyncTreeChecked(data.layerid, isChecked);
		if (isChecked) {
			this.addRecentLayer(data);

			let result: any;
			if (OnemapEvent.LayerCheckServiceAPI) {
				//应用端的解析接口
				OnemapEvent.LayerCheckServiceAPI(true, data).then((res: IMapLayer) => {
					let _layer = cloneDeep(res);
					if (res.options.customProperty) {
						_layer = this.LayerStore.MapLayers.find((x: any) => x.layerid == res.layerid);
						
						// 在更新属性前保存勾选状态
						const currentCheckedKeys = this._TreeRefComponent.getCheckedKeys();
						Utils.EditMapLayer(_layer.options.customProperty, res.options.customProperty);
						
						// 更新后恢复勾选状态
						nextTick(() => {
							this._TreeRefComponent.setCheckedKeys(currentCheckedKeys);
							if (this._TreeRefComponent.filterNodeMethod) {
								this._TreeRefComponent.filter(this.LayerFilterText.value)
							}
						})
					}
					
					_layer.layerOrder = 1;
					if (_layer.subLayers && _layer.subLayers.length > 0) {
						_layer.subLayers[0].layerOrder = 1;
					}
					result = this._Onemap.AddLayer(_layer);
					if (this._Onemap.MapType.value === mapType.cesium) {
						loadLayer(result);
					}
				}).catch(() => {
					this.asyncTreeChecked(data.layerid, false);
				});
			} else {
				const temp = cloneDeep(data);
				temp.layerOrder = 1;
				if (temp.subLayers && temp.subLayers.length > 0) {
					temp.subLayers[0].layerOrder = 1;
				}
				result = this._Onemap.AddLayer(temp);
				if (this._Onemap.MapType.value === mapType.cesium) {
					loadLayer(result);
				}
			}
		} else {
			this._Onemap.RemoveLayerById(data);
		}

		nextTick(() => {
			this.asyncCheckAll(this._RTreeRefComponent);
		})
	}

	public checkAllHandle = (data: any) => {
		console.log(data);
	}

	//#endregion


	//#region 我的收藏
	// 收藏图层列表
	public currentFavoritesTreeData: Ref<Array<IMapLayer>> = ref([]);

	/** 更新收藏目录树勾选状态 */
	public updateFavoritesNode(
		FavRefComponent: any,
		data: any,
		isChecked: boolean
	) {
		if (FavRefComponent) {
			const one = FavRefComponent.data?.find((x: IMapLayer) => x.layerid == data.layerid);
			if (one) {
				FavRefComponent.setChecked(data.layerid, isChecked);
			}
		}
	}
	//#endregion

	//#region 3. 最近浏览  RTreeRefComponent ****************************************
	/** 3.1 图层树节点（Node）的数据类型 */
	// public RecentlytreeProps = ref({
	//   key: "layerid",
	//   label: "name",
	//   children: "children",
	// });
	// 3.2 全选状态事件
	public checkAll = ref(false);
	public isIndeterminate = ref(false);
	public resize?: Function;
	/** 3.3 存放最近20个开启的服务 */
	public currentBrowseMap = ref<Array<IMapLayer>>([]);

	/** 最近浏览过滤二三维显示 */
	public LayerRecentFilterNode(value: string, data: IMapLayer): boolean {
		if (!value) return true;
		return data.name?.toLowerCase().includes(value.toLowerCase()) ?? false;
	}

	// 3.4 最近浏览目录树点击事件
	public onRecentlyTreeCheck = (
		RTreeRefComponent: any,
		data: any
	) => {
		const isChecked = RTreeRefComponent.getCheckedKeys(true).includes(
			data.layerid
		);
		console.log("isChecked: ", isChecked);
		this.setCheckedLayerid(isChecked, data, "T");
		if (isChecked) {
			const rtreeNode = RTreeRefComponent.getNode(data.layerid);
			if (rtreeNode) {
				rtreeNode.data.isLeaf = true;
			}

			const treeNode = this._TreeRefComponent.getNode(data.layerid);
			if (treeNode) {
				treeNode.data.isLeaf = true;
			}

			const favNode = this._FavRefComponent && this._FavRefComponent.getNode(data.layerid);
			if (favNode) {
				favNode.data.isLeaf = true;
			}

			nextTick(() => {
				RTreeRefComponent.setCheckedKeys(this.LayerStore.CheckedLayerids);
				setTimeout(() => {
					const treeNode = this._TreeRefComponent.getNode(data.layerid);
					if (treeNode) {
						treeNode.data.isLeaf = false;
					}

					const rtreeNode = RTreeRefComponent.getNode(data.layerid);
					if (rtreeNode) {
						rtreeNode.data.isLeaf = false;
					}

					const favNode = this._FavRefComponent && this._FavRefComponent.getNode(data.layerid);
					if (favNode) {
						favNode.data.isLeaf = false;
					}
				}, 2000)
			})
		}

		const loadLayer = (result: any) => {
			if (result && (result instanceof Promise)) {
				result.then((resultLayer: any) => {
					if (resultLayer && resultLayer.loaded) {
						if (resultLayer.setOpacity) {
							const op = parseFloat(this.getOpacity(resultLayer.layerid).toString()).toFixed(2);
							resultLayer.setOpacity(parseFloat(op));
						}
					}
				}).catch(() => {
					this.asyncTreeChecked(data.layerid, false);
				})
			} else {
				if (result && result.alpha && result.layerid) {
					const op = parseFloat(this.getOpacity(result.layerid).toString()).toFixed(2);
					result.alpha = parseFloat(op);
				}
			}
		}

		this.asyncTreeChecked(data.layerid, isChecked);
		if (isChecked) {
			let result: any;
			if (OnemapEvent.LayerCheckServiceAPI) {
				//应用端的解析接口
				OnemapEvent.LayerCheckServiceAPI(true, data).then((res: IMapLayer) => {
					const ly = this.LayerStore.MapLayers.find((x: any) => x.layerid == res.layerid);
					if (res.options.customProperty) {
						ly.options.customProperty = res.options.customProperty;
					}
					const temp = cloneDeep(res);
					temp.layerOrder = 1;
					if (temp.subLayers && temp.subLayers.length > 0) {
						temp.subLayers[0].layerOrder = 1;
					}
					result = this._Onemap.AddLayer(temp);
					loadLayer(result);
				});
			} else {
				const temp = cloneDeep(data);
				temp.layerOrder = 1;
				if (temp.subLayers && temp.subLayers.length > 0) {
					temp.subLayers[0].layerOrder = 1;
				}
				result = this._Onemap.AddLayer(temp);
				loadLayer(result);
			}
		} else {
			this._Onemap.RemoveLayerById(data);
			nextTick(() => {
				this.asyncCheckAll(RTreeRefComponent);
			})
		}
	};

	public addRecentLayer(item: IMapLayer) {
		// 如果最近浏览中没有该图层，则添加
		// 获取该图层类型是否存在最近浏览中
		let layer = this.LayerStore.MapLayers.find((x: any) => x.layerid == item.layerid);
		if (layer && !this.currentBrowseMap.value.find((x: any) => x.layerid == item.layerid)) {
			let layerTypeData = this.currentBrowseMap.value.filter((x: any) => x.layerType == layer.layerType);
			if (layerTypeData.length > 0) {
				// 获取该类型顶部的数组索引
				let idx = this.currentBrowseMap.value.findIndex((x: any) => x.layerid == layerTypeData[0].layerid);
				//插入到顶部位置
				this.currentBrowseMap.value.splice(idx, 0, layer);
			} else {
				// 如果最近浏览为空，则直接添加
				if (this.currentBrowseMap.value.length == 0) {
					this.currentBrowseMap.value.push(layer);
				} else {
					// 获取该类型的上一个类型
					let isInsert = false;
					const idxType = this.layerOrderConst.findIndex((x: string) => x == layer.layerType);
					if (idxType < 0) return; // 未找到类型

					for (let i = idxType - 1; i >= 0; i--) {
						const lastTypeData = this.currentBrowseMap.value.filter((x: any) => x.layerType == this.layerOrderConst[i]);
						if (lastTypeData && lastTypeData.length > 0
							&& !this.currentBrowseMap.value.find((x: any) => x.layerid == item.layerid)) {
							// 获取该类型顶部的数组索引
							let idx = this.currentBrowseMap.value.findIndex((x: any) => x.layerid == lastTypeData[0].layerid);
							//插入到顶部位置
							this.currentBrowseMap.value.splice(idx, 0, layer);
							isInsert = true;
							break;
						}
					}

					if (!isInsert) {
						// 判断是否存在三维类型
						const _3dModelData = this.currentBrowseMap.value.filter((x: any) => x.layerType == ILayerType.ModelLayer);
						if (_3dModelData && _3dModelData.length > 0) {
							// 获取该类型顶部的数组索引
							let idx = this.currentBrowseMap.value.findIndex((x: any) => x.layerid == _3dModelData[0].layerid);
							//插入到顶部位置
							this.currentBrowseMap.value.splice(idx, 0, layer);
						} else {
							// 如果没有找到，则插入到最后
							if (!this.currentBrowseMap.value.find((x: any) => x.layerid == item.layerid)) {
								this.currentBrowseMap.value.push(layer);
							}
						}
					}
				}
			}
		}
	};

	/** 收藏目录树选择事件 */
	public onFavoritesTreeCheck(
		TreeRefComponent: any,
		RTreeRefComponent: any,
		FavRefComponent: any,
		data: any
	) {
		const isChecked = FavRefComponent.getCheckedKeys(true).includes(
			data.layerid
		);

		const treeNode = TreeRefComponent.getNode(data.layerid);
		const rtreeNode = RTreeRefComponent.getNode(data.layerid);
		const favNode = FavRefComponent && FavRefComponent.getNode(data.layerid);

		if (isChecked) {
			if (treeNode) {
				treeNode.data.isLeaf = true;
			}

			if (rtreeNode) {
				rtreeNode.data.isLeaf = true;
			}

			if (favNode) {
				favNode.data.isLeaf = true;
			}

			// 禁止用户对单个图层在短时间内快速的勾选取消
			setTimeout(() => {
				if (treeNode) {
					treeNode.data.isLeaf = false;
				}

				if (rtreeNode) {
					rtreeNode.data.isLeaf = false;
				}

				if (favNode) {
					favNode.data.isLeaf = false;
				}
			}, 2000)
		}

		this.setCheckedLayerid(isChecked, data, "T");
		const loadLayer = (result: any) => {
			if (result && (result instanceof Promise)) {
				result.then((resultLayer: any) => {
					if (resultLayer && resultLayer.loaded) {
						if (resultLayer.setOpacity) {
							const op = parseFloat(this.getOpacity(resultLayer.layerid).toString()).toFixed(2);
							resultLayer.setOpacity(parseFloat(op));
						}
					}
				}).catch(() => {
					this.setCheckedLayerid(false, data, "T");
					this.asyncTreeChecked(data.layerid, false);
				})
			} else {
				if (result && result.alpha && result.layerid) {
					const op = parseFloat(this.getOpacity(result.layerid).toString()).toFixed(2);
					result.alpha = parseFloat(op);
				}
			}
		}

		this.asyncTreeChecked(data.layerid, isChecked);
		if (isChecked) {
			this.addRecentLayer(data);

			let result: any;
			if (OnemapEvent.LayerCheckServiceAPI) {
				//应用端的解析接口
				OnemapEvent.LayerCheckServiceAPI(true, data).then((res: IMapLayer) => {
					if (res.options.customProperty) {
						treeNode && (treeNode.data.options.customProperty = res.options.customProperty);
						rtreeNode && (rtreeNode.data.options.customProperty = res.options.customProperty);
						favNode && (favNode.data.options.customProperty = res.options.customProperty);
						nextTick(() => {
							if (this._TreeRefComponent.filterNodeMethod) {
								this._TreeRefComponent.filter(this.LayerFilterText.value)
							}
						})
					}
					const temp = cloneDeep(res);
					temp.layerOrder = 1;
					if (temp.subLayers && temp.subLayers.length > 0) {
						temp.subLayers[0].layerOrder = 1;
					}
					result = this._Onemap.AddLayer(temp);
					if (this._Onemap.MapType.value === mapType.cesium) {
						loadLayer(result);
					}
				}).catch(() => {
					this.asyncTreeChecked(data.layerid, false);
				});
			} else {
				const temp = cloneDeep(data);
				temp.layerOrder = 1;
				if (temp.subLayers && temp.subLayers.length > 0) {
					temp.subLayers[0].layerOrder = 1;
				}
				result = this._Onemap.AddLayer(temp);
				if (this._Onemap.MapType.value === mapType.cesium) {
					loadLayer(result);
				}
			}
		} else {
			this._Onemap.RemoveLayerById(data);
		}
	}

	// 我的收藏服务列表
	public myFavoritesTreeData = ref<Array<IMapLayer>>([])
	public myFavoritesTreeDataFilter = computed((): any => {
		return this.myFavoritesTreeData.value.filter(
			(item: any) =>
				item.useMapModel === undefined ||
				item.useMapModel?.some(
					(item: any) => item == this._Onemap.MapType.value
				)
		);
	});

	/** 移除图层
	 * @param isClear
	 */
	//@ts-ignore
	public onRemove(
		data: any,
		TreeRefComponent: any,
		FavRefComponent: any,
	) {
		this._Onemap.RemoveLayerById(data);
		nextTick(() => {
			if (TreeRefComponent && TreeRefComponent.setCheckedKeys) TreeRefComponent.setChecked(data.layerid, false);
			if (FavRefComponent && FavRefComponent.setCheckedKeys) FavRefComponent.setChecked(data.layerid, false);
		})
		let index = this.currentBrowseMap.value.findIndex((x: any) => x.layerid == data.layerid)
		if (index !== -1) {
			this.currentBrowseMap.value.splice(index, 1)
		}
	}

	/** 移除全部图层
	 * @param isClear
	 */
	//@ts-ignore
	public onRemoveAll() {
		for (let ly of this.currentBrowseMap.value) {
			this._Onemap.RemoveLayerById(ly);
		}
		this.currentBrowseMap.value = [];
	}

	/**
	 * 处理图层上移操作
	 * @param node 
	 */
	public onSortUp = (node: IMapLayer) => {
		this._Onemap.moveUpIndex(node);
	};

	/**
	 * 处理图层下移操作
	 * @param node 
	 */
	public onSortDown = (node: any) => {
		this._Onemap.moveDownIndex(node);
	};

	public onSortDrag = (sourceNode: any, endNode: any) => {
		console.log("排序", sourceNode, endNode);
		this._Onemap.moveDragIndex(sourceNode, endNode);
	}

	//#endregion

	//#region 天地图和航片结合表
	// 选择的年份
	public selectYear = ref("");

	// 航片结合表年份
	public years = ref([]);

	public APCTLayer: any = null;

	/**
	 * 控制天地图影像/矢量底图显隐
	 */
	public changeBasemap(isShow?: boolean) {
		console.log("控制天地图影像/矢量底图显隐")
		if (isShow !== undefined) {
			globalPropStore.tdtConfig.imageVisible = isShow;
		} else {
			globalPropStore.tdtConfig.imageVisible = !globalPropStore.tdtConfig.imageVisible;
		}

		const imageLayer = this._Onemap.GetLayerByID(globalPropStore.tdtConfig.imageId);
		if (imageLayer) {
			this._Onemap.setLayerVisible(globalPropStore.tdtConfig.imageId, globalPropStore.tdtConfig.imageVisible);
		}
	}

	/**
	 * 控制天地图标注底图显隐
	 */
	public changeLabel = (isShow?: boolean) => {
		console.log("控制天地图标注底图显隐")
		if (isShow !== undefined) {
			globalPropStore.tdtConfig.labelVisible = isShow;
		} else {
			globalPropStore.tdtConfig.labelVisible = !globalPropStore.tdtConfig.labelVisible;
		}
		const labelLayer = this._Onemap.GetLayerByID(globalPropStore.tdtConfig.labelId);
		if (labelLayer) {
			this._Onemap.setLayerVisible(globalPropStore.tdtConfig.labelId, globalPropStore.tdtConfig.labelVisible);
		}
	}

	/**
	 * 控制航片结合表是否显示
	 */
	public changeAPCT(item?: any, _isInit = false) {
		if (item == null && this.APCTLayer) {
			// 隐藏
			this.selectYear.value = "";
			this._Onemap.RemoveLayerById(this.APCTLayer)
		} else if (item) {
			// 显示子图层
			this.selectYear.value = item.name;

			if (this._Onemap.MapType.value == mapType.cesium) {
				this._Onemap.RemoveLayerById(this.APCTLayer)
				const ly = cloneDeep(this.APCTLayer);
				// ly.subLayers[0].options = { mapoption: { layers: `${item.id}` } }
				ly.subLayers[0].url = ly.options.url + `?layers=show%3A${item.id}`
				this._Onemap.AddLayer(ly);
			} else if (this._Onemap.MapType.value == mapType.arcgis) {
				!_isInit && this._Onemap.RemoveLayerById(this.APCTLayer)
				const ly = cloneDeep(this.APCTLayer);
				this._Onemap.AddLayer(ly);
				// prom.then((ly: any) => {
				// 	console.log(ly, item);
				// 	if (ly && ly.setVisibleLayers) {
				// 		ly.setVisibleLayers([item.id])
				// 	}
				// 	console.log("控制显示2", ly)
				// }).catch((e: any) => {
				// 	console.log("图层加载出错：", e)
				// })
			}
		}
	}
	//endregion
}
