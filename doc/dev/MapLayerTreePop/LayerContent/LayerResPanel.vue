<template>
	<div id="unfold" :class="['layer-res-panel', props.PropStore.foldLayerResPanel.value ? 'fold' : 'unfold-n']"
		:style="'height: calc(100% -200px);'">
		<div class="layer-res-panel__title" @click="setContentVisible">
			<div style="position: relative">
				<span v-html="Icons.ResCatalogIcon"></span><span style="margin-left: 35px;">图层资源</span>
			</div>
			<el-icon class="el-icon-transform" :size="20">
				<CaretTop />
			</el-icon>
		</div>
		<el-tabs v-show="!props.PropStore.foldLayerResPanel.value" v-model="selectTabName">
			<el-tab-pane v-for="tab in props.TabComponents" :label="tab.title" :name="tab.name">
				<component :is="tab.Control" :MapControlName="props.MapControlName" :LayerStore="props.LayerStore"
					:PropStore="props.PropStore" :Data="tab.Data" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script lang="ts" setup>
import { defineProps, ref, onMounted, watch } from "vue";
import { CaretTop } from "@element-plus/icons-vue";
import { Icons } from "../../../../packages/onemapkit";

const props = defineProps({
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: false,
	},
	LayerStore: {
		type: Object as any,
		require: true,
	},
	PropStore: {
		type: Object as any,
		require: true,
	},
	/** 是否展开，默认展开 */
	IsFold: {
		type: Boolean,
		default: true,
		required: false
	},
	TabComponents: {
		type: Array as any,
		default: [],
		required: false
	}
});

watch(() => props.TabComponents, (newVal) => {
	const defaultTab = newVal.find((tab: any) => tab.isDefault);
	if (defaultTab) {
		selectTabName.value = defaultTab.name;
	}
});

//#region 变量声明
const selectTabName = ref("");

//#endregion


//#region 方法声明

/**
 * 设置面板是否可见
 */
const setContentVisible = () => {
	props.PropStore.foldLayerResPanel.value = !props.PropStore.foldLayerResPanel.value;
};


//#endregion


//#region 生命周期

onMounted(() => {
	props.PropStore.foldLayerResPanel.value = props.IsFold;

	// 设置默认选中的tab
	const defaultTab = props.TabComponents.find((tab: any) => tab.isDefault);
	if (defaultTab) {
		selectTabName.value = defaultTab.name;
	}
});

//#endregion
</script>

<style lang="scss" scoped>
@import "./LayerContent.scss";
</style>
