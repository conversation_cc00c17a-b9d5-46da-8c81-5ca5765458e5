import { ref } from "vue";

export const symbs = ref([
	{
		id: 1,
		label: "模糊",
		value: "like",
		type: 1,
	},
	{
		id: 2,
		label: "精确",
		value: "equal",
		type: 1,
	},
	{
		id: 3,
		label: "=",
		value: "=",
		type: 2,
	},
	{
		id: 4,
		label: ">",
		value: ">",
		type: 2,
	},
	{
		id: 5,
		label: "<",
		value: "<",
		type: 2,
	},
	{
		id: 6,
		label: ">=",
		value: ">=",
		type: 2,
	},
	{
		id: 7,
		label: "<=",
		value: "<=",
		type: 2,
	},
	{
		id: 8,
		label: "<>",
		value: "<>",
		type: 2,
	},
]);

export const ops = ref([
	{ label: "且", value: "and" },
	{ label: "或", value: "or" },
]);

