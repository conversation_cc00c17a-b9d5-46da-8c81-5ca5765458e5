<template>
	<div class="attrSearch-panel" v-loading="layerLoading">
		<el-form ref="form" size="small">
			<span class="panel-label">图层</span>
			<el-form-item>
				<el-select v-model="layer" class="selectLayer" value-key="name" placeholder="请选择图层" @change="layerChange">
					<el-option v-for="(item, index) in layers" :key="index" :label="item.name" :value="item"
						:disabled="getCanQuery(item)" :title="getCanQuery(item) ? '图层不可查询' : ''">
					</el-option>
				</el-select>
				<el-select v-if="layer" v-model="subLayer" class="selectLayer" style="margin-left: 5px" placeholder="请选择子图层"
					@change="subLayerChange">
					<el-option v-for="item in subLayers" :key="item.id" :label="item.name" :value="item.id"
						:disabled="getSubCanQuery(item)" :title="getSubCanQuery(item) ? '子图层不可查询' : ''">
					</el-option>
				</el-select>
			</el-form-item>
			<span class="panel-label">查询字段</span>
			<el-form-item>
				<div class="select-list">
					<div v-if="selectFieldList.length <= 0" style="
              width: 100%;
              text-align: center;
              font-size: 14px;
              color: var(--el-text-color-secondary);
            ">
						请添加查询字段
					</div>
					<div style="display: flex; margin-bottom: 5px; margin-bottom: 5px" v-for="item in selectFieldList"
						:key="item.id">
						<el-select size="small" v-model="item.name" class="selectSubLayer" style="width: 120px" placeholder="请选择字段"
							@change="fieldSelectFeild(item)">
							<el-option v-for="it in getSearchFields()" :key="it.name" :label="it.alias" :value="it.name">
							</el-option>
						</el-select>
						<el-select size="small" v-model="item.symb" style="width: 65px; margin: 0px 3px">
							<el-option v-for="it in item.type == 'esriFieldTypeString'
								? symbs.filter((x:any) => x.type == 1)
								: symbs.filter((x:any) => x.type == 2)" :key="it.id" :label="it.label" :value="it.value">
							</el-option>
						</el-select>
						<el-input size="small" style="flex: 1; margin: 0px 3px; width: 100%" v-model="item.text">
						</el-input>
						<el-select size="small" placeholder=" " style="width: 50px; margin: 0px 3px" v-model="item.op">
							<el-option v-for="item in ops" :key="item.value" :label="item.label" :value="item.value">
							</el-option>
						</el-select>
						<el-button type="danger" title="删除" style="margin-right: 5px; width: 10px" @click="deleteFeild(item)">
							<el-icon>
								<Close />
							</el-icon>
						</el-button>
					</div>
				</div>
			</el-form-item>
			<el-form-item class="btn-body" label-width="56px">
				<el-button class="mode-switch" size="small" :disabled="layerDataInfo.length == 0 || customData.length == 0"
					@click="isSimpleMode = !isSimpleMode">{{ isSimpleMode ? "详情模式" :
						"简洁模式" }}</el-button>
				<el-button :disabled="!layer" size="small" style="display: block" @click="appendField">添加字段</el-button>
				<el-button @click="resetForm()">重置</el-button>
				<el-button class="btn-class" :loading="fieldLoading" :disabled="!layer" type="primary" @click="search(null)">查询</el-button>
			</el-form-item>
		</el-form>
		<span class="panel-label"></span>
		<span style="margin-left: 5px" v-if="featureCount > 0 && layerDataInfo.length > 0">
			查询到 <span style="color: red">{{ featureCount }}</span> 条记录</span>
		<el-button class="btn-class" size="small" style="position: absolute; right: 20px" @click="exportPre('kml')"
			v-if="canExportKML && featureCount > 0 && layerDataInfo.length > 0" v-loading="kmlLoading">导出Kml</el-button>
		<el-button class="btn-class" size="small" :style="`position: absolute; ${canExportKML ? 'right: 95px;' : 'right: 20px;'
			}`" @click="exportPre('excel')" v-if="canExportExcel && featureCount > 0 && layerDataInfo.length > 0"
			v-loading="excelLoading">导出Excel</el-button>
		<el-divider style="margin: 9px 0" />
		<div id="tab-query-content" v-loading="fieldLoading" :class="ifresult ? ['tab-query-content'] : ['tab-query-content-nodata']">
			<div class="result" id="layer-search-result">
				<el-empty v-if="layerDataInfo.length <= 0" style="width: 100%;height: calc(100% - 45px);" description="暂无查询结果" />
				<el-card v-if="!isSimpleMode" v-for="(item, index) of layerDataInfo" :key="index" @click="highlightOBJID(item)"
					:class="['box-card']">
					<div class="text_item" v-for="(value, key) of item" :key="value">
						<div class="text_items" v-if="!['isLocation', 'id'].includes(String(key))">
							<div class="text_label">{{ key }}</div>
							<div class="text_value">{{ value }}</div>
						</div>
					</div>

					<el-button type="primary" size="small" @click="goto(item)" v-if="item?.isLocation"
						style="position: absolute; right: 15px; top: 15px; opacity: 0.8">定位</el-button>
				</el-card>
				<table v-if="isSimpleMode && layerDataInfo.length > 0" class="custom-table" border="0px" cellpadding="0"
					cellspacing="0">
					<tr class="first-tr">
						<td class="first-td">&nbsp;</td>
						<td v-for="fd in customField">{{ fd }}</td>
					</tr>
					<template v-for="item in customData">
						<tr class="first-tr">
							<td class="first-td" style="cursor: pointer" @click="item.isExpand = !item.isExpand">
								<span style="white-space: nowrap">详情</span>
							</td>
							<td v-for="(fd, index) in customField" :class="index == 0 ? 'flex' : ''"
								:style="fd == '详情' ? 'width: 50px; ' : ''">
								<el-icon title="定位到图形" style="cursor: pointer; color: #409eff" v-if="index == 0 && item.isLocation"
									@click="goto(item.data)" :size="16">
									<Location />
								</el-icon>
								{{ item[fd] }}
							</td>
						</tr>
						<Transition name="fade" mode="out-in" appear>
							<tr v-if="item.isExpand">
								<td style="color: black" :colspan="customField.length + 1">
									<div class="text_item" v-for="(value, key) of item.data" :key="value">
										<div class="text_items" v-if="!['isLocation', 'id'].includes(String(key))">
											<div class="text_label">{{ key }}</div>
											<div class="text_value">
												{{ value }}
											</div>
										</div>
									</div>
								</td>
							</tr>
						</Transition>
					</template>
				</table>
			</div>
			<el-pagination v-if="layerDataInfo.length > 0 && isPage" v-model:current-page="currentPage"
				v-model:page-size="pageSize" :page-sizes="pageSizes" small layout="prev, pager, next, jumper"
				:total="featureCount" @size-change="handleSizeChange" @current-change="search(currentPage)" />
		</div>
	</div>
</template>
<script lang="ts" setup>
import request from "@/utils/axios";
import { ref, onMounted, watch } from "vue";
import { symbs, ops } from "../querylogic";
import { Location, Close } from "@element-plus/icons-vue";
import tokml from "geojson-to-kml";
import {
	ElMessage,
	ElMessageBox,
	ElButton,
	ElSelect,
	ElOption,
	ElForm,
	ElFormItem,
	ElInput,
	ElEmpty,
	ElCard,
	ElPagination,
	ElLoading,
	ElDivider,
} from "element-plus";
import { serviceType, LayerQuery, getOnemap, mapType, OnemapEvent } from "onemapkit";
import { parseAttributeVal } from "../utils";
import { cloneDeep } from "lodash";
import mapAction from "../SpaceQuery/map-action";
import { convertToGeoJSONCoordinates } from "@/utils/convert";

const props = defineProps({
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: true,
	},
	LayerStore: {
		type: Object as any,
		default: null,
	},
});

const _inOnemap = getOnemap(props.MapControlName);

//#region 变量声明区域
const kmlLoading = ref(false);
const excelLoading = ref(false);
const layerLoading = ref(false); // 图层列表加载中
const layerData = ref([] as any[]);

let dataStore: any = null;
let highlightRow: any = null;

const layerDataInfo = ref([] as any[]); // 展示的数据
const featureCount = ref(0);

const featuresResult = ref([] as any[]);

// 选择的图层
const layer = ref(null as any);
const layers = ref([] as any[]);
const subLayer = ref(null as any); // 选择子图层
const subLayers = ref([] as any); // 子图层集合
const selectFieldList = ref([] as any[]);
const fieldLoading = ref(false);
const ifresult = ref(false);

// 图层坐标数据的图形类型
let layerGeometryType = "";

// 字段
const fields = ref([] as any[]);

// 权限控制
const canExportExcel = ref(true); // 是否允许导出Excel
const canExportKML = ref(true); // 是否允许导出KML

// 是否为简洁模式
const isSimpleMode = ref(true);

// 分页
const isPage = ref(false); // 图层是否可分页
const currentPage = ref(1); // 当前页码
const pageSize = ref(20); // 每页数量
const pageSizes = ref([20, 100, 200, 500, 1000]); // 分页数量选择

const layerId = "attrQueryLayer";

// 符号化样式

//添加查询条件
let fid = 1;

const handleSizeChange = (val: number) => {
	pageSize.value = val;
};

const appendField = () => {
	selectFieldList.value.push({
		id: ++fid,
		text: "",
		op: "and",
		name: "",
		symb: "=",
	});
};

// 删除字段
const deleteFeild = (item: any) => {
	selectFieldList.value.splice(
		selectFieldList.value.findIndex((x: any) => x.id == item.id),
		1
	);
};

//重置
const resetForm = () => {
	layer.value = null;
	subLayer.value = null;
	fields.value = [];
	subLayers.value = [];
	layerDataInfo.value = [];
	selectFieldList.value = [];
	ifresult.value = false;
	excelLoading.value = false;
	kmlLoading.value = false;
	customField.value = [];
	customData.value = [];
	dataStore = null;
	highlightRow = null;
	featureCount.value = 0;
	fieldLoading.value = false;

	// 清除所有图形
	_inOnemap.removeAllGraphicsByLayerId(layerId);
	mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId);
	mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId + "_highliht");
	mapAction.removeGeometryById(layerId);
	mapAction.removeGeometryById(layerId + "_highliht");
};
//#endregion

// 主图层是否可查
const getCanQuery = (one: any): boolean => {
	if (one.options.customProperty.enableQuery === undefined) return false;
	else return !one.options.customProperty.enableQuery;
}

// 子图层是否可查
const getSubCanQuery = (one: any): boolean => {
	return !one.enableQuery;
}

const customField = ref([] as any[]);
const customData = ref([] as any[]);

const fieldSelectFeild = (val: any) => {
	let dt = fields.value.find((x) => val.name == x.name);
	let one = JSON.parse(JSON.stringify(dt));
	for (let f of Object.getOwnPropertyNames(one)) {
		val[f] = dt[f];
	}
	val.text = "";
	val.op = "and";
	if (val.type == "string" || one.type == "esriFieldTypeString") {
		val.symb = "like";
	} else {
		val.symb = "=";
	}
};

// 获取一次图层数据
onMounted(async () => {
	// 清除所有图形
	_inOnemap.removeAllGraphicsByLayerId(layerId);
});

/**
 * 定位到图形
 * @param row 
 */
const goto = (row: any) => {
	console.log("定位到图形", row);

	highlightRow = row;

	// 寻找ObjectID字段名称，有些图层的ObjectID字段名称大小写可能不一致
	let objId: string = "id";
	if (objId) {
		let one: any = layerData.value.find(
			(x) => x.attributes[objId] == row[objId]
		);
		if (one) {
			if (layerGeometryType == "point" && one.geometry && (one.geometry.x === undefined || one.geometry.y === undefined)) {
				ElMessage.warning("该要素无空间数据");
				return
			} else if (layerGeometryType == "polyline" && one.geometry && one.geometry.paths.length == 0) {
				ElMessage.warning("该要素无空间数据");
				return
			} else if (layerGeometryType == "polygon" && one.geometry && one.geometry.rings.length == 0) {
				ElMessage.warning("该要素无空间数据");
				return
			}

			let geometry = JSON.parse(JSON.stringify(one.geometry));
			geometry.type = layerGeometryType;

			// _inOnemap.drawGraphic(one.attributes, geometry, symbol, false, layerId + "_highliht", true);
			let geo = cloneDeep(geometry);
			geo.type = layerGeometryType;
			geo = convertGeometry(geo, 4490);

			// 绘制高亮地块
			if (_inOnemap.MapType.value == mapType.arcgis) {
				mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId + "_highliht");
				mapAction.drawGraphicsTempLayer(
					_inOnemap.EsriObj,
					_inOnemap.MapViewer,
					geo,
					false,
					layerId + "_highliht",
					9999999,
					{
						color: [255, 255, 0, 0.3],
						lineColor: [255, 255, 0, 1.0],
					}
				);
			} else {
				mapAction.removeGeometryById(layerId + "_highliht");
				mapAction.initDrawPrimitiveCollection(
					_inOnemap.MapViewer,
					layerId + "_highliht"
				);
				mapAction.drawPrimitiveGeometry(
					_inOnemap.MapViewer,
					layerId + "_highliht",
					geo,
					{
						fillColor: "rgba(255, 255, 0, 0.3)",
						outlineColor: "#ffff00",
					}
				);
			}

			_inOnemap.zoomByGeometry([geo]);
		}
	}
};

// 坐标转换
const convertGeometry = (geometry: any, wkid: any) => {
	let geo = cloneDeep(geometry);
	if (geometry.type === "point") {
		const p = convertToGeoJSONCoordinates(geometry, geometry.spatialReference.wkid, wkid);
		geo.x = p[0] as number;
		geo.y = p[1] as number;
		geo.spatialReference.wkid = 4490;
		geo.spatialReference.latestWkid = 4490;
	} else if (geometry.type === "polyline") {
		const polyline = convertToGeoJSONCoordinates(geometry, geometry.spatialReference.wkid, wkid) as any;
		geo.paths = polyline;
		geo.spatialReference.wkid = 4490;
		geo.spatialReference.latestWkid = 4490;
	} else if (geometry.type === "polygon") {
		const polygon = convertToGeoJSONCoordinates(geometry, geometry.spatialReference.wkid, wkid) as any;
		geo.rings = polygon;
		geo.spatialReference.wkid = 4490;
		geo.spatialReference.latestWkid = 4490;
	}
	return geo;
}

// 处理查询到的数据
const handleFeatureData = (data: any) => {
	if (!data) {
		fieldLoading.value = false;
		return;
	}
	console.log("查询到的数据handleFeatureData: ", data);
	let dt = JSON.parse(JSON.stringify(data.features));
	layerGeometryType = data.geometryType;
	if (_inOnemap.MapType.value == mapType.arcgis) {
		mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId);
		for (let one of dt) {
			if (!fieldLoading.value) {
				mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId);
				return;
			};

			if (one.geometry) {
				let geo = cloneDeep(one.geometry);
				geo.type = data.geometryType
				geo = convertGeometry(geo, 4490);
				mapAction.drawGraphicsTempLayer(
					_inOnemap.EsriObj,
					_inOnemap.MapViewer,
					geo,
					false,
					layerId,
					999999,
					{
						color: [173, 220, 220, 0.3],
						lineColor: [25, 229, 229, 1.0],
					}
				);
			}
		}
	} else {
		// 清除所有图形
		mapAction.removeGeometryById(layerId);
		mapAction.initDrawPrimitiveCollection(_inOnemap.MapViewer, layerId);
		for (let one of dt) {
			if (!fieldLoading.value) {
				mapAction.removeGeometryById(layerId);
				return;
			}
			if (one.geometry) {
				let geo = cloneDeep(one.geometry);
				geo.type = data.geometryType
				geo = convertGeometry(geo, 4490);
				mapAction.drawPrimitiveGeometry(
					_inOnemap.MapViewer,
					layerId,
					geo,
					{
						fillColor: "rgba(173, 220, 220,0.3)",
						outlineColor: "#19e5e5",
					}
				);
			}
		}
	}

	// let config = layer.value?.options;
	const customProperty = layer.value?.options?.customProperty;
	const checkFields = getShowInListFields();
	featuresResult.value = data.features;

	// // 权限控制
	// canExportExcel.value = false;
	// canExportKML.value = false;
	// if (config && config.enabledExports) {
	// 	// 判断是否有excel导出权限
	// 	if (config.enabledExports.find((x: string) => x == "excel")) {
	// 		canExportExcel.value = true;
	// 	}

	// 	// 判断是否有kml导出权限
	// 	if (config.enabledExports.find((x: string) => x == "kml")) {
	// 		canExportKML.value = true;
	// 	}
	// }

	customField.value = [];
	customField.value[0] = "OBJECTID";

	const subLayerInfo =
		customProperty && customProperty.layers
			? customProperty.layers.find((item: any) => {
				return item.id == subLayer.value;
			})
			: null;

	if (customProperty) {
		try {
			if (customProperty.layers) {
				if (subLayerInfo) {
					customField.value[0] = subLayerInfo.displayField;
				}
			}
		} catch (ex) {
			console.log(ex);
		}
	}

	// 添加条件字段
	if (selectFieldList && selectFieldList.value.length > 0) {
		for (let one of selectFieldList.value) {
			if (!customField.value.find(x => x == one.name)) {
				customField.value.push(one.alias)
			}
		}
	}

	let idx = 0;
	let dtBak = JSON.parse(JSON.stringify(dt));
	if (!checkFields) {
		fieldLoading.value = false;
		ifresult.value = true;
		layerDataInfo.value = [];
		return;
	}
	for (let item of dt) {
		item.id = ++idx;
		let dataAttr: any = [];
		// 字段排序
		let checkFieldTemp = JSON.parse(JSON.stringify(checkFields));
		let num = 0;
		for (let one of checkFieldTemp) {
			one.order = num;
			++num;
		}

		let newData: any = {};
		newData["详情"] = "详情";

		let showFd = fields.value.find((item: any) => {
			return (
				item.name == customField.value[0] ||
				item.alias == customField.value[0] ||
				item.thirdAlias == customField.value[0]
			);
		});
		if (showFd) {
			customField.value[0] = showFd.thirdAlias
				? showFd.thirdAlias
				: showFd.alias;
		}

		for (let it of data.fields) {
			//根据配置字段信息判断是否显示
			let tempField = checkFieldTemp.find((item: any) => {
				return (
					item.name == it.name ||
					item.alias == it.name ||
					item.thirdAlias == it.name
				);
			});

			// 如果获取不到，说明是新加的字段，默认显示
			if (!tempField) {
				continue;
			}

			if (
				tempField &&
				tempField.hasOwnProperty("showInList") &&
				!tempField.showInList
				// && (showFd && (tempField.name != showFd.name))) {
			) {
				continue;
			}

			let f = it;
			if (tempField && tempField.hasOwnProperty("showInList")) {
				f = JSON.parse(JSON.stringify(tempField));
			}
			let key = f.thirdAlias ? f.thirdAlias : f.alias ? f.alias : f.name;

			try {
				dataAttr[tempField.order] = {
					id: key,
					value: item.attributes[f.name],
				};
			} catch (e) {
				console.log(e);
			}

			if (tempField && tempField.valueType) {
				dataAttr[tempField.order] = {
					id: key,
					value: parseAttributeVal(
						item.attributes[f.name],
						tempField.valueType,
						tempField.dicDatas
					),
				};
			}

			if (!dataAttr[tempField.order]) {
				dataAttr[tempField.order] = {
					id: key,
					value: "-",
				};
			}
		}

		let attr: any = {};
		for (let one of dataAttr) {
			if (one) {
				attr[one.id] = one.value;
			}
		}

		item.attributes = attr;
		item.attributes.id = idx;
		item.attributes.isLocation = item.geometry ? true : false;
		// 添加条件字段
		if (selectFieldList && selectFieldList.value.length > 0 && dtBak[idx - 1]) {
			for (let one of selectFieldList.value) {
				let ftItem = dtBak[idx - 1];
				const fd = fields.value.find(x => x.name == one.name);
				if (ftItem && ftItem.attributes) {
					newData[one.alias] = parseAttributeVal(
						ftItem.attributes[one.name],
						fd.valueType,
						fd.dicDatas
					) ?? one.text;
				}
			}
		}

		// 如果别名设置为空，则取图层服务的别名
		if (!showFd && data && data.fieldAliases) {
			let ftItem = data.fieldAliases[customField.value[0]];
			if (ftItem) {
				customField.value[0] = ftItem;
			}
		}

		let showName = attr[customField.value[0]];
		if (!showName && showFd && dtBak[idx - 1]) {
			showName = dtBak[idx - 1].attributes[showFd.name];
		}

		newData[customField.value[0]] = showName;
		newData["data"] = item.attributes;
		newData["geometry"] = item.geometry;
		newData["isLocation"] = item.geometry ? true : false;
		newData["isExpand"] = false;
		customData.value.push(newData);
	}

	ifresult.value = true;
	fieldLoading.value = false;
	layerData.value = dt;
	layerDataInfo.value = [];
	for (let one of layerData.value) {
		layerDataInfo.value.push(one.attributes);
	}
};

// 查询
const search = async (page: any) => {
	let pageNum = (page !== undefined && page !== null) ? page : 1;
	featuresResult.value = [];
	customData.value = [];
	dataStore = null;
	highlightRow = null;
	if (!layer.value) {
		ElMessage.error("请选择要搜索的图层");
		return;
	}
	if (subLayers.value.length > 0 && !subLayer.value && subLayer.value !== 0) {
		ElMessage.error("请选择要搜索的子图层");
		return;
	}

	if (selectFieldList && selectFieldList.value.length > 0) {
		for (let one of selectFieldList.value) {
			if (!one.name || !one.text) {
				ElMessage.error("字段或条件不能为空");
				return;
			}
		}
	}

	for (let item of selectFieldList.value) {
		if (!item.text) {
			ElMessage.error(`筛选条件${item.alias ? ` ${item.alias} ` : ""}不能为空`);
			return;
		}
	}

	try {
		layerData.value = [];
		dataStore = null;
		highlightRow = null;
		fieldLoading.value = true;

		let where = ``;
		for (let i = 0; i < selectFieldList.value.length; ++i) {
			let item = selectFieldList.value[i];
			if (item.type == "esriFieldTypeString" || item.type == "string") {
				let text = item.symb == "like" ? `%${item.text}%` : item.text;
				where += `${item.name} ${item.symb == "like" ? "like" : "="} '${text}'`;
			} else {
				where += `${item.name}${item.symb}${item.text}`;
			}

			// 添加and or
			if (i < selectFieldList.value.length - 1) {
				where += ` ${item.op} `;
			}
		}

		const query = new LayerQuery(layer.value.url);

		// 查询数据总数
		if (page == null) {
			query
				.queryTotalCountAsync(
					subLayer.value,
					where,
					layer.value.serviceType,
					null
				)
				.then((count: number) => {
					featureCount.value = count;
				})
				.catch((e: any) => {
					console.log(e);
				});
		}

		query.query(
			subLayer.value,
			["*"],
			where,
			pageNum,
			pageSize.value,
			true,
			layer.value.serviceType,
			null,
			(result: any, _isPage: any) => {
				isPage.value = _isPage !== undefined ? _isPage : false;
				dataStore = result;
				handleFeatureData(result);
			}
		);
	} catch (e) {
		fieldLoading.value = false;
		console.log(e);
	}
};

const layerChange = async () => {
	if (!layer.value) return;

	fields.value = [];
	featuresResult.value = [];
	layerDataInfo.value = [];
	selectFieldList.value = []
	ifresult.value = false
	layerLoading.value = true
	subLayer.value = null;
	subLayers.value = [];

	// 清除所有图形
	_inOnemap.removeAllGraphicsByLayerId(layerId);
	mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId);
	mapAction.clearDrawByLayerId(_inOnemap.MapViewer, layerId + "_highliht");
	mapAction.removeGeometryById(layerId);
	mapAction.removeGeometryById(layerId + "_highliht");
	try {
		// 判断是否存在图层配置，不存则从地图服务获取
		if (layer.value?.options?.customProperty
			&& layer.value?.options?.customProperty.layers
			&& layer.value?.options?.customProperty.layers.length > 0) {
			for (let item of layer.value?.options?.customProperty.layers) {
				if (item.defaultVisibility) {
					const subRes = await request.get({ url: `${layer.value.url}/${item.id}?f=json` });
					subLayers.value.push({
						id: item.id,
						name: item.name,
						hasConfig: true, // 是否存在配置
						enableQuery: subRes.geometryType ? true : false	// 是否可查
					});
				}
			}
		} else {
			try {
				const res = await request.get({ url: `${layer.value.url}?f=json` });
				if (res) {
					for (let item of res.layers) {
						const subRes = await request.get({ url: `${layer.value.url}/${item.id}?f=json` });
						subLayers.value.push({
							id: item.id,
							name: item.name,
							hasConfig: false, // 是否存在配置
							enableQuery: subRes.geometryType ? true : false	// 是否可查
						});
					}
				} else {
					// 获取失败，从layers中删除该图层
					if (
						layers.value.findIndex((x) => x.layerid == layer.value.layerid) !=
						-1
					) {
						layers.value.splice(
							layers.value.findIndex((x) => x.layerid == layer.value.layerid),
							1
						);
						layer.value = null;
					}
				}
			} catch (error) {
				console.log(error);
				// 获取失败，从layers中删除该图层
				if (
					layers.value.findIndex((x) => x.layerid == layer.value.layerid) != -1
				) {
					layers.value.splice(
						layers.value.findIndex((x) => x.layerid == layer.value.layerid),
						1
					);
					layer.value = null;
				}
			}
		}

		if (subLayers.value && subLayers.value.length > 0) {
			let sub = subLayers.value.filter((x: any) => x.enableQuery);
			if (sub.length > 0) {
				subLayer.value = sub[0].id;
				subLayerChange();
			} else {
				subLayer.value = null;
			}
		}

	} catch (error) {
		console.log(error);
	}
	layerLoading.value = false;
};

const subLayerChange = async () => {
	layerLoading.value = true;
	fields.value = [];
	layerDataInfo.value = [];
	selectFieldList.value = [];
	ifresult.value = false;
	featureCount.value = 0;
	customField.value = [];
	customData.value = [];

	// 如果存在配置，则从配置中获取子图层及字段信息
	let sub = subLayers.value.find((x: any) => x.id == subLayer.value);
	if (sub && sub.hasConfig) {
		let layerConfig = layer.value.options.customProperty.layers.find(
			(x: any) => x.id == sub.id
		);
		if (layerConfig && layerConfig.fields) {
			let id = 0;
			for (let item of layerConfig.fields) {
				let temp = cloneDeep(item);
				temp.id = id++;
				if (temp.thirdAlias && temp.thirdAlias != "") {
					temp.alias = temp.thirdAlias;
				}
				fields.value.push(temp);
			}
		}
	} else {
		let res = await request.get({ url: `${layer.value.url}/${sub.id}?f=json` });
		if (res && res.fields) {
			let id = 0;
			for (let item of res.fields) {
				fields.value.push({
					id: id++,
					name: item.name,
					alias: item.alias,
					type: item.type,
					visibility: true,
					showInList: true,
					isSearch: true,
				});
			}
		}
	}

	layerLoading.value = false;
};

/**
 * 获取是否可查询字段列表
 */
const getSearchFields = (): any[] => {
	let fn = (x: any) => {
		return (
			(x.visibility === undefined ||
				x.visibility == null ||
				x.visibility === true) &&
			(x.isSearch === undefined || x.isSearch == null || x.isSearch === true)
		);
	};
	return fields.value.filter((x: any) => fn(x));
};

/**
 * 获取是否显示在列表中的字段列表
 */
const getShowInListFields = (): any[] => {
	let fn = (x: any) => {
		if (x.showInList === undefined || x.showInList == null) {
			x.showInList = true;
		}
		return (
			(x.visibility === undefined ||
				x.visibility == null ||
				x.visibility === true) &&
			(x.showInList === undefined ||
				x.showInList == null ||
				x.showInList === true)
		);
	};
	return fields.value.filter((x: any) => fn(x));
};

//高亮卡片
const highlightOBJID = (id: string) => {
	console.log(id);
};

const downloadFileByStream = (data: any, fileName: any, type?: any) => {
	let blob = type ? new Blob([data], { type: 'application/octet-stream' }) : new Blob([data]);

	// 判断是否有msSaveOrOpenBlob，在客户端上以本地方式保存文件（任意大小），方法如同从 Internet 下载文件
	//@ts-ignore
	if (typeof window.navigator.msSaveOrOpenBlob === "function") {
		// msSaveBlob只能保存，不能在线打开
		//@ts-ignore
		window.navigator.msSaveOrOpenBlob(blob, fileName);
	} else {
		// 创建URL
		const objectUrl = window.URL.createObjectURL(blob);
		downloadFileByUrl(objectUrl, fileName);
	}
}

const downloadFileByUrl = (url: any, fileName: any) => {
	// 创建a标签
	const a = window.document.createElement("a");
	a.style.display = "none";
	a.href = url;
	// 设置下载名称
	a.download = fileName;
	document.body.appendChild(a);
	// 触发标签点击事件
	a.click();
	document.body.removeChild(a);
	// 释放URL
	window.URL.revokeObjectURL(url);
}

/**
 * 通过接口导出excel
 * @param {*} data 
 */
const exportExcel = (data: any) => {
	let sheet = {
		sheetName: "属性查询结果",
		cols: [],
		rows: [],
		colStyle: {
			ontSize: 15,
			fontName: "黑体",
			bolded: true,
			textAlign: "center",
		},
		rowStyle: {
			fontSize: 12,
			fontName: "宋体",
			bolded: false,
			textAlign: "left",
		},
	} as any;

	// 生成表头字段
	let i = 0;
	let sheetFields = [];

	const visibilityList = getShowInListFields();
	const checkFields = getSearchFields();
	if (checkFields) {
		for (let f of checkFields) {
			let one = visibilityList.find((x:any) => x.name == f.name)
			if (one && f.thirdAlias) {
				one.thirdAlias = f.thirdAlias
			}
		}
	}
	
	for (let one of visibilityList.filter((x:any) => x.name != "Shape")) {
		sheetFields.push({
			colName: one.name,
			alias: one.thirdAlias ? one.thirdAlias : one.alias,
			order: ++i,
		});
	}
	sheet.cols = sheetFields;

	// 生成数据
	let rows = [];
	i = 0;
	for (let item of data) {
		let items = [];
		for (let key of Object.keys(item.attributes)) {
			let tempField = fields.value.find(fields => {
				return fields.name == key || fields.alias == key || fields.thirdAlias == key;
			});
			if (tempField && tempField.hasOwnProperty("showInList") && !tempField.showInList) {
				continue;
			} else {
				items.push({
					colName: key,
					value: item.attributes[key] || item.attributes[key] == "0" ? item.attributes[key] : " ",
				});
			}
		}
		rows.push({
			items: items,
			order: ++i,
		});
	}

	sheet.rows = rows;

	if (!rows.length) {
		ElMessage.warning("没有可以导出的数据");
		return;
	}

	let loading = ElLoading.service({
		lock: true,
		text: "文件下载中...",
		background: "rgba(0,0,0,.7)",
	});
	const d = new Date();
	const fileName = `属性查询结果(${layer.value.name} - ${d.getFullYear()}${d.getMonth() + 1}${d.getDate()}).xls`;
	if (OnemapEvent.ExportExcelAPI) {
		OnemapEvent.ExportExcelAPI([sheet])
			.then((data: any) => {
				if (data) {
					downloadFileByStream(data, fileName);
					loading.close();
					excelLoading.value = false
					kmlLoading.value = false
				} else {
					loading.close();

					ElMessage.error("导出失败")
					excelLoading.value = false
					kmlLoading.value = false
				}

			})
			.catch((err: any) => {
				console.log(err);
				loading.close();
				kmlLoading.value = false
				excelLoading.value = false
			});
	}
};

/**
 * 转为geojson
 * @param graphics 
 */
const getGeojson = (graphics: any) => {
	let p = {
		type: 'FeatureCollection',
		features: []
	} as any;

	for (let g of graphics) {
		let data = {
			"type": "Feature",
			"properties": g.attributes,
			"geometry": {}
		} as any;

		if (g.geometry.type === "point") {
			data.geometry = {
				type: 'Point',
				coordinates: [g.geometry.x, g.geometry.y]
			}
		} else if (g.geometry.type === "polyline") {
			data.geometry = {
				type: 'LineString',
				coordinates: g.geometry.paths
			}
		} else if (g.geometry.type === "polygon") {
			data.geometry = {
				type: 'Polygon',
				coordinates: g.geometry.rings
			}
		}
		p.features.push(data);
	}
	return JSON.stringify(p);
}

/**
 * 导出kml
 */
const exportKml = async (data: any) => {
	try {
		console.log("exportKml");
		kmlLoading.value = true;
		for (let item of data) {
			item.geometry = convertGeometry(item.geometry, 4490);
		}
		let geojson = getGeojson(data);
		let kml = tokml(JSON.parse(geojson));
		let element = document.createElement("a");
		element.style.display = "none";
		let blob = new Blob([kml]);
		element.href = URL.createObjectURL(blob);

		let d = new Date();
		element.download = `属性查询结果坐标(${layer.value.name} - ${d.getFullYear()}${d.getMonth() + 1
			}${d.getDate()}).kml`;
		element.click();
		element.remove();
		kmlLoading.value = false;
		ElMessage.success("文件生成中，请耐心等耐，您可继续进行其他操作！");
	} catch (err: any) {
		console.log(err);
		kmlLoading.value = false;
		excelLoading.value = false;
	}
};


/**
 * 导出函数
 * @param action 
 */
const exportPre = async (action: any) => {
	console.log("导出前预处理");
	if (action == "excel" && excelLoading.value) return
	if (action == "kml" && kmlLoading.value) return

	try {
		let isFail = false
		let fn = async () => {
			let size = 1000;	// 每页数
			let total = Math.ceil(featureCount.value / 1000);	// 总页数
			let exportData: any[] = [];	// 要导出的数据
			let fail: any = {};	// 允许的失败次数

			let exportFunction = null
			if (action == "excel") {
				exportFunction = exportExcel
			} else if (action == "kml") {
				exportFunction = exportKml
			} else {
				return
			}

			// 如果不支持分页，只查一次
			if (!isPage.value) {
				total = 1
			}

			for (let i = 0; i < total; ++i) {
				let data: any = {
					f: "json"
				};

				let where = ``;
				for (let i = 0; i < selectFieldList.value.length; ++i) {
					let item = selectFieldList.value[i];
					if (item.type == "esriFieldTypeString" || item.type == "string") {
						let text = item.symb == "like" ? `%${item.text}%` : item.text;
						where += `${item.name} ${item.symb == "like" ? "like" : "="} '${text}'`;
					} else {
						where += `${item.name}${item.symb}${item.text}`;
					}

					// 添加and or
					if (i < selectFieldList.value.length - 1) {
						where += ` ${item.op} `;
					}
				}
				if (selectFieldList.value && selectFieldList.value.length > 0 && where) {
					data.where = where;
				} else {
					data.where = "1=1";
				}

				if (isPage.value) {
					data.resultRecordCount = size
					data.resultOffset = i * size
				}
				data.outFields = "*"
				data.returnGeometry = action == "kml"
				data.returnM = false
				data.returnZ = false
				data.spatialRel = "esriSpatialRelIntersects"
				data.outSR = 4490

				try {
					let result = await request.get({
						url: `${layer.value.url}/${subLayer.value}/query`,
						params: data
					})

					if (result) {
						for (let one of result.features) {
							if (one.geometry) {
								one.geometry.spatialReference = result.spatialReference
								switch (result.geometryType) {
									case "esriGeometryPolygon":
										one.geometry.type = "polygon"
										break
									case "esriGeometryPolyline":
										one.geometry.type = "polyline"
										break
									case "esriGeometryPoint":
										one.geometry.type = "point"
										break
								}
							}
							if (fields.value) {
								fields.value.forEach((item: any) => {
									if (item && item.valueType) {
										one.attributes[item.name] = parseAttributeVal(one.attributes[item.name], item.valueType, item.dicDatas);
									}
								})
							}
							exportData.push(one)
						}
					}
				} catch (e) {
					if (!fail["fail" + i]) fail["fail" + i] = 0
					++fail["fail" + i]
					if (fail["fail" + i] > 5) {
						// 报错超过5次，停止导出
						console.log(e)
						ElMessage.error("服务异常，导出失败")
						kmlLoading.value = false;
						excelLoading.value = false;
						isFail = true
						return
					}
					--i
				}
			}

			if (isFail) return

			exportFunction(exportData)
		}

		if (featureCount.value >= 10000) {
			ElMessageBox.confirm("您要导出的数据过多，可能会导出失败，请增加筛选条件减少导出的数据，或者尝试继续导出，是否继续？", "提示", {
				confirmButtonText: "继续导出",
				cancelButtonText: "取消导出",
				type: "warning",
			}).then(async () => {
				if (action == "excel") excelLoading.value = true
				else if (action == "kml") kmlLoading.value = true

				fn()
			})
		} else {
			if (action == "excel") excelLoading.value = true
			else if (action == "kml") kmlLoading.value = true

			fn()
		}
	} catch (err: any) {
		console.log(err);
		kmlLoading.value = false;
		excelLoading.value = false;
	}
};

watch(
	() => props.LayerStore.CheckedLayerids,
	(val) => {
		for (let id of val ?? []) {
			let item = props.LayerStore.MapLayers.find((x: any) => x.layerid == id)
			if (
				item &&
				[
					serviceType.ArcgisDynamicLayer,
					serviceType.ArcgisTileLayer,
					serviceType.ArcgisMapImageLayer,
					serviceType.ArcgisFeatureLayer,
				].includes(item.serviceType)
			) {
				// 如果图层列表中不存在该图层，则添加
				if (
					layers.value.findIndex((x) => x.layerid == item.layerid) == -1
				) {
					let isQuery = true;
					if (item.options.customProperty && item.options.customProperty.enableQuery !== undefined) {
						isQuery = item.options.customProperty.enableQuery
					}

					for (let it of item.subLayers) {
						let one = { ...it };
						one.disabled = !isQuery;
						one.id = it.layerid;
						layers.value.push(one);
					}
				}
			}
		}

		let tempLayer = cloneDeep(layers.value);
		for (let item of tempLayer) {
			if (!val.find((x: string) => x == item.layerid)) {
				// 如果图层列表中存在该图层，则删除
				if (
					layers.value.findIndex((x) => x.layerid == item.layerid) != -1
				) {
					layers.value.splice(
						layers.value.findIndex((x) => x.layerid == item.layerid),
						1
					);
					if (layer.value && layer.value.layerid == item.layerid) {
						// 查询的图层被取消勾选，则清除数据和图形
						layer.value = null;
						resetForm();
					}
				}
			}
		}
	}, { deep: true }
);

watch(
	() => _inOnemap.isMapReady.value,
	async (newval) => {
		if (newval
			&& dataStore
			&& dataStore.features
			&& dataStore.features.length > 0) {
			// 未知原因，load为true了还是绘制不出图像，需要延迟一下，临时解决方案
			setTimeout(() => {
				mapAction.removeDrawPrimitiveCollection();
				handleFeatureData(dataStore);
				if (highlightRow) {
					goto(highlightRow);
				}
			}, 1000)
		}
	}
);

</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
