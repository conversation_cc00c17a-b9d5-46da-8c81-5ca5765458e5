@charset "UTF-8";

:deep(.el-checkbox) {
    margin: 5px;
}

#query-panel {
    width: 100%;
    padding: 15px 15px 0px 15px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 10px;
    margin: 10px 0px;
    position: relative;

    .query-condition {
        width: 100%;

        &__title {
            background-color: white;
            width: 100px;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 10px;
            position: absolute;
            top: -13px;
            left: 15px;
            color: #606266;
            font-size: 11px;
            padding: 3px 0px;
        }
    }

    .panel-item {
        width: 100%;
        display: flex;
        margin: 5px 0px;
    }

    .panel-btn {
        display: flex;
        justify-content: right;
        padding-right: 10px;
        margin-top: 20px;
    }
}

.v-enter-active,
.v-leave-active {
    transition: opacity 0.2s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
}