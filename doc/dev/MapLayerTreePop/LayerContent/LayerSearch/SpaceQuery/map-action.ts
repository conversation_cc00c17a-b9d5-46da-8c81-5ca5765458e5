
import { convertToGeoJSONCoordinates } from "@/utils/convert";
import * as Cesium from "@onemapkit/cesium";

// 绘制对象键值对
let drawPrimitiveCollection: Array<any> = [];
/** polyline绘制线的初始颜色*/
let polylineDefaultColor = Cesium.Color.fromCssColorString("#409eff");
/** polygon绘制线的初始颜色*/
let polygonDefaultColor = Cesium.Color.WHITESMOKE.withAlpha(0.3);
/**point绘制带你的初始颜色 */
let pointDefaultColor = Cesium.Color.fromCssColorString("#409eff")

interface GeometryOptions {
  /** 填充颜色  */
  fillColor?: string;
  /** 外轮廓颜色  */
  outlineColor?: string;
  /** 线宽  适用于线*/
  lineWidth?: number;
  /** 外轮廓线宽*/
  outlineWidth?: number;
}

type PROJECTION_Type = "4490" | "4524" | "4326" | "4545"| 4490 | 4524 | 4326 | 4545;
export default {
  // fullExtent: (esriLib, mapObj) => {
  //     if (
  //         mapBase.fullExtentInfo &&
  //         mapBase.fullExtentInfo.x &&
  //         mapBase.fullExtentInfo.y &&
  //         mapBase.fullExtentInfo.zoom
  //     ) {
  //         let point = new esriLib.Point(
  //             mapBase.fullExtentInfo.x,
  //             mapBase.fullExtentInfo.y,
  //             new esriLib.SpatialReference({ wkid: 4490 }),
  //         );
  //         mapObj.centerAndZoom(point, mapBase.fullExtentInfo.zoom);
  //     }
  // },
  //坐标定位
  locateByXY(
    esriLib: any,
    mapObj: any,
    x: number,
    y: number,
    zoom = 8,
    showMarker = false
  ) {
    let point = new esriLib.Point(
      x,
      y,
      new esriLib.SpatialReference({ wkid: 4490 })
    );
    mapObj.centerAndZoom(point, zoom);
    if (showMarker) {
      this.showMarkerByPoint(esriLib, mapObj, point);
    }
  },
  //定位点
  locateByPoint(
    esriLib: any,
    mapObj: any,
    point: any,
    zoom = 8,
    showMarker = false
  ) {
    mapObj.centerAndZoom(point, zoom);
    if (showMarker) {
      this.showMarkerByPoint(esriLib, mapObj, point);
    }
  },
  //根据点位显示默认marker
  showMarkerByPoint(esriLib: any, mapObj: any, point: any) {
    mapObj.graphics.clear();
    // 创建一个标记符号对象
    var markerSymbol = new esriLib.SimpleMarkerSymbol();
    // 设置标记符号的样式
    markerSymbol.setStyle(esriLib.SimpleMarkerSymbol.STYLE_SQUARE);
    markerSymbol.setColor(new esriLib.Color([255, 0, 0, 0.5]));
    // 创建一个图形对象
    var graphic = new esriLib.Graphic(point, markerSymbol);
    // 将图形对象添加到地图上
    mapObj.graphics.add(graphic);
  },
  // 创建绘制图层，绘制某个对象
  drawGraphicsTempLayer(
    esriLib: any,
    mapObj: any,
    geometry0: any,
    isClear = true,
    graphicId = "temp_drawGraphicsTempLayer",
    order = 999999,
    color?: any
  ) {
    let graphicsLayer = mapObj.getLayer(graphicId);
    if (!graphicsLayer) {
      graphicsLayer = new esriLib.GraphicsLayer({ id: graphicId });
      mapObj.addLayer(graphicsLayer);
    }
    mapObj.reorderLayer(graphicsLayer, order);
    if (isClear) {
      graphicsLayer.clear();
    }

    // 绘制方法
    const draw = (geometry: any) => {
      // console.log(geometry,'------draw')
      if(geometry.x) {

        let pointSymbol = new esriLib.SimpleMarkerSymbol(
          esriLib.SimpleMarkerSymbol.STYLE_CIRCLE,
          16,
          new esriLib.SimpleLineSymbol(
            esriLib.SimpleLineSymbol.STYLE_SOLID,
            new esriLib.Color(color && color.lineColor ? color.lineColor : [255, 0, 0, 1.0]),
            4,
          ),
          new esriLib.Color(color && color.color ? color.color : [173, 220, 220, 1.0]),
        );
        let pt = new esriLib.Point([geometry.x, geometry.y], new esriLib.SpatialReference({ wkid: 4490 }));
        graphicsLayer.add(new esriLib.Graphic(pt, pointSymbol))
      }else if (geometry.paths) {
        let currentColor = color?color.lineColor:[173, 220, 220, 1]
        let LineSymbol = new esriLib.SimpleLineSymbol(esriLib.SimpleLineSymbol.STYLE_SOLID, new esriLib.Color(currentColor), 2)
        graphicsLayer.add(new esriLib.Graphic(new esriLib.Polyline(geometry), LineSymbol))
      }  else {

        let fillSymbol = new esriLib.SimpleFillSymbol().setColor(
          new esriLib.Color(
            color && color.fillColor ? color.fillColor : [173, 220, 220, 0.35]
          )
        );
        fillSymbol.setOutline(
          new esriLib.SimpleLineSymbol(
            esriLib.SimpleLineSymbol.STYLE_SOLID,
            new esriLib.Color(
              color && color.lineColor ? color.lineColor : [0, 255, 255]
            ),
            5
          )
        );
        const polygon = new esriLib.Polygon(geometry);
        graphicsLayer.add(new esriLib.Graphic(polygon, fillSymbol));
      }

    };

    if (geometry0.spatialReference.wkid == 4490) {
      draw(geometry0);
    } else {
      // 判断4524是否需要反转坐标
      let geo = geometry0;
      if (geo.spatialReference && geo.spatialReference.wkid == 4524) {
        // 反转rings的x、y坐标
        let reverseRingsCoordinates = (rings: any) => {
          return rings.map(function (ring: any) {
            return ring.reverse().map(function (point: any) {
              return [point[1], point[0]];
            });
          });
        };

        // 反转paths的x、y坐标
        let reversePathsCoordinates = (paths: any) => {
          return paths.map(function (path: any) {
            return path.map(function (point: any) {
              return [point[1], point[0]];
            });
          });
        };
        try {
          if (geo.type == "polygon") {
            // 判断坐标是否为反向
            if (
              geo.rings[0][0][0] < 15000000 ||
              geo.rings[0][0][1] > 10000000
            ) {
              geo.rings = reverseRingsCoordinates(geo.rings);
            }
          } else if (geo.type == "polyline") {
            // 判断坐标是否为反向
            if (
              geo.paths[0][0][0] < 15000000 ||
              geo.paths[0][0][1] > 10000000
            ) {
              geo.rings = reversePathsCoordinates(geo.rings);
            }
          }
        } catch (e) {
          console.log(e);
        }
      }
      //@ts-ignore
      let temp = convertToGeoJSONCoordinates(
        geo,
        //@ts-ignore
        `${geometry0.spatialReference.wkid}`,
        "4490"
      );
      let geo4490 = {
        type: geometry0.type,
        spatialReference: {
          wkid: 4490,
        },
      };
      if (geometry0.type == "point") {
        //@ts-ignore
        geo4490.x = temp[0];
        //@ts-ignore
        geo4490.y = temp[1];
      } else if (geometry0.type == "polyline") {
        //@ts-ignore
        geo4490.paths = temp;
      } else if (geometry0.type == "polygon") {
        //@ts-ignore
        geo4490.rings = temp;
      }

      draw(geo4490);
    }
  },
  //地图绽放至对象
  gotoGeometry(esriLib: any, mapObj: any, geometry: any) {
    if (geometry.type == "point") {
      mapObj.centerAndZoom(geometry, 20);
    } else if (geometry.type == "multipoint") {
      if (geometry.points) {
        if (geometry.points.length == 1) {
          let x = 0,
            y = 0;
          if (geometry.points[0].x && geometry.points[0].y) {
            x = geometry.points[0].x;
            y = geometry.points[0].y;
          } else if (geometry.points[0][0] && geometry.points[0][1]) {
            x = geometry.points[0][0];
            y = geometry.points[0][1];
          }

          let pt = new esriLib.Point(
            [x, y],
            new esriLib.SpatialReference({ wkid: 4490 })
          );
          console.log(pt);
          mapObj.centerAndZoom(pt, 17);
        } else if (geometry.points.length == 2) {
          let polyline = new esriLib.Polyline(
            new esriLib.SpatialReference({ wkid: 4490 })
          );
          polyline.addPath(geometry.points);
          if (polyline) {
            console.log(polyline);
            let extent = polyline.getExtent();
            if (extent) {
              mapObj.setExtent(extent.expand(1.8));
            }
          }
        } else {
          let extent = geometry.getExtent();
          if (extent) {
            mapObj.setExtent(extent.expand(1.8));
          }
        }
      }
    } else {
      if (!geometry.getExtent) {
        if (geometry.type == "polygon") {
          geometry = new esriLib.Polygon(geometry);
        } else if (geometry.type == "polyline") {
          geometry = new esriLib.Polyline(geometry);
        }
      }

      let extent = geometry.getExtent();
      if (extent) {
        mapObj.setExtent(extent.expand(1.8));
      }
    }
  },
  gotoExtent(mapObj: any, extent: any) {
    mapObj.setExtent(extent.expand(1.5));
  },

  /**
   * 定位到多个图形的全幅范围
   * @param {*} $esri ESRI对象
   * @param {*} mapObj Map对象
   * @param {*} geometrys 多个geometry对象
   */
  gotoGeometryCenter($esri: any, mapObj: any, geometrys: any) {
    if (geometrys || geometrys.type == "extent") {
      // 如果是4490，直接计算中心点并定位
      let extent = null;
      const geometryService = new $esri.GeometryService(
        `${
          import.meta.env.VITE_BASE_URL_PREFIX
        }/onemap/proxy/query/gis/GeometryServer`
      );

      let params = new $esri.ProjectParameters();
      params.outSR = new $esri.SpatialReference({ wkid: 4490 });

      let convertData = [];
      if (geometrys.type && geometrys.type == "extent") {
        convertData = [geometrys];
      } else {
        for (let item of geometrys) {
          let one = item.geometry;
          if (one.spatialReference.wkid == 4490) {
            if (one.type == "point") {
              let ex = new $esri.Extent({
                type: "point",
                spatialReference: one.spatialReference,
                xmax: one.x,
                xmin: one.x,
                ymax: one.y,
                ymin: one.y,
              });
              if (!extent) {
                extent = ex;
              } else {
                extent.union(ex);
              }
            } else if (one.type == "polyline") {
              let ex = new $esri.Polyline(one);
              if (!extent) extent = ex.getExtent();
              else {
                extent.union(ex.getExtent());
              }
            } else if (one.type == "polygon") {
              let ex = new $esri.Polygon(one);
              if (!extent) extent = ex.getExtent();
              else {
                extent.union(ex.getExtent());
              }
            }
          } else {
            if (one.type == "point") {
              let ex = new $esri.Extent({
                type: "point",
                spatialReference: one.spatialReference,
                xmax: one.x,
                xmin: one.x,
                ymax: one.y,
                ymin: one.y,
              });
              convertData.push(ex.getExtent());
            } else if (one.type == "polyline") {
              let p = new $esri.Polyline(one);
              let ex = p.getExtent();
              convertData.push(ex);
            } else if (one.type == "polygon") {
              let p = new $esri.Polygon(one);
              let ex = p.getExtent();
              // 如果是4524，判断坐标是否需要反转
              if (p.spatialReference.wkid == 4524) {
                if (ex.xmax < 15000000 || ex.ymax > 10000000) {
                  const temp = JSON.parse(JSON.stringify(ex));
                  ex.xmax = temp.ymax;
                  ex.xmin = temp.ymin;
                  ex.ymax = temp.xmax;
                  ex.ymin = temp.xmin;
                }
              }
              convertData.push(ex);
            }
          }
        }
      }

      if (convertData && convertData.length > 0) {
        params.geometries = convertData;
        geometryService.project(
          params,
          (geo: any) => {
            if (geo && geo.length > 0) {
              let extent = null;
              for (let ex of geo) {
                if (!extent) extent = ex;
                else extent.union(ex);
              }

              if (extent) {
                mapObj.setExtent(extent.expand(1.8));
              }
            }
          },
          (err: any) => {
            console.log("定位失败", err);
          }
        );
      } else {
        if (extent) {
          mapObj.setExtent(extent.expand(1.8));
        }
      }
    }
  },

  /**
   * 清除行政区边界
   * @param {*} mapObj
   */
  clearDrawRegion(mapObj: any) {
    let graphicLayer = mapObj.getLayer("temp_region");
    if (graphicLayer) {
      graphicLayer.clear();
    }
  },

  /**
   * 清除所有图形
   * @param {*} mapObj
   */
  clearDraw(mapObj: any) {
    mapObj.graphics.clear();
    let arr = [
      "temp_region",
      "temp_drawGraphicsTempLayer",
      "temp_drawAndGoToGeometry",
    ];
    for (let id of arr) {
      let graphicLayer = mapObj.getLayer(id);
      if (graphicLayer) {
        graphicLayer.clear();
      }
    }
  },

  /**
   * 清除指定Layer的图形
   * @param {*} mapObj
   */
  clearDrawByLayerId(mapObj: any, layerId: any) {
    if (layerId && mapObj && mapObj.loaded) {
      let graphicLayer = mapObj.getLayer(layerId);
      if (graphicLayer) {
        graphicLayer.clear();
      }
    }
  },

  //地图切换专题显示
  switchThematics(mapObj: any, thematicsId: any) {},
  /**
   * 创建简单图标对象
   * @param {Object} $esri
   * @param {String} path
   * @param {String} color
   * @param {Object} outlineConfig
   * @returns
   */
  createSimpleMarkerSymbol(
    $esri: any,
    path: any,
    color: any,
    outlineConfig: any
  ) {
    console.log(
      "🚀 ~ file: map-action.js:315 ~ createSimpleMarkerSymbol ~ outlineConfig:",
      outlineConfig
    );
    let markerSymbol = new $esri.SimpleMarkerSymbol();
    if (outlineConfig) {
      let markerOutLineSymbol = new $esri.SimpleLineSymbol(
        outlineConfig.style,
        new $esri.Color(outlineConfig.color),
        outlineConfig.width
      );
      markerSymbol.setOutline(markerOutLineSymbol);
    } else {
      markerSymbol.setOutline(null);
    }
    if (path) {
      markerSymbol.setStyle("path");
      markerSymbol.setPath(path);
    } else {
      markerSymbol.setStyle("circle");
    }
    markerSymbol.setColor(new $esri.Color(color));
    markerSymbol.setSize("22");
    return markerSymbol;
  },

  /**
   * 初始化绘制对象
   * @param viewer
   * @param id
   */
  initDrawPrimitiveCollection(viewer: any, id: any) {
    let primitive = drawPrimitiveCollection.find((x) => x.id == id);
    if (!primitive) {
      const primitive = new Cesium.PrimitiveCollection();
      drawPrimitiveCollection.push({
        id: id,
        primitive: primitive,
      });
      viewer.scene.primitives.add(primitive);
    }
    return primitive;
  },

  removeDrawPrimitiveCollection(){
    drawPrimitiveCollection = []
  },

  /**
   * 通过ID查找集合里的绘制对象，将图形加入
   * @param viewer
   * @param id
   * @param geometry
   * @param options
   * @returns
   */
  drawPrimitiveGeometry(
    viewer: Cesium.Viewer,
    id: String,
    geometry: any,
    options?: GeometryOptions
  ) {
    let one = drawPrimitiveCollection.find((x) => x.id == id);
    let primitive: Cesium.PrimitiveCollection;
    if (!one) {
      primitive = this.initDrawPrimitiveCollection(viewer, id);
    } else {
      primitive = one.primitive;
    }    
    if (primitive) {
      if(geometry.paths) {  //线
        const polylinePrimitive = this.createPolylineByGeometry(viewer, geometry, options);
        primitive.add(polylinePrimitive);
      } else if (geometry.x && geometry.y) {  //点
        this.createPointByGeometry(viewer, geometry, options).then((pointPrimitive) => {
          primitive.add(pointPrimitive);
        });
        return
      } else {
        const polygonPrimitive = this.createPolygonByGeometry(
          viewer,
          geometry,
          options
        );
        primitive.add(polygonPrimitive);
        return;
      }

      //   switch (geometry.type){
      //     case Geometry_c3d_Type.polygon:
      //       const polygonPrimitive = createPolygonByGeometry(viewer, geometry, options);
      //       primitive.add(polygonPrimitive);
      //       return
      //     case Geometry_c3d_Type.polyline:
      //       const polylinePrimitive = createPolylineByGeometry(viewer, geometry, options);
      //       primitive.add(polylinePrimitive);
      //       return
      //     case Geometry_c3d_Type.point:
      //       createPointByGeometry(viewer, geometry, options).then((pointPrimitive) => {
      //         primitive.add(pointPrimitive);
      //       });
      //       return
      //     case Geometry_c3d_Type.multipoint:
      //       createMultiPointByGeometry(viewer, geometry, options).then((multiPointPrimitive) => {
      //         primitive.add(multiPointPrimitive);
      //       });
      //       return
      //   }
    }
  },

  async createPointByGeometry(viewer:Cesium.Viewer, geometry:any, options?:GeometryOptions){
    let wkid = geometry.spatialReference.wkid;
    /** 转换经纬度*/
    let wgs84DegreePoint = this.convertPointToWGS84(wkid, [geometry.x, geometry.y]);
  
    // @ts-ignore
    let pointPrimitive:Cesium.PrimitiveCollection = await this.createPointGeometryPrimitive(viewer, wgs84DegreePoint, options);
    return pointPrimitive;
  },

  async createPointGeometryPrimitive(viewer:Cesium.Viewer, degreePoint:Array<number>, options?:GeometryOptions){

    const points = new Cesium.PointPrimitiveCollection();
    let cartesian = Cesium.Cartesian3.fromDegrees(degreePoint[0], degreePoint[1]);
    let elliCartographic = Cesium.Ellipsoid.WGS84.cartesianToCartographic(cartesian);
    let data = await Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [elliCartographic]);
    let lng = Cesium.Math.toDegrees(data[0].longitude);
    let lat = Cesium.Math.toDegrees(data[0].latitude);
    let height = data[0].height;
    let point = new Cesium.PointPrimitive();
  
    point.position = Cesium.Cartesian3.fromDegrees(lng, lat, height);
    point.color = options?.outlineColor ? Cesium.Color.fromCssColorString(options.outlineColor) : pointDefaultColor;
    point.pixelSize = 10;
    point.disableDepthTestDistance = Number.POSITIVE_INFINITY;
    points.add(point)
  
    return points;
  },


  // convertPointToWGS84(wkid:number, degree:number[]) :Array<number>{
  //     // @ts-ignore
  //     const newProj4 = proj4(PROJECTION[wkid.toString()],PROJECTION.WGS84Projection,degree);
  //     return [newProj4[0], newProj4[1]];
  // },

  createPolygonByGeometry(
    viewer: Cesium.Viewer,
    geometry: any,
    options?: GeometryOptions
  ) {
    /** 获取geometry的坐标系*/
    let wkid = geometry.spatialReference.wkid;
    /** 获取geometry的rings*/
    let geometryRings = geometry.rings;
    /** 将geometryRings转换坐标 并转为Array<Array<number>>的degreeRings*/
    let degreeRings = this.convertDegreeRingsToWGS84DegreeRings(
      geometryRings,
      wkid
    );
    /** 创建 polygon primitive*/
    return this.createPolygonGeometryPrimitive(degreeRings, options);
  },


  createPolylineByGeometry(viewer:Cesium.Viewer, geometry:any, options?:GeometryOptions){
    /** 获取geometry的坐标系*/
    let wkid = geometry.spatialReference.wkid;
    /** 获取geometry的paths*/
    let geometryPaths = geometry.paths;
    /** 将geometryPaths转换坐标 并转为Array<Array<number>>的degreePaths*/
    let degreePaths = this.convertDegreePathsToWGS84DegreePaths(geometryPaths, wkid);
    /** 创建 polyline primitive*/
    return this.createPolylineGeometryPrimitive(degreePaths, options);
  },

convertDegreePathsToWGS84DegreePaths(geometryPaths:Array<Array<Array<number>>>, wkid:PROJECTION_Type){
    let wgs84Paths:Array<Array<number>> = [];
    for(let i =0; i < geometryPaths.length; i++){
        let wgs84Ring: Array<number> = [];
        for(let j=0; j < geometryPaths[i].length; j++){
            const newProjPoint = this.convertPointToWGS84(wkid, [geometryPaths[i][j][0], geometryPaths[i][j][1]]);
            wgs84Ring.push(newProjPoint[0]);
            wgs84Ring.push(newProjPoint[1]);
        }
        wgs84Paths.push(wgs84Ring);
    }
    return wgs84Paths;
},

  convertDegreeRingsToWGS84DegreeRings(
    geometryRings: Array<Array<Array<number>>>,
    wkid: PROJECTION_Type
  ) {
    let wgs84Rings: Array<Array<number>> = [];
    for (let i = 0; i < geometryRings.length; i++) {
      let wgs84Ring: Array<number> = [];
      for (let j = 0; j < geometryRings[i].length; j++) {
        const newProjPoint = this.convertPointToWGS84(wkid, [
          geometryRings[i][j][0],
          geometryRings[i][j][1],
        ]);
        wgs84Ring.push(newProjPoint[0]);
        wgs84Ring.push(newProjPoint[1]);
      }
      wgs84Rings.push(wgs84Ring);
    }
    return wgs84Rings;
  },

  convertPointToWGS84(wkid: PROJECTION_Type, degree: number[]): Array<number> {
    // @ts-ignore
    return [degree[0], degree[1]];
  },

  /**
   * @description  创建面与线primitive结合的 primitiveCollection
   * @param degreeRings
   * @param options
   */
  createPolygonGeometryPrimitive(
    degreeRings: Array<Array<number>>,
    options?: GeometryOptions
  ) {
    /** 处理degreeRings 转换为polygonHierarchy */
    // let holes:Cesium.PolygonHierarchy[] = [];
    let holes: Cesium.PolygonHierarchy[] = [];
    const len = degreeRings.length;
    for (let i = len - 1; i >= 0; i--) {
      holes = [
        new Cesium.PolygonHierarchy(
          Cesium.Cartesian3.fromDegreesArray(degreeRings[i]),
          holes
        ),
      ];
    }

    const polygonInstance = new Cesium.GeometryInstance({
      geometry: new Cesium.PolygonGeometry({
        polygonHierarchy: holes[0],
      }),
      // id : ''
    });
    /** 循环degreeRings创建线的GeometryInstances*/
    let polylineInstances: Array<Cesium.GeometryInstance> = [];
    for (let i = 0; i < degreeRings.length; i++) {
      const polygonInstance = new Cesium.GeometryInstance({
        geometry: new Cesium.GroundPolylineGeometry({
          positions: Cesium.Cartesian3.fromDegreesArray(degreeRings[i]),
          width: 4.0,
        }),
        // id : ''
      });
      polylineInstances.push(polygonInstance);
    }
    /** 创建一个primitiveCollection存储 线 面 的primitive*/
    let groundPrimitive = new Cesium.PrimitiveCollection();
    let polygonPrimitive = new Cesium.GroundPrimitive({
      geometryInstances: polygonInstance,
      appearance: new Cesium.EllipsoidSurfaceAppearance({
        translucent: true,
        material: new Cesium.Material({
          fabric: {
            type: "Color",
            uniforms: {
              color: options?.fillColor
                ? Cesium.Color.fromCssColorString(options.fillColor)
                : polygonDefaultColor,
            },
          },
        }),
      }),
    });
    let polylinePrimitive = new Cesium.GroundPolylinePrimitive({
      geometryInstances: polylineInstances,
      releaseGeometryInstances: false,
      appearance: new Cesium.PolylineMaterialAppearance({
        translucent: true,
        material: new Cesium.Material({
          fabric: {
            type: "Color",
            uniforms: {
              color: options?.outlineColor
                ? Cesium.Color.fromCssColorString(options.outlineColor)
                : polylineDefaultColor,
            },
          },
        }),
      }),
    });
    groundPrimitive.add(polygonPrimitive);
    groundPrimitive.add(polylinePrimitive);
    return groundPrimitive;
  },

  /**
 * @description  创建面与线primitive结合的 primitiveCollection
 * @param degreePaths
 * @param options
 */
createPolylineGeometryPrimitive(degreePaths: Array<Array<number>>, options?:GeometryOptions){
  /** 循环degreePaths创建线的GeometryInstances*/
  let polylineInstances:Array<Cesium.GeometryInstance> = [];
  for(let i = 0; i < degreePaths.length; i++){
    const polylineInstance = new Cesium.GeometryInstance({
      geometry: new Cesium.GroundPolylineGeometry({
        positions: Cesium.Cartesian3.fromDegreesArray(degreePaths[i]),
        width : 4.0
      }),
      // id : ''
    });
    polylineInstances.push(polylineInstance);
  }
  /** 创建一个primitiveCollection存储 线 面 的primitive*/
  // let groundPrimitive = new Cesium.PrimitiveCollection();
  // let polylinePrimitive = new Cesium.GroundPolylinePrimitive({
    return new Cesium.GroundPolylinePrimitive({
    geometryInstances: polylineInstances,
    releaseGeometryInstances:false,
    appearance : new Cesium.PolylineMaterialAppearance({
      translucent: true,
      material: new Cesium.Material({
        fabric: {
          type: 'Color',
          uniforms: {
            color: options?.outlineColor ? Cesium.Color.fromCssColorString(options?.outlineColor) : polylineDefaultColor
          }
        }
      })
    })
  })
  // groundPrimitive.add(polylinePrimitive);
  // return groundPrimitive;
},

  /**
   * 通过ID查找绘制对象，清除绘制
   * @param id
   */
  removeGeometryById(id: String) {
    let one = drawPrimitiveCollection.find((x) => x.id == id);
    if (one) {
      const primitive = one.primitive;
      primitive && primitive.length > 0 && primitive.removeAll();
    }
  },

  locateGeometryArr(viewer: Cesium.Viewer, geometryArr: any) {
    let degreeArr: Array<number> = [];
    
    geometryArr.forEach((geometry: any) => {
      const geometryDegreeArr = this.getPolygonGeometryDegreeArr(geometry);
      for (let i = 0; i < geometryDegreeArr.length; i++) {
        degreeArr.push(geometryDegreeArr[i]);
      }
    });
    this.locateByDegreeArray(viewer, degreeArr);
  },

  getPolygonGeometryDegreeArr(geometry: any) {
    if(geometry.x && geometry.y) {
      let degreeArr:Array<number> = [];
      let degreePoint = this.convertPointToWGS84(geometry.spatialReference.wkid || "4490", [geometry.x, geometry.y]);
      degreeArr.push(degreePoint[0]);
      degreeArr.push(degreePoint[1]);
      return degreeArr;
    } else if (geometry.paths) {
      let degreeArr:Array<number> = [];
      let paths:Array<Array<Array<number>>> = geometry.paths;
      paths.forEach((path) => {
          path.forEach((degreePoint) => {
              degreeArr.push(degreePoint[0]);
              degreeArr.push(degreePoint[1]);
          })
      })
      return degreeArr;
    } else {
      let degreeArr: Array<number> = [];
      let rings: Array<Array<Array<number>>> = geometry.rings;
      rings.forEach((ring) => {
        ring.forEach((degreePoint) => {
          degreeArr.push(degreePoint[0]);
          degreeArr.push(degreePoint[1]);
        });
      });
      return degreeArr;
    }

  },

  /**
   * @description 定位
   * @param viewer
   * @param degreeArray [lon,lat,lon,lat...]
   * @param scale
   * @param duration
   */ locateByDegreeArray(
    viewer: Cesium.Viewer,
    degreeArray: Array<number>,
    scale = 2,
    duration: number = 3
  ) {
    let heading = viewer.camera.heading;
    let pitch = viewer.camera.pitch;
    this.flyToRectangle(
      viewer,
      Cesium.Cartesian3.fromDegreesArray(degreeArray),
      heading,
      pitch,
      scale,
      duration
    );
    // let rectangle = Cesium.Rectangle.fromCartesianArray(Cesium.Cartesian3.fromDegreesArray(degreeArray));
    // let boundingSphere = Cesium.BoundingSphere.fromRectangle3D(rectangle);
    //
    // let range = boundingSphere.radius*5.0;
    // viewer.camera.flyToBoundingSphere(boundingSphere, {
    //     offset: new Cesium.HeadingPitchRange(heading, pitch, range)
    // });
  },

  /***
   * @description 定位到一串笛卡尔坐标点
   * @param viewer viewer对象
   * @param cartesian3s   一串笛卡尔坐标点
   * @param heading
   * @param pitch
   * @param scale     构建的3d球体的比例
   * @param duration  飞行时间
   * @param callBack  定位完成后的回调函数
   */
  flyToRectangle(
    viewer: Cesium.Viewer,
    cartesian3s: Array<Cesium.Cartesian3>,
    heading: number,
    pitch: number,
    scale = 1.0,
    duration = 3,
    callBack = () => {}
  ) {
    if (!viewer) {
      console.log("三维球未初始化！");
      return;
    }
    if (!Array.isArray(cartesian3s)) {
      console.log("定位范围不对！");
      return;
    }
    if (scale < 0.1) {
      scale = 1.0;
    }
    let rec = Cesium.Rectangle.fromCartesianArray(cartesian3s);
    
    let boundingSphere = Cesium.BoundingSphere.fromRectangle3D(rec);
    boundingSphere.radius = boundingSphere.radius * scale && boundingSphere.radius * scale > 100 ? boundingSphere.radius * scale : 100;
    // if(boundingSphere.radius < 80){
    //   boundingSphere.radius = 80
    // }
    viewer.camera.flyToBoundingSphere(boundingSphere, {
      duration: duration,
      maximumHeight: undefined,
      complete: function () {
        if (callBack) {
          callBack();
        } else {
          console.log("定位失败！");
        }
      },
      cancel: function () {
        console.log("定位取消！");
      },
      offset: {
        heading: heading,
        pitch: pitch,
        range: 0.0,
      },
    });
  },
};
