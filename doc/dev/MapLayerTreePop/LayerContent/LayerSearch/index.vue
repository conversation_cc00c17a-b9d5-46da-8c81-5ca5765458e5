<template>
  <div class="header">
    <el-tabs v-model="activeName" type="card" @tab-change="tabHandle">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="'tab' + index"
        :label="item.label"
        :name="item.name"
        :lazy="true"
      >
        <component
          ref="queryRef"
          v-show="item.name === activeName"
          :is="item.component"
          :PropStore="props.PropStore"
          :LayerStore="props.LayerStore"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, markRaw } from "vue";
import AttrQeury from "./AttrQuery/index.vue";
import SpaceSearch from "./SpaceQuery/index.vue";
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: false,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});
//getOnemap,getOptions
// const _inOnemap = getOnemap(props.MapControlName);
// const _inOptions =getOptions(props.MapControlName);

const queryRef = ref(null);
const activeName = ref("layerSearch");
const tabs = ref([
  {
    name: "layerSearch",
    label: "属性查询",
    component: markRaw(AttrQeury),
  },
  {
    name: "rangeSearch",
    label: "空间查询",
    component: markRaw(SpaceSearch),
  },
]);

const tabHandle = () => {
	const unfold = document.querySelector("#unfold") as any;
	const tabQueryContent = document.querySelector("#tab-query-content") as any;
	if (unfold && tabQueryContent) {
		tabQueryContent.style.height = (unfold.clientHeight - 420) + "px"
	}
}
</script>
<style lang="scss" scoped>
.header {
  height: calc(100vh - 160px);
  margin-top: 8px;

  // #4CACFE
  :deep(.el-tabs__nav-scroll) {
    width: 370px !important;
    margin: 0 auto !important;
  }

  :deep(.el-tabs__nav) {
    border: none !important;
  }

  :deep(.el-tabs__item) {
    height: 30px !important;
    line-height: 30px !important;
    font-size: 12px;
    background: #d9d9d9;
    margin: 0 2px;
    border-radius: 4px 4px 0 0;
    color: #000000 !important;
    padding: 0 19px;

    &:first-child {
      border-left: 1px solid #e4e7ed !important;
    }

    &.is-active {
      background: #fff;
      color: #000000;
      border-left: 1px solid #e4e7ed;
      border-right: 1px solid #e4e7ed;
      border-top: 1px solid #e4e7ed;
      height: 31px !important;
      line-height: 31px !important;
    }
  }

  :deep(.el-tabs__header) {
    margin-bottom: 10px;
  }
}

.wid100 {
  width: 100%;
}
</style>
