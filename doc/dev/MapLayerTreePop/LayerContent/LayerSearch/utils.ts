
export const parseAttributeVal = (val:any, valueType: string, dict: any): any => {
	if (valueType == undefined) return val
	switch (valueType) {
		case "0": return val
		case "1": {
			// 处理null值
			if (dict["null"]) {
				if ([null, undefined, "null", ""].find(x => x == val)) {
					return dict["null"]
				}
			}

			if (dict[val]) {
				return dict[val]
			}
			break
		}
		case "3": {
			let a = ""
			let urls = JSON.parse(val)
			for (let url of Object.getOwnPropertyNames(urls)) {
				if (a) a += "<br>"
				a += `<a style="color:#409eff" href="${urls[url]}" target="__blank__">${url}</a>`
			}
			return a
		}
		case "4": {
			// 非数字类型不做处理
			if (isNaN(val)) return val
			if (val == "") return val

			try {
				return parseFloat(val).toFixed(Number(dict))
			} catch(e) {
				return val
			}
		}
		case "5": {
			// 时间格式化
			try {
				return val
			} catch(e) {
				return val
			}
		}
	}

	return val
}