/**
 * 以下为业务系统迁移过来的代码
 */

import request from "../../../packages/utils/axios/";
import { IMapLayer } from "../../../packages/onemapkit";
const baseUrl = "/dev/api";

/**
 * 收藏类型
 */
export enum IFavoriteType {
	LayerService = 0,	// 普通图层
	OnlineService = 1	// 在线服务图层
}


export class FavoritesAPI {
	private layerStore: any;
	constructor(_layerStore: any) {
		this.layerStore = _layerStore;

	}
	public async get() {
		const res = await request.post({
			url: `${baseUrl}/api/map/collection/list`,
			data: {}
		});
		// 解析标准化服务列表数据
		let resultData:any[] = [];
		if (res && res.data && res.data.list) {
			// 解析普通图层
			const layerList = res.data.list.filter((x:any) => x.mapType == IFavoriteType.LayerService);
			for(let item of layerList) {
				let one = this.layerStore.MapLayers.find((x:IMapLayer) => x.layerid == item.mapId);
				if (one) {
					one.favId = item.id;
					one.mapType = IFavoriteType.LayerService;
					one.favorited = true;
					resultData.push(one);
				}
			}

			// 解析在线服务(demo暂不解析)
			const onlineList = res.data.list.filter((x:any) => x.mapType == IFavoriteType.OnlineService);
			const onlineLayerList:any[] = [];
			for(let item of onlineLayerList) {
				item.favId = item.id;
				item.mapType = IFavoriteType.OnlineService;
				item.favorited = true;
				resultData.push(item);
			}
		}

		return resultData;
	}
	public async add(data: any) {
		return request.post({
			url: `${baseUrl}/api/map/collection/add`, data
		});
	}
	public async remove(id: string) {
		return request.delete({
			url: `${baseUrl}/api/map/collection/delete`, 
			params: {
				id: id
			}
		});
	}
	updateEvent: null | Function = null;
}

/**
 * 专题列表接口
 * @returns 
 */
export class MaticListAPI {

		public async get() {
			const data = {};
			const result:any = await request.post({
				url: `${baseUrl}/api/map/thematic/list`, data
			});;
			if (result && result.data && result.data.count > 0) {
				return result.data.list;
			} else return [];
		}
		public async add(data: any) {
			return request.post({
				url: `${baseUrl}/api/map/thematic/add`, data
			});
		}
		public async delete(id: string) {
			return request.delete({
				url: `${baseUrl}/api/map/thematic/delete`, 
				params: {
					id: id
				}
			});
		}
		update: Function | null = null;
}