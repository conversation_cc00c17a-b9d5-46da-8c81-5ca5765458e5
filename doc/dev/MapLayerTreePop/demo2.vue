<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapReadyEvent="_MapReadyEvent"
    />
    <LayerContent
      :PropStore="PropStore"
      :LayerStore="layerStore"
      :MapTreeHeight="600"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  Map,
  LayerContent,
  defaultParameter,
  InitMapControl,
} from "../../../packages/onemapkit";


/**第一步：导入图层参数*/
import {
  layerStore,
  PropStore,
  ServiceUrl,
  BaseMapLayerUrl,
  _Onemap,
  initShowMapLayer,
  CustomTools,
} from "../../layer";

InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  DrawTools: defaultParameter.defaultEditToolItems,
  MapTools: {
    Toolset: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});
const _MapReadyEvent = () => {
  initShowMapLayer();
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc( 100vh - 300px );
  position: relative;
}
</style>
