<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef" 
      :Onemap="_Onemap"
      :Store="{
        layers: layerStore,
        Props: PropStore,
      }"
      :Options="_Options"
    ></Map>
    <CommonTool />
    <draw-panel
      :Onemap="_Onemap"
      :Store="{
        layers: layerStore,
        Props: PropStore,
      }"
    />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import {
  Map,
  DrawPanel,
  mapType, getOption
} from "../../../packages/onemapkit";

import * as CesiumTools from "@onemapkit/cesium-tools";

/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */
//导入图层参数
import {
  layerStore,
	_Onemap,
  ServiceUrl,
  BaseMapLayerUrl,
  initShowMapLayer
} from "../../layer"; 

const MapControlRef = ref(null);

const _Options = computed(() => {
  if (_Onemap.MapType.value === mapType.cesium) {
    return Object.assign(getOption(), {
      id: "mapviewerContainer",
      BASE_URL: "ThirdParty",
      mapType: mapType.cesium,
      // MapViewer: MapViewer,
      BaseMapLayer: BaseMapLayerUrl(),
      TerrainUrl: ServiceUrl.TerrainUrl,
    });
  } else {
    return {
      id: "mapviewerContainer",
      mapType: mapType.arcgis,
      // mapViewer: MapViewer,
    };
  }
});

//mounted
onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 500px;
  position: relative;
}
</style>
