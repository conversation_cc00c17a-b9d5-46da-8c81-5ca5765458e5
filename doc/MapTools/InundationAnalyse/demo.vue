<template>
    <div class="container" id="parentContainer">
        <Map ref="MapControlRef" :LayerStore="layerStore" :PropStore="_PropStore" @MapReadyEvent="_MapReadyEvent" />
      <!-- <MapControl
        :LayerStore="layerStore"
        @MapRealtimeEvent="_MapRealtimeEvent"
        @MapClickEvent="_MapClickEvent"
        @MapReadyEvent="_MapReadyEvent"
        /> -->
        <InundationAnalyse
          :Onemap="_Onemap"
        />
    </div>
  </template>
  <script lang="ts" setup> 
  import { onMounted, ref, reactive } from "vue";
  import {
    MapControl,
    InitMapControl,
    type IPosition,
    InundationAnalyse,
    OnemapEvent,
    defaultParameter,
  } from "../../../packages/onemapkit";
  
  import axios from "axios";
  import {
    PropStore as _PropStore,
    layerStore,
    _Onemap,
    ServiceUrl,
    CustomTools,initShowMapLayer,BaseMapLayerUrl
  } from "../../layer";
  //#region
  InitMapControl(_Onemap, {
        BASE_URL: "ThirdParty",
        MapControlName: "mainMapControl",
        BaseMapLayer: BaseMapLayerUrl(),
        TerrainUrl: "/dev-api/cesium/mapdata/08dcc357-5831-4d5d-8b9f-045c1e8de241",
        MapLayerTree: Object.assign(
            defaultParameter.defaultLayerContentTab,
            defaultParameter.defaultMapTreeTab,
            {
                // 是否显示详细底图面板
                basemap: {
                    visible: true
                }
            }
        ),
        DrawTools: defaultParameter.defaultEditToolItems,
        MapTools: {
            Toolset: CustomTools,
            CustomToolSize: {
                width: 400,
                height: 420,
            },
        },
    });

    const _MapReadyEvent = () => {
        initShowMapLayer();
    };
  
  //mounted
  onMounted(async () => {});
  
  const debugDock = reactive({ right: 100, top: 50 });
  const _marginSize = (right: number, top: number) => {
    debugDock.right = right;
    debugDock.top = top;
  };
  
  //#endregion
  </script>
  <style lang="scss" scoped>
  .container {
    width: 100%;
    height: 700px;
    position: relative;
  }
  
  .checkbox-button {
    position: relative;
  }
  
  .checkbox-button button {
    padding-right: 30px; /* 留出足够空间给打勾 */
    position: relative;
  }
  
  .checkbox-button input[type="checkbox"] {
    opacity: 0; /* 使checkbox不可见 */
    position: absolute;
    top: 0;
    right: 0;
  }
  
  .checkbox-button input[type="checkbox"]:checked + button:after {
    content: "✔"; /* 打勾的字符 */
    position: absolute;
    top: 0;
    right: 0;
    font-size: 20px; /* 打勾的大小 */
    color: green; /* 打勾的颜色 */
  }
  </style>
  