<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :Options="Options"
      :MapReadyEvent="MapReadyEvent"
    /> 
    <location-panel />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import {
  Map,
  LocationPanel,
  mapType,
} from "../../../packages/onemapkit"; 
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools"; 

/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */
//导入图层参数
import {
  _Onemap,
  ServiceUrl,
  BaseMapLayerUrl,
  initShowMapLayer
} from "../../layer";

const MapControlRef = ref(null);

let MapViewer: Cesium.Viewer | undefined;
const Options = computed(() => {
  if (_Onemap.MapType.value === mapType.cesium) {
    return Object.assign(getOption(), {
      MapControlName: "mainMapControl",
  		BASE_URL: "ThirdParty",
      mapType: mapType.cesium,
      MapViewer: MapViewer,
      BaseMapLayer: BaseMapLayerUrl(),
      TerrainUrl: ServiceUrl.TerrainUrl,
    });
  } else {
    return {
      mapType: mapType.arcgis,
      MapViewer: MapViewer,
    };
  }
});

const MapReadyEvent = (obj: any, avg: any) => {
  initShowMapLayer();
};
//mounted
onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
