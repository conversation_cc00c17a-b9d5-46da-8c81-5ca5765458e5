<template>
  <div class="container" id="parentContainer">
    <MapControl
      ref="MapControlRef" 
      :PropStore="PropStore"
      :LayerStore="layerStore" 
      @MapReadyEvent="_MapReadyEvent"
    ></MapControl>
    <TimeSliderPanel
      :Onemap="_Onemap"
      :PropStore="PropStore"
      :LayerStore="layerStore"
    />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";
import {
  mapType,
  Map,
  TimeSliderPanel, MapControl, InitMapControl,
  defaultParameter 
} from "../../../packages/onemapkit";
import {
  layerStore,
  _Onemap,
  PropStore,
  BaseMapLayerUrl,CustomTools,
  ServiceUrl,initShowMapLayer
} from "../../layer";


/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */
//导入图层参数

_Onemap.setMapType(mapType.arcgis);


const MapControlRef = ref(null);

let MapViewer: Cesium.Viewer | undefined;
//这里也可以获取MapViewer对象： Options.mapViewer
let viewer: Cesium.Viewer | undefined;

  
const _Options = {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapLayerTree: Object.assign(
    defaultParameter.defaultLayerContentTab,
    defaultParameter.defaultMapTreeTab
  ),
  MapTools: {
    CustomTools: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
};
InitMapControl(_Onemap, _Options);

const _MapReadyEvent = (obj: any, avg: any) => {
  initShowMapLayer();
};
//mounted
onMounted(async () => {});
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
