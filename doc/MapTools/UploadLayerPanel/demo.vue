<template>
  <div class="container" id="parentContainer">
    <Map
      ref="MapControlRef"
      :Onemap="_Onemap"
      :MapReadyEvent="MapReadyEvent"
      :PropStore="PropStore"
      :LayerStore="layerStore"
      :Options="_Options"
    ></Map> 
    <UploadLayerPanel
      :Onemap="_Onemap"
      :PropStore="PropStore"
      :LayerStore="layerStore"
      :Store="{
        layers: layerStore,
        Props: PropStore,
        ToolsProps: PropStore.ToolsProps,
      }"
    />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import {
  Map,
  mapType,
  UploadLayerPanel,
  OnemapClass,getOptions
} from "../../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";

/**第一步：实例化图层参数
 * 1.initMaplayer参数  函数，用法 initMaplayer();
 * 2.layerStore 是一个存放图层的Store对象
 */
//导入图层参数
import { layerStore, PropStore,_Onemap, ServiceUrl, BaseMapLayerUrl } from "../../layer";

PropStore.MapType = mapType.arcgis;
_Onemap.setMapType(mapType.arcgis);


const MapControlRef = ref(null);
Cesium.Ion.defaultAccessToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjZjViZWZjMC0zMmUxLTQ5NWEtYjZkNS0wM2VjMmM1NWE1YTgiLCJpZCI6OTEwODksImlhdCI6MTY1MDc5MjI2MH0.IhkngSXwFixfux7Z4HBWmAw-pFhqfDzmyUO0v3lLy_4";
  
//初始地图类型及初始化参数
const _Options = computed(() => {
  if (_Onemap.MapType.value === mapType.cesium) {
    return Object.assign(getOptions(), {
      id: "mapviewerContainer",
      BASE_URL: "ThirdParty",
      // mapType: mapType.cesium,
      BaseMapLayer: BaseMapLayerUrl(),
      TerrainUrl: ServiceUrl.TerrainUrl,
    });
  } else {
    return {
      id: "mapviewerContainer",
      // mapType: mapType.arcgis,
      BaseMapLayer: BaseMapLayerUrl(),
      // TerrainUrl: ServiceUrl.TerrainUrl,
      // BASE_URL: "ThirdParty",
    };
  }
}); 
//mounted
onMounted(async () => {});
const MapReadyEvent = (obj: any, avg: any) => {};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
