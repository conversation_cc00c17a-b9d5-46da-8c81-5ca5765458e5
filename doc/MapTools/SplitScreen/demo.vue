<!-- html -->
<template>
  <el-button type="primary" @click="funsplitscreeen">分屏(Arcgis) </el-button>
  <div class="container" id="parentContainer">
    <MapControl 
    ref="MapControlREF"
      MapControlName="mainMapControl"
      :PropStore="PropStore"
      :LayerStore="layerStore"
    ></MapControl>
    <PopPanel
      ref="PopPanelREF"
      v-if="PopPanelREFVisible"
      :height="800"
      @close="popanelClose()"
    >
      <splitscreen
        MapControlName="mainMapControl"
        :PropStore="PropStore"
        :LayerStore="layerStore" 
      />
    </PopPanel>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";
import {
  MapControl,
  splitscreen,
  PopPanel,
  InitMapControl, getOptions
} from "../../../packages/onemapkit";
 
/**第一步： 导入图层参数*/
import {
  _Onemap,
  PropStore,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  CustomTools,
} from "../../layer"; 
const PopPanelREFVisible = ref(false);
/** 第二步：配置公共参数 MapControlRef*/
const _Options = computed(() => {
  return Object.assign(getOptions(), {
    BASE_URL: "ThirdParty",
    BaseMapLayer: BaseMapLayerUrl(),
    TerrainUrl: ServiceUrl.TerrainUrl,
    requestRenderMode:false
  });
});
InitMapControl(_Onemap, {
  MapControlName: "mainMapControl",
  Options: _Options.value, 
  MapTools: {
    CustomTools: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 420,
    },
  },
});
const MapControlREF=ref();
const popanelClose=()=>{
  PopPanelREFVisible.value=false; 
} 
onMounted(() => {});
function funsplitscreeen() {
  PopPanelREFVisible.value = true;  
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 600px;
  position: relative;
}
</style>
