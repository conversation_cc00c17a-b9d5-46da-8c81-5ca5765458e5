<!-- html -->
<template>
  表单绑定值： {{ formDataValue }}
  <uform  
    :formDataValue="formDataValue"
    :formDataFrame="formDataFrame.data"
    :ustyle="formDataFrame.ustyle"
  ></uform>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { uform,Utils } from "../../packages/onemapkit";
interface Option {
  key: number;
  label: string;
  initial: string;
}
const generateData = () => {
  const data: Option[] = [];
  const states = [
    "California",
    "Illinois",
    "Maryland",
    "Texas",
    "Florida",
    "Colorado",
    "Connecticut ",
  ];




  const initials = ["CA", "IL", "MD", "TX", "FL", "CO", "CT"];
  states.forEach((city, index) => {
    data.push({
      label: city,
      key: index,
      initial: initials[index],
    });
  });
  return data;
};

const __data = ref<Option[]>(generateData());

const formDataFrame = {
  /**name是绑定值的名称，相当于field，value是这个字段的默认值 */
  data: [
    [
      {
        type: "input",
        label: "输入框",
        name: "name",
        value: "yusy",
        changeEvent: (_value) => {
          console.log(_value);
        },
        //以上是每个组件的必填属性
        //以下为组件的属性
        placeholder: "input val",
        size: "small",
        disabled: false,
        textarea: "input",
        showpassword: false,
        maxlength: 20,
        minlength: 0,
        readonly: false,
        required:true,
        style: "margin:10 20px 100px 100px",
      },
      {
        type: "inputnumber",
        label: "数字输入框",
        name: "name",
        value: "yusy",
        changeEvent: (_value) => {
          console.log(_value);
        },
        //以上是每个组件的必填属性
        //以下为组件的属性
        placeholder: "input val",
        size: "small",
        disabled: false,
        textarea: "input",
        showpassword: false,
        min: 0,
        max: 10,
        step:1,
        precision:0,
        controls:true,
        controlsposition:'',
        readonly: false,
        required:true,
        style: "margin:10 20px 100px 100px",
      },
      {
        type: "select",
        label: "下拉框",
        name: "select1",
        value: {
          label: "lblBBB",
          name: "BBB",
          value: "BBB",
          disabled: true,
        },
        required:true,
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        placeholder: "selectval",
        size: "small",
        disabled: false,
        multiple: true,
        filterable: true,
        options: [
          {
            label: "lblAAA",
            name: "AAA",
            value: "AAA",
          },
          {
            label: "lblBBB",
            name: "BBB",
            value: "BBB",
            disabled: true,
          },
          {
            label: "lblCCC",
            name: "CCC",
            value: "CCC",
          },
        ],
        style: "margin:0px",
      },
    ],
    [
      {
        type: "check",
        label: "复选框",
        name: "check",
        value: false,
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        size: "small",
        disabled: false,
        border: false,
        checked: false,
        style: "margin:0px",
      },
      {
        type: "check",
        label: "复选框",
        showLabel: false,
        name: "check1",
        value: false,
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        size: "small",
        disabled: false,
        border: true,
        checked: true,
        style: "margin:0px",
      },
    ],
    [
      {
        type: "checkboxgroup",
        label: "复选框组",
        name: "checkboxgroup",
        value: ['subcheckboxLabel'],
        subtype: "checkbox",
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        size: "small",
        border: true,
        style: "margin:0px",
        items: [
          {
            key: "subcheckboxLabel",
            label: "subcheckboxLabel",
            disabled: false,
            checked: false,
          },
          {
            key: "subcheckboxLabel2",
            label: "subcheckboxLabel2",
            disabled: false,
            checked: true,
          },
        ],
      },
    ],
    [
      {
        type: "checkboxgroup",
        label: "按钮复选组",
        name: "checkboxgroupButton",
        value: ['subcheckboxLabel'],
        subtype: "checkboxbutton",
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        size: "small",
        border: true,
        style: "margin:0px",
        items: [
          {
            key: "subcheckboxLabel",
            label: "subcheckboxLabel",
            disabled: false,
            checked: false,
          },
          {
            key: "subcheckboxLabel2",
            label: "subcheckboxLabel2",
            disabled: false,
            checked: false,
          },
        ],
      },
    ],
    [
      {
        type: "radio",
        label: "单选",
        name: "radio",
        value: 'lblCCC',
        size: "small",
        disabled: false,
        //style: "margin:0px",
        group: [
          {
            label: "lblAAA",
            name: "AAAname",
            disabled: true,
            style: "margin:0px",
          },
          {
            label: "lblBBB",
            name: "BBBname",
            disabled: false,
            style: "margin:0px",
          },
          {
            label: "lblCCC",
            name: "CCCname",
          },
        ],
        changeEvent: (arg) => {
          console.log(arg);
        },
      },
    ],
    [
      {
        type: "slider",
        label: "滑动条",
        name: "slider",
        value: 3,
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        min: -10,
        max: 10,
        step: 0.1,
        disabled: false,
        showinput: true,
        showinputcontrols: true,
        size: "small",
        inputsize: "small",
        vertical: false,
        height: "50px",
        width: "400px",
        debounce: 1000,
        //style: "margin:0px;width:400px",
      },
    ],
    [
      {
        type: "switch",
        label: "开关",
        name: "switch",
        value: "开启",
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        size: "small",
        disabled: false,
        activetext: "开启",
        inactivetext: "关闭",
        activevalue: true,
        inactivevalue: false,
        inlineprompt: true,
        validateevent: true,
        style: "margin:0px",
      },
    ],
    [
      {
        type: "datepicker",
        label: "日期选择",
        name: "datepicker",
        value: "2023-09-26",
        changeEvent: (_value) => {
          console.log(_value);
        },
        //********** */
        readonly: false,
        disabled: false,
        size: "small",
        editable: true,
        placeholder: "选择日期",
        startplaceholder: "开始日期",
        endplaceholder: "结束日期",
        utype: "date",
        format: "YYYY-MM-DD",
        valueformat: "YYYY-MM-DD",
        defaulttime: [new Date(2000, 1, 1, 0, 0, 0)],
        style: "margin:0px",
      },
    ],
    [
      {
        type: "timepicker",
        label: "时间选择",
        name: "timepicker",
        value: new Date(2016, 9, 10, 8, 40, 50),
        changeEvent: (arg) => {
          console.log(arg);
        },
        //********** */
        readonly: false,
        disabled: false,
        size: "small",
        editable: true,
        placeholder: "选择时间1",
        startplaceholder: "开始时间",
        endplaceholder: "结束时间",
        utype: "datetime",
        format: "HH:mm:ss",
        defaultvalue: [
          new Date(2016, 9, 10, 8, 40, 50),
          //new Date(2016, 9, 10, 9, 30, 40),//datetimerange
        ],
      },
      {
        type: "timeselect",
        label: "选择时间",
        showLabel: true,
        name: "timeselect",
        value: new Date(2016, 9, 10, 8, 40, 50),
        changeEvent: (arg) => {
          console.log(arg);
        },
        //********** */
        disabled: false,
        editable: true,
        size: "small",
        placeholder: "选择时间",
        start: "00:00:00",
        end: "24:59:59",
        step: "1:00:00",
        mintime: "00:00:00",
        maxtime: "24:59:59",
        format: "hh:mm:ss A",
        style: "margin:0px",
      },
    ],
    [
      {
        type: "transfer",
        label: "条件选取",
        showLabel: false,
        name: "transfer",
        value: [0,2],
        changeEvent: (arg) => {
          console.log(arg);
        },

        //********** */
        direction: "vertical",
        labelSize: "small",
        transferLabel: false,
        alignment: "normal",
        data: __data.value,
        filterable: true,
        filterplaceholder: "筛选关键字",
        filtermethod: function (query, item) {
          return item.initial.toLowerCase().includes(query.toLowerCase());
        },
        titles: ["AAA", "BBB"],
        // format: "HH:mm:mm",
        validateevent: true,
        style: "margin:0px",
      },
    ],
    [
      {
        type: "colorpicker",
        label: "选取颜色",
        name: "colorpicker",
        value: "rgb(255, 0, 0)",
        changeEvent: (arg) => {
          console.log(arg);
        },

        //********** */
        disabled: false,
        size: "small",
        showalpha: false,
        colorformat: "rgb",
        validateevent: true,
        style: "margin:0px",
      },
    ],
    [
      {
        type: "button",
        label: "确定",
        showLabel: false,
        name: "button",
        value: "buttonValue",
        changeEvent: (obj, arg) => {
          console.log(obj, arg);
        },

        //********** */
        space: "330px",
        size: "small",
        disabled: false,
        utype: "primary",
        plain: false,
        text: false,
        bg: false,
        link: false,
        round: false,
        circle: false,
        color: "",
        dark: false,
        autoinsertspace: true,
        style: "margin:0px",
      },
      {
        type: "button",
        label: "确定按钮",
        showLabel: false,
        name: "button2",
        value: "buttonValue2",
        changeEvent: (obj, arg) => {
          console.log(obj, arg);
        },

        //********** */
        space: "50px",
        size: "small",
        disabled: false,
        utype: "primary",
        plain: false,
        text: false,
        bg: false,
        link: false,
        round: false,
        circle: false,
        color: "",
        dark: false,
        autoinsertspace: true,
        style: "margin:0px",
      },
    ],
  ],
  ustyle: {
    formItemStyle: `margin: 0px 0px`,
    rowStyle: [`margin:10px 20px 10px 30px`, `margin:10px 0px 0px 0px`],
    colSpace: [20, 200],
  },
};

//@ts-ignore
const formDataValue: any = ref(Utils.getDataValueFromDataFrame(formDataFrame.data));

// let __formDataValue: any = ref({});
// const formItems = formDataFrame.data || [];
// formItems.forEach((element: any) => {
//   element.forEach((item: any) => {
//     console.log("====================", item.name, item.value, "item =", item);
//     __formDataValue.value[item.name] = item.value;
//   });
//   // console.log("====================",element);
//   formDataValue[element.name] = element.value;
// });

// console.log("++++++++++++++__formDataValue ", __formDataValue);

// console.log("formDataValue = ",formDataValue)
onMounted(() => {});
</script>
