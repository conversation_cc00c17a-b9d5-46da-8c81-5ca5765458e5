<template>
  <utable :tabledata="tabledata"></utable>
</template>

<script setup>
import {utable} from "../../packages/onemapkit"; 

const fileds = [
  {
    field: "attr",
    title: "参数",
    align: "center",
  },
  {
    field: "type",
    title: "类型",
    align: "center",
  },
  {
    field: "red",
    title: "说明",
    align: "center",
    width: "350px",
  },
  {
    field: "sel",
    title: "可选值",
    align: "center",
  },
  {
    field: "def",
    title: "默认值",
    align: "center",
  },
];

const tabledata = [
  {
    fldName: "formDataValue",
    fldType: "Object",
    fldDescribe: "表单对象绑定的值",
    fldOption: "",
    fldDefault: '{}',
  },
  {
    fldName: "formDataFrame",
    fldType: "Array",
    fldDescribe: "数据源的结构",
    fldOption: "",
    fldDefault: `
      {
        type: "button",
        label: "确定",
        showLabel: false,
        name: "button",
        value: "buttonValue",
        changeEvent: (obj, arg) => {
          console.log(obj, arg);
        },
        ...
      }
    `,
  },
  {
    fldName: "ustyle",
    fldType: "Object",
    fldDescribe: "样式参数",
    fldOption: "",
    fldDefault: `
      {
        //style 格式的样式
        formItemStyle: 'margin: 1px 10px,
        //表单按行排列，这个数组是每行里的每个组件的间隔，一般这个数组长度等于表单行数，超过数组长度的取第1个元素值
        colSpace: [20],
      }`
    ,
  },
];
 
</script>

<style lang="scss" scoped></style>
