<!-- html -->
<template>
  <el-button type="primary" @click="funpopShow">弹出面板</el-button>
  <el-button type="primary" @click="showHorizontalExtents"
    >打印水平灯带内容</el-button
  >
  <el-button type="primary" @click="showVerticalExtents"
    >打印竖直灯带内容</el-button
  >
  <pop-panel
    v-if="popShow"
    :title="'测试面板'"
    :width="434"
    :height="600"
    @close="close"
  >
    <template #content>
      <LightBar
        ref="PathRoamingRef"
        MapControlName="mainMapControl"
        :PropStore="PropStore"
        :horizontalExtents="horizontalExtents"
        :verticalExtents="verticalExtents"
      ></LightBar>
    </template>
  </pop-panel>

  <div class="container" id="parentContainer">
    <MapControl
      MapControlName="mainMapControl"
      :PropStore="PropStore"
      :LayerStore="layerStore"
      @MapReadyEvent="MapReadyEvent"
    ></MapControl>
  </div>
</template>

<script setup lang="ts">

import { ref } from "vue";
import {
  MapControl,
  PopPanel,
  LightBar,
  mapType,
  InitMapControl,
  getOnemap,
} from "../../packages/onemapkit";
import * as Cesium from "@onemapkit/cesium";

/**第一步：导入图层参数 */
import {
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  initShowMapLayer,
  _Onemap,
} from "../layer";

if (_Onemap.MapType.value != mapType.cesium)
  _Onemap.setMapType(_Onemap.MapType.value);
/**  第二步：配置公共参数 */

const Options = {
  MapControlName: "mainMapControl",
  BASE_URL: "ThirdParty",
  mapType: mapType.cesium,
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  Components: {
    MapLayerTreeVisible: false,
    SwitchMapVisible: false,
    PropertyVisible: false,
    MapToolsVisible: false,
    BottomInfoVisible: false,
  },
};
InitMapControl(_Onemap, Options);
const horizontalExtents = [
  [
    [108.38916887672225, 22.71077050157349, 162.8],
    [108.389637000403, 22.71076880703452, 162.8],
    [108.38963779411348, 22.710945968785726, 162.8],
    [108.38916954792613, 22.71094766390723, 162.8],
  ],
  [
    [108.389638565446, 22.711128775274112, 162.2],
    [108.3896408404992, 22.711308038063418, 162.2],
    [108.38917112632991, 22.711309831279372, 162.2],
    [108.38917034483357, 22.7111305691563, 162.2],
  ],
  [
    [108.38964083229588, 22.711671987725282, 164],
    [108.38964154104895, 22.711839875158205, 164],
    [108.38917337122888, 22.711841572856773, 164],
    [108.38917269940825, 22.711673675829477, 164],
  ],
  [
    [108.38917148616132, 22.711859702366638, 127.11],
    [108.38916687433692, 22.710768777016707, 127.11],
    [108.38963897002026, 22.710767023191096, 127.11],
    [108.38964353599889, 22.711857963083943, 127.11],
  ],
];

const verticalExtents = [
  {
    bottomPoint: [108.38963699306925, 22.710768794263167, 130.2],
    topHeight: 162.6,
  },
  {
    bottomPoint: [108.38916879645184, 22.710770517143533, 127.2],
    topHeight: 162.6,
  },
  {
    bottomPoint: [108.38964153770165, 22.7118398720877, 130.2],
    topHeight: 164,
  },
  {
    bottomPoint: [108.38917338105188, 22.71184158846506, 127.2],
    topHeight: 164,
  },
  {
    bottomPoint: [108.38963780149855, 22.710945962292904, 127.2],
    topHeight: 162.6,
  },
  {
    bottomPoint: [108.38963862813327, 22.711128839760946, 127.2],
    topHeight: 162,
  },
  {
    bottomPoint: [108.3896393420625, 22.711308045593064, 128.57],
    topHeight: 162,
  },
  {
    bottomPoint: [108.38964084520754, 22.71167191045441, 127.2],
    topHeight: 164,
  },
  {
    bottomPoint: [108.38917261333134, 22.71167371578595, 127.2],
    topHeight: 164,
  },
  {
    bottomPoint: [108.38917108619695, 22.711309774998522, 127.2],
    topHeight: 162,
  },
  {
    bottomPoint: [108.38917028095615, 22.71113057006303, 127.2],
    topHeight: 162,
  },
  {
    bottomPoint: [108.38916954130342, 22.710947645057864, 127.2],
    topHeight: 162.6,
  },
];

//MapControl 组件的地图加载完毕的回调方法,是获取MapViewer的一种方式
const MapReadyEvent = (_cesiumViewer: any) => {
  const cesiumViewer = getOnemap(_cesiumViewer).MapViewer;

  initShowMapLayer();
  cesiumViewer.scene.requestRenderMode = false;
  const tilesetPromise = Cesium.Cesium3DTileset.fromUrl(ServiceUrl.model_XXJT);
  tilesetPromise.then((tileset) => {
    cesiumViewer.scene.primitives.add(tileset);
    cesiumViewer.flyTo(tileset);
  });
};

let popShow = ref(false);
const funpopShow = () => {
  popShow.value = true;
};
const close = () => {
  popShow.value = false;
};

const PathRoamingRef = ref(undefined);

const showHorizontalExtents = () => {
  if (PathRoamingRef.value) {
    console.log((PathRoamingRef.value as any).getHorizontalExtents());
  }
};

const showVerticalExtents = () => {
  if (PathRoamingRef.value) {
    console.log((PathRoamingRef.value as any).getVerticalExtents());
  }
};
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 400px;
  position: relative;
}
</style>
