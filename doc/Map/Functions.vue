<template> 
  <utable :fields="fields" :tabledata="tabledata"></utable>
</template>

<script setup>
import { ref } from "vue";
import {utable} from "../../packages/onemapkit";
const fields = ref([]);
// import("../table/docProps.ts").then((res) => { fields.value = res.fields;  });
 
const tabledata = [
  {
    fldName: "AddLayer",
    fldType: "Function",
    fldDesc: "添加图层",
    fldOptV: "综合图层加载方法，根据 options.serviceType 参数自动加载图层",
    fldDefV: "{}"
  },
  {
    fldName: "AddArcGIS3DTiles",
    fldType: "Function",
    fldDesc: "ArcGIS 的 i3s格式的 3Dtiles 三维数据加载方法",
    fldOptV: " 参数类型为 Option 类型",
    fldDefV: ""
  },
  {
    fldName: "AddSuper3DTiles",
    fldType: "Function",
    fldDesc: "SuperMap 的 3Dtiles 三维数据加载方法",
    fldOptV: " 参数类型为 Option 类型",
    fldDefV: ""
  },
  {
    fldName: "AddCesium3DTiles",
    fldType: "Function",
    fldDesc: "Cesium 的 3Dtiles 三维数据加载方法",
    fldOptV: " 参数类型为 Option 类型",
    fldDefV: ""
  },
  {
    fldName: "AddArcTilesMap",
    fldType: "Function",
    fldDesc: "OGC散列切片（天地图切片方案） 数据加载方法",
    fldOptV: " 参数类型为 Option 类型",
    fldDefV: ""
  },
  {
    fldName: "AddGaodeMap",
    fldType: "Function",
    fldDesc: "高德数据加载方法",
    fldOptV: " 参数类型为 Option 类型",
    fldDefV: ""
  },
  {
    fldName: "AddArcMapServer",
    fldType: "Function",
    fldDesc: "Arcgis 数据加载方法",
    fldOptV: " 参数类型为 Option 类型",
    fldDefV: ""
  },
  {
    fldName: "AddTerrain",
    fldType: "Function",
    fldDesc: "Cesium地形服务 SuperMap地形服务 ArcGIS地形服务",
    fldOptV: " 参数类型为 Option 类型，path或 Cesium地形服务、SuperMap地形服务、ArcGIS地形服务等地形类型",
    fldDefV: ""
  } ,
  {
    fldName: "isExistLayer",
    fldType: "Function",
    fldDesc: "判断图层是不是存在",
    fldOptV: " 参数类型为  id 或  IMapLayer对象 类型",
    fldDefV: ""
  },
  {
    fldName: "removeImagerLayerById",
    fldType: "Function",
    fldDesc: "根据id移除影像图层",
    fldOptV: " 参数类型为  id 或  IMapLayer对象 类型",
    fldDefV: ""
  },
  {
    fldName: "getImagerLayerByID",
    fldType: "Function",
    fldDesc: "根据id获取影像",
    fldOptV: " 参数类型为  id 或  IMapLayer对象 类型",
    fldDefV: ""
  } ,
  {
    fldName: "gotoPoint",
    fldType: "Function",
    fldDesc: "定位到指定点位",
    fldOptV: " 参数类型为  id 或  IMapLayer对象 类型",
    fldDefV: ""
  }              
];
 
</script>

<style lang="scss" scoped></style>
