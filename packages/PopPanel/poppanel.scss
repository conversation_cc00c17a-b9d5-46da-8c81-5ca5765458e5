@charset "UTF-8";
.user-panel {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transition: top 0.2s, left 0.2s, height 0.2s;
  z-index: 1000;
  padding: 0px !important;
  will-change: top, left, height;
  //   resize: both;
  //   overflow: scroll;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f5f5f5;
    cursor: move;
    position: relative;
    &__min {
      width: 20px !important;
      height: 20px !important;
      position: absolute;
      top: 50%;
      right: 50px;
      transform: translateY(-50%);
      cursor: pointer;

      :deep(.tool-icon) {
        width: 20px;
        height: 20px;
        &:hover {
          background: #95aec4;
          border-radius: 10px;
        }
      }
    }
    &__max {
      width: 20px !important;
      height: 20px !important;
      position: absolute;
      top: 50%;
      right: 50px;
      transform: translateY(-50%);
      cursor: pointer;

      :deep(.tool-icon) {
        width: 20px;
        height: 20px;
        &:hover {
          background: #95aec4;
          border-radius: 10px;
        }
      }
    }
    &__full {
      width: 20px !important;
      height: 20px !important;
      position: absolute;
      top: 50%;
      right: 80px;
      transform: translateY(-50%);
      cursor: pointer;

      :deep(.tool-icon) {
        width: 20px;
        height: 20px;
        &:hover {
          background: #95aec4;
          border-radius: 10px;
        }
      }
    }
    &__close {
      width: 20px !important;
      height: 20px !important;
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
      cursor: pointer;

      :deep(.tool-icon) {
        width: 20px;
        height: 20px;

        &:hover {
          background: #95aec4;
          border-radius: 10px;
        }
      }
    }
  }
}

.user-panel:focus,
.user-panel:focus-within {
  z-index: 1999;
  padding: 5px;
}

.panel-content {
  flex: 1;
  overflow: auto;
  width: 100%;
  padding: 5px;
  height: calc(100% - 40px);
  box-sizing: border-box;
}
.panel-content-nooverflow{
  padding: 10px;
}
.panel-resizer {
  position: absolute;
  width: 10px;
  height: 10px;
  cursor: se-resize;

  &-bottom {
    bottom: 0;
    right: 0;
  }

  &-top {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 5px;
    cursor: ns-resize;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    .resize-handle-icon {
      width: 24px;
      height: 12px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      
      &::before,
      &::after,
      &::before {
        content: '';
        width: 16px;
        height: 2px;
        background-color: #95aec4;
        border-radius: 1px;
        transition: all 0.2s;
      }

      &::before {
        content: '';
        width: 16px;
        height: 2px;
        background-color: #95aec4;
        border-radius: 1px;
        transition: all 0.2s;
      }

      &::after {
        content: '';
        width: 16px;
        height: 2px;
        background-color: #95aec4;
        border-radius: 1px;
        transition: all 0.2s;
      }
    }

    &:hover .resize-handle-icon::before,
    &:hover .resize-handle-icon::after,
    &:hover .resize-handle-icon::before {
      background-color: #4a90e2;
      width: 20px;
    }
  }
}

.minimized {
  width: 200px !important;
  height: 39px !important;
  transition: all 0.2s;
  // 定义最小化状态的样式
}

.bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}

  /* 设置滚动条的样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  /* 滚动槽 */
  ::-webkit-scrollbar-track {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px #eee;
  }
  
  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
  }