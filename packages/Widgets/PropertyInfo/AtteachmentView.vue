<template>
	<PopPanel title="附件列表" @close="onClose()" :width="800" :max-height="420">
		<el-table :data="data" style="width: 100%;" empty-text="暂无数据">
			<el-table-column type="index" align="center" label="#" width="50"></el-table-column>
			<el-table-column align="center" prop="name" label="附件名"></el-table-column>
			<el-table-column align="center" prop="extension" label="扩展名"></el-table-column>
			<el-table-column align="center" prop="size" label="附件大小"></el-table-column>
			<el-table-column align="center" prop="filePath" label="路径"></el-table-column>
			<el-table-column align="center" label="操作" width="200">
				<template v-slot="{ row }">
					<span class="table-tools">
						<el-button v-show="row && row.extension && row.extension.includes('.dwg')" @click="showinfo(row)"
							type="primary" size="small">查看</el-button>
						<el-button v-if="appendix" @click="download(row)" type="primary" size="small">下载</el-button>
					</span>
				</template>
			</el-table-column>
		</el-table>
	</PopPanel>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, onMounted, inject } from "vue";
import { PopPanel } from "../../onemapkit";
import request from "../../utils/axios/";

const props = defineProps({
	// 多媒体地址
	filepath: {
		type: String,
		default: "",
	},
	appendix: {
		type: Boolean,
		default: false,
	},
	configId: {
		type: String,
		default: '',
	},
	PropStore: {
		type: Object,
		default: null,
	}

})
const data: any = ref([]);
const urlObj: any = props.PropStore.getStorage('urlObj')?.value

onMounted(() => {
	if (props.filepath) {
		data.value = [];
		request.get({
			url: urlObj.atteachmentListUrl + props.configId + '/list',
			params: {
				filePath: props.filepath,
			},
		})
			.then((res) => {
				console.log('获取列表res', res);

				if (res.data && res.data.length > 0) {
					let dt: any = res.data;
					for (let item of dt) {
						if (item.length) {
							let num: any = item.length / 1024 / 1024
							item.size = parseFloat(num).toFixed(2) + " MB"
						}
					}
					data.value = dt
				}
			})
			.catch((err) => {
				console.log(err);
			});
	}
})
const emits = defineEmits(['closeAtteachment'])
//关闭
const onClose = () => {
	emits('closeAtteachment', '')
};

const showinfo = (row: any) => {
	let path = row.filePath;
	if (path.includes("总平")) {
		path = path.replaceAll("总平/", "")
	}
	window.open(
		`${urlObj.atteachmentReviewUrl}?v=${path.replaceAll(
			"/",
			""
		)}&&name=${row.name.replace(".pdf", ".ocf")}`,
		"__blank__"
	);
};

const download = (row: any) => {
	console.log('row', row);
	request.get({
		url: urlObj.atteachmentDownUrl + props.configId,
		params: {
			filePath: row.filePath,
			name: row.name
		},
		// responseType: 'blob'
	})
		.then((res:any) => {
			let fileURL = window.URL.createObjectURL(new Blob([res]));
			let fileLink = document.createElement('a');

			fileLink.href = fileURL;
			fileLink.setAttribute('download', `${row.name}`);
			document.body.appendChild(fileLink);

			fileLink.click();
		})
		.catch((err) => {
			console.log(err);
		});
};
</script>
<style lang="scss" scoped>
.el-table--border th {
	background-color: #f8f8f8;
}

.switch-info {
	color: red;
	margin-left: 10px;
	font-size: 11px;
}

.el-form-item {
	margin-bottom: 10px;
}
</style>
