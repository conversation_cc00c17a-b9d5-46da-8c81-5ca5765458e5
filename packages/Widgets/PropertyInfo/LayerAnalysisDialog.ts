
 //数字加减
 export const addNumber = (num1:number,num2:number) => {
    if(!num1){
      num1 = 0;
    }
    if(!num2){
      num2 = 0;
    }
    let sq1,sq2,m;
    try {
      sq1 = num1.toString().split(".")[1].length;
    }
    catch (e) {
      sq1 = 0;
    }
    try {
      sq2 = num2.toString().split(".")[1].length;
    }
    catch (e) {
      sq2 = 0;
    }
    m = Math.pow(10,Math.max(sq1, sq2));
    return (num1 * m + num2 * m) / m;
  }
  
  //遍历数组查找能够分析的图层
  export const findAnalystLayer =  (mapService:any) => {
    let layers:any = []
    function findAnalystLayer(data:any) {
        data.forEach(function (item:any) {
            if (item.children && item.children.length) {
                findAnalystLayer(item.children);
            } else if (item.attributes && item.attributes.isAnalyst) {
                layers.push(item)
            }
        })
    }
    findAnalystLayer(mapService)
    return layers;
  }
  
  //遍历数组查找符合id的图层
  export const getLayerByID = (id:string,arr:any) =>{
      for (let i = 0; i < arr.length; i++) {
          const node = arr[i];
          if(id == node.layerid){
              return node
          }
          if(node.children && node.children.length){
              let foundNode:any = getLayerByID(id,node.children)
              if(foundNode){
                  return foundNode
              }
          }
      }
      return null
  
  }
  