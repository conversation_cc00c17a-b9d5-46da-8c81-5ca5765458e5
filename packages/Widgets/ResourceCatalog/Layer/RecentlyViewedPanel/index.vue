<template>
	<div class="layer-tree">
		<div style="display: flex">
			<el-checkbox :disabled="currentTreeData.length <= 0" v-model="checkAll" @change="checkAllHandle"
				:indeterminate="isIndeterminate" class="recent-check"></el-checkbox>
			<el-button :disabled="currentTreeData.length <= 0" class="recent-btn" @click="onRemoveAll">全部移除</el-button>
		</div>
		<el-tree class="recent-tree" ref="treeRef" empty-text="暂无数据" show-checkbox node-key="layerid" @check="onTreeCheck"
			:height="treeHeight" :data="currentTreeData" :props="treeProps">
			<template #default="{ node, data }">
				<custom-tree-node :isRecent="true" :tree-data="currentTreeData" :node="node" :data="data" :Onemap="props.Onemap"
					:more-operate="true" @doAction="doAction" />
			</template>
		</el-tree>
	</div>
</template>

<script lang="ts" setup>
import {
	defineProps,
	ref,
	onMounted,
	watch,
	nextTick,
} from "vue";
import { 
	type IMapLayer,
	OnemapClass 
} from "../../../../onemapkit";
import CustomTreeNode from "../../../../CustomTreeNode/CustomTreeNode.vue";
const props = defineProps({
	/**
	 * 图层加载前的事件,由业务系统自己实现,通过属性方式传递至目录树组件
	 */
	LayerCheckEvent: {
		type: Function,
		default: {
			Event: null,
		},
	},
	Onemap: {
		type: OnemapClass,
		default: () => {
			let tmp = new OnemapClass();
			tmp.isMapReady = ref(false);
			return tmp;
		},
		require: true,
	},
	LayerStore: {
		type: Object as any,
		default: null,
	},
	PropStore: {
		type: Object as any,
		default: null,
	},
});
//选中的节点ID列表，用于处理树的勾选状态
let checkedKeys: Array<string> = [];

// 当前的目录树
const currentTreeData = ref([] as any[]);
// 全选状态
const checkAll = ref(false);
const isIndeterminate = ref(false);

const treeProps = ref({
	value: "layerid",
	label: "name",
	children: "children",
});

watch(
	() => props.LayerStore.currentNodeLayerid,
	() => {
		updateTreeData();
		let ids:any = []
		for (let item of currentTreeData.value) {
			ids.push(item.layerid)
		}

		
		props.Onemap?.reorderLayer({
			layer: null,
			ids: ids
		}, "sort");
	}
);

// 树形控件高度
const treeHeight = ref(400);

// 树形控件Ref
const treeRef = ref();

onMounted(() => { });

// const checkHandle = () => {};
const onTreeCheck = (data: any) => {
	//根据所有勾选的节点，判断当前的动作是勾选还是取消勾选
	const checkedId = treeRef.value.getCheckedKeys(true);
	nextTick(async () => {
		const checked = checkedId.includes(data.layerid);
		if (checked) {
			if (data) {
				//判断是否有勾选的外部事件，该事件应该是异步函数，返回值为修改后的图层配置，如果没有，则直接加载图层，如果有，则在事件执行完后，加载修改后的图层配置
				if (props.LayerCheckEvent) {
					props.LayerCheckEvent(checked, data).then((res: any) => {
						if (res) {
							const ly = props.Onemap.AddLayer(res);
							props.LayerStore.setCheckedLayerid(true, res);
                            //@ts-ignore
							if (ly && ly?.load) {
                            //@ts-ignore
								ly.load().then(() => {
									nextTick(() => {
										let ids:any = []
										for (let item of currentTreeData.value) {
											ids.push(item.layerid)
										}
										console.log("排序：", ids)
										props.Onemap?.reorderLayer({
											layer: data,
											ids: ids.reverse()
										}, "sort");
									})
								});
							}
						}
					});
				} else {
					const ly = props.Onemap.AddLayer(data);
					props.LayerStore.setCheckedLayerid(true, data);

                    //@ts-ignore
					if (ly?.load) {
						//@ts-ignore
						ly.load().then(() => {
							nextTick(() => {
								let ids:any = []
								for (let item of currentTreeData.value) {
									ids.push(item.layerid)
								}

								props.Onemap?.reorderLayer({
									layer: data,
									ids: ids
								}, "sort");
							})
						});
					}
				}
			}
		} else {
			props.Onemap.RemoveLayerById(data);
			props.LayerStore.setCheckedLayerid(false, data);
		}
	});
};

// const reorder = (data) => {};

const updateTreeData = () => {
	//根据勾选图层的values，更新最近浏览的目录树。有的话，不处理，没有的话，添加到最近浏览的目录树中。
	props.LayerStore.checkedLayers.forEach((value: any, key: string) => {
		if (checkedKeys.includes(key)) return;
		checkedKeys.push(key);
		let ly = JSON.parse(JSON.stringify(value));
		ly.checked = true;
		ly.disabled = "";
		ly.order = currentTreeData.value.length + 1;
		currentTreeData.value.push(ly);
	});

	// 更新排序
	let dt = currentTreeData.value.sort((a, b) => {
		if (a.order > b.order) {
			return -1
		} else if (a.order > b.order) {
			return 0
		} else {
			return 1
		}
	})

	//dom刷新后，再设置选中状态
	nextTick(() => {
		currentTreeData.value = JSON.parse(JSON.stringify(dt))
		treeRef.value.setCheckedKeys(
			Array.from(props.LayerStore.checkedLayers.keys())
		);
	});
};

/**
 * 移除全部图层
 */
const onRemoveAll = (isClear = true) => {
	console.log(isClear);
	checkedKeys = []
};

const checkAllHandle = () => { };

const onSortUp = (node: IMapLayer) => {
	// 获取索引位置
	let index = currentTreeData.value.findIndex(x => x.layerid == node.layerid)
	if (index > 0) {
		const layer = currentTreeData.value.find(x => x.layerid == node.layerid)
		const upLayer = currentTreeData.value[index - 1]
		const upOrder = upLayer.order
		upLayer.order = layer.order
		layer.order = upOrder

		let dt = currentTreeData.value.sort((a, b) => {
			if (a.order > b.order) {
				return -1
			} else if (a.order > b.order) {
				return 0
			} else {
				return 1
			}
		})

		nextTick(() => {
			currentTreeData.value = JSON.parse(JSON.stringify(dt))
			let ids:any = []
			for (let item of currentTreeData.value) {
				ids.push(item.layerid)
			}
			props.Onemap?.reorderLayer({
				layer: node,
				ids: ids
			}, "sort");
		})
	}
};

const onSortDown = (node: any) => {
	let index = currentTreeData.value.findIndex(x => x.layerid == node.layerid)
	if ((currentTreeData.value.length - 1) > index) {
		const layer = currentTreeData.value.find(x => x.layerid == node.layerid)
		const upLayer = currentTreeData.value[index + 1]
		const upOrder = upLayer.order
		upLayer.order = layer.order
		layer.order = upOrder

		let dt = currentTreeData.value.sort((a, b) => {
			if (a.order > b.order) {
				return -1
			} else if (a.order > b.order) {
				return 0
			} else {
				return 1
			}
		})

		nextTick(() => {
			currentTreeData.value = JSON.parse(JSON.stringify(dt))
			let ids:any = []
			for (let item of dt) {
				ids.push(item.layerid)
			}
			props.Onemap?.reorderLayer({
				layer: node,
				ids: ids
			}, "sort");
		})
	}
};

const onRemove = (node: any) => {
	nextTick(() => {
		currentTreeData.value.splice(currentTreeData.value.indexOf((x:any) => x.layerid == node.layerid), 1)
		props.LayerStore?.setCheckedLayerid(false, node);
	})
	node.checked = false;
	props.Onemap?.RemoveLayerById(node);
	checkedKeys.splice(checkedKeys.indexOf(node.layerid), 1);
};

/**
 * 处理图层顺序调整/移除操作
 * @param {*} data
 */
const doAction = (data: any) => {
	const { action, node } = data;
	if (action === "up") {
		onSortUp(node);
	} else if (action === "down") {
		onSortDown(node);
	} else if (action === "remove") {
		onRemove(node);
	}
};
</script>

<style lang="scss" scoped>
.layer-tree {
	height: calc(100vh - 360px);
	width: 100%;
	overflow: auto;
	position: relative;
}

.recent-tree {
	margin-top: 0px;
}

.recent-check {
	margin-left: 24px;
}

.recent-btn {
	position: absolute;
	z-index: 20;
	right: 10px;
	min-height: 20px;
	padding: 5px;
	margin-top: 5px;
}

:deep(.curstom-icon) {
	width: 20px !important;
	height: 20px !important;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
}
</style>
