@charset "utf-8";

.attrSearch-panel {
	width: 100%;
	background: #fff;

	:deep(.el-form) {
		margin-top: 10px;
	}

	.select-list {
		height: 100px;
		overflow: auto;
		margin-top: 5px;
		border: 1px solid var(--el-border-color);
		border-radius: 3px;
		width: 100%;
		margin-left: 5px;
		margin-right: 5px;
	}

	.selectLayer {
		//width: calc(50% - 10px);
		width: 140px;
		margin-top: 5px;
		padding-left: 8px;

		:deep(.el-input__inner) {
			height: 28px;
		}
	}
}

:deep(.el-form-item__label) {
	line-height: 24px;
}

.warning {
	width: 100%;
	text-align: center;
	font-weight: bold;
	padding: 20px 0px;
	font-size: 15px;
}

.selectSubLayer {
	width: 300px;
}

.error-info {
	align-items: center;
	justify-content: center;
	flex-direction: column;
	float: right;
	color: red;
	font-weight: bold;
	margin-left: 20px;
	line-height: 30px;
}

:deep(.el-form-item) {
	margin-bottom: 5px;
}

.header {
	width: 100%;

	// .selectLayer {
	//     width: 90%;
	// }

	.el-form-item {
		display: flex;
		margin-bottom: 5px;
	}

	:deep(.el-form-item__content) {
		flex: 1;
		flex-wrap: nowrap;
		line-height: 40px;
		position: relative;
		font-size: 14px;
	}

	.searchBox {
		display: flex;
		padding: 5px;

		.el-input {
			width: calc(90% - 60px);
			margin-right: 10px;
		}
	}
}

:deep(.el-pagination) {
	text-align: center;
}

:deep(.el-dialog__header) {
	background-color: #e1e4e6;
	color: #000;
}

:deep(.el-dialog__headerbtn) {
	top: 10px;
	right: 10px;

	:deep(&:hover) {
		color: #fff;
	}
}

.btn-body {
	text-align: center;
	width: 100%;
	padding: 5px 0px;
	font-size: 14px;

	:deep(.el-form-item__content) {
		justify-content: right !important;
		padding-right: 10px;
	}
}

.field-list {
	overflow: auto;
}

.tab-query-content {
	// padding: 0 10px 10px 10px;
	height: calc(100vh - 490px);
	overflow: auto;
}

.result {
	width: 390px;
	overflow-x: auto;
	height: calc(100% - 25px);
	padding: 0px 5px;
	position: relative;
}

.result-item {
	padding: 10px 0px;
}

.result-left {
	float: left;
}

.result-right {
	width: 100%;
}

.box-card {
	width: 100%;
	position: relative;
	margin-bottom: 30px;
	margin-top: 10px;
	padding-bottom: 20px;
}

.text_item {
	width: 100%;
	text-align: left;
	padding: 5px;

	.text_items {
		width: 100%;
		margin-bottom: 4px;
		text-align: left;
	}

	div {
		font-size: 11px;
		float: left;
	}

	.text_label {
		width: 35%;
		word-break: break-all;
		text-align: left;
	}

	.text_value {
		width: 45%;
		word-break: break-all;
		padding-left: 15px;
		text-align: left;
	}
}

.highlight {
	border-color: #99cff4;
}

.cardHandle {
	display: flex;
	justify-content: center;
	align-items: center;
}

.at_top {
	z-index: 9999;
}

.panel-label {
	font-size: 14px;
	border-left: 5px solid var(--el-color-primary);
	margin-left: 5px;
	padding-left: 4px;
}

:deep(.el-pagination__jump) {
	margin-left: 3px;
}

.custom-table {
	width: 100%;
	background-color: white;
	table-layout: auto;
	font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
	font-size: 12px;
	margin-top: 10px;

	tr:hover {
		td {
			background-color: #f5f7fa;
		}

	}

	tr {
		padding: 2px;
		border: 1px solid #ebeef5;

		td {
			padding: 15px;
			border-top: 1px solid #ebeef5;
			text-align: center;
			background-color: #eff2f6;
			font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
		}

		td:first-child {
			width: 60px;
			padding: 10px;
			color: #3c7fe7;
			font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
		}

		td:not(:first-child) {
			min-width: 120px;
			color: #606266;
			padding: 10px;
			font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
		}
	}

	.first-td {
		width: 60px;
		text-align: left;
		padding: 10px;
	}

	.first-tr {
		background-color: #fff;
		text-align: left;

		td {
			background-color: #fff;
			text-align: left;
			padding: 10px;
		}
	}
}

.flex {
	display: flex;
}

.mode-switch {
	position: absolute;
	left: -50px;
}
