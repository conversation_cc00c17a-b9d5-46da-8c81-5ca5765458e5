<template>
	<div id="params-list" :style="heightStyle">
		<div class="params-condition">
			<div class="params-condition__title">条件列表</div>
			<div class="panel-item">
				<table>
					<div class="cond-list" v-for="it in allCondition2" :key="it.id">
						<div class="cond-detail" v-html="it.showText"></div>
						<div class="cond-op">
							<el-icon @click="editField(it)">
								<Edit />
							</el-icon>&nbsp;
							<el-icon style="color:red;" @click="deleteField(it)">
								<Delete />
							</el-icon>
						</div>
					</div>
				</table>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from "vue";
import { Edit, Delete } from '@element-plus/icons-vue';

const emit = defineEmits(["editField", "deleteField"])

const props = defineProps({
	allCondition: {
		type: Array,
		default: () => [] as any[],
	},
	heightStyle: {
		type: String,
		default: "height: calc(100vh - 385px);"
	}
})

const allCondition2 = ref([] as any[])

watch(
	() => props.allCondition,
	(list) => {
		allCondition2.value = JSON.parse(JSON.stringify(list))
		for (let item of allCondition2.value) {
			let showText = ''
			for (let cond of item.conditions) {
				if (!showText) {
					showText = `${cond.layer}.${cond.sublayer}`
				}
				else {
					showText += `&nbsp; &nbsp;<span class="condition-symb">${cond.op == 'and' ? ' 且 ' : ' 或 '}</span>`
				}

				// 字段名
				showText += `&nbsp;&nbsp;<span class="field-name">${cond.alias}</span>&nbsp;  &nbsp;`

				if (['like', 'equal'].includes(cond.symb)) {
					showText += `<span class="condition-operation">${cond.symb == 'like' ? '包含' : '等于'}</span>&nbsp; &nbsp;`
				}
				else {
					showText += `<span class="condition-operation">${cond.symb}</span>&nbsp; &nbsp;`
				}

				showText += `<span class="condition-text">${cond.text}</span>`
			}
			item.showText = showText
		}
	}, { deep: true }
)

// 编辑字段
const editField = (ly: any) => {
	ly.isEdit = true
	emit("editField", ly)
}

// 删除字段
const deleteField = (ly: any) => {
	emit("deleteField", { ly: ly })
}

</script>

<style lang="scss" scoped>
@import "styles/paramsList.scss";
</style>

<style lang="scss">
/**
	条件文字的颜色配置
*/
// 图层名称颜色
.layer-name {
	color: #000000;
}

// 字段名称颜色
.field-name {
	color: #858534;
}

// 条件颜色
.condition-text {
	color: #ff0000;
}

// 包含/等于/符号
.condition-operation {
	color: #409eff;
}

// 且/或
.condition-symb {
	color: #00ff40;
}
</style>