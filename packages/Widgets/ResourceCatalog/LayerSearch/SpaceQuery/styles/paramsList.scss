@charset "UTF-8";

/**
	条件文字的颜色配置
*/
// 图层名称颜色
.layer-name {
    color: #000000;
}

// 字段名称颜色
.field-name {
    color: #858534;
}

// 条件颜色
.condition-text {
    color: #ff0000;
}

// 包含/等于/符号
.condition-operation {
    color: #409eff;
}

// 且/或
.condition-symb {
    color: #00ff40;
}

#params-list {
    width: 100%;
    padding: 15px 5px 0px 5px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 10px;
    margin: 18px 0px 10px 0px;
    position: relative;
    font-size: 11px;
    color: #606266;

    .params-condition {
        width: 100%;
        height: 100%;
        overflow: auto;

        &__title {
            background-color: white;
            width: 80px;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 10px;
            position: absolute;
            top: -13px;
            left: 15px;
            color: #606266;
            font-size: 11px;
            padding: 3px 0px;
        }
    }

    .panel-item {
        width: 100%;
        display: flex;
        margin: 5px 0px;

        table {
            width: 100%;

            tr:nth-of-type(n+2) td {
                border-top: 1px solid #ccc;
            }

            tr td {
                padding: 3px;
                box-sizing: border-box;
            }

            .opera {
                width: 20px;
                text-align: center;
            }
        }
    }
}

.cond-list {
    display: flex;

    .cond-op {
        width: 50px;
    }

    .cond-detail {
        width: calc(100% - 51px);
    }
}

.cond-list:not(:first-child) {
    margin-top: 8px;
}

.red {
    color: #ff0000;
}

.blue {
    color: #409eff;
}

:deep(.el-icon) {
    font-size: 15px;
    cursor: pointer;
}