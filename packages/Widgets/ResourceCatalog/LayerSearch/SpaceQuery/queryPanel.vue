<template>
	<div id="query-panel">
		<div class="query-condition">
			<div class="query-condition__title">属性条件设置</div>
			<div class="panel-item">
				<el-select :disabled="editLayer.isEdit" size="small" v-model="layer" placeholder="请选择图层" @change="layerChange">
					<el-option v-for="item in layers" :key="item?.options?.layerid" 
						:label="item?.options?.name" :value="item?.options?.layerid">
					</el-option>
				</el-select>
				<el-select :disabled="editLayer.isEdit" v-if="selectSubLayers.length > 0" size="small" style="margin-left: 9px;" v-model="subLayer" placeholder="请选择子图层"
					@change="subLayerChange">
					<el-option v-for="(item, index) in selectSubLayers" :key="index" :label="item.name"
						:disabled="!item.geometryType"
						:title="!item.geometryType ? '该图层不可查' : ''"
						:value="item.id">
					</el-option>
				</el-select>
			</div>
			<!-- 添加字段 -->
			<div class="panel-item" v-if="!editLayer.isEdit">
				<el-select v-show="allCondition && allCondition.filter( (x:any) => (x.id == layer) && (x.subid == subLayer)).length > 0" size="small" placeholder=" " style="width: 48px; margin: 0px 5px" v-model="field.op">
					<el-option v-for="item in ops" :key="item.value" :label="item.label" :value="item.value">
					</el-option>
				</el-select>
				<el-select size="small" v-model="field.name" style="width: 123px;" placeholder="请选择字段" @change="fieldChange">
					<el-option v-for="it in fields" :key="it.name" :label="it.alias" :value="it.name">
					</el-option>
				</el-select>
				<el-select size="small" v-model="field.symb" style="width: 60px; margin: 0px 5px">
					<el-option v-for="item in (field.type == 'string'
					? symbs.filter((x) => x.type == 1)
					: symbs.filter((x) => x.type == 2))" :key="item.id" :label="item.label" :value="item.value">
					</el-option>
				</el-select>
				<el-input size="small" style="flex: 1; margin: 0px 5px; width: 100%;" v-model="field.text">
				</el-input>
			</div>
			<!-- 编辑字段 -->
			<template v-if="editLayer.isEdit">
				<div class="panel-item" v-for="(item, index) in editLayer.conditions" :key="item.id">
					<el-select v-if="index != 0" size="small" placeholder=" " style="width: 48px; margin: 0px 5px" v-model="item.op">
						<el-option v-for="item in ops" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select>
					<div v-if="index == 0" style="width: 58px; height: 24px;">&nbsp;</div>
					<el-select size="small" v-model="item.name" style="width: 123px;" placeholder="请选择字段" @change="fieldChange2">
						<el-option v-for="it in fields" :key="it.name" :label="it.alias" :value="it.name">
						</el-option>
					</el-select>
					<el-select size="small" v-model="item.symb" style="width: 60px; margin: 0px 5px">
						<el-option v-for="it in symbHandle(item)" :key="item.id" :label="it.label" :value="it.value">
						</el-option>
					</el-select>
					<el-input size="small" style="flex: 1; margin: 0px 5px; width: 100%;" v-model="item.text">
					</el-input>
					<el-icon style="color:red;" @click="deleteField(item)"><Delete /></el-icon>
				</div>
			</template>
			<div class="panel-item panel-btn">
				<el-button size="small" @click="onClose">隐藏</el-button>
				<el-button size="small" type="primary" @click="editLayer.isEdit ? onSaveEdit() : onSaveCondition()">{{ editLayer.isEdit ? '保存' : '添加' }}</el-button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits, defineProps } from 'vue';
import {  ElMessageBox, ElMessage } from "element-plus";
import { Delete } from '@element-plus/icons-vue';
import { ops, symbs } from "../const";
import { type IMapLayer } from "../../../../onemapkit";

const props = defineProps<{
  layers?: IMapLayer[],
	allCondition?: any[]
}>();

const emit = defineEmits(["onSaveCondition", "onCloseCondition"]);

// 当前选中的图层
const layer = ref("")

// 选中的子图层
const subLayer = ref("")

// 选择的子图层列表
const selectSubLayers = ref([] as any[])

// 字段列表
const fields = ref([] as any[])

// onMounted(() => {
// 	console.log(props.layers)
// 	debugger
// })

const editLayer = ref({
	id: 1,
	isEdit: false,
	conditions: [] as any[]
})

// 当前编辑的字段
const field = ref({
	text: "",
	op: "and",
	type: "string",
	name: "",
	alias: "",
	symb: "like",
	layer: '',
	sublayer: '',
	tid: ""
} as any)

// 图层change事件
const layerChange = (val: any) => {
	let ly = props.layers?.find(x => x.layerid == val)
	if (ly) {
		// 默认选中第一个子图层
		selectSubLayers.value = ly.options?.customProperty?.layers
		subLayer.value = ly.options?.customProperty?.layers[0].id
		fields.value = ly.options?.customProperty?.layers[0].fields
		field.value.layer = ly.options?.name
		subLayerChange(subLayer.value)
	} else {
		selectSubLayers.value = []
	}
}

// 子图层select change事件
const subLayerChange = (val: any) => {
	fields.value = [];
	let one = selectSubLayers.value.find((item) => {
		return item.id == val;
	})

	field.value.sublayer = one.name

	if (one.fields && one.fields.length > 0) {
		let layerFields:any = JSON.parse(JSON.stringify(one.fields));
		let id:number = 100;
		for (let item of layerFields) {
			item.id = ++id;
			if (item.type == "esriFieldTypeString") {
				item.type = "string";
			}
		}
		fields.value = layerFields
	} else {
		ElMessage.error("该图层没有字段可查询")
	}
}

// 选择字段change事件
const fieldChange = (val: any) => {
	const f = fields.value.find(x => x.name == val)

	for(let name of Object.getOwnPropertyNames(f)) {
		if (Object.getOwnPropertyNames(field.value).includes(name)) {
			field.value[name] = f[name] as any
		}
	}

	if (f.type.toLowerCase().includes('string')) {
		field.value.symb = 'like'
	}
	else {
		field.value.symb = '='
	}
}

// 编辑选择字段change事件
const fieldChange2 = (val: any) => {
	const f = fields.value.find(x => x.name == val)
	let fd = editLayer.value.conditions.find(x => x.name == val)
	for(let name of Object.getOwnPropertyNames(f)) {
		if (Object.getOwnPropertyNames(field.value).includes(name)) {
			fd[name] = f[name]
		}
	}

	if (f.type.toLowerCase().includes('string')) {
		fd.symb = 'like'
	}
	else {
		fd.symb = '='
	}
}

const onClose = () => {
	emit("onCloseCondition")
}

// 保存条件
const onSaveCondition = () => {
	if ((props.layers && props.layers.length <= 0) || !layer.value) {
		ElMessageBox.alert("请选择要查询的图层")
		return
	}

	if (!field.value.name || !field.value.text) {
		ElMessageBox.alert("请输入查询条件")
		return
	}
	
	let condition = JSON.parse(JSON.stringify(props.allCondition))

	// 如果存在子图层，取子图层ID
	let cond:any = null
	if (selectSubLayers.value.length > 0) {
		cond = condition.find((x:any) => (x.id == layer.value) && (x.subid == subLayer.value))
	}
	else {
		cond = condition.find((x:any) => x.id == layer.value)
	}

	// 判断是否存在相同的图层
	if (cond) {
		if (!cond.conditions) cond.conditions = []

		// tid有值表示是编辑字段
		if (field.value.tid) {
			let fd = cond.conditions.find((x:any) => x.tid)
			fd.text = field.value.text
			fd.op = field.value.op
			fd.type = field.value.type
			fd.name = field.value.name
			fd.alias = field.value.alias
			fd.symb = field.value.symb
			fd.layer = field.value.layer
			fd.sublayer = field.value.sublayer
			fd.tid = ""	// 修改完成后置空
		}
		else {
			cond.conditions.push({ ...field.value })
		}
	}
	else {
		condition.push({
			id: layer.value,
			subid: subLayer.value,
			conditions: [{ ...field.value }]
		})
	}

	// 排序
	condition = condition.sort((a: any, b: any) => {
		return a.id.localeCompare(b.id)
	})

	emit("onSaveCondition", condition)
}

// 编辑字段
const editField = (data: any) => {
	layer.value = data.id
	if (data.subid !== '' || data.subid !== undefined) {
		subLayer.value = data.subid
		layerChange(data.id)
		subLayerChange(data.subid)
	}

	editLayer.value = { ...JSON.parse(JSON.stringify(data)) }
}

// 删除字段
const deleteField = (fd: any) => {
	let idx = editLayer.value.conditions.findIndex(x => x.name == fd.name)
	if (idx > -1) {
		editLayer.value.conditions.splice(idx, 1)
	}
}

// 编辑保存
const onSaveEdit = () => {
	for(let item of editLayer.value.conditions) {
		if (!item.text) {
			ElMessageBox.alert(`请输入 ${item.alias} 查询条件`)
			return
		}
	}
	
	emit("onSaveCondition", editLayer.value)
}

const symbHandle = (item: any): any[] => {
	return ((item.type == 'string' ? symbs.value.filter((x) => x.type == 1) : symbs.value.filter((x) => x.type == 2)))
}

defineExpose({
	editField
})

</script>

<style lang="scss" scoped>
@import "styles/queryPanel.scss";
</style>
