<template>
  <div
    id="unfold"
    :class="[
      'layer-res-panel',
      foldLayerResPanel ? 'fold' : showBottom ? 'unfold-h' : 'unfold-n',
    ]"
  >
    <div class="layer-res-panel__title" @click="setContentVisible">
      <div style="position: relative">
        <span v-html="ResCatalogIcon['ResCatalogIcon']"></span
        ><span style="margin-left: 35px">图层资源面板</span>
      </div>
      <el-icon class="el-icon-transform" :size="20">
        <CaretTop />
      </el-icon>
    </div>
    <el-tabs
      class="layer-res-panel__container"
      v-show="!foldLayerResPanel"
      v-model="activeName"
    >
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="'tab' + index"
        :label="(item as any).label"
        :name="(item as any).name"
      > 
        <component v-if="(item as any).name === 'Layer'"
          :is="(item as any).component"
          :open="!foldLayerResPanel"
          :showBaseMap="showBaseMap"
          :LayerCheckEvent="props.LayerCheckEvent"
          :Onemap="props.Onemap"
          :PropStore="props.PropStore"
          :LayerStore="props.LayerStore"
        />
        <component v-else
          :is="(item as any).component"
          :open="!foldLayerResPanel"
          :showBaseMap="showBaseMap" 
          :Onemap="props.Onemap"
          :PropStore="props.PropStore"
          :LayerStore="props.LayerStore"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import {
  onMounted,
  onUnmounted,
  ref,
  watch,
  markRaw,
  nextTick, 
} from "vue";
import ResCatalogIcon from "./MainIcon.json";
import Layer from "./Layer/index.vue";
import Legend from "./Legend/index.vue";
import LayerSearch from "./LayerSearch/index.vue";
import { CaretTop } from "@element-plus/icons-vue";
import { OnemapClass } from "../../onemapkit";
const props = defineProps({
  Onemap: {
    type: OnemapClass,
    default: () => {
      let tmp = new OnemapClass();
      tmp.isMapReady = ref(false);
      return tmp;
    },
    require: false,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  tabs: {
    type: Array,
    default: () => [
      {
        name: "Layer",
        label: "图层目录",
        component: markRaw(Layer),
      },
      {
        name: "Legend",
        label: "地图图例",
        component: markRaw(Legend),
      },
      {
        name: "Query",
        label: "图层查询",
        component: markRaw(LayerSearch),
      },
    ],
  },
  showBottom: {
    type: Boolean,
    default: false,
  },
  LayerCheckEvent: {
    type: Function,
    default: () => {
      console.log("没定义自己定义图层加载前的业务层事件'LayerCheckEvent'函数");
    },
    require: false,
  },
});
const foldLayerResPanel = ref(true);
const queryPanel = ref(null);
const activeName = ref("Layer");
const showBaseMap = ref(true);

const resize = () => {
  // let isBR = document.body.clientWidth < 1500;
  // const container = document.querySelector(".layer-res-panel__container");
  //   if (container) {
  //     container.style.maxHeight = isBR
  //       ? "calc(100vh - 125px)"
  //       : "calc(100vh - 110px)";
  //   }
  //   const container2 = document.querySelector(".layer-panel__container");
  //   if (container2) {
  //     container2.style.height = isBR
  //       ? "calc(100vh - 360px)"
  //       : "calc(100vh - 340px)";
  //   }
  //   const container3 = document.querySelector(".tab-legend-content-body");
  //   if (container3) {
  //     container3.style.height = isBR
  //       ? "calc(100vh - 200px)"
  //       : "calc(100vh - 180px)";
  //   }
  //   const container4 = document.querySelector(".tab-legend-content-body2");
  //   if (container4) {
  //     container4.style.height = isBR
  //       ? "calc(100vh - 200px)"
  //       : "calc(100vh - 180px)";
  //   }
};

onMounted(() => {
  nextTick(() => {
    resize();
    window.addEventListener("resize", resize);
  });
});
onUnmounted(() => {
  window.removeEventListener("resize", resize);
});

watch(
  () => activeName.value,
  (val: any) => {
    if (
      val == "Query" &&
      queryPanel.value &&
      (queryPanel.value as any)?.clickVisible
    ) {
      // queryPanel.value.clickVisible();
    }
  }
);

const setContentVisible = () => {
  foldLayerResPanel.value = !foldLayerResPanel.value;

  let hightDiv = document.getElementById("unfold");

  console.log(
    "====ysy",
    hightDiv,
    hightDiv?.clientHeight,
    hightDiv?.offsetHeight
  );

  //   mapBase.setLayerResPanel(foldLayerResPanel.value);
};
// const setLayerPanelOpen = () => {
//   console.log("panel methods,set layeropen");
//   foldLayerResPanel.value = false;
//   mapBase.setLayerResPanel(false);
// };
// eventBus.on("TriggerSpaceQuery", (graphics) => {
//   foldLayerResPanel.value = false;
//   mapBase.setLayerResPanel(false);
//   activeName.value = "Query";
//   queryPanel.value.openRangeSearch(graphics);
//   //   graphicBufferListOuter = graphics;
//   //beginAnalysis();
// });
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
