<template>
	<div class="bottom-coord" :class="{ 's-bottom-coord': shortWidth }">
		<div style="display: flex">
			<div class="coordinate">
                   
				<div style="width: 100%">
				    <div>
					<div class="no-wrap" v-if="PropStore.bottomInfoData && PropStore.bottomInfoData.value.checkImageNumber">
						{{ PropStore.bottomInfoData.value.checkImageNumber }} 
						<span style="margin-right:8px; margin-left:3px">| </span>
						<!-- | <br v-if="isBR" /><span style="color: red">{{ PropStore.bottomInfoData.value.hint }}</span> -->
					</div>
					<div class="no-wrap">
						<br v-if="isBR" /><span style="color: red">{{ PropStore.bottomInfoData.value.hint }}</span>
					</div>
					</div>
					<!-- <span>罗盘2: {{ newV}}</span> -->
					<span v-if="mapPoint">
						<span>
							经度：{{ ToDegrees(parseFloat(mapPoint.x).toFixed(5)).substring(0, 1) != "-" ? "E" : "W" }}
							{{ ToDegrees(parseFloat(mapPoint.x).toFixed(5)).substring(0, 1) == "-"
                               ?parseFloat(mapPoint.x).toFixed(5).slice(1): parseFloat(mapPoint.x).toFixed(5)}}</span>
						<span>
							, 纬度：{{ ToDegrees(parseFloat(mapPoint.y).toFixed(5)).substring(0, 1) != "-" ? "N" : "S" }}
							{{ ToDegrees(parseFloat(mapPoint.y).toFixed(5)).substring(0, 1) == "-" ? parseFloat(mapPoint.y).toFixed(5).slice(1): parseFloat(mapPoint.y).toFixed(5)}}</span>
						<span v-show="Onemap && Onemap.MapType.value == mapType.cesium" v-if="mapPoint.heading !== undefined">
							| 倾斜角：{{ parseFloat(mapPoint.pitch + 90).toFixed(2) }}°</span>
						<span v-show="Onemap && Onemap.MapType.value == mapType.cesium" v-if="mapPoint.heading !== undefined">
							, 罗盘方位：{{ isChange?newV:parseFloat(mapPoint.heading).toFixed(2) }}°</span>
							<!-- <span>, 罗盘方位：{{ newV }}</span> -->
						<span v-show="Onemap && Onemap.MapType.value == mapType.cesium" v-if="mapPoint.height !== undefined">
							, 高度：{{ parseFloat(mapPoint.height).toFixed(2) }}</span>
						<span
							v-if="bestRatio !== undefined">&nbsp;&nbsp;|&nbsp;&nbsp;比例尺
							1:
							{{ bestRatio }}</span>
							<span v-if="PropStore.bottomInfoData.value.customInfo" v-html="PropStore.bottomInfoData.value.customInfo"></span>
					</span>
				</div>
			</div>

			<div style="padding-top:21px">
				<span class="scale-hint"
					@click="setScale(bestResolution)">以最佳比例1:{{
						bestResolution }}显示</span>
				<span class="scale-hint" style="margin-left: 10px"
					@click="setDialogVisible = true; scaleInput = null;">设置</span>
			</div>
		</div>
		<div class="copyright">{{
			PropStore.bottomInfoData.value.company ? PropStore.bottomInfoData.value.company : "南宁市自然资源信息集团有限公司" }} 提供技术支持
		</div>

		<el-dialog v-model="setDialogVisible" title="设置显示比例" width="300" destroy-on-close>
			<div class="set-scale-dialog">
				<span style="margin-left: 15px;font-weight: bold">1：</span>
				<el-select v-model="scaleInput" placeholder="请选择" filterable allow-create default-first-option type="number" size="small">
					<el-option v-for="item in scaleOptions" :key="item" :label="item" :value="item">
					</el-option>

				</el-select>
				<el-button style="margin-left: 15px;" type="text" @click="setScale(scaleInput)">设置</el-button>
			</div>

		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import * as Cesium from "@onemapkit/cesium";
import { ref, onMounted, computed, watch, onUnmounted, defineExpose } from "vue";
import {ElMessage} from 'element-plus'
import {
	getOnemap,
	mapType,
	base,
	compass
	
} from "@/onemapkit";

const props = defineProps({
	PropStore: {
		type: Object as any,
		default: null,
	},
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: true,
	}
});
const isBR = ref(document.body.clientWidth < 1500);
const shortWidth = ref(false);
const Onemap = getOnemap(props.MapControlName)
let mouseMoveEvent: any = null;
let mouseLeftDownEvent: any = null;
// Cesium相机发生移动的事件。该事件触发时，相机对应的参数完成计算，在该事件内更新底栏相关的内容
let cameraChangeEvent: any = undefined
const newV=ref<string>("null");
const bestRatio = ref<undefined | number>(undefined);
let notUpdate = false; // 是否不通过地图更新底栏的比例尺信息
const isChange=ref(false);

// 鼠标信息
const mapPoint: any = computed(() => props.PropStore.bottomInfoData.value.coordinates);

watch(() => Onemap.isMapReady.value, (val?: boolean) => {
	if (val) {
		mouseMoveEvent = Onemap.setMapEventHandler(base.MouseEventType.MOUSE_MOVE, {
			handlerName: "mouseMoveEvent",
			isThrottle: true,
			delayTime: 5,
			handler: (avg: any) => {
				if (Onemap.MapType.value == mapType.cesium) {
					isChange.value=false;
					const realtimePoint = Onemap.getMapRealtimePoint(avg.endPosition);
					props.PropStore.bottomInfoData.value.coordinates = realtimePoint;
					//console.log("-- heading1 ",props.PropStore.bottomInfoData.value.coordinates.heading)
					if(props.PropStore.bottomInfoData.value.coordinates.heading.toFixed(2)==360.00){
						props.PropStore.bottomInfoData.value.coordinates.heading=0;
					}
				} else if (Onemap.MapType.value == mapType.arcgis) {
					props.PropStore.bottomInfoData.value.coordinates = {
						x: avg.mapPoint.x,
						y: avg.mapPoint.y
					};
				}
			}
		})

		if (Onemap.MapType.value ==mapType.cesium) {
			cameraChangeEvent = Onemap.MapViewer.camera.changed.addEventListener(() => {
				// 更新比例尺
				if(notUpdate){
					notUpdate = false;
					return;
				}
				bestRatio.value = Math.round(Onemap.MapViewer.camera.scale);
        		props.PropStore.bottomInfoData.value.bestRatio = bestRatio.value;
			});
            Onemap.MapViewer.camera.moveEnd.addEventListener(handleHeadingChanged)
			
		} else if (Onemap.MapType.value == mapType.arcgis) {
			Onemap.MapViewer.on('zoom-end', () => {
				if(notUpdate){
					notUpdate = false;
					return;
				}
				bestRatio.value = Math.round(Onemap.MapViewer.getScale());
				props.PropStore.bottomInfoData.value.bestRatio = bestRatio.value;
			});
		}
		
	}
})

function ToDegrees(val:any){
      if (typeof val == "undefined" || val == "") {
        console.log("coordInfo undefined!!!!!!")
        return "";
      }
      //console.log("coordInfo OK!!!!!!")
      var i = val.toString().indexOf(".");
      //console.log("coordInfo VALUE ----",val)
      var strDu = i < 0 ? val : val.toString().substring(0, i); //获取度
      var strFen = "";
      var strMiao = "";
      var numFen=0;
      var numMiao=0;
      if (i > 0) {
        strFen = "0" + val.toString().substring(i);
        numFen=Number(strFen);
        strFen = numFen * 60 + "";
        i = strFen.indexOf(".");
        if (i > 0) {
          strMiao = "0" + strFen.substring(i);
          strFen = strFen.substring(0, i); //获取分
          numMiao=Number(strMiao);
          strMiao = numMiao * 60 + "";
          i = strMiao.indexOf(".");
		  if(i==-1){
            strMiao=String(strMiao);			
		  }else{
            strMiao = strMiao.substring(0, i); //取到小数点后面三位
            numMiao = parseFloat(strMiao); //精确小数点后面两位
            strMiao=String(numMiao);

		  }

        }
      }
       //console.log("RETURN coordInfo VALUE ----",strDu + "°" + strFen + "'" + strMiao + '"')
      return strDu + "°" + strFen + "'" + strMiao + '"'; // 这里可以修改成你想要的格式例如你可以
    };  


const handleHeadingChanged=()=>{
	isChange.value=true;
	let newHeading=Cesium.Math.toDegrees(Onemap.MapViewer.camera.heading);
	// let newV2=newHeading.toFixed(2)
	let newHeading2=newHeading.toFixed(2);
	//console.log("  newHeading ",newHeading,"  newHeading2 ",newHeading2);
	//newHeading=parseFloat(newHeading.toFixed(2));
	// newHeading=roundTo2DecimalPlaces(newHeading);
    if(newHeading===360||newHeading2==='360.00'){
		let setNorth=0.00;
		//newHeading=roundTo2DecimalPlaces(setNorth);
        //newHeading=parseFloat(setNorth.toFixed(2));
		newHeading2=setNorth.toFixed(2);
    }
    if(newHeading>360){
          newHeading=newHeading-360;
		  newHeading2=newHeading.toFixed(2);
    }
	newV.value=newHeading2;
	// newV.value=newV.value.toFixed(2);
}

function roundTo2DecimalPlaces(num:number):number{
	return Math.round(num*100)/100;
}

// 最佳比率
const bestResolution = ref(1000);

const setDialogVisible = ref(false);
const scaleInput = ref();
const scaleOptions = ref([
	1000
	, 2500
	, 5000
	, 10000
	, 25000
	, 50000
	, 100000
	, 250000
	, 500000
	, 1000000
]);

// 缩放到比例
const setScale = (val: number) => {
	if(!val) {
		ElMessage.warning("请设置有效的比例");
		return;
	}
	setDialogVisible.value = false;
	Onemap.setScale(val);
	bestRatio.value = val;
	notUpdate = true; // 关闭一次相机位置更新获取比例尺的操作（由于计算精度的问题，定位的比例尺往往和最终的比例尺不一致）
}
onMounted(() => {
	// if (props.PropStore && props.PropStore.bottomInfoData) {
	// 	bestResolution.value = props.PropStore.bottomInfoData.value.bestRatio ? props.PropStore.bottomInfoData.value.bestRatio : 1000
	// }
	
	// if(Onemap.MapViewer){
    //   Onemap.MapViewer.camera.changed.addEventListener(handleHeadingChanged);
	// }
    
});

onUnmounted(() => {
	if (mouseMoveEvent) {
		Onemap.removeMapEventHandler(base.MouseEventType.MOUSE_MOVE, {
			handlerName: "mouseMoveEvent",
		})
	}
		
	if(cameraChangeEvent){
		// 销毁底栏时，销毁监听的事件
		cameraChangeEvent();
	}
})

// 抛出外面的，更改bestResolution的方法
const updateBestResolution = (resolution: number) => {
	bestResolution.value = resolution;
}

defineExpose({
	updateBestResolution
});
</script>
<style lang="scss" scoped>
.bottom-coord {
	width: 100%;
	position: absolute;
	bottom: 0px;
	font-size: 12px;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.coordinate,
	.scale-hint,
	.copyright {
		padding: 5px;
	}

	.coordinate {
		background: rgba(255, 255, 255, 0.65);
		border-radius: 0px 4px 0 0;
		margin-right: 5px;
		padding: 5px;
	}

	.scale-hint {
		background: var(--primaryColor, #1890ff);
		color: #fff;
		border-radius: 4px 4px 0 0;
		cursor: pointer;
	}

	.copyright {
		background: rgba(255, 255, 255, 0.65);
		border-radius: 4px 0 0 0;
		margin-top: 12px;
	}

	.set-scale-dialog {
		display: flex;
		padding-top: 10px;

		.el-select {
			width: 200px;
		}
		.el-button {
			padding-top: 0;
		}
	}
}

.s-bottom-coord {
	width: 62% !important;
}

.no-wrap{
	//white-space: nowrap;
	display: inline-block;
}
</style>
