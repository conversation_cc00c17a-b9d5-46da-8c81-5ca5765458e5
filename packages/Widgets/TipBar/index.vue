<template>
  <div id="tip-bar" v-if="tipsSwitch && rescV" :style="computedStyles">
    <div class="rulertext">
      <div style="text-align: center; display: inline-block">
        <!-- 经度：E{{ parseFloat(mapPoint.x).toFixed(5)}} -->
        经度：{{
          ToDegrees(parseFloat(mapPoint.x).toFixed(5)).substring(0, 1) != "-"
            ? "E"
            : "W"
        }}
        {{
          ToDegrees(parseFloat(mapPoint.x).toFixed(5)).substring(0, 1) == "-"
            ? ToDegrees(parseFloat(mapPoint.x).toFixed(5)).slice(1)
            : ToDegrees(parseFloat(mapPoint.x).toFixed(5))
        }}
      </div>
      <div style="text-align: center; display: inline-block">
        <!-- 纬度：N{{ parseFloat(mapPoint.y).toFixed(5) }} -->
        纬度：{{
          ToDegrees(parseFloat(mapPoint.y).toFixed(5)).substring(0, 1) != "-"
            ? "N"
            : "S"
        }}
        {{
          ToDegrees(parseFloat(mapPoint.y).toFixed(5)).substring(0, 1) == "-"
            ? ToDegrees(parseFloat(mapPoint.y).toFixed(5)).slice(1)
            : ToDegrees(parseFloat(mapPoint.y).toFixed(5))
        }}
      </div>
      <div
        v-show="Onemap && Onemap.MapType.value == mapType.cesium"
        v-if="mapPoint.height !== undefined"
        style="text-align: right; display: inline-block"
      >
        高度：{{
          Number(parseFloat(mapPoint.height).toFixed(2)) < 0
            ? 0 - Number(parseFloat(mapPoint.height).toFixed(2))
            : parseFloat(mapPoint.height).toFixed(2)
        }}米
      </div>
      <div
        v-if="PropStore.bottomInfoData.value?.SJ"
        style="text-align: center; display: inline-block"
      >
        影像时间：{{ PropStore.bottomInfoData.value.SJ }}
      </div>
      <div
        v-if="PropStore.bottomInfoData.value?.FBL"
        style="text-align: center; display: inline-block"
      >
        影像精度：{{ PropStore.bottomInfoData.value.FBL }}
      </div>
    </div>
  </div>
</template>



<script lang="ts" setup>
import { ref, onMounted, computed, watch, onUnmounted } from "vue";
import { getOnemap, mapType, base } from "@/onemapkit";
import { useSwitchStore } from "../../../src/store/useSwitchStore";
const props = defineProps({
  Left: {
    type: String,
    default: null,
  },
  Top: {
    type: String,
    default: null,
  },
  rescV: {
    type: Boolean,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
});

const computedStyles = computed(() => {
  return {
    left: props.Left,
    top: props.Top,
    rescV: props.rescV,
  };
});

const tipsSwitch = ref<boolean>(true);
const initializeTipsSwitch = () => {
  const getSwitch = localStorage.getItem("switchStore");
  if (getSwitch !== null) {
    tipsSwitch.value = JSON.parse(getSwitch);
  }
};
const updateStorageChange = (event: StorageEvent) => {
  if (event.key === "switchStore") {
    // console.log("=========== switchStore START")
    tipsSwitch.value = JSON.parse(event.newValue || "false");
  }
};

// let storageListener:(event:StorageEvent)=>void;
// storageListener=(event:StorageEvent)=>{
//   console.log("=========== switchStore START")
//     if(event.key==='switchStore'&&event.storageArea===localStorage){
//       console.log("=========== switchStore ",event.newValue)
//       updateStorageChange();
//     }
//   };

// const switchStore=useSwitchStore();
// const initialization = ref(false);
// const tipsSwitch=computed(()=>{
//   if(initialization.value){
//     console.log("---get getSwitch3 ",switchStore.switchtip)
//     return switchStore.switchtip;
//   }else{
//     const getSwitch =localStorage.getItem('switchStore');
//     if(getSwitch){
//      console.log("---get getSwitch4 ",JSON.parse(getSwitch))
//      return JSON.parse(getSwitch);
//   }
//   }
//   // const getSwitch =localStorage.getItem('switchStore');
//   // if(getSwitch){
//   //  console.log("---get getSwitch3 ",switchStore.switchtip)
//   //  return JSON.parse(getSwitch);
//   // }
//   // return switchStore.switchtip;
// })

const Onemap = getOnemap(props.MapControlName);
let mouseMoveEvent: any = null;
// Cesium相机发生移动的事件。该事件触发时，相机对应的参数完成计算，在该事件内更新底栏相关的内容
let cameraChangeEvent: any = undefined;

function ToDegrees(val: any) {
  if (typeof val == "undefined" || val == "") {
    console.log("coordInfo undefined!!!!!!");
    return "";
  }
  //console.log("coordInfo OK!!!!!!")
  var i = val.toString().indexOf(".");
  //console.log("coordInfo VALUE ----",val)
  var strDu = i < 0 ? val : val.toString().substring(0, i); //获取度
  var strFen = "";
  var strMiao = "";
  var numFen = 0;
  var numMiao = 0;
  if (i > 0) {
    strFen = "0" + val.toString().substring(i);
    numFen = Number(strFen);
    strFen = numFen * 60 + "";
    i = strFen.indexOf(".");
    if (i > 0) {
      strMiao = "0" + strFen.substring(i);
      strFen = strFen.substring(0, i); //获取分
      numMiao = Number(strMiao);
      strMiao = numMiao * 60 + "";
      i = strMiao.indexOf(".");
      if (i == -1) {
        strMiao = String(strMiao);
      } else {
        strMiao = strMiao.substring(0, i); //取到小数点后面三位
        numMiao = parseFloat(strMiao); //精确小数点后面两位
        strMiao = String(numMiao);
      }
    }
  }
  //console.log("RETURN coordInfo VALUE ----",strDu + "°" + strFen + "'" + strMiao + '"')
  return strDu + "°" + strFen + "'" + strMiao + '"'; // 这里可以修改成你想要的格式例如你可以
}

// 鼠标信息
const mapPoint: any = computed(
  () => props.PropStore.bottomInfoData.value.coordinates
);

// const tipBarRef=ref<HTMLElement | null>(null);

watch(
  () => Onemap.isMapReady.value,
  (val?: boolean) => {
    if (val) {
      mouseMoveEvent = Onemap.setMapEventHandler(
        base.MouseEventType.MOUSE_MOVE,
        {
          handlerName: "mouseMoveEvent",
          isThrottle: true,
          delayTime: 5,
          handler: (avg: any) => {
            if (Onemap.MapType.value == mapType.cesium) {
              const realtimePoint = Onemap.getMapRealtimePoint(avg.endPosition);
              props.PropStore.bottomInfoData.value.coordinates = realtimePoint;
              // console.log("--- ",realtimePoint)
              // console.log("test3 ",Cesium.SceneTransforms.wgs84ToWindowCoordinates(Onemap.MapViewer.scene,cartesian))
            } else if (Onemap.MapType.value == mapType.arcgis) {
              props.PropStore.bottomInfoData.value.coordinates = {
                x: avg.mapPoint.x,
                y: avg.mapPoint.y,
              };
            }
          },
        }
      );
    }
  }
);
onMounted(() => {
  // window.addEventListener('storage',storageListener);
  // if (props.PropStore && props.PropStore.bottomInfoData) {
  // 	bestResolution.value = props.PropStore.bottomInfoData.value.bestRatio ? props.PropStore.bottomInfoData.value.bestRatio : 1000
  // }
  //window.addEventListener("mousemove",updateTipBar);
  // document.addEventListener("mousemove",updateTipBar);
  initializeTipsSwitch();
  window.addEventListener("storage", updateStorageChange);
});

onUnmounted(() => {
  if (mouseMoveEvent) {
    Onemap.removeMapEventHandler(base.MouseEventType.MOUSE_MOVE, {
      handlerName: "mouseMoveEvent",
    });
  }
  if (cameraChangeEvent) {
    // 销毁底栏时，销毁监听的事件
    cameraChangeEvent();
  }
  window.removeEventListener("storage", updateStorageChange);
});
</script>
<style lang="scss" scoped>
// .tip-bar2 {
// pointer-events: none;
// }
#tip-bar {
  // height: 60px;
  padding: 5px 10px;
  position: absolute;
  border-radius: 10px;
  font-size: 12px;
  background-color: #fff;
  opacity: 0.9;
  pointer-events: none;
}

.rulertext {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  text-shadow: 0px 0px 0px #fff, 1px 0px 0px #fff, 0px -1px 0px #fff;
}
.rulertext div {
  height: 18px;
  font-size: 11px;
  line-height: 18px;
  // display: flex;
  // flex-direction: row;
}
</style>







