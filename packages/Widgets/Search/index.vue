<template>
  <div v-if="isClose" class="searchdialog-container">
    <div
      class="searchdialogDiv"
      ref="SearchDialogRef"
      :id="props.searchDialogID"
      :style="{
        zIndex: 500,
        width: `300px`,
        height: `35px`,
        position: `absolute`,
        margin: `0px`,
        padding: `0px`,
        //输入的样式
        ...props.searchStyle,
      }"
    >
      <el-autocomplete
        ref="SearchInputRef"
        v-model="searchKey"
        clearable
        :fetch-suggestions="querySearch"
        :placeholder="props.searchPlaceHolder"
        @select="(item: any) => { searchKey = item.value; }"
        :fit-input-width="true"
        @keydown.enter.native="searchSubmit(2)"
        @clear="handleClearKey"
        >
        <template #default="{ item }">
         <div class="value">{{ item.value }}</div>
         <span @click.stop="del(item)" class="delbtn">删除</span>
      </template>
        <template #append>
          <el-button :icon="Search" @click="searchSubmit(1)" />
        </template>
      </el-autocomplete>
    </div>
    <!-- 查询结果 -->
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  type PropType,
  type CSSProperties,
  onMounted,
  nextTick,
  reactive,
  onBeforeUnmount,
} from "vue";
import { Search } from "@element-plus/icons-vue";
import { getOnemap, PropStorage, variableType, CommonEventEnum } from "../../onemapkit";
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: PropStorage,
    default: null,
    require: true,
  },
  searchDialogID: {
    type: String,
    default: "undefined2",
  },
  searchPlaceHolder:{
    type:String,
    default: "请输入搜索关键字",
  },
  searchStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  SearchHandler: {
    type: Object as PropType<(keyword: string) => any>,
    default: undefined,
  },
});
//@ts-ignore
const _Onemap = getOnemap(props.MapControlName) as OnemapClass;
const SearchDialogRef = ref();
const SearchInputRef = ref();
let isClose = ref(true);
const searchKey = ref("");


const emits = defineEmits(["SearchEvent","SearchClearEvent","InputChange"]);
const _SearchEvent = (keyword: string) => {
  emits("SearchEvent", keyword);
};
const _InputChangeEvent = (keyword: any) => {
  console.log("keyword", keyword);
  emits("InputChange", keyword);
};

props.PropStore.setStorage({
  storagekey: "searchKeys",
  isLocalStorage: true,
  initStoragevalue: [],
  variableType: variableType.ProxyReactive,
});

const test = reactive<{ key: string[] }>({ key:[] });


const suggestions = props.PropStore.getStorage("searchKeys"); 
const del= async(item:any)=>{
  SearchInputRef.value.activated=true;
  const index = suggestions.indexOf(item.value);
  if (index > -1) {
    suggestions.splice(index, 1);
  }
  refreshSuggestions();
};
const refreshSuggestions = () => {
  querySearch(searchKey.value, (results: any[]) => {
    SearchInputRef.value.suggestions = results;
  });
};

const querySearch = (queryString: string, cb: any) => {
  const queryKeys = queryString.replace(/\s+/g, " ").trim().split(" ");
  const matchCount = (targetString: any): any => {
    return queryKeys.reduce((count, keyword) => {
      return targetString.includes(keyword) ? count + 1 : count;
    }, 0);
  };
  const results = suggestions
    .filter((str:any) => matchCount(str) > 0)
    .map((str:any) => ({
      str,
      count: matchCount(str),
    }))
    .sort((a:any, b:any) => b.count - a.count)
    .map((item:any) => item.str);
  const tmp: Array<{ value: string }> = [];
  results.reverse()
  for (let i = 0; i < results.length; i++) {
    //console.log("results[i]", results[i]);
    tmp.push({ value: results[i] });
    // if (i > 9) {
    //   i = results.length;
    // }
  }
  //console.log("results", tmp);
  cb(tmp);
  nextTick(() => {
    setTimeout(() => {
        // 检查组件引用和列表长度
        if (
          SearchInputRef &&
          results.length > 0 &&
          SearchInputRef.value.suggestions?.length > 0
        ) {
          try {
            SearchInputRef.value.highlight(0);
          } catch (err) {
            console.warn('高亮失败:', err);
          }
        }
      }, 50);
  });
  _InputChangeEvent(queryString)
};
function searchSubmit(type: number) {
  if (type == 2&&searchKey.value=="") {
    return
    //@ts-ignore
  }
  //本地化存储
  if (searchKey.value != "") {
    if (suggestions.includes(searchKey.value) == false) {
      suggestions.push(searchKey.value);
    }
  }
  //searchKey为空时也反馈出去，业务层方便自己做逻辑
  if (props.SearchHandler) {
    props.SearchHandler(searchKey.value);
  }
  //触发事件

  _SearchEvent(searchKey.value);
  test.key.push(searchKey.value as any); 
}

//@ts-ignore 暂未启用
function _SearchStyle() {
  SearchDialogRef.value.style.zIndex = 500;
  SearchDialogRef.value.style.width = `300px`;
  SearchDialogRef.value.style.height = `35px`;
  SearchDialogRef.value.style.position = `absolute`;
  SearchDialogRef.value.style.margin = `0px`;
  SearchDialogRef.value.style.padding = `0px`;
  //输入的样式
  if (props?.searchStyle) {
    Object.keys(props.searchStyle).forEach((key: string) => {
      SearchDialogRef.value.style[key] = (props.searchStyle as any)[key];
    });
  }
}
function ClosePanel() {
  isClose.value = false;
}
function setSearchStyle(option: CSSProperties) {
  (option as any).forEach((itm: any) => {
    SearchDialogRef.value.style[itm.name] = itm.value;
  });
}
function handleClearKey() {
  searchKey.value = "";
  emits("SearchClearEvent");
}
onMounted(() => {
  if (_Onemap) {
    // 注册移除搜索图层事件
  _Onemap.addEventListener(CommonEventEnum.RemoveSearchLayer, handleClearKey);
  }
});

onBeforeUnmount(() => {
  _Onemap.removeEventListener(CommonEventEnum.RemoveSearchLayer, handleClearKey);
});
defineExpose({ ClosePanel, setSearchStyle });
</script>
<style lang="scss" scoped>
.searchdialog-container {
  position: absolute; /* 改为 fixed 使对话框在视口中固定 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  //z-index: 0;
  pointer-events: none; /* Ensure the div can receive pointer events */
  /*background-color: lightgreen;*/
}
.searchdialogDiv {
  /*position: relative;*/
  background: white;
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 500;
  pointer-events: auto;
  border: 1px solid rgb(255, 255, 255);
  border-radius: 2px;
  align-items: center;
  display: flex;
  position: absolute;
}
.my-autocomplete{
  height: 40px;
}
:deep(.el-autocomplete) {
  height: 100%;
  
  .el-input {
    height: 100%;
}
}
.delbtn{
  font-size: 11px;
  float: right;
  margin-right: 5px;
}
.value{
  float: left;
}
.delbtn{
  float:right;
   margin-right: 5px;
   }
  .el-autocomplete__loading .el-input__inner {
  background-image: url('data:image/svg+xml,<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.8 376.8a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 828.8a32 32 0 0 1-45.248 45.248L647.2 692.992a32 32 0 0 1 0-45.248zM828.8 195.2a32 32 0 0 1 0 45.248L692.992 376.8a32 32 0 0 1-45.248-45.248L783.552 195.2a32 32 0 0 1 45.248 0zm-452.544 452.544a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248L376.8 647.2a32 32 0 0 1 45.248 0z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 14px;
  animation: rotating 1.5s linear infinite;
}
.el-autocomplete__popper {
  transform-origin: top center;
  transition:
    opacity 0.2s ease,
    transform 0.2s ease;
}

.el-autocomplete__popper[data-popper-placement^='bottom'] {
  transform: scaleY(0.95);
}

.el-autocomplete__popper[data-popper-placement^='bottom'].is-active {
  transform: scaleY(1);
}
</style>
