<template>
  <div v-if="isClose" class="SearchResult-container">
    <div
      ref="SearchResultRef"
      class="SearchResultdiv"
      :id="props.resultDialogID"
      :style="{
        position: `absolute`,
        zIndex: 501,
        overflow: `hidden`,
        margin: `0px`,
        padding: `0px`,
        display: `flex`,
        flexDirection: `column`,
        ...props.resultStyle,
      }"
    >
      <slot></slot>
      <div v-if="isPagination"
        :style="{
          width: props?.resultStyle?.width ? props.resultStyle.width : `400px`,
        }"
        style="margin-top: auto; justify-content: center"
      >
        <div class="search-result-text">
          搜索
          <span >"{{ props.pageInfo?.keyword }}"</span>
          ,共
          <span >"{{ props.pageInfo?.total || 0 }}"</span>
           条结果，共
          <span >{{
            Math.ceil((props.pageInfo?.total || 0) / 10) > 99
              ? "99+"
              : Math.ceil((props.pageInfo?.total || 0) / 10).toString()
          }}</span>
          页
        </div>
        <el-pagination
          style="justify-content: center"
          :layout="props?.pageInfo?.layout ? props.pageInfo.layout : `prev, pager, next`"
          :page-size="props?.pageInfo?.pageSize ? props.pageInfo.pageSize : 10"
          :pager-count="props?.pageInfo?.pageCount ? props.pageInfo.pageCount : 5"
          :total="props.pageInfo?.total || 0"
          :current-page="currentPage"
          @size-change="_SizeChange"
          @current-change="_CurrentChange"
          @prev-click="_PrevClick"
          @next-click="_NextClick"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, type PropType, type CSSProperties } from "vue";
// import { getOnemap } from "../../onemapkit";

interface ParameterType {
  isQuery: Boolean;
  //该分段中的开始搜索序号
  StartCount: number;
  //在分段中的顺序号，在搜索结果中从第几个开始取出来显示
  currentNum: number;
  eventType: string;
}
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  resultDialogID: {
    type: String,
    default: undefined,
  },
  resultStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  pageInfo: {
    type: Object as PropType<{
      keyword?: string;
      total?: number;
      pageSize?: number;
      pageCount?: number;
      layout?: string;
    }>,
    default: undefined,
  },
  onQueryHandler: {
    type: Function as PropType<
      (val: { isQuery: boolean; StartCount: number; currentNum: number; eventType: string }) => any
    >,
    default: undefined,
  },
});

const emits = defineEmits(["onQuery"]);
let currentPageStage = 0;
const SearchResultRef = ref();
const isPagination=ref(false)
const currentPage=ref(1)

/** 构建分页事件参数
 * @param {number} currentPageNum 页数
 * @param {string} eventType 事件类别
 */
function poiQueryParameter(currentPageNum: number = 0, eventType: string): ParameterType {
  currentPage.value=currentPageNum
  const _pageSize = props?.pageInfo?.pageSize || 10;
  //分页数；如 1 2 3 4 5
  const _pageCount = props?.pageInfo?.pageCount || 5;
  //计算在第几分段,向服务器发送请求的时候是一段一段发送的，分页数的整数倍
  const _pageStage = (currentPageNum / _pageCount) | 0;
  //不需要重新收索
  if (currentPageStage == _pageStage) {
    return {
      isQuery: false,
      //该分段中的开始搜索序号
      StartCount: _pageStage * _pageCount * _pageSize,
      //在分段中的顺序号，在搜索结果中从第几个开始取出来显示
      currentNum: currentPageNum % _pageCount,
      eventType: eventType,
    };
  }
  //需要重新收索
  else {
    currentPageStage = _pageStage;
    return {
      isQuery: true,
      StartCount: _pageStage * _pageCount * _pageSize,
      currentNum: currentPageNum % _pageCount,
      eventType: eventType,
    };
  }
}

const _SizeChange = (val: number) => {
  const param: ParameterType = poiQueryParameter(val, "SizeChange");
  if (props.onQueryHandler) {
    props.onQueryHandler(param as any);
  }
  emits("onQuery", param);
};
const _CurrentChange = (val: number) => {
  const param: ParameterType = poiQueryParameter(val, "CurrentChange");
  if (props.onQueryHandler) {
    props.onQueryHandler(param as any);
  }
  emits("onQuery", param);
};
const _PrevClick = (val: number) => {
  const param: ParameterType = poiQueryParameter(val, "PrevClick");
  if (props.onQueryHandler) {
    props.onQueryHandler(param as any);
  }
  currentPage.value=currentPage.value-1
  emits("onQuery", param);
};
const _NextClick = (val: number) => {
  const param: ParameterType = poiQueryParameter(val, "NextClick");
  if (props.onQueryHandler) {
    props.onQueryHandler(param as any);
  }
  currentPage.value=currentPage.value+1
  emits("onQuery", param);
};

//**********************************************

let isClose = ref(true);
function SetfirstPag(){
  currentPage.value=1
}
function ClosePanel() {
  isClose.value = false;
}

function VisiblePagination(vel:boolean){
  isPagination.value=vel
}

function SearchResultStyle(option: CSSProperties) {
  (option as any).forEach((itm: any) => {
    SearchResultRef.value.style[itm.name] = itm.value;
  });
}

defineExpose({ ClosePanel, SearchResultStyle,VisiblePagination,SetfirstPag});

</script>
<style lang="scss" scoped>
.SearchResult-container {
  position: absolute; /* 改为 fixed 使对话框在视口中固定 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  //z-index: 0;
  pointer-events: none; /* Ensure the div can receive pointer events */
  /*background-color: lightgreen; */
}
.SearchResultdiv {
  /*position: relative;*/
  background: rgb(255, 255, 255);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 500;
  pointer-events: auto;
  border: 1px solid rgb(255, 255, 255);
  border-radius: 2px;
  align-items: center;
  display: flex;
  position: absolute;
  //font-weight: bold;
}
.search-result-text{
  margin-left: 10px;
}
.search-result-text span{
 color: #0094ff;
}
</style>
