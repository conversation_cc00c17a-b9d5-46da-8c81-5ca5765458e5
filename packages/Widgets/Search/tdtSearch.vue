<template>
  <Search
    :MapControlName="props.MapControlName"
    :PropStore="props.PropStore"
    :searchDialogID="props.searchDialogID"
    :searchStyle="{
      top: `-1px`,
      left: props?.searchStyle?.left ? props.searchStyle.left : `112px`,
      width: `287px`,
      ...props.searchStyle,
    }"
    :SearchHandler="props.SearchHandler"
    @SearchEvent="_SearchEvent as any"
  >
    <SearchResult
      v-if="CurrentResult.length > 0"
      :MapControlName="props.MapControlName"
      :resultDialogID="props.resultDialogID"
      :resultStyle="{
        top: '37px',
        left: props?.resultStyle?.left ? props.resultStyle.left : `-1px`,
        width: `400px`,
        height: `400px`,
        position: `absolute`,
        ...props?.resultStyle,
      }"
      :pageInfo="{
        keyword: _keyword,
        total: _TotalNum,
        ...props.pageInfo,
      }"
      :onQueryHandler="props.onQueryHandler"
      @onQuery="_onQuery"
    >
      <div
        v-if="true"
        style="
          width: 100%;
          height: calc(100% - 55px);
          background-color: rgb(250, 250, 250);
        "
      >
        <el-scrollbar style="background-color: rgb(250, 250, 250)">
          <ul id="resultUlid" style="padding-left: 0px">
            <li
              style="
                display: flex;
                padding-left: 0px;
                border-bottom: 1px solid #ebebeb; /* 这里可以调整颜色和样式 */
                padding: 3; /* 增加上下内边距 */
              "
              v-for="(item, idx) in CurrentResult"
              :id="item?.id"
              :key="idx"
              @click="_pageMouseclick($event, item, idx)"
              @mouseover="_pageMouseover($event, idx)"
              @mouseleave="_pageMouseleave($event, idx)"
            >
              <div
                style="
                  position: relative;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  width: 100px;
                  max-width: 100px;
                  padding-left: 0px;
                  pointer-events: none; /* 禁用鼠标事件  */
                "
              >
                <div
                  style="position: relative; height: 45px"
                  class="image-container"
                >
                  <img
                    style="margin-top: 10px"
                    :src="Utils.getImage('LabelRed', idx)"
                    alt="Red"
                  />
                </div>
                <span style="color: rgb(109, 109, 109)">{{
                  item.prompt[0].admins[0].adminName
                }}</span>
              </div>
              <div style="width: 100%; padding-left: 0px; pointer-events: none">
                <h4 style="color: rgb(5, 5, 255)">
                  {{ item?.title }}
                </h4>
                <h4 style="color: rgb(37, 37, 37)">
                  {{ item?.address }}
                </h4>
              </div>
            </li>
          </ul>
        </el-scrollbar>
      </div>
    </SearchResult>
  </Search>
</template>

<script lang="ts" setup>
import {
  nextTick,
  ref,
  watch,
  type PropType,
  type CSSProperties,
  onMounted,
  onUnmounted,
} from "vue";
import {
  getOnemap,
  Search,
  SearchResult,
  PropStorage,
  LayerStorage,
  Utils,
  type ITdtResultType,
  type ITdtDataItemType,
  OnemapClass,
  mapType, base
} from "../../onemapkit";
import * as Cesium from "@onemapkit/cesium"; 

// import { cloneDeep } from "lodash";
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: PropStorage,
    default: null,
  },
  LayerStore: {
    type: LayerStorage,
    default: null,
  },
  /** Search */
  searchDialogID: {
    type: String,
    default: "undefined2",
  },
  searchStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  /** 收索栏的查询事件 */
  SearchHandler: {
    type: Object as PropType<(keyword: string) => any>,
    default: undefined,
  },
  /** result */
  resultDialogID: {
    type: String,
    default: undefined,
  },
  resultStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  pageInfo: {
    type: Object as PropType<{
      keyword?: string;
      total?: number;
      pageSize?: number;
      pageCount?: number;
      layout?: string;
    }>,
    default: undefined,
  },
  /** 结果面板中的查询事件 */
  onQueryHandler: {
    type: Object as PropType<
      (val: {
        isQuery: boolean;
        StartCount: number;
        currentNum: number;
        eventType: string;
      }) => any
    >,
    default: undefined,
  },
});
const _Onemap: OnemapClass = getOnemap(props.MapControlName);
/** 当前显示在结果栏中的poi */
const CurrentResult = ref<Array<ITdtDataItemType>>([]);
/** 查询返回的总的poi */
let SearchData: Array<ITdtDataItemType> = [];
const _keyword = ref();
const _extent = ref();
/** 存放总的查询到的条数 */
const _TotalNum = ref(0);
/** 存放点击POI的ID */
let clickItemID = "";
//@ts-ignore
let isClickSearch = false;
/**cesium 中的POI图层数据 */
let TdtPoiDataLayer: any = undefined;
/** 地图点击事件 */
let _MapPoiClickEvent: any = undefined;
//*************** */

/** 注册地图的poi查询事件 */
//@ts-ignore
function MapPoiClickEvent() {
  _MapPoiClickEvent = _Onemap.setMapEventHandler(base.MouseEventType.LEFT_CLICK, {
    handlerName: "querytdtpoi",
    handler: (avg: any) => {
      if (SearchData.length == 0) return;
      if (_Onemap.MapType.value == mapType.cesium) {
        const pickedLabel = _Onemap.MapViewer.scene.pick(avg.position);
        if (pickedLabel?.id == undefined) return;
        if (Cesium.defined(pickedLabel)) {
          const _poi = pickedLabel.id;

          if (Array.isArray(_poi) == false) {
            const idx = CurrentResult.value.findIndex(
              (itm) => itm.id == _poi.id
            );
            clickItemID = czmPoiClickHander({
              id: _poi.id,
              oldid: clickItemID,
              index: idx,
              mapgraphic: _poi,
              Image: false as any,
              DataSource: TdtPoiDataLayer,
            });
          }
        }
      } else {
        if (avg?.graphic?.attributes?.id == undefined) return;
        const idx = CurrentResult.value.findIndex(
          (itm) => itm.id == avg.graphic.attributes.id
        );
        clickItemID = arcPoiClickHander({
          id: avg.graphic.attributes.id,
          oldid: clickItemID,
          index: idx,
          mapgraphic: avg,
          Image: false as any,
          DataSource: TdtPoiDataLayer,
        });
      }
    },
  });
}

/** 点击搜索组件上的搜索按钮事件
 * @param keyword
 */
function _SearchEvent(keyword: string) {
  const extent = _Onemap.getMapExtent();
  const _mapBound = `${extent.west},${extent.south},${extent.east},${extent.north}`;
  if (
    (_keyword.value == keyword && _extent.value == _mapBound) ||
    keyword == ""
  ) {
    return;
  }
  _keyword.value = keyword;
  _extent.value == _mapBound;
  const tmp = _Onemap.QueryTdtPoi({
    keyword: _keyword.value,
    mapBound: _extent.value,
  });

  tmp.then((data) => {
    if ((data as any) == false) {
      return;
    }
    _TotalNum.value = data.count;
    SearchData = data.Data;
    CurrentResult.value = SearchData.slice(0, 10);

    _Onemap.CreatePoiGraphics({
      poilayer: TdtPoiDataLayer,
      PoiData: SearchData,
      CurrentList: CurrentResult.value,
    });
  });
  clickItemID = "";
}
/** 点击分页栏上的按钮事件
 * @param option
 */
function _onQuery(option: {
  isQuery: boolean;
  //该分段中的开始搜索序号
  StartCount: number;
  //在分段中的顺序号，在搜索结果中从第几个开始取出来显示
  currentNum: number;
  eventType: string;
}) {
  if (option.isQuery) {
    const tmp: Promise<ITdtResultType> = _Onemap.QueryTdtPoi({
      keyword: _keyword.value,
      mapBound: _extent.value,
      start: option.StartCount,
    });
    tmp.then((data: ITdtResultType) => {
      SearchData = data.Data;
      CurrentResult.value = SearchData.slice(
        option.currentNum,
        option.currentNum + 10
      );
      _Onemap.CreatePoiGraphics({
        poilayer: TdtPoiDataLayer,
        PoiData: SearchData,
        CurrentList: CurrentResult.value,
      });
      clickItemID = "";
    });
  } else {
    CurrentResult.value = SearchData.slice(
      option.currentNum,
      option.currentNum + 10
    );
    _Onemap.CreatePoiGraphics({
      poilayer: TdtPoiDataLayer,
      PoiData: SearchData,
      CurrentList: CurrentResult.value,
    });
    clickItemID = "";
  }
}
//#region   收索结果列表的点击事件 返回最新点击Poi的ID
function _pageMouseclick(event: any, item: any, idx: number) {
  if (clickItemID == item.id) {
    return; //点击的同一记录 就返回
  }
  _PoiClickHander(event.currentTarget.children[0].children[0], item.id, idx);
  _Onemap.zoomToPoint({
    x: Number(item.lonlat.x),
    y: Number(item.lonlat.y),
  });
}
function _PoiClickHander(img: any, itemid: any, idx: number) {
  if (_Onemap.MapType.value == mapType.cesium) {
    clickItemID = czmPoiClickHander({
      id: itemid,
      oldid: clickItemID,
      index: idx,
      mapgraphic: undefined,
      Image: img,
      DataSource: TdtPoiDataLayer,
    });
  } else {
    clickItemID = arcPoiClickHander({
      id: itemid,
      oldid: clickItemID,
      index: idx,
      mapgraphic: undefined,
      Image: img,
      DataSource: TdtPoiDataLayer,
    });
  }
}
function czmPoiClickHander(option: {
  id?: string;
  oldid?: string;
  index: number;
  mapgraphic?: any;
  Image?: any;
  DataSource?: Cesium.CustomDataSource;
}): string {
  //#region 1.如果有旧的
  if (option?.oldid != "") {
    const _oldIdx = CurrentResult.value.findIndex(
      (itm) => itm.id == option?.oldid
    );
    //1.1 Image
    requestAnimationFrame(() => {
      const _tmp = document.getElementById(option?.oldid as any);
      if (_tmp) {
        (_tmp.children[0].children[0].children[0] as any).src = Utils.getImage(
          "LabelRed",
          _oldIdx
        );
      }
    });
    //1.2 Map
    if (option?.DataSource) {
      const oldObj: any = option.DataSource.entities.getById(
        option?.oldid as any
      );
      oldObj.billboard = {
        image: Utils.getImage("LabelRed", _oldIdx), // 替换为你的图像链接
        scale: 1.0,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴近地面
      };
    }
  }
  //#endregion

  //#region  2.当前的
  //2.1 Image
  requestAnimationFrame(() => {
    if (option?.Image) {
      option.Image.src = Utils.getImage("LabelBlue", option?.index);
    } else {
      const _idx = CurrentResult.value.findIndex((itm) => itm.id == option?.id);
      const _tmp = document.getElementById(option?.id as any);
      if (_tmp) {
        (_tmp.children[0].children[0].children[0] as any).src = Utils.getImage(
          "LabelBlue",
          _idx
        );
      }
    }
  });

  //2.2 当前的
  if (option?.mapgraphic) {
    option.mapgraphic.billboard = {
      image: Utils.getImage("LabelBlue", option?.index), // 替换为你的图像链接
      scale: 1.0,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴近地面
    };
  } else {
    const _idx = CurrentResult.value.findIndex((itm) => itm.id == option?.id);
    const _Obj: any = (option?.DataSource as any).entities.getById(option.id);
    if (_Obj) {
      _Obj.billboard = {
        image: Utils.getImage("LabelBlue", _idx), // 替换为你的图像链接
        scale: 1.0,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴近地面
      };
    }
  }
  //#endregion
  return option.id as any;
}
function arcPoiClickHander(option: {
  id?: string;
  oldid?: string;
  index: number;
  mapgraphic?: any;
  Image?: any;
  DataSource?: Cesium.CustomDataSource | any;
}): string {
  //#region 1.如果有旧的
  if (option?.oldid != "") {
    const _oldIdx = CurrentResult.value.findIndex(
      (itm) => itm.id == option?.oldid
    );
    //1.1 Image
    requestAnimationFrame(() => {
      const _tmp = document.getElementById(option?.oldid as any);
      if (_tmp) {
        (_tmp.children[0].children[0].children[0] as any).src = Utils.getImage(
          "LabelRed",
          _oldIdx
        );
      }
    });
    //1.2 Map
    if (option?.DataSource) {
      const oldgraphic: any = option?.DataSource.graphics.find(
        (itm: any) => itm.attributes.id == (option?.oldid as any)
      );
      oldgraphic.symbol.url = Utils.getImage("LabelRed", _oldIdx);
      oldgraphic.setSymbol(oldgraphic.symbol);
    }
  }
  //#endregion

  //#region  2.当前的
  //2.1 Image
  requestAnimationFrame(() => {
    if (option?.Image) {
      option.Image.src = Utils.getImage("LabelBlue", option?.index);
    } else {
      const _idx = CurrentResult.value.findIndex((itm) => itm.id == option?.id);
      const _tmp = document.getElementById(option?.id as any);
      if (_tmp) {
        (_tmp.children[0].children[0].children[0] as any).src = Utils.getImage(
          "LabelBlue",
          _idx
        );
      }
    }
  });

  //2.2 当前的
  if (option?.mapgraphic) {
    option.mapgraphic.graphic.symbol.url = Utils.getImage(
      "LabelBlue",
      option?.index
    );
    option.mapgraphic.graphic.setSymbol(option.mapgraphic.graphic.symbol);
  } else {
    const _idx = CurrentResult.value.findIndex((itm) => itm.id == option?.id);
    const oldgraphic: any = option?.DataSource.graphics.find(
      (itm: any) => itm.attributes.id == (option?.id as any)
    );
    oldgraphic.symbol.url = Utils.getImage("LabelBlue", _idx);
    oldgraphic.setSymbol(oldgraphic.symbol);
  }
  //#endregion
  return option.id as any;
}
//#endregion

/** 切换地图后更新当前选择的兴趣点 */
function _PoiImage(itemid: any) {
  const _idx = CurrentResult.value.findIndex((itm) => itm.id == itemid);
  if (_idx == -1) return;
  if (_Onemap.MapType.value == mapType.cesium) {
    {
      const _Obj: any = (TdtPoiDataLayer as any).entities.getById(itemid);
      _Obj.billboard = {
        image: Utils.getImage("LabelBlue", _idx), // 替换为你的图像链接
        scale: 1.0,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴近地面
      };
    }
  } else {
    const oldgraphic: any = (TdtPoiDataLayer as any).graphics.find(
      (itm: any) => itm.attributes.id == (itemid as any)
    );
    oldgraphic.symbol.url = Utils.getImage("LabelBlue", _idx);
    oldgraphic.setSymbol(oldgraphic.symbol);
  }
}
//#region 收索结果列表的鼠标移进移出效果
function _pageMouseover(event: any, idx: number) {
  const listItem: HTMLDivElement = event.currentTarget.children[0].children[0];
  (listItem.children[0] as any).src = Utils.getImage("LabelBlue", idx);
  (listItem.children[0] as any).style.marginTop = "5px";
}
function _pageMouseleave(event: any, idx: number) {
  if (clickItemID == event.currentTarget.id) {
    return;
  }
  const listItem: HTMLDivElement = event.currentTarget.children[0].children[0];
  (listItem.children[0] as any).src = Utils.getImage("LabelRed", idx);
  (listItem.children[0] as any).style.marginTop = "10px";
}
//#endregion

onMounted(() => {
  _keyword.value = "";
});
onUnmounted(() => {
  SearchData.length = 0;
  _Onemap.removeMapEventHandler(base.MouseEventType.LEFT_CLICK, {
    handlerName: "querytdtpoi",
    Handler: _MapPoiClickEvent,
  });
}),
  watch(
    () => _Onemap.isMapReady.value,
    (newval) => {
      if (newval) {
        nextTick(() => {
          _keyword.value = "";
          const tmp = _Onemap.CreatePoiGraphicLayer();
          if (tmp instanceof Promise) {
            tmp.then((layer: any) => {
              TdtPoiDataLayer = layer;
              _Onemap.CreatePoiGraphics({
                poilayer: TdtPoiDataLayer,
                PoiData: SearchData,
                CurrentList: CurrentResult.value,
              });
              if (CurrentResult.value.length > 0 && clickItemID != "") {
                _PoiImage(clickItemID);
              }
            });
          } else {
            TdtPoiDataLayer = tmp;
            _Onemap.CreatePoiGraphics({
              poilayer: TdtPoiDataLayer,
              PoiData: SearchData,
              CurrentList: CurrentResult.value,
            });
            if (CurrentResult.value.length > 0 && clickItemID != "") {
              _PoiImage(clickItemID);
            }
          }
          MapPoiClickEvent();
        });
      }
    }
  );
</script>
<style lang="scss" scoped>
.tdtSearchResult-container {
  position: absolute; /* 改为 fixed 使对话框在视口中固定 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 0;
  pointer-events: none; /* Ensure the div can receive pointer events */
  /*background-color: lightgreen; */
}
.SearchResultdiv {
  /*position: relative;*/
  background: rgb(255, 255, 255);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 500;
  pointer-events: auto;
  border: 1px solid rgb(255, 255, 255);
  border-radius: 2px;
  align-items: center;
  display: flex;
  position: absolute;
  font-weight: bold;
}
</style>
