<template>
	<!-- 放大缩小 -->
	<div class="map-zoom map-righ-button">
		<Icons.zoomAddIcon title="放大" class="zoom-icon" @click="zoomIn" />
		<Icons.zoomSubIcon title="缩放" class="zoom-icon" @click="zoomOut" />
	</div>
</template>

<script lang="ts" setup>
import {
	getOnemap,
	Icons,
	mapType,
} from "../../onemapkit";
import * as Cesium from "@onemapkit/cesium";

const props = defineProps({
	PropStore: {
		type: Object as any,
		default: null,
	},
	MapControlName: {
		type: String,
		default: "mainMapControl",
		require: true,
	}
});

const _inOnemap = getOnemap(props.MapControlName)

const zoomIn = () => {
	_inOnemap.zoomIn();

	// 同步定位
	if (props.PropStore && props.PropStore.updateLocationEvent) {
		setTimeout(() => {
			let extent:any;
			if (_inOnemap.MapType.value == mapType.cesium) {
				extent = _inOnemap.getExtent();
				extent.wkid = 4490;
				extent.pitch = Cesium.Math.toDegrees(_inOnemap.MapViewer.camera.pitch);
				extent.roll = Cesium.Math.toDegrees(_inOnemap.MapViewer.camera.roll);
				extent.heading = Cesium.Math.toDegrees(_inOnemap.MapViewer.camera.heading);
			} else {
				extent = _inOnemap.MapViewer.extent;
				extent.wkid = 4490;
			}
			props.PropStore.updateLocationEvent(extent);
		}, 1000)
	}
}

const zoomOut = () => {
	_inOnemap.zoomOut();

	// 同步定位
	if (props.PropStore && props.PropStore.updateLocationEvent) {
		setTimeout(() => {
			let extent:any;
			if (_inOnemap.MapType.value == mapType.cesium) {
				extent = _inOnemap.getExtent();
				extent.wkid = 4490;
				extent.pitch = Cesium.Math.toDegrees(_inOnemap.MapViewer.camera.pitch);
				extent.roll = Cesium.Math.toDegrees(_inOnemap.MapViewer.camera.roll);
				extent.heading = Cesium.Math.toDegrees(_inOnemap.MapViewer.camera.heading);
			} else {
				extent = _inOnemap.MapViewer.extent;
				extent.wkid = 4490;
			}
			props.PropStore.updateLocationEvent(extent);
		}, 1000)
	}
}

</script>

<style lang="scss" scoped>
.map-zoom {
	position: absolute;
	bottom: 36px;
	right: 14px;
	font-size: 12px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 1px;
	cursor: pointer;

	.zoom-icon {
		margin: 4px 0;
		width: 28px;
		height: 24px;
	}
}
</style>
