import * as identify from "@arcgis/core/rest/identify";
import IdentifyParameters from "@arcgis/core/rest/support/IdentifyParameters";
import Point from "@arcgis/core/geometry/Point.js";
import { type IPosition } from "../../onemapkit";
import Extent from "@arcgis/core/geometry/Extent";
import _ from "lodash";
/**
 * 点选方法
 * @param mapPoint 地图点位
 * @param Onemap Onemap对象
 * @param Store
 * @param mapOptions identify需要传extent,width,height等参数
 */
export async function mapIdentify(
  mapPoint: IPosition,
  LayerList: any,
  // PropStore: any,
  mapOptions: any,
  propertyData: any,
  isIdentifyFinish:any
) {
  console.log('点选参数mapPoint,LayerList,mapOptions',mapPoint,LayerList,mapOptions);

  let keyIdValue = 0;
  let finishnum=0;
  if (!mapOptions || !mapOptions.extent) {
    console.log("mapOptions or mapOptions.extent is null");
    return;
  }
  const queryPoint = new Point({
    x: mapPoint.x,
    y: mapPoint.y,
    spatialReference: { wkid: mapPoint.wkid },
  });

  // 偏移量
  const deltaLat = 0.000270; // 30米对应的纬度偏移
  const deltaLon = 0.000296; // 30米对应的经度偏移

  const tolerancePixels = 30; // 前提的 tolerance（像素）设置

  // 计算边界
  const minX = mapPoint && mapPoint.x ?(mapPoint.x - deltaLon): 108; // 西边界
  const maxX = mapPoint && mapPoint.x ?(mapPoint.x + deltaLon): 108; // 东边界
  const minY = mapPoint && mapPoint.y ?(mapPoint.y - deltaLat): 22; // 南边界
  const maxY = mapPoint && mapPoint.y ?(mapPoint.y + deltaLat): 22; // 北边界

  const queryExtent = new Extent({
    "spatialReference": {
      "wkid": 4490
    },
    "xmin": minX,
    "ymin": minY,
    "xmax": maxX,
    "ymax": maxY
  })
  if (!queryPoint) {
    console.log("create queryPoint failed");
    return;
  }
  //console.log("11");
  let queryIds = [] as any;

  LayerList.forEach((value: any) => {
    queryIds.push(value);
  });
  if (queryIds && queryIds.length > 0) {
    let params = new IdentifyParameters();
    params.geometry = queryPoint;
    params.returnGeometry = true;
    params.returnFieldName = true;
    params.returnUnformattedValues = false;
    params.tolerance = 3;
    params.layerOption = "visible";
    params.width = mapOptions.width;
    params.height = mapOptions.height;
    params.mapExtent = mapOptions.extent;

    // params.geometry = queryPoint;
    // params.returnGeometry = true;
    // params.layerOption = "visible";
    // params.returnUnformattedValues = true;
    // params.tolerance = 30;
    // // params.imageDisplay = "1920,879,96", // 固定值
    // params.width = 1920;
    // params.height = 879;
    // params.dpi = 96;
    // params.mapExtent = queryExtent; // 动态计算的地图范围

    const systemFields = ["Shape"];
    // PropStore.setPropertyData([]);
    await queryIds.forEach(async (checkItem: any) => {
      //debugger
      //let qyerLayer = mapObj.getLayer(checkItem.id)
      if (
        checkItem &&
        checkItem.subLayers &&
        checkItem.subLayers.length > 0 &&
        checkItem.subLayers[0].url
      ) {
        // PropStore.setPropertyVisible(true);
        //根据ID获取图层信息，拿到子图层配置
        //let newLayerInfoRes = await layerMethods.getLayerInfo(checkItem.id);
        //console.log("new layer info:", newLayerInfoRes);
        // let minScale = JSON.parse(newLayerInfoRes.data.attribute).minScale;
        // if (minScale < scale.value && minScale != 0) {
        //   return;
        // }
        // let newLayerInfo = null;
        // let newLayerInfoStr =
        //   newLayerInfoRes &&
        //   newLayerInfoRes.data &&
        //   newLayerInfoRes.data.attribute
        //     ? newLayerInfoRes.data.attribute
        //     : null;
        // if (newLayerInfoStr) {
        //   try {
        //     newLayerInfo = JSON.parse(newLayerInfoStr);
        //   } catch (ex) {
        //     console.log(ex);
        //   }
        // }

        // if (
        //   newLayerInfo &&
        //   newLayerInfo.layers &&
        //   newLayerInfo.layers.length > 0
        // ) {
        //   let visibieLayer = newLayerInfo.layers.filter(
        //     (x) => x.defaultVisibility
        //   );
        //   let ids = [];
        //   for (let { id } of visibieLayer) {
        //     ids.push(id);
        //   }
        //   params.layerIds = ids;
        // }
        //console.log("identify params:", checkItem.subLayers[0]?.url);
        keyIdValue=keyIdValue+1
        identify.identify(checkItem.subLayers[0]?.url, params).then(
          (response: any) => {

            //console.log('isIdentifyFinish',isIdentifyFinish);
            if (response.results && response.results.length > 0) {
              //获取所有子图层
              let tempDatas = JSON.parse(JSON.stringify(response.results));
              //console.log("identify result", tempDatas);
              let layerIdList = [] as any;
              let subLayers = [] as any;
              let layerInfoSublayers = null as any;
              let layerTagId = "";
              if (checkItem.options && checkItem.options.layerTagId) {
                layerTagId = checkItem.options.layerTagId;
              }
              tempDatas.forEach((item: any) => {
                item.parentId = checkItem.subLayers[0].layerid
                if (layerIdList.indexOf(item.layerId) < 0) {
                  layerIdList.push(item.layerId);
                  //subLayers.push({ layerId: item.layerId, layerlabel: item.layerName, layerTagId: layerTagId })
                  subLayers.push({
                    layerId: item.layerId,
                    layerlabel: checkItem.name,
                    layerTagId: layerTagId,
                  });
                }
              });


              subLayers.forEach(async (subLayer: any) => {
                let subChildren = tempDatas.filter((item: any) => {
                  return item.layerId == subLayer.layerId;
                });
                let tempLayerSubLayer = null as any;
                if (layerInfoSublayers) {
                  tempLayerSubLayer = layerInfoSublayers.find((item: any) => {
                    return item.id == subLayer.layerId;
                  });
                }
                //console.log("temp layer sublayer info,", tempLayerSubLayer);
                subChildren.forEach((item: any) => {
                  //生成keyId，
                  item.keyId = "pkid" + ++keyIdValue;
                  //console.log("item.attributes:", item);
                  item.layerRootId = checkItem.id;
                  //找显示字段对应的字段配置（identify返回的是alias）
                  if (
                    tempLayerSubLayer &&
                    tempLayerSubLayer.displayField &&
                    tempLayerSubLayer.fields
                  ) {
                    let tempField = tempLayerSubLayer.fields.find(
                      (item: any) => {
                        return (
                          item.name == tempLayerSubLayer.displayField ||
                          item.alias == tempLayerSubLayer.displayField
                        );
                      }
                    );
                    if (tempField) {
                      item.layerlabel = item.attributes[tempField.alias]
                        ? item.attributes[tempField.alias]
                        : item.attributes[tempField.name];
                    }
                  } else if (item.displayFieldName) {
                    item.layerlabel = item.attributes[item.displayFieldName];
                  } else {
                    let obj = Object.getOwnPropertyNames(item.attributes);
                    let objName = obj.find(
                      (x) => x.toLowerCase() == "objectid"
                    );
                    if (objName) {
                      item.layerlabel = item.attributes[objName];
                    }
                  }
                  //先控制显隐

                  let order = 0;
                  // 主逻辑
                  if (tempLayerSubLayer && tempLayerSubLayer.fields && item) {
                    item.layerlabel = getLayerLabel(tempLayerSubLayer, item);

                    let orderAttributes = null;
                    if (tempLayerSubLayer && tempLayerSubLayer.fields) {
                      orderAttributes = _.cloneDeep(tempLayerSubLayer.fields);
                      for (let one of orderAttributes) {
                        one.order = order;
                        ++order;
                      }
                    }

                    let displayAttributes = processDisplayAttributes(item, orderAttributes, systemFields);
                    displayAttributes.sort((a: any, b: any) => a.order - b.order);

                    for (let one of displayAttributes) {
                      delete one.order;
                    }

                    item.displayAttributes = displayAttributes;
                    }
                });
                subLayer.children = subChildren;
                // PropStore.pushPropertyData(subLayer);
                propertyData.push(subLayer)


              });
              //propertyData=
              //   if (queryInteval <= 0) {
              //     queryInteval = new Date().getTime();
              //   }
              //   // 每次更新treeData的间隔
              //   const constInterval = 100;
              //   let nowTime = new Date().getTime();
              //   let interval = 0;
              //   if (nowTime - queryInteval > constInterval) {
              //     interval = constInterval;
              //     queryInteval = new Date().getTime() + constInterval;
              //   } else {
              //     interval = Math.abs(nowTime - queryInteval) + constInterval;
              //     queryInteval = new Date().getTime() + interval;
              //   }
            }
                finishnum=finishnum+1
                if(finishnum==queryIds.length){
                      isIdentifyFinish.value=true
                }

          },
          function (ex: any) {
            finishnum=finishnum+1
            if(finishnum==queryIds.length){
              isIdentifyFinish.value=true
            }
            console.log(ex);
          }
        );
      } else {
        console.log("maplayer does not have sublayers");
      }
    });
  } else {
    console.log("no checked layers");
  }
}
function getLayerLabel(tempLayerSubLayer: any, item: any): string {
  if (tempLayerSubLayer && tempLayerSubLayer.displayField && tempLayerSubLayer.fields && item) {
    let tempField = tempLayerSubLayer.fields.find(
      (item: any) => {
        return (
          item.name == tempLayerSubLayer.displayField ||
          item.alias == tempLayerSubLayer.displayField
        );
      }
    );
    if (tempField) {
      return item.attributes[tempField.alias] ? item.attributes[tempField.alias] : item.attributes[tempField.name];
    }
  } else if (item && item.displayFieldName) {
    return item.attributes[item.displayFieldName];
  } else if (item && item.attributes) {
    let obj = Object.getOwnPropertyNames(item.attributes);
    let objName = obj.find(
      (x) => x.toLowerCase() == "objectid"
    );
    if (objName) {
      return item.attributes[objName];
    }
  }
  return '';
}

function processDisplayAttributes(item: any, orderAttributes: any[], systemFields: string[]): any[] {
  let displayAttributes = [];
  let order = 0;
  for (const attItem of Object.keys(item.attributes)) {
    let findSystem = false;
    for (let i = 0; i < systemFields.length; i++) {
      if (attItem.toLowerCase() == systemFields[i].toLowerCase()) {
        findSystem = true;
        break;
      }
    }
    if (!findSystem) {
      if (orderAttributes) {
        let tempField = orderAttributes.find((item: any) => {
          return item.name == attItem || item.alias == attItem;
        });
        if (tempField) {
          let val = item.attributes[attItem];
          if (tempField.visibility) {
            let labelStr = tempField.alias;
            if (tempField.thirdAlias) {
              labelStr = tempField.thirdAlias;
            }
            displayAttributes.push({
              label: labelStr,
              value: val,
              order: tempField.order,
            });
          }
        }
      } else {
        displayAttributes.push({
          label: attItem,
          value: item.attributes[attItem],
          order: order,
        });
        ++order;
      }
    }
  }
  return displayAttributes;
}
