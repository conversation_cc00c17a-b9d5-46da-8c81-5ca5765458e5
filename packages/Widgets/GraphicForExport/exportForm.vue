<template>
    <div>
      <el-form :model="exportFormModel">
        <el-form-item
          label="导出格式"
          label-width="80px"
          prop="format"
          :required="true"
          :style="{marginBottom: '10px'}"
        >
          <el-select size="small" v-model="exportFormModel.format" placeholder="请选择格式">
            <el-option
              v-for="item in exportFormat"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="坐标类型"
          label-width="80px"
          prop="coord"
          :required="true"
          :style="{marginBottom: '10px'}"
        >
          <el-select size="small" v-model="exportFormModel.coord" placeholder="请选择坐标系">
            <el-option
              v-for="item in coords"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
  
      <span class="dialog-footer">
        <el-button @click="closeDialog" size="small">取 消</el-button>
        <el-button
          type="primary"
          style="margin-right: 27px"
          @click="exportCroodData()"
          size="small"
        >导出</el-button>
      </span>
    </div>
  </template>
  
  
  <script lang="ts" setup>
  import { ref, defineProps, onMounted, defineEmits } from "vue";
  import { saveAs } from "file-saver";
  import tokml from "geojson-to-kml";
  
      let props = defineProps ({
        PropStore: {
          type: Object as any,
          default: null,
        },
        exportData: {
          type: Object,
          default: null,
        },
        Onemap: {
          type: Object,
          default: null,
        },
      })
  
      const exportFormModel = ref({
        format:'geojson',
        coord: 4490
      }); //坐标导出条件
  
      const coords = ref([
        {
          label: "2000地理坐标系（4490）",
          value: 4490,
        },
        {
          label: "2000平面坐标系_有带号（4524）",
          value: 4524,
        },
        {
          label: "2000平面坐标系_无带号（4545）",
          value: 4545,
        },
        {
          label: "WGS84地理坐标系（4326）",
          value: 4326,
        },
        {
          label: "墨卡托投影（3857）",
          value: 3857,
        },
        {
          label: "西安80坐标系（2360）",
          value: 2360,
        },
        {
          label: "北京54坐标系（2412）",
          value: 2412,
        },
      ]);
  
      const exportFormat = ref([
        {
          label: "Geojson",
          value: "geojson",
        },
        {
          label: "ArcGISJson",
          value: "ArcGISJson",
        },
        {
          label: "kml",
          value: "kml",
        }
      ]);
      onMounted(() => {
        exportFormModel.value = {
          format: "ArcGISJson",
          coord: coords.value[0].value,
        };
      });
  
      //下载坐标
      const exportCroodData = () => {
        let json = createGeoJsonData(props.exportData);
        let name = "data";
        if (exportFormModel.value.format == "geojson") {
          let blob = new Blob([JSON.stringify(json)], {
            type: "application/json;charset=ansi",
          });        
          saveAs(blob, name + ".geojson");
        } else if (exportFormModel.value.format == "ArcGISJson") {
          json = geoJsonToEsriJson(json);
          let blob = new Blob([JSON.stringify(json)], {
            type: "application/json;charset=ansi",
          });
          saveAs(blob, name + ".json");
        } else if (exportFormModel.value.format == "kml") {
          
  
          if(json.length > 1){  
            const newJson:any = {
              type: "FeatureCollection",
              features: [],
            };
            for (const it of json) {
              newJson.features.push(it.features[0])
            }
            console.log('newJson',newJson);
            
            let kml = new File([tokml(newJson)], name + ".kml", {
              type: "text/xml;charset=utf-8",
            });
            saveAs(kml);
          }else{
            let kml_doc = tokml(json);
            let kml = new File([kml_doc], name + ".kml", {
              type: "text/xml;charset=utf-8",
            });
            saveAs(kml);
          }
        }
      };
      const geoJsonToEsriJson = (geoJsons:any) => { 
          let esriJson:any = {
            displayFieldName: "",
            fieldAliases: {
              FID: "FID",
              LABEL: "LABEL",
              LAYER_NAME: "LAYER_NAME",
              FIELD: "FIELD",
            },
            geometryType: "esriGeometryPolygon",
            spatialReference: {
              wkid: exportFormModel.value.coord,
              latestWkid: exportFormModel.value.coord,
            },
  
            fields: [
              {
                name: "FID",
                type: "esriFieldTypeOID",
                alias: "FID",
              },
              {
                name: "LABEL",
                type: "esriFieldTypeString",
                alias: "LABEL",
                length: 300,
              },
              {
                name: "LAYER_NAME",
                type: "esriFieldTypeString",
                alias: "LAYER_NAME",
                length: 300,
              },
              {
                name: "FIELD",
                type: "esriFieldTypeString",
                alias: "FIELD",
                length: 300,
              },
            ],
  
            features: [],
          };
          for (let i = 0; i < geoJsons.features.length; i++) {
            let feature = geoJsons.features[i];
            let esriFeature:any = {
              geometry: {
                rings: feature.geometry.coordinates,
              },
              attributes: feature.properties,
            };
            esriJson.features.push(esriFeature);
          }
        return esriJson;
      }
      //生成Geojson数据
      const createGeoJsonData = (rows:any) => {
        //修改坐标系
        let i = 0;
        //坐标转换
        const geojson:any = {
          type: "FeatureCollection",
          features: [],
        };
        rows.forEach((item:any) => {
          let geo:any
          if (
            (item.geometry.spatialReference.wkid != exportFormModel.value.coord)
          ) {
              let points = item.geometry.rings[0].map((it:any)=>{
                return {
                  x:it[0],
                  y:it[1]
                }
              })
              let newGeometry = props.Onemap.projectPoints(item.geometry.spatialReference.wkid,exportFormModel.value.coord,points)
              geo = {
                rings:[newGeometry],
              }
          }
          else{
            geo = item.geometry
          }
  
          if (item.geometry.type == "polygon" || item.geometry.rings) {
            geojson.features.push({
              type: "Feature",
              geometry: {
                type: "Polygon",
                coordinates: geo.rings,
              },
              attributes: item.attributes,
              properties: {
                FID: i++,
                LABEL: item.sonName,
                LAYER_NAME: item.parentName,
                FIELD: item.id,
              },
            });
          }
  
        });
        return geojson;
      };
      const emit = defineEmits(['setGraphicForExportShow']);
      const closeDialog = () => {
        emit('setGraphicForExportShow',false)
      };
    
  
  </script>
  
  <style>
  </style>
  