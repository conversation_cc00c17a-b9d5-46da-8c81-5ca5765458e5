<template>
    <div class="properties border exportPositon">
      <div class="head">
        <div><span>图形导出工具</span></div>
        <div class="close" @click="closePanel">
          <el-icon>
                      <Close />
                  </el-icon>
        </div>
      </div>
      <div class="content">
        <span v-show="graphics.length==0">请先在地图上点选图斑，并在弹出属性面板中点击【图形数据导出】按钮，将图形添加到导出列表中！</span>
        <div class="list-panel">
          <div v-show="graphics.length>0">
            <el-button @click="selectAll()" size="small">全选</el-button>
            <el-button @click="cancelCheckAll()" size="small">取消选择</el-button>
            <el-button @click="deleteAll()" size="small">清空列表</el-button>
            <div class="multi-select">
              <el-checkbox-group
                v-model="selectedItems"
                direction="vertical"
                class="checkbox-group"
                @change="handleCheckChange"
              >
                <el-checkbox
                  v-for="(item, index) in graphics"
                  :key="item.id"
                  :label="item"
                  style="padding:inherit; white-space: pre-wrap;
                  width: 200px; margin-bottom: 10px;"
                  class="checkboxClass"
                >
                    {{ index +1}}.{{ item.parentName }}_{{item.sonName}}
                    <el-tooltip content="点击这里将图斑从列表中移除" placement="bottom" effect="light">
                      <el-icon :style="{color:'red'}" @click="deleteItem(item)"><Close /></el-icon>
                    </el-tooltip>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div>
            <export-form v-show="selectedItems.length>0 && graphics.length>0" :PropStore="PropStore" @setGraphicForExportShow="closePanel" :exportData="selectedItems" :Onemap="Onemap"></export-form>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { onMounted, ref, watch, defineProps, computed, defineEmits } from "vue";
  import { mapType, getOnemap } from "../../onemapkit";
  import { ElMessage } from "element-plus";
  import { Close } from "@element-plus/icons-vue";
  import exportForm from "./exportForm.vue";
  
  const props = defineProps({
    MapControlName: {
      type: String,
      default: "mainMapControl",
      require: true,
    },
    PropStore: {
      type: Object as any,
      default: null,
    },
    Onemap: {
      type: Object as any,
      default: null,
    },
  
  });

  const changedata = (data:any)=>{
    graphics.value = data
  }
  defineExpose({changedata})
  // const graphics:any = computed(()=> {
  //   if(props.PropStore.getStorage('GraphicForExportList')){
  //     return props.PropStore.getStorage('GraphicForExportList').value
  //   }else{
  //     return []
  //   }
  // })
  const graphics:any = ref([])
  watch(
    ()=> props.PropStore.getStorage('GraphicForExportList').value,
    (val:boolean)=>{
      console.log('到这监听GraphicForExportList', val);
      graphics.value = val
    },
    {
      immediate: true,
      deep: true
    }
  )
  const selectedItems:any = ref([])
  const emit = defineEmits(['setGraphicForExportShow']);
  const closePanel = () => {
    emit('setGraphicForExportShow',false)
  }
  const selectAll = () => {
    selectedItems.value = graphics.value
  }
  const cancelCheckAll = () => {
    selectedItems.value = []

  }
  const deleteAll = () => {
    graphics.value = []
    selectedItems.value = []
    setstore()
  }
  const handleCheckChange = (row:any) => {
    console.log('selectedItems.value',selectedItems.value);
    
  }
  const deleteItem = (row:any) => {
    console.log('row',row);
    
    let oindex = graphics.value.findIndex((item:any)=>{
      return item.id == row.id
    })
    let selectIndex = selectedItems.value.findIndex((item:any)=>{
      return item.id == row.id
    })
    graphics.value.splice(oindex,1)
    if(selectIndex >= 0){
      selectedItems.value.splice(selectIndex,1)
    }
    setstore()
  }
  const setstore=()=>{
    props.PropStore.setStorage({
      storagekey: "GraphicForExportList",
      isLocalStorage: true,
      initStoragevalue: graphics.value,
      variableType: 1
    })
  }
  
  onMounted(() => {
    // 加载图标
  });
  </script>
  <style lang="scss" scoped>
  .exportPositon{
    z-index: 500;
    width: 260px;
    // height: 160px;
    position: absolute;
    top: 20px;
    right: 70px;
    background: white;
    .head{
      background: #e1e4e6;
      height: 40px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .close{
        width: 20px;
        height: 20px;
        border-radius: 10px;
        background: #000;
        color: #fff;
        font-size: 18px;
        cursor: pointer;
      }
    }
    .content{
      padding: 10px;
    }
    .multi-select {
      display: flex;
      flex-direction: column;
      max-height: 500px;
      padding-top: 10px;
      overflow-y: auto;
      overflow-x: hidden;
    }
    :deep(.el-checkbox){
      height: auto !important;
    }
    .itemName{
      max-height: 65px;
      width: 184px; 
    }
    .closeIcon{
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  </style>
  