<template>
  <div class="" v-loading="isPropertyLoading">
    <div v-if="treeData && treeData.length" class="properties border">
      <div class="properties__tree">
        <el-tree
          :data="treeData"
          node-key="keyId"
          :props="{
            label: 'layerlabel',
          }"
          :expand-on-click-node="false"
          default-expand-all
          :check-on-click-node="false"
          @node-click="onNodeClick"
          @current-change="onNodeChange"
          ref="treeRef"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node-1">
              <i
                :class="[
                  'node-pre-icon',
                  'iconfont',
                  data.children && data.children.length
                    ? 'icon-zhankaidaqianzhui'
                    : 'icon-zhankaiqianzhui',
                ]"
              ></i>
              <span
                :title="node.label"
                :class="[
                  'custom-tree-node-1__label',
                  data.disabled ? 'tree-disabled' : '',
                  data.layerId == 'disabled' ? 'tree-root-disabled' : '',
                ]"
                >{{ node.label }}</span
              >
            </div>
          </template>
        </el-tree>
      </div>

      <div class="properties__content">
        <table
          v-if="attributes && attributes.length"
          class="properties__table"
          width="100%"
          cellpadding="0"
          cellspacing="0"
        >
          <tr
            v-for="(item, index) in attributes
                  ? attributes.filter(
                      (x:any) => !['__BDCDYH', '__ZRZGUID'].includes(x.label)
                    )
                  : []"
            :key="'attributes' + index"
            class="properties__table-tr"
          >
            <th class="properties__table-th">{{ item.label }}</th>
            <td class="properties__table-td">
              <span>{{
                item.value == "Null" ? "" : handleData(item.value)
              }}</span>
            </td>
          </tr>
        </table>
        <el-empty v-else description="暂无数据"></el-empty>
      </div>
    </div>
    <el-empty v-else description="暂无数据"></el-empty>
  </div>
</template>
<script lang="ts" setup>
import { ref, onUnmounted} from "vue";
import { OnemapClass } from "../../onemapkit";
//面板加载状态
const isPropertyLoading = ref(false);
//点选属性展示树数据
const treeData = ref([]) as any;
let currentNodeData = null;
//右侧表格数据
const attributes = ref([]) as any;
//树组件
const treeRef = ref(null) as any;
//props

//@ts-ignore
const props = defineProps({
  Onemap: {
    type: OnemapClass,
    default: () => {
      let tmp = new OnemapClass();
      tmp.isMapReady = ref(false);
      return tmp;
    },
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});
/**
 * @description: 获取属性查询数据
 */
// watch(
//   () => props.PropStore.propertyData,
//   (propertyData) => {
//     console.log("监听的propertyData", propertyData);
//     // mapBase.setCurrentDialog("2");
//     treeData.value = propertyData;
//     // 没有内容时清空右边的面板属性
//     if (treeData.value.length <= 0) {
//       attributes.value = [];
//     }
//     nextTick(() => {
//       // Zindex();
//       if (treeRef.value) {
//         treeRef.value.setCurrentKey(treeData.value[0].children[0].keyId);
//       }
//     });
//   },
//   { immediate: true, deep: true }
// );
/**
 * @description: 点击树节点
 */

//@ts-ignore
const onNodeClick = (obj: any, node: any, nodeRef: any) => {
  if (node.level == 1) {
    return;
  }
  attributes.value = [];
  const { data } = node;
  currentNodeData = data;
  if (node && node.isLeaf && node.data.displayAttributes) {
    attributes.value = node.data.displayAttributes;
  }
  console.log("当前点击的data", data);

  if (node && node.isLeaf && node.data.feature) {
    if (node && node.isLeaf && node.data.feature) {
      // let polygon = new $esri.Polygon(node.data.feature.geometry);
      // mapMethods.drawGeometry($esri, mapObj, polygon, true, "propertyInfo");
    }
  }
};
/**
 * @description: 树节点切换
 */

//@ts-ignore
const onNodeChange = (obj: any, node: any) => {
  if (node.level == 1) {
    return;
  }
  attributes.value = [];
  if (node && node.isLeaf && node.data.displayAttributes) {
    currentNodeData = node.data;
    console.log(currentNodeData);
    attributes.value = node.data.displayAttributes;
  }
};
//特殊情况，有的数据会有.3的情况，需要处理
const handleData = (val: any) => {
  try {
    if (val.charAt(0) == ".") {
      val = "0" + val;
      return val;
    } else {
      return val;
    }
  } catch (e) {
    console.log(e);
    return val;
  }
};

onUnmounted(() => {});
</script>

<style lang="scss" scoped>
// @import "@/styles/util-panel.scss";

.tree-root-disabled {
  color: #ccc;
}

.tree-disabled {
  color: red;
}

.switch-show {
  float: left;
  margin: -5px 20px 5px 10px;
}
.control {
  width: 80%;
}
.coordinate {
  float: right;
  font-size: 14px;
  margin: 0px 20px 0px 0px;
}

.properties {
  border: 1px solid #e6ebf5;
  height: 300px;
  display: flex;
  justify-content: space-between;

  li {
    margin: 5px 0;
    display: flex;
    flex-direction: row;
    justify-content: center;

    .icon {
      height: 12px;
      width: 12px;
    }

    .choose-content {
      line-height: 16px;
      height: 16px;
      margin-left: 5px;
      margin-right: 2px;
      font-size: 12px;
      cursor: pointer;
      caret-color: rgba(0, 0, 0, 0);
      width: 80%;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .active {
    background-color: #c3e2f2;
  }

  &__tree {
    width: 30%;
    overflow: auto;
    position: relative;
    border-right: 1px solid #e6ebf5;

    .tree-wrapper {
      padding: 2px;
    }
  }

  &__content {
    width: 70%;
    overflow-y: scroll;
  }

  &__table {
    font-size: 12px;

    &-tr {
      &:nth-child(2n) {
        .properties__table-th {
          background: #eaf3fa;
        }

        .properties__table-td {
          background: #f5f5f5;
          position: relative;
        }
      }
    }

    &-th,
    &-td {
      min-width: 50px;
      padding: 5px;
      text-align: left;
      position: relative;
    }
  }
}

.custom-tree-node-1 {
  .node-pre-icon {
    color: #f7b500;
  }

  &__label {
    padding: 5px;
  }
}

:deep(.el-tree) {
  font-size: 12px;
}

:deep(.el-icon-caret-right:before) {
  background: url("./../../../assets/images/tree-expand-icon.png") no-repeat;
  image-rendering: -moz-smooth;
  /* Firefox */
  image-rendering: -o-smooth;
  /* Opera */
  image-rendering: -webkit-optimize-smooth;
  /*Webkit (non-standard naming) */
  image-rendering: smooth;
  content: "";
  display: block;
  width: 14px;
  height: 14px;
  background-size: 100%;
}

:deep(.el-tree-node__expand-icon.expanded.el-icon-caret-right:before) {
  background: url("./../../../assets/images/tree-retract-icon.png") no-repeat;
  content: "";
  display: block;
  width: 14px;
  height: 14px;
  background-size: 100%;
}

:deep(.el-tree-node__expand-icon.is-leaf) {
  padding-left: 0px;
}

:deep(.el-tree-node__expand-icon.is-leaf.el-icon-caret-right:before) {
  background: none;
  content: "";
  position: absolute;
  width: 1em;
  left: 15px;
  border-top: 1px dashed #b4b4b4;
}

:deep(.el-tree-node__content) {
  position: relative;
  height: 24px !important;
  background-color: transparent;
}

:deep(.el-tree-node__content:hover) {
  background-color: transparent;

  > .el-tree-node__content {
    .custom-tree-node-1__label {
      background-color: #c3e2f2;
      border: 1px #a7c9dc solid;
    }
  }
}

:deep(.el-tree-node.is-current) {
  background-color: transparent;

  > .el-tree-node__content {
    .custom-tree-node-1__label {
      background-color: #c3e2f2;
      border: 1px #a7c9dc solid;
    }
  }
}

:deep(.el-tree-node__children) {
  position: relative;
  width: 300px;
}

:deep(.el-tree-node__children:before) {
  content: "";
  left: 32px;
  position: absolute;
  height: calc(100% - 1em);
  border-left: 1px dashed #b4b4b4;
}

:deep(.el-tree-node) {
  position: relative;
}

.zrz-btn {
  margin-top: 10px;
  margin-left: 10px;
}

.zrz-btn2 {
  position: absolute;
  top: 0px;
  right: 0px;
}

.el-collapse {
  width: 100%;
}
:deep(.el-collapse-item) {
  width: 100%;
}
.properties__table-trr {
  width: 100%;
  display: flex;
  flex-direction: row;
  font-size: 12px;
  .properties__table-th {
    min-width: 50px;
    width: 50%;
    padding: 5px;
    height: 26px;
    line-height: 20px;
    text-align: left;
    position: relative;
    background: #eaf3fa;
    font-weight: bold;
  }
  .properties__table-td {
    width: 50%;
    height: 26px;
    line-height: 20px;
    background: #f5f5f5;
  }
  .no-dark {
    background-color: #fff;
  }
}
</style>
