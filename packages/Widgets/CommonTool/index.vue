<template>
  <div :class="['operate-panel']">
    <!-- <PropertySwitch /> -->
    <!-- <base-map v-if="showBaseMap" /> -->
    <!-- <full-extent v-if="showFullExtent" /> -->
    <!-- <Switch23D :SwitchMapEvent="props.SwitchMapEvent" /> -->
  </div>
</template>
<script lang="ts" setup>
 

 

// const props = defineProps({
//   options: {
//     type: Object,
//     default: () => ({
//       showSwitch23D: true,
//       showSwitchProperty: true,
//     }),
//   },
// });
</script>
<style lang="scss" scoped>
.operate-panel {
  position: absolute;
  bottom: 110px;
  right: 15px;

  .iconfont {
    font-size: 24px;
  }
}
</style>
