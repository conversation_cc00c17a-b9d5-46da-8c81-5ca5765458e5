<template>
  <div class="slider-demo-block">
    <span class="demonstration">局部模式(半径)</span>
    <el-switch
      class="demonstration"
      style="margin-left: 5px; margin-right: 10px"
      v-model="_isRange"
      inline-prompt
      active-text="是"
      inactive-text="否"
      @change="_ChangeUnderGround(_alphavalue, _distanceValue, _isRange)"
    />
    <el-slider
      v-model="_distanceValue"
      :disabled="!_isRange"
      :min="500"
      :max="20000"
      @change="_ChangeUnderGround(_alphavalue, _distanceValue, _isRange)"
    />
  </div>
  <div class="slider-demo-block">
    <span class="demonstration">地形透明度</span>
    <el-slider
      v-model="_alphavalue"
      :step="props.Step"
      :min="props.Min"
      :max="props.Max"
      @change="_ChangeUnderGround(_alphavalue, _distanceValue, _isRange)"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted /*, reactive, type PropType*/ } from "vue";
import { getOnemap } from "../onemapkit";
import * as CzmSet from "./tooljs";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  Max: {
    type: Number,
    default: 1,
    require: false,
  },
  Min: {
    type: Number,
    default: 0,
    require: false,
  },
  Step: {
    type: Number,
    default: 0.1,
    require: false,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});
const _inOnemap = getOnemap(props.MapControlName);

let _isRange = ref(true);
const _distanceValue = ref(8000);
const _alphavalue = ref(0.5);

const _ChangeUnderGround: any = (
  alpha: number,
  distance: number,
  isRange: boolean
): any => {
  CzmSet.ChangeUnderGround(_inOnemap.MapViewer, true, {
    distance: distance,
    isRange: isRange,
    alpha: alpha,
  });
};
onUnmounted(() => {
  CzmSet.ChangeUnderGround(_inOnemap.MapViewer, false);
});
</script>

<style lang="scss" scoped>
.slider-demo-block {
  display: flex;
  align-items: center;
}
.slider-demo-block .el-slider .startvalue {
  margin-top: 0;
  margin-left: 10px;
  margin-right: 10px;
}
// .slider-demo-block .demonstration {
//   font-size: 14px;
//   color: var(--el-text-color-secondary);
//   line-height: 44px;
//   flex: 1;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   margin-bottom: 0;
//   width: 100%;
// }
.slider-demo-block .demonstration + .el-slider {
  flex: 0%;
}
</style>
