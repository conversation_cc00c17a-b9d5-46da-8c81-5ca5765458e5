<template>
  <el-form-item style="margin: 0px 10px 0px 25px" label="对比度">
    <el-slider
      style="padding-left: 0px; padding-right: 10px"
      v-model="props.PropStore.Globalvariable.SceneColor.contrast"
      :step="0.001"
      :min="0.5"
      :max="1.5"
      @change="_ChangeValue('contrast')"
    />
  </el-form-item>
  <el-form-item style="margin: 0px 10px 0px 40px" label="亮度">
    <el-slider
      style="padding-left: 0px; padding-right: 10px"
      v-model="props.PropStore.Globalvariable.SceneColor.brightness"
      :step="0.001"
      :min="0"
      :max="2.5"
      @change="_ChangeValue('brightness')"
    />
  </el-form-item>
  <el-form-item style="margin: 0px 10px 0px 25px" label="饱和度">
    <el-slider
      style="padding-left: 0px; padding-right: 10px"
      v-model="props.PropStore.Globalvariable.SceneColor.saturation"
      :step="0.001"
      :min="0"
      :max="2.5"
      @change="_ChangeValue('saturation')"
    />
  </el-form-item>

  <el-form-item style="margin: 0px 10px 0px 12px" label="暗部调亮">
    <el-switch
      inline-prompt
      active-text="开启"
      inactive-text="关闭"
      v-model="props.PropStore.Globalvariable.SceneColor.enhancelight"
      @change="_ChangeValue('enhancelight')"
    />
  </el-form-item>
  <div style="width: 100%; display: flex; justify-content: flex-end"> 
    <el-button
      class="startvalue"
      type="success"
      style="margin-right: 2px;width: 110px"
      text
      bg
      @click="_SaveColorValue('default')"
      >保存为默认色彩</el-button
    >
    <el-button
      class="startvalue"
      type="success"
      style="margin-right: 0px;width: 110px"
      text
      bg
      @click="_SaveColorValue('enhance')"
      >保存为鲜艳色彩</el-button
    >
  </div>
  <el-divider style="margin-top: 6px; margin-bottom: 6px" />
  <div style="width: 100%; text-align: center">
    <el-button
      class="startvalue"
      type="success"
      style="width: 70px"
      text
      bg
      @click="_ChangeValue('original')"
      >原始色彩</el-button
    >
    <el-button
      class="startvalue"
      type="success"
      style="margin-left: 25px; width: 70px"
      text
      bg
      @click="_ChangeValue('default')"
      >默认色彩</el-button
    >
    <el-button
      class="startvalue"
      type="success"
      style="margin-left: 25px; width: 70px"
      text
      bg
      @click="_ChangeValue('enhance')"
      >鲜艳色彩</el-button
    >
    <el-button
      class="startvalue"
      type="warning"
      style="margin-left: 25px; width: 90px"
      text
      bg
      @click="_SystemInitialValue()"
      >恢复系统初值</el-button
    >
  </div>
</template>

<script lang="ts" setup>
import * as base from "../base/base";
import { PropStorage,getOnemap,getOption } from "../onemapkit";
import { onMounted ,onUnmounted } from "vue";
// import { cloneDeep } from "lodash";
//亮度：Brightness, 对比度：contrast, 饱和度：saturation
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: PropStorage,
    default: null,
  },
}); 

const _Onemap = getOnemap(props.MapControlName);
//@ts-ignore
const _Options = getOption(props.MapControlName);
const _SaveColorValue = (key: any) => {
  switch (key) {
    case "default":
      {
        props.PropStore.Globalvariable.defaultSceneColor = [
          props.PropStore.Globalvariable.SceneColor.enhancelight,
          props.PropStore.Globalvariable.SceneColor.saturation,
          props.PropStore.Globalvariable.SceneColor.brightness,
          props.PropStore.Globalvariable.SceneColor.contrast,
        ];
      }
      break;
    case "enhance":
      {
        props.PropStore.Globalvariable.enhanceSceneColor = [
          props.PropStore.Globalvariable.SceneColor.enhancelight,
          props.PropStore.Globalvariable.SceneColor.saturation,
          props.PropStore.Globalvariable.SceneColor.brightness,
          props.PropStore.Globalvariable.SceneColor.contrast,
        ];
      }
      break;
  }
};
const _SystemInitialValue = () => {
  props.PropStore.Globalvariable.SceneColor = {
    enhancelight: true,
    saturation: 1.263,
    brightness: 1.813,
    contrast: 1.03,
  };
  props.PropStore.Globalvariable.originalSceneColor = [false, 1, 1, 1];
  props.PropStore.Globalvariable.defaultSceneColor = [true, 1.263, 1.813, 1.03];
  props.PropStore.Globalvariable.enhanceSceneColor = [true, 2.0, 1.9, 1.02];
};
const _ChangeValue = (key: any = "") => { 
  switch (key) {
    case "original":
      base.Update3DTilesColor(
        props.PropStore.Globalvariable.originalSceneColor[0],
        props.PropStore.Globalvariable.originalSceneColor[1],
        props.PropStore.Globalvariable.originalSceneColor[2],
        props.PropStore.Globalvariable.originalSceneColor[3]
      );
      break;
    case "default":
      base.Update3DTilesColor(
        props.PropStore.Globalvariable.defaultSceneColor[0],
        props.PropStore.Globalvariable.defaultSceneColor[1],
        props.PropStore.Globalvariable.defaultSceneColor[2],
        props.PropStore.Globalvariable.defaultSceneColor[3]
      );
      break;
    case "enhance":
      base.Update3DTilesColor(
        props.PropStore.Globalvariable.enhanceSceneColor[0],
        props.PropStore.Globalvariable.enhanceSceneColor[1],
        props.PropStore.Globalvariable.enhanceSceneColor[2],
        props.PropStore.Globalvariable.enhanceSceneColor[3]
      );
      break;
    default: //enhancelight,saturation,brightness,contrast
      props.PropStore.Globalvariable.SceneColor = props.PropStore.Globalvariable.SceneColor;
      base.Update3DTilesColor(
        props.PropStore.Globalvariable.SceneColor.enhancelight,
        props.PropStore.Globalvariable.SceneColor.saturation,
        props.PropStore.Globalvariable.SceneColor.brightness,
        props.PropStore.Globalvariable.SceneColor.contrast
      );
      break;
  }
  console.log("");
};

const _MouseStopEvent = (avg: any) => {
  console.log("============*******", avg);
};
const _MouseStopEvent1 = (avg: any) => {
  console.log("============456", avg);
};
let _MouseStop:any = undefined;
const funpopShow1 = () => { 
  // PopPanelREFVisible.value = true; 
  //1.注册事件
  _MouseStop = _Onemap.setMapEventHandler(base.MouseEventType.MOUSE_STOP, {
    isThrottle: true,
    delayTime: 300,
    handlerName: "eventName21",
    handler: _MouseStopEvent,
    RealtimeHandler: _MouseStopEvent1,
  });
}; 
onMounted(() => {
  funpopShow1();
 // MapControlRef.value.isPropertyVisible =false;
});
onUnmounted(()=>{
  //2.移出事件
  _Onemap.removeMapEventHandler(base.MouseEventType.MOUSE_STOP,{
    handler: _MouseStop,
    handlerName: "eventName21",
  })
})
</script>

<style lang="scss" scoped>
.slider-demo-block {
  display: flex;
}

.slider-demo-block .el-slider .startvalue {
  margin-top: 0;
  margin-left: 10px;
  margin-right: 10px;
} 
.slider-demo-block .demonstration + .el-slider {
  flex: 0%;
}
</style>
