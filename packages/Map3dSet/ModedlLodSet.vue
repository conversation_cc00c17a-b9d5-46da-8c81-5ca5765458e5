<template>
  <el-form-item label="模型精度">
    <div style="display: flex; width: 100%">
      <el-input
        v-model="props.PropStore.Globalvariable.screenSpaceError"
        placeholder="选择屏幕分辨率"
        @input="_ChangeValue('model')"
      >
        <template #append>
          <el-select
            v-model="props.PropStore.Globalvariable.screenSpaceError"
            placeholder="屏幕分辨率"
            style="width: 80px"
            @change="_ChangeValue('model')"
          >
            <el-option
              v-for="item in props.SequenceLOD"
              :label="item"
              :value="item"
            />
          </el-select>
        </template>
      </el-input>
      <el-button
        class="startvalue"
        style="margin-left: 5px; width: 80px"
        type="success"
        text
        bg
        @click="_ResetDefaultValue('model')"
        >恢复默认值</el-button
      >
    </div>
  </el-form-item>
  <el-form-item label="地形精度">
    <div style="display: flex; width: 100%">
      <el-slider
        style="padding-left: 10px; padding-right: 10px"
        v-model="props.PropStore.Globalvariable.terrainMaxLevel"
        :step="1"
        :min="1"
        :max="18"
        @change="_ChangeValue('terrain')"
      />
      <el-button
        class="startvalue"
        style="margin-left: 5px; width: 80px"
        type="success"
        text
        bg
        @click="_ResetDefaultValue('terrain')"
        >恢复默认值</el-button
      >
    </div>
  </el-form-item>
</template>

<script lang="ts" setup>
import { getOnemap } from "../onemapkit";
import * as Tst from "./tooljs";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  SequenceLOD: {
    type: Array<number>,
    default: [8, 16, 32, 64, 128],
    require: false,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});

const _inOnemap = getOnemap(props.MapControlName);
const _ChangeValue: any = (avg: any) => {
  // console.log("=========_ResetDefaultValue",  props.PropStore.Globalvariable.terrainMaxLevel);
  // _inOnemap.MapViewer.scene.terrainProvider.changeMaxZoom(
  //   props.PropStore.Globalvariable.terrainMaxLevel
  // );
  // // 刷新一下地形
  // _inOnemap.MapViewer.scene.globe._surface.invalidateAllTiles();
  // console.log("=========_ResetDefaultValue",  _inOnemap.MapViewer);
  if (avg == "model") {
    Tst.setScreenSpaceError(
      _inOnemap.MapViewer,
      props.PropStore.Globalvariable.screenSpaceError
    );
  } else {
    Tst.setTerrainMaxLod(
      _inOnemap.MapViewer,
      props.PropStore.Globalvariable.terrainMaxLevel
    );
  }
};
const _ResetDefaultValue: any = (avg: any) => { 
  if (avg == "model") {
    props.PropStore.Globalvariable.screenSpaceError =
      props.PropStore.defaultToolset.screenSpaceError;
    Tst.setScreenSpaceError(
      _inOnemap.MapViewer,
      props.PropStore.defaultToolset.screenSpaceError
    );
  } else {
    props.PropStore.Globalvariable.terrainMaxLevel =
      props.PropStore.defaultToolset.terrainMaxLevel;
    Tst.setTerrainMaxLod(
      _inOnemap.MapViewer,
      props.PropStore.defaultToolset.terrainMaxLevel
    );
  }
};
</script>

<style lang="scss" scoped>
.slider-demo-block {
  display: flex;
}
.slider-demo-block .el-slider .startvalue {
  margin-top: 0;
  margin-left: 10px;
  margin-right: 10px;
}
// .slider-demo-block .demonstration {
//   font-size: 14px;
//   color: var(--el-text-color-secondary);
//   line-height: 44px;
//   flex: 1;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   margin-bottom: 0;
//   width: 100%;
// }
.slider-demo-block .demonstration + .el-slider {
  flex: 0%;
}
</style>
