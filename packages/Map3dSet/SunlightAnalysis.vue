<template>
  <el-form-item style="margin: 0px 25px 0px 25px" label="时间轴">
    <el-slider
      v-model="timevalue"
      :max="24"
      :min="0"
      :step="1"
      :marks="marks"
      @change="changeTime"
    />
  </el-form-item>
  <div style="margin-top: 50px; display: flex; justify-content: center">
    <el-button type="success" text bg @click="AutoRunEvent">{{
      isAutoRun ? "暂停自运行" : "自动运行"
    }}</el-button> 
    <el-form-item style="margin: 0px 40px 0px 2px" label="速度">
      <el-slider
        v-model="speedvalue"
        style="width: 100px"
        :max="1000"
        :min="1"
        :step="1"
        @change="RunSpeed"
      />
    </el-form-item>
    <el-checkbox label="显示阴影" v-model="isShadow" @change="changeShadowe"
        ></el-checkbox
      >
      <el-form-item style="margin: 0px 0px 0px 30px" label="节气">
        <el-select  
          v-model="calendarvalue"
          placeholder="选择节气"
          style="width: 80px;"
          @change="changeCalendar"
        >
          <el-option
            v-for="item in calendar"
            :key="item.key"
            :label="item.key"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
  </div> 
</template>

<script lang="ts" setup>
import { ref } from "vue";
import * as Weather from "./weather";
import { getOnemap } from "../onemapkit";
import * as Cesium from "@onemapkit/cesium";
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});
const _inOnemap = getOnemap(props.MapControlName);

const isAutoRun = ref(false);
const speedvalue = ref(50);
const isShadow = ref(false);
const timevalue = ref(12);
const calendarvalue = ref("春分");
let currentTime = new Date().getFullYear() - 1 + "-03-20";
console.log("**************_year", currentTime);
const calendar = [
  { key: "春分", value: new Date().getFullYear() - 1 + "-03-20" },
  { key: "夏至", value: new Date().getFullYear() - 1 + "-06-21" },
  { key: "秋分", value: new Date().getFullYear() - 1 + "-09-22" },
  { key: "冬至", value: new Date().getFullYear() - 1 + "-12-21" },
];
const marks = {
  0: "0",
  4: "4",
  8: "8",
  12: "12",
  16: "16",
  20: "20",
  24: "24",
};
const init = () => {
  _inOnemap.MapViewer.scene.globe.enableLighting = true;
  _inOnemap.MapViewer.shadows = isShadow.value;
  _inOnemap.MapViewer.terrainShadows = Cesium.ShadowMode.RECEIVE_ONLY;
  _inOnemap.MapViewer.shadowMap.darkness = 0.5; //阴影透明度--越大越透明

  let startTime = Cesium.JulianDate.fromIso8601("2024-04-07");
  _inOnemap.MapViewer.clock.startTime = Cesium.JulianDate.addHours(
    startTime,
    -8,
    new Cesium.JulianDate()
  );
};
init();
const changeTime = () => {
  isAutoRun.value = false;

  // let startTime = Cesium.JulianDate.fromIso8601(state.time.startTime);
  let startTime = Cesium.JulianDate.fromIso8601("2023-04-07");
  _inOnemap.MapViewer.clock.startTime = Cesium.JulianDate.addHours(
    startTime,
    -8,
    new Cesium.JulianDate()
  );
  // let date = Cesium.JulianDate.fromIso8601("2023-04-07T"+timevalue.value.toString()+":00:00Z");
  //earth.viewer.clock.startTime = Cesium.JulianDate.addHours(startTime, -8, new Cesium.JulianDate());

  // let utc = Cesium.JulianDate.fromDate(new Date(timevalue.value*60*1000));
  console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$", startTime);
  //北京时间
  _inOnemap.MapViewer.clockViewModel.currentTime = Cesium.JulianDate.addHours(
    startTime,
    timevalue.value,
    new Cesium.JulianDate()
  );
};
const AutoRunEvent = () => {
  isAutoRun.value = !isAutoRun.value;
  sunlightAnalysis();
};
const RunSpeed = () => {
  Weather.Weather.speed = speedvalue.value;
  console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$", speedvalue.value);
};
const changeShadowe = () => {};
const changeCalendar = () => {
  currentTime = calendarvalue.value;

  console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$ changeCalendar", currentTime);
};

function sunlightAnalysis() {
  Weather.Weather.viewer = _inOnemap.MapViewer;
  Weather.Weather.ccc();
}
</script>

<style lang="scss" scoped>
.slider-demo-block {
  max-width: 600px;
  display: flex;
  align-items: center;
}
.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}
:deep(.el-slider__bar) {
  background-color: var(--el-slider-runway-bg-color);
}

// .slider-demo-block .demonstration {
//   font-size: 14px;
//   color: var(--el-text-color-secondary);
//   line-height: 44px;
//   flex: 1;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   margin-bottom: 0;
//   width: 100%;
// }
.slider-demo-block .demonstration + .el-slider {
  flex: 0%;
}
</style>
