import * as Cesium from "@onemapkit/cesium";

export class SnowEffect {
  private snowSize: Number;
  private snowSpeed: Number;
  private viewer: Cesium.Viewer;
  private snowStage: any;

  constructor(viewer: any, options: any) {
    if (!viewer) throw new Error("no viewer object!");
    options = options || {};
    this.snowSize = Cesium.defaultValue(options.snowSize, 0.02); //最好小于0.02
    this.snowSpeed = Cesium.defaultValue(options.snowSpeed, 60.0);
    this.snowStage = Cesium.defaultValue(
      options.snowStage,
      new Cesium.PostProcessStage({
        name: "czm_snow",
        fragmentShader: this.snow(),
        uniforms: {
          snowSize: () => {
            return this.snowSize;
          },
          snowSpeed: () => {
            return this.snowSpeed;
          },
        },
      })
    );
    this.viewer = viewer;
    this.viewer.scene.postProcessStages.add(this.snowStage);
    // this.init();
  }

  init() {
    this.snowStage = new Cesium.PostProcessStage({
      name: "czm_snow",
      fragmentShader: this.snow(),
      uniforms: {
        snowSize: () => {
          return this.snowSize;
        },
        snowSpeed: () => {
          return this.snowSpeed;
        },
      },
    });
    this.viewer.scene.postProcessStages.add(this.snowStage);
  }

  destroy() {
    if (!this.viewer || !this.snowStage) return;
    this.viewer.scene.postProcessStages.remove(this.snowStage);
    this.snowStage.destroy();
    // delete this.snowSize;
    // delete this.snowSpeed;
  }

  show(visible: any) {
    this.snowStage.enabled = visible;
  }

  snow() {
    return "uniform sampler2D colorTexture;\n\
            varying vec2 v_textureCoordinates;\n\
            uniform float snowSpeed;\n\
                    uniform float snowSize;\n\
            float snow(vec2 uv,float scale)\n\
            {\n\
                float time=czm_frameNumber/snowSpeed;\n\
                float w=smoothstep(1.,0.,-uv.y*(scale/10.));if(w<.1)return 0.;\n\
                uv+=time/scale;uv.y+=time*2./scale;uv.x+=sin(uv.y+time*.5)/scale;\n\
                uv*=scale;vec2 s=floor(uv),f=fract(uv),p;float k=3.,d;\n\
                p=.5+.35*sin(11.*fract(sin((s+p+scale)*mat2(7,3,6,5))*5.))-f;d=length(p);k=min(d,k);\n\
                k=smoothstep(0.,k,sin(f.x+f.y)*snowSize);\n\
                return k*w;\n\
            }\n\
            void main(void){\n\
                vec2 resolution=czm_viewport.zw;\n\
                vec2 uv=(gl_FragCoord.xy*2.-resolution.xy)/min(resolution.x,resolution.y);\n\
                vec3 finalColor=vec3(0);\n\
                //float c=smoothstep(1.,0.3,clamp(uv.y*.3+.8,0.,.75));\n\
                float c=0.;\n\
                c+=snow(uv,30.)*.0;\n\
                c+=snow(uv,20.)*.0;\n\
                c+=snow(uv,15.)*.0;\n\
                c+=snow(uv,10.);\n\
                c+=snow(uv,8.);\n\
                c+=snow(uv,6.);\n\
                c+=snow(uv,5.);\n\
                finalColor=(vec3(c));\n\
                gl_FragColor=mix(texture2D(colorTexture,v_textureCoordinates),vec4(finalColor,1),.5);\n\
                }\n\
                ";
  }
}
//@ts-ignore
// Cesium["SnowEffect"] = SnowEffect;

export class RainEffect {
  private tiltAngle: Number;
  private rainSize: Number;
  private rainSpeed: Number;
  private viewer: Cesium.Viewer;
  private rainStage: any;
  constructor(viewer: any, options: any) {
    if (!viewer) throw new Error("no viewer object!");
    options = options || {};
    //倾斜角度，负数向右，正数向左
    this.tiltAngle = Cesium.defaultValue(options.tiltAngle, -0.6);
    this.rainSize = Cesium.defaultValue(options.rainSize, 0.3);
    this.rainSpeed = Cesium.defaultValue(options.rainSpeed, 60.0);
    this.rainStage = Cesium.defaultValue(
      options.rainStage,
      new Cesium.PostProcessStage({
        name: "czm_rain",
        fragmentShader: this.rain(),
        uniforms: {
          tiltAngle: () => {
            return this.tiltAngle;
          },
          rainSize: () => {
            return this.rainSize;
          },
          rainSpeed: () => {
            return this.rainSpeed;
          },
        },
      })
    );
    this.viewer = viewer;
    this.viewer.scene.postProcessStages.add(this.rainStage);
    // this.init();
  }

  init() {
    this.rainStage = new Cesium.PostProcessStage({
      name: "czm_rain",
      fragmentShader: this.rain(),
      uniforms: {
        tiltAngle: () => {
          return this.tiltAngle;
        },
        rainSize: () => {
          return this.rainSize;
        },
        rainSpeed: () => {
          return this.rainSpeed;
        },
      },
    });
    this.viewer.scene.postProcessStages.add(this.rainStage);
  }

  destroy() {
    if (!this.viewer || !this.rainStage) return;
    this.viewer.scene.postProcessStages.remove(this.rainStage);
    this.rainStage.destroy();
    // delete this.tiltAngle;
    // delete this.rainSize;
    // delete this.rainSpeed;
  }

  show(visible: any) {
    this.rainStage.enabled = visible;
  }

  rain() {
    return "uniform sampler2D colorTexture;\n\
                varying vec2 v_textureCoordinates;\n\
                uniform float tiltAngle;\n\
                uniform float rainSize;\n\
                uniform float rainSpeed;\n\
                float hash(float x) {\n\
                    return fract(sin(x * 133.3) * 13.13);\n\
                }\n\
                void main(void) {\n\
                    float time = czm_frameNumber / rainSpeed;\n\
                    vec2 resolution = czm_viewport.zw;\n\
                    vec2 uv = (gl_FragCoord.xy * 2. - resolution.xy) / min(resolution.x, resolution.y);\n\
                    vec3 c = vec3(.6, .7, .8);\n\
                    float a = tiltAngle;\n\
                    float si = sin(a), co = cos(a);\n\
                    uv *= mat2(co, -si, si, co);\n\
                    uv *= length(uv + vec2(0, 4.9)) * rainSize + 1.;\n\
                    float v = 1. - sin(hash(floor(uv.x * 100.)) * 2.);\n\
                    float b = clamp(abs(sin(20. * time * v + uv.y * (5. / (2. + v)))) - .95, 0., 1.) * 20.;\n\
                    c *= v * b;\n\
                    gl_FragColor = mix(texture2D(colorTexture, v_textureCoordinates), vec4(c, 1), .5);\n\
                }\n\
                ";
  }
}
//@ts-ignore
// Cesium["RainEffect"] = RainEffect;

export class FogEffect {
  private visibility: Number;
  private color: Cesium.Color;
  private _show: boolean;
  private viewer: Cesium.Viewer;
  private fogStage: any;
  constructor(viewer: Cesium.Viewer, options?: any) {
    if (!viewer) throw new Error("no viewer object!");
    options = options || {};
    this.visibility = Cesium.defaultValue(options.visibility, 0.1);
    this.color = Cesium.defaultValue(
      options.color,
      new Cesium.Color(0.8, 0.8, 0.8, 0.5)
    );
    this._show = Cesium.defaultValue(options.show, !0);
    this.fogStage = new Cesium.PostProcessStage({
      name: "czm_fog",
      fragmentShader: this.fog(),
      uniforms: {
        visibility: () => {
          return this.visibility;
        },
        fogColor: () => {
          return this.color;
        },
      },
    });
    this.viewer = viewer;

    this.viewer.scene.postProcessStages.add(this.fogStage);
    this.init();
  }

  init() {
    this.fogStage = new Cesium.PostProcessStage({
      name: "czm_fog",
      fragmentShader: this.fog(),
      uniforms: {
        visibility: () => {
          return this.visibility;
        },
        fogColor: () => {
          return this.color;
        },
      },
    });
    this.viewer.scene.postProcessStages.add(this.fogStage);
  }

  destroy() {
    if (!this.viewer || !this.fogStage) return;
    this.viewer.scene.postProcessStages.remove(this.fogStage);
    this.fogStage.destroy();
    // delete this.visibility;
    // delete this.color;
  }

  show(visible: any) {
    this._show = visible;
    // this.fogState.enabled = this._show;
    this.fogStage.enabled = this._show;
  }

  fog() {
    return "uniform sampler2D colorTexture;\n\
         uniform sampler2D depthTexture;\n\
         uniform float visibility;\n\
         uniform vec4 fogColor;\n\
         varying vec2 v_textureCoordinates; \n\
         void main(void) \n\
         { \n\
            vec4 origcolor = texture2D(colorTexture, v_textureCoordinates); \n\
            float depth = czm_readDepth(depthTexture, v_textureCoordinates); \n\
            vec4 depthcolor = texture2D(depthTexture, v_textureCoordinates); \n\
            float f = visibility * (depthcolor.r - 0.3) / 0.2; \n\
            if (f < 0.0) f = 0.0; \n\
            else if (f > 1.0) f = 1.0; \n\
            gl_FragColor = mix(origcolor, fogColor, f); \n\
         }\n";
  }
}

export const Weather = {
  snow: undefined as any,
  rain: undefined as any,
  fog: undefined as any,
  viewer: undefined as any,
  /**
   * 下雪
   */
  addSnow() {
    let that = this;
    that.destoryWeather();
    that.snow = new SnowEffect(that.viewer, {
      snowSize: 0.02, //雪大小 ，默认可不写
      snowSpeed: 60.0, //雪速，默认可不写
    });
  },

  addRain() {
    let that = this;
    that.destoryWeather();
    that.rain = new RainEffect(that.viewer, {
      tiltAngle: -0.6, //倾斜角度
      rainSize: 0.6, //雨大小
      rainSpeed: 120.0, //雨速
    });
  },

  addFog() {
    this.destoryWeather();
    this.fog = new FogEffect(this.viewer, {
      visibility: 0.2,
      color: new Cesium.Color(0.8, 0.8, 0.8, 0.3),
    });
  },
  destoryWeather() {
    if (this.snow) {
      this.snow.destroy();
    }
    if (this.rain) {
      this.rain.destroy();
    }
    if (this.fog) {
      this.fog.destroy();
    }
  },
  //****************************** */

  lastStage: undefined as any,
  removeStage() {
    this.viewer.scene.postProcessStages.remove(this.lastStage);
    this.lastStage = null;
  },

  //下雨
  rainning() {
    this.removeStage();
    var rain = new Cesium.PostProcessStage({
      name: "hi_rain",
      fragmentShader: FS_Rain,
    });
    this.viewer.scene.postProcessStages.add(rain);
    this.lastStage = rain;
  },
  //下雪
  snowwing() {
    this.removeStage();
    var snow = new Cesium.PostProcessStage({
      name: "hi_snow",
      fragmentShader: FS_Snow,
    });
    this.viewer.scene.postProcessStages.add(snow);
    this.lastStage = snow;
  },
  //晴天
  clean() {
    this.removeStage();
    this.lastStage = null;
  },
  handleRain(rainWidth: any, rainSpeed: any) {
    this.removeStage();
    var rain = new Cesium.PostProcessStage({
      name: "hi_rain",
      fragmentShader: FS_Rain,
      uniforms: {
        tiltAngle: 0.5, // 倾斜角度
        rainSize: 0.6, // 雨大小
        rainWidth, //雨长度
        rainSpeed, //雨速
      },
    });
    this.viewer.scene.postProcessStages.add(rain);
    this.lastStage = rain;
  },
  inter: undefined as any,
  startSunshine(option: any) {
    this.viewer.scene.globe.enableLighting = true;
    this.viewer.shadows = true;
    this.viewer.terrainShadows = Cesium.ShadowMode.ENABLED;
    this.viewer.shadowMap.darkness = option.darkness || 1.1; //阴影透明度--越大越透明
    let time = 0;
    this.inter = setInterval(() => {
      let date = option.date || 0 + time;
      if (null == option.date || 0) {
        //时间传空的时候，就从当前时间开始
        date = new Date().getTime() + time;
      }
      let utc = Cesium.JulianDate.fromDate(new Date(date));
      this.viewer.clockViewModel.currentTime = Cesium.JulianDate.addHours(
        utc,
        0,
        new Cesium.JulianDate()
      );
      time = time + 1000 * 60;
    }, option.speed || 0.002);
  },
  speed : 50, 
  ccc() {
    this.viewer.scene.globe.enableLighting = true;
    this.viewer.shadows = true;
    this.viewer.terrainShadows = Cesium.ShadowMode.RECEIVE_ONLY;
    this.viewer.shadowMap.darkness = 0.5; //阴影透明度--越大越透明
    // this.viewer.shadowMap.softShadows = true;
    // let time = 0; 
    this.inter = setInterval(() => {
      console.log("************", this.speed);
      // 改变时间设置光照效果 new Date().getTime() +
      // let date =  this.speed;
      let utc = Cesium.JulianDate.fromDate(new Date(this.speed));
      //北京时间
      this.viewer.clockViewModel.currentTime = Cesium.JulianDate.addHours(
        utc,
        0,
        new Cesium.JulianDate()
      );
      // this.speed = this.speed + 1000 * 60;
    }, 100);

    // this.viewer.scene.globe.enableLighting = true;
    // this.viewer.shadows = true;
    // this.viewer.terrainShadows = Cesium.ShadowMode.RECEIVE_ONLY;
    // //viewer.shadowMap.softShadows  = true
    // this.viewer.shadowMap.darkness = 0.02; //阴影透明度--越大越透明
    // let time = 0;
    // this.inter = setInterval(() => {
    //   // 改变时间设置光照效果
    //   let date = new Date().getTime() + time;
    //   let utc = Cesium.JulianDate.fromDate(new Date(date));
    //   //北京时间=UTC+8=GMT+8 不用加8
    //   this.viewer.clockViewModel.currentTime = Cesium.JulianDate.addHours(
    //     utc,
    //     0,
    //     new Cesium.JulianDate()
    //   );
    //   time = time + 100 * 60;
    // }, 100);
    // viewer.scene.light = new Cesium.DirectionalLight({
    // 	//去除时间原因影响模型颜色
    // 	direction: new Cesium.Cartesian3(0.35492591601301104, -0.8909182691839401, -0.2833588392420772)
    // })
  },
};

// Cesium.PostProcessStageLibrary.createSnowStage = function() {
//     var snow = new PostProcessStage({
//         name : 'czm_snow',
//         fragmentShader : Snow
//     });
//     return snow;
// }

// PostProcessStageLibrary.createRainStage = function() {
//     var snow = new PostProcessStage({
//         name : 'czm_rain',
//         fragmentShader : Rain
//     });
//     return snow;
// }

/***********************下雨*************************/
// 雨
const FS_Rain = `uniform sampler2D colorTexture;
 in vec2 v_textureCoordinates;
 uniform float tiltAngle;
 uniform float rainSize;
 uniform float rainWidth;
 uniform float rainSpeed;
 float hash(float x){
        return fract(sin(x*233.3)*13.13);
 }
 out vec4 vFragColor;
 void main(void){
    float time = czm_frameNumber / rainSpeed;
  vec2 resolution = czm_viewport.zw;
  vec2 uv=(gl_FragCoord.xy*2.-resolution.xy)/min(resolution.x,resolution.y);
  vec3 c=vec3(1.0,1.0,1.0);
  float a= tiltAngle;
  float si=sin(a),co=cos(a);
  uv*=mat2(co,-si,si,co);
  uv*=length(uv+vec2(0,4.9))*rainSize + 1.;
  float v = 1.0 - abs(sin(hash(floor(uv.x * rainWidth)) * 2.0));
  float b=clamp(abs(sin(20.*time*v+uv.y*(5./(2.+v))))-.95,0.,1.)*20.;
  c*=v*b;
 vFragColor = mix(texture(colorTexture, v_textureCoordinates), vec4(c,.3), .3);
 }
 `;
//@ts-ignore
const FS_Rain1 =
  "uniform sampler2D colorTexture;\n\
 varying vec2 v_textureCoordinates;\n\
\n\
 float hash(float x){\n\
     return fract(sin(x*133.3)*13.13);\n\
}\n\
\n\
void main(void){\n\
\n\
 float time = czm_frameNumber / 60.0;\n\
vec2 resolution = czm_viewport.zw;\n\
\n\
vec2 uv=(gl_FragCoord.xy*2.-resolution.xy)/min(resolution.x,resolution.y);\n\
vec3 c=vec3(.6,.7,.8);\n\
\n\
float a=-.4;\n\
float si=sin(a),co=cos(a);\n\
uv*=mat2(co,-si,si,co);\n\
uv*=length(uv+vec2(0,4.9))*.3+1.;\n\
\n\
float v=1.-sin(hash(floor(uv.x*100.))*2.);\n\
float b=clamp(abs(sin(20.*time*v+uv.y*(5./(2.+v))))-.95,0.,1.)*20.;\n\
c*=v*b; \n\
\n\
gl_FragColor = mix(texture2D(colorTexture, v_textureCoordinates), vec4(c,1), 0.5);  \n\
}\n\
";
/*************************下雪*********************/
const FS_Snow =
  "uniform sampler2D colorTexture;\n\
varying vec2 v_textureCoordinates;\n\
\n\
float snow(vec2 uv,float scale)\n\
{\n\
float time = czm_frameNumber / 60.0;\n\
float w=smoothstep(1.,0.,-uv.y*(scale/10.));if(w<.1)return 0.;\n\
uv+=time/scale;uv.y+=time*2./scale;uv.x+=sin(uv.y+time*.5)/scale;\n\
uv*=scale;vec2 s=floor(uv),f=fract(uv),p;float k=3.,d;\n\
p=.5+.35*sin(11.*fract(sin((s+p+scale)*mat2(7,3,6,5))*5.))-f;d=length(p);k=min(d,k);\n\
k=smoothstep(0.,k,sin(f.x+f.y)*0.01);\n\
return k*w;\n\
}\n\
\n\
void main(void){\n\
vec2 resolution = czm_viewport.zw;\n\
vec2 uv=(gl_FragCoord.xy*2.-resolution.xy)/min(resolution.x,resolution.y);\n\
vec3 finalColor=vec3(0);\n\
float c = 0.0;\n\
c+=snow(uv,30.)*.0;\n\
c+=snow(uv,20.)*.0;\n\
c+=snow(uv,15.)*.0;\n\
c+=snow(uv,10.);\n\
c+=snow(uv,8.);\n\
c+=snow(uv,6.);\n\
c+=snow(uv,5.);\n\
finalColor=(vec3(c)); \n\
gl_FragColor = mix(texture2D(colorTexture, v_textureCoordinates), vec4(finalColor,1), 0.5); \n\
\n\
}\n\
";
