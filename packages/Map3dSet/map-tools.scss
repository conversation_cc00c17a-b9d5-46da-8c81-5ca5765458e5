$primary-color: var(--primaryColor, #1890ff);

li, ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.toolbox {
  position: absolute;
  top: 20px;
  right: 15px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
  z-index: 999;
  &-wrapper {
    position: relative;
  }
  &-more,
  &-settings {
    cursor: pointer;
    background: #535353;
    padding: 2px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    -webkit-text-size-adjust: none;
    -webkit-transform: scale(0.8);
    margin-bottom: 4px;
    text-align: center;
  }
}
.tool-list {
  background: #fff;
  padding: 2px;
  border-radius: 4px;
  box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
  width: 40px;
  &-wrapper {
    padding-left: 6px;
    padding-right: 6px;
  }

  &-item {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #bab9b9;

    &.is-active {
      color: var(--primaryColor, #1890ff);
    }

    &.is-disabled {
      cursor: not-allowed;

      .util-list-item__icon,
      .util-list-item__name {
        color: #c7c7c7;
      }
    }

    &:last-child {
      border-bottom: 0;
      padding-bottom: 0;
    }

    &__icon {
      width: 20px;
      height: 20px;
    }

    &__name {
      // margin-top: 2px;
      font-size: 12px;
      text-align: center;
    }
  }
}

#moreUtils {
  position: absolute;
  top: 0;
  right: 55px;
  background: #fff;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  padding: 8px 12px;
  padding-top: 0px;
  min-width: 250px;
  min-height: 100px;
  max-height: 500px;
  overflow-y: auto;
}
.more-util-list {
  display: flex;
  flex-wrap: wrap;

  &__title {
    font-size: 12px;
    color: #888;
    margin-top: 10px;
  }

  &-item {
    width: 48px;
    margin: 5px;
    cursor: pointer;
    text-align: center;
    position: relative;

    .is-active {
      color: var(--primaryColor, #1890ff);
    }

    &.is-disabled {
      .more-util-list-item__icon,
      .more-util-list-item__name {
        color: #c7c7c7;
        fill: #c7c7c7;
      }
    }

    &.is-active {
      .util-list-item__icon,
      .util-list-item__name {
        color: var(--primaryColor, #1890ff);
        fill: var(--primaryColor, #1890ff);
      }
    }

    &:nth-child(5n + 1) {
      margin-left: 0;
    }

    &:nth-child(5n) {
      margin-right: 0;
    }

    &__icon {
      position: relative;
      width: 20px;
      height: 20px;
      display: inline-block;
      text-align: center;
      vertical-align: middle;
      background: #f5f5f5;
      border-radius: 4px;
      padding: 4px 14px;
    }

    &__name {
      margin-top: 5px;
      font-size: 12px;
      text-align: center;
    }
  }
}

.custiom-utils-container {
  border: 1px dashed #ddd;
  background: #f5f5f5;
  border-radius: $map-border-radius;
  text-align: center;
  padding: 3px;
  margin: 5px 20px;
  font-size: 12px;
  color: #666;
  cursor: pointer;

  &:hover {
    border-color: $primary-color;
    color: $primary-color;
  }
  span {
    margin-left: 5px;
  }

}

.toolbox-buttons {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.toolbox-buttons > div {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
  color: #000;
}

.toolbox-buttons > div svg {
  width: 24px;
  height: 24px;
  margin-right: 5px;
  fill: currentColor;
}

.toolbox-buttons > div span {
  font-size: 14px;
}

.more-button {
  position: relative;
  cursor: pointer;
}

.more-panel {
  position: absolute;
  top: 30px;
  right: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 10px;
  z-index: 1;
}

.tool-component {
  margin-top: 20px;
}

.active {
  color: #409eff;
}

.more-util-list {
  display: flex;
  flex-wrap: wrap;

  &__title {
    font-size: 12px;
    color: #888;
    margin-top: 10px;
  }

  &-item {
    width: 48px;
    margin: 8px;
    cursor: pointer;
    text-align: center;
    position: relative;

    &.is-active {
      color: $primary-color;
	    fill: $primary-color;
    }

    &.is-disabled {
      .more-util-list-item__icon,
      .more-util-list-item__name {
        color: #c7c7c7;
        fill:#c7c7c7;
      }
    }

    &.is-active {
      .util-list-item__icon,
      .util-list-item__name {
        color: $primary-color;
		    fill: $primary-color;
      }
    }

    &:nth-child(5n + 1) {
      margin-left: 0;
    }

    &:nth-child(5n) {
      margin-right: 0;
    }

    &__icon {
      background: #f5f5f5;
      padding: 4px 14px;
    }

    &__icon:hover {
      color: $primary-color;
    }

    &__name {
      margin-top: 5px;
      font-size: 12px;
      //yusy
      text-align: center; 
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.general-utils-container {
  margin:5px;
  border-bottom: 1px solid #eee;
}


.custom-dialog-container {
  .el-dialog__body {
    padding: 10px 20px 20px 20px;
  }
}

.curstom-icon {
  width: 24px;
  height: 17px;
}

.ml-0 {
  margin-left: 0 !important;
  margin-top: 10px;
}


.more-util-list-item-move {
  cursor: move;
  width: 50px;
  margin: 8px; 
}

.setting-conatiner {
  margin-top: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-container {
  display: flex;
  height: 64px;
  justify-content: center;
  flex-direction: column;
  margin-left: 0px;
}

.drag-container {
  width: 100%;
  position: relative;
  border: 1px dashed #ddd;
  border-radius: 4px;
  text-align: center;
  padding: 3px;
  margin: 10px;
  font-size: 12px;
  color: $primary-color;
  height: 64px;
}

.custom-utils-container {
  position: absolute;
  top: 0;
  left: -2px;
  width: 100%;
  height: 100%;
  padding: 0 10px;

  .more-util-list-item {
    cursor: pointer;
  }
  .more-util-list-item :hover {
    color: $primary-color;
  }

  .close {
    position: absolute;
    right: -5px;
    top: -5px;
    color: red;
    border: 1px solid #fff;
    z-index: 99;
  }
}
