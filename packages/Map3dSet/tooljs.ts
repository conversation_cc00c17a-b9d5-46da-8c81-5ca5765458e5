import * as Cesium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";

/**
 * 开启和关闭地下模式
 * @param {Cesium.Viewer} _viewer
 * @param {boolean} isOpen
 * @param {Object} [options]
 * @param {number} [options.distance = 8000]
 * @param {boolean} [options.isRange = true]
 * @param {number} [options.alpha = 0.5]
 */
export function ChangeUnderGround(_viewer: any, isOpen: boolean, options?: any) { 
  if (isOpen) {
    _viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;
    _viewer.scene.globe.translucency.enabled = true;

    _viewer.scene.globe.translucency.frontFaceAlphaByDistance =
      new Cesium.NearFarScalar(400.0, 0.0, options.distance, 1.0);
    let _alpha = Number(options.alpha);
    _alpha = !isNaN(_alpha) ? _alpha : 1.0;
    _alpha = Cesium.Math.clamp(_alpha, 0.0, 1.0);
    _viewer.scene.globe.translucency.frontFaceAlphaByDistance.nearValue =
      _alpha;
    _viewer.scene.globe.translucency.frontFaceAlphaByDistance.farValue =
      options.isRange ? 1 : _alpha;
  } else {
    _viewer.scene.screenSpaceCameraController.enableCollisionDetection = true;
    _viewer.scene.globe.translucency.enabled = false;
  }
}
/**
 * 重力设置
 * @param {Cesium.Viewer} _viewer
 * @param {boolean} [isGravity = false]
 */
export function setGravity(_viewer: any, isGravity: boolean = false) {
  _viewer.scene.globe.enableGravity = isGravity;
}
/**
 * HDR设置
 * @param {Cesium.Viewer}  _viewer
 * @param {boolean} isHDR
 */
export function setHDR(_viewer: any, isHDR: boolean = false) {
  _viewer.scene.highDynamicRange = isHDR;
}
/**
 *设置抗锯齿效果
 * @param {Cesium.Viewer} _viewer
 * @param {boolean} [isFXAA = false]
 */
export function setFXAA(_viewer: any, isFXAA: boolean = false) {
  if (isFXAA) {
    new CesiumTools.Fxaa(_viewer).show();
  } else {
    new CesiumTools.Fxaa(_viewer).stop();
  }
}

// CesiumTools.CesiumOfflineCache.use("myCesiumOfflineCache");
/**
 * 增加Url规则 CesiumTools.CesiumOfflineCache.getUseSize();
 * @param { Array<string>} _urls
 */
// export function addDBCacheUrl(_urls: Array<string>) {
//   _urls.forEach((url)=>{
//     CesiumTools.CesiumOfflineCache.addRuleList(url);
//   })
// }
// export function ClearOfflineCache() {
//   CesiumTools.CesiumOfflineCache.clear();
// }

export const colorCorrection = new Cesium.CustomShader({
  // lightingModel: Cesium.LightingModel.UNLIT, // 禁用光照
  uniforms: {
    saturation: {
      // 饱和度
      type: Cesium.UniformType.FLOAT,
      value: 1.0,
    },
    brightness: {
      // 亮度
      type: Cesium.UniformType.FLOAT,
      value: 1.0,
    },
    contrast: {
      // 对比度
      type: Cesium.UniformType.FLOAT,
      value: 1.0,
    },
    darkHeighLight: {
      // 暗部提亮
      type: Cesium.UniformType.BOOL,
      value: false,
    },
  },
  fragmentShaderText: `
  vec3 colorCorrection(vec3 originalColor){
    float gray = 0.2125 * originalColor.r + 0.7154 * originalColor.g + 0.0721 * originalColor.b;
    vec3 grayColor = vec3(gray, gray, gray);
    vec3 finalColor = mix(grayColor, originalColor, saturation);
  
    finalColor = finalColor * brightness;
  
    float optionsValue = 0.5 * brightness;
    vec3 optionsColor = vec3(optionsValue, optionsValue, optionsValue);
    finalColor = mix(optionsColor, finalColor, contrast);
  
    return finalColor;
  }

  void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
    vec3 color = material.diffuse;
    float gray = 0.2125 * color.r + 0.7154 * color.g + 0.0721 * color.b;
    vec3 brighten = vec3(1.0) * (1.0-gray) * 0.01;
    if(darkHeighLight){
      color += brighten;
    }
    material.diffuse = colorCorrection(color);
  }
  `,
});
/**
 * 调整3DTiles整体颜色
 * @param {Boolean} darkHeighLight 是否使用暗部提亮
 * @param {Number} saturation 饱和度
 * @param {Number} brightness 亮度
 * @param {NUmber} contrast 对比度
 */
export function Update3DTilesColor(
  darkHeighLight: boolean,
  saturation: number,
  brightness: number,
  contrast: number
) {
  colorCorrection.uniforms.darkHeighLight.value = darkHeighLight;
  colorCorrection.uniforms.saturation.value = saturation;
  colorCorrection.uniforms.brightness.value = brightness;
  colorCorrection.uniforms.contrast.value = contrast;
}

export const setTerrainMaxLod = (viewer: any, lod: number) => { 
  viewer.scene.terrainProvider.changeMaxZoom(lod); 
  viewer.scene.globe._surface.invalidateAllTiles();  
};
export const setScreenSpaceError = (viewer: any, spaceError: number) => { 
  viewer.scene.screenSpaceCameraController.maximumScreenSpaceError = spaceError
};


// 操作习惯改变事件
export const MouseOperaMode =(viewer:any, isArcMode: boolean)=> { 
  if(isArcMode){
      // 1 为 true ArcGIS操作事件
      // 平移：
      viewer.scene.screenSpaceCameraController.rotateEventTypes =
          Cesium.CameraEventType.LEFT_DRAG;
      // 旋转：
      viewer.scene.screenSpaceCameraController.tiltEventTypes = [
          Cesium.CameraEventType.PINCH,
          Cesium.CameraEventType.RIGHT_DRAG,
      ];
      // 缩放：
      viewer.scene.screenSpaceCameraController.zoomEventTypes = [
          Cesium.CameraEventType.PINCH,
          Cesium.CameraEventType.WHEEL,
          Cesium.CameraEventType.MIDDLE_DRAG,
      ];
  } else {
      // 0 为 false Cesium 习惯
      // 平移
      viewer.scene.screenSpaceCameraController.rotateEventTypes =
          Cesium.CameraEventType.LEFT_DRAG;
      // 旋转：
      viewer.scene.screenSpaceCameraController.tiltEventTypes = [
          Cesium.CameraEventType.MIDDLE_DRAG,
          Cesium.CameraEventType.PINCH,
          {
              eventType: Cesium.CameraEventType.LEFT_DRAG,
              modifier: Cesium.KeyboardEventModifier.CTRL,
          },
          {
              eventType: Cesium.CameraEventType.RIGHT_DRAG,
              modifier: Cesium.KeyboardEventModifier.CTRL,
          },
      ];
      // 缩放：
      viewer.scene.screenSpaceCameraController.zoomEventTypes = [
          Cesium.CameraEventType.RIGHT_DRAG,
          Cesium.CameraEventType.WHEEL,
          Cesium.CameraEventType.PINCH,
      ];
  }
}