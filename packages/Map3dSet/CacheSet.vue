<template>
  <el-form-item style="margin-bottom: 2px" label="显存大小管理(MB)">
    <div style="display: flex; width: 100%">
      <el-slider
        style="width: calc(100% - 60px); padding-left: 0px; padding-right: 10px"
        v-model="props.PropStore.Globalvariable.graphicsMemory"
        :step="props.Step"
        :min="props.Min"
        :max="props.Max"
        @change="_ChangeGraphicsMemory(props.PropStore.Globalvariable.graphicsMemory)"
      />
      <el-input-number
        v-model="props.PropStore.Globalvariable.graphicsMemory"
        :controls="false"
        style="width: 120px"
        placeholder="Please input"
        @input="_ChangeGraphicsMemory(props.PropStore.Globalvariable.graphicsMemory)"
      />
      <el-button
        class="startvalue"
        style="margin-left: 5px; width: 80px"
        type="success"
        text
        bg
        @click="
          _ChangeGraphicsMemory(props.PropStore.defaultToolset.graphicsMemory)
        "
        >恢复默认值</el-button
      >
    </div>
  </el-form-item>
  <el-divider style="margin-top: 15px; margin-bottom: 5px" />
  <div style="margin: 0px 6px 10px 0px; color: grey">浏览器缓存管理</div>

  <el-table
    :data="props.PropStore.Globalvariable.indexDBCache"
    style="width: 100%; text-align: center"
    :max-height="360"
    :row-style="{ 'line-height': '30px' }"
    :show-header="true"
    size="small"
    border
  >
    <!--2.1.1 序号 -->
    <el-table-column type="index" label="序号" width="50" align="center" />
    <!--2.1.2 地址 -->
    <el-table-column label="域名地址" align="center">
      <template #default="scope">
        <el-input
          style="margin: 0px; padding: 0px"
          v-model="props.PropStore.Globalvariable.indexDBCache[scope.$index]"
          placeholder="请输入域名地址"
        >
        </el-input
      ></template>
    </el-table-column>
    <!--2.1.3 操作的按钮  @input="_ChangeWebsite(scope)"-->
    <el-table-column label="操作" width="80" align="center">
      <template #default="scope"
        ><el-button
          size="small"
          text
          type="warning"
          style="margin: 2px 3px; padding: 0px"
          @click="_ChangeWebsite(scope.row)"
          >确定</el-button
        >
        <el-button
          size="small"
          text
          type="warning"
          style="margin: 2px 3px; padding: 0px"
          @click="_deleterow(scope.row)"
          >移除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <div
    style="
      margin-top: 2px;
      margin-right: 0px;
      display: flex;
      justify-content: flex-end;
    "
  > <el-button class="startvalue" type="success" text bg @click="_test"
      >test</el-button
    >
    <el-button class="startvalue" type="success" text bg @click="_AddWebsite"
      >增加缓存地址</el-button
    >
    <el-button
      class="startvalue"
      type="success"
      style="margin-left: 5px; width: 80px"
      text
      bg
      @click="base.ClearDBCache()"
      >清除缓存</el-button
    >
  </div>
</template>

<script lang="ts" setup>
import { getOnemap } from "../onemapkit";
import * as base from "../base/base"; 

import * as weather from "./weather"; 
const _test =()=>{
  weather.Weather.viewer=_inOnemap.MapViewer;
  // weather.Weather.handleRain(0.0005,0.00006)
  
  weather.Weather.ccc()
  // let ccc = CesiumTools.IndexedDBCache.getRuleList();
  // console.log("=================", props.PropStore.Globalvariable.indexDBCache, ccc);
}

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  Max: {
    type: Number,
    default: 10240,
    require: false,
  },
  Min: {
    type: Number,
    default: 512,
    require: false,
  },
  Step: {
    type: Number,
    default: 1,
    require: false,
  },
  PropStore: {
    type: Object as any,
    default: null,
    require: true,
  },
});

const _inOnemap = getOnemap(props.MapControlName);
const _ChangeGraphicsMemory = (_value: any) => {
  _inOnemap.MaximumMemory.maximumTotalMemoryUsage = _value;
};
const _deleterow = (row: any) => {
  props.PropStore.Globalvariable.indexDBCache.splice(
    props.PropStore.Globalvariable.indexDBCache.indexOf(row),
    1
  );
  props.PropStore.Globalvariable.indexDBCache.forEach((url: any) => {
    if (url != "") {
      base.addDBCacheUrl(url);
    }
  });
   
};
const _AddWebsite = () => {
  props.PropStore.Globalvariable.indexDBCache.push("");
};
const _ChangeWebsite = (level: any) => {
  props.PropStore.Globalvariable.indexDBCache[level.$index] = level.row;
  props.PropStore.Globalvariable.indexDBCache.forEach((url: any) => {
    if (url != "") {
      base.addDBCacheUrl(url);
    }
  }); 
};
</script>

<style lang="scss" scoped>
.slider-demo-block {
  display: flex;
}
.slider-demo-block .el-slider .startvalue {
  margin-top: 0;
  margin-left: 10px;
  margin-right: 10px;
}
.slider-demo-block .demonstration + .el-slider {
  flex: 0%;
}
</style>
