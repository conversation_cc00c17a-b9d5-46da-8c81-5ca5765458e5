

/** 默认的图层更多操作组件列表 */
const defaultLayerMoreOperateList = {
	/** 收藏 */
	favorites: {
		label: "favorites",
		name: "收藏",
		component: null,
		style: {
			color: "#606266",
		},
		visible: true,
		onClick: function (_operateList: any, _node: any, _data: any) {
			console.log(_operateList, _node, _data);
			debugger;
		},
	},
	/** 定位 */
	location: {
		label: "location",
		name: "定位",
		component: null,
		style: {
			color: "#606266",
		},
		visible: false,
		onClick: function (_operateList: any, _node: any, _data: any) {
			console.log(_operateList, _node, _data);
			debugger;
		},
	},
	/** 透明度 */
	opacity: {
		label: "opacity",
		name: "透明度",
		component: null,
		style: {
			color: "#606266",
		},
		visible: false,
		onClick: function (_operateList: any, _node: any, _data: any) {
			console.log(_operateList, _node, _data);
			debugger;
		},
	},
	/** 可见比例 */
	visibleScale: {
		label: "visibleScale",
		name: "可见比例",
		component: null,
		style: {
			color: "#606266",
		},
		visible: false,
		onClick: function (_operateList: any, _node: any, _data: any) {
			console.log(_operateList, _node, _data);
			debugger;
		},
	}
}

export default defaultLayerMoreOperateList;
