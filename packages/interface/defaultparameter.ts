import {
  geometryType, 
  type IToolItemType,
  type Options,
  DockType,
} from "./Icommon";
import * as Icons from "../icon/index";

//*****************************  treeType start  *****************************/
export type MapTreeTabType = {
  /** 当地图出现专题地图、地图查询时就要制定默认Tab值 */
  initTabpane?: String | any;
  /** 地图模式转换按钮 */
  SwitchMapVisible?: boolean | any;
  /** 地图模块 */
  basemap?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
  /** 图层树模块 */
  tabLayers?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
    /** 图层收藏模块 */
    tabFavorites?:
    | {
      visible: Boolean | any;
      Label: String | any;
      name: String | any;
    } | any;
  /** 最近浏览模块 */
  tabRecently?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
  /** 专题地图模块 */
  tabSpecial?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
};
export const defaultMapTreeTab: MapTreeTabType = {
  initTabpane: "layersmap", 
  basemap: {
    visible: true,
    Label: "基础底图",
    name: "basemap",
  },
  tabLayers: {
    visible: true,
    Label: "图层目录",
    name: "layersmap",
  },
	tabFavorites: {
		visible: true,
    Label: "我的收藏",
    name: "favorites",
	},
  tabRecently: {
    visible: true,
    Label: "最近浏览",
    name: "recentlymap",
  },
  tabSpecial: {
    visible: true,
    Label: "专题地图",
    name: "specialmap",
  },
};
export const defaultMultScreenMapTreeTab: MapTreeTabType = {
  initTabpane: "layersmap",
  SwitchMapVisible: true,
  basemap: {
    visible: true,
    Label: "基础底图",
    name: "basemap",
  },
  tabLayers: {
    visible: true,
    Label: "图层目录",
    name: "layersmap",
  },
  tabRecently: {
    visible: true,
    Label: "最近浏览",
    name: "recentlymap",
  },
  tabSpecial: {
    visible: false,
    Label: "专题地图",
    name: "specialmap",
  },
};

export type LayerContentTabType = {
  /** Map底部信息是否显示 */
  BottomInfoVisible?: Boolean | any;
  /** 图层树组件 */
  LayerTree?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
  /** 图层搜索组件 */
  LayerSearch?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
  /** 图里组件 */
  Legend?:
    | {
        visible: Boolean | any;
        Label: String | any;
        name: String | any;
      }
    | any;
};
export const defaultLayerContentTab: LayerContentTabType = {
  BottomInfoVisible: true,
  LayerTree: {
    visible: true,
    Label: "图层目录",
    name: "MapLayerTree",
  },
  LayerSearch: {
    visible: true,
    Label: "地图查询",
    name: "LayerSearch",
  },
  Legend: {
    visible: true,
    Label: "地图图例",
    name: "Legend",
  },
};

export type ComponentsType = {
  /** 图层树组件 */
  MapLayerTreeVisible?: boolean | any;
  /** 地图工具组件 */
  MapToolsVisible?: boolean | any;
  /** 底部信息栏组件 */
  BottomInfoVisible?: boolean | any;
  /** 地图切换组件 */
  SwitchMapVisible?: boolean | any;
  /** 属性查询组件 */
  PropertyVisible?: boolean | any;
};
export const defaultComponents: ComponentsType = {
  MapLayerTreeVisible: false,
  SwitchMapVisible: false,
  PropertyVisible: false,
  MapToolsVisible: false,
  BottomInfoVisible: false,
};
export const defaultSplitScreenComponents: ComponentsType = {
  MapLayerTreeVisible: false,
  SwitchMapVisible: true,
  PropertyVisible: false,
  MapToolsVisible: false,
  BottomInfoVisible: false,
};
export const defaultEditToolItems: Array<IToolItemType> = [
  {
    label: "绘制点",
    name: "drawpoint",
    icon: Icons.PointIcon,
    Dock: DockType.center,
    geoType: geometryType.point,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制线",
    name: "drawpolyline",
    Dock: DockType.center,
    icon: Icons.polylineIcon,
    geoType: geometryType.polyline,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制多边形",
    name: "drawpolygon",
    icon: Icons.Polygon2DIcon,
    Dock: DockType.center,
    geoType: geometryType.polygon,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制圆",
    name: "drawcircle",
    icon: Icons.CircleIcon,
    Dock: DockType.center,
    geoType: geometryType.circle,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制矩形",
    name: "drawrectangle",
    icon: Icons.RectangleIcon,
    Dock: DockType.center,
    geoType: geometryType.rectangle,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
];
/**
 * 点线面圆矩形绘制
 */
export const defaultQueryToolItems: Array<IToolItemType> = [
  {
    label: "绘制点",
    name: "drawpoint",
    icon: Icons.PointIcon,
    Dock: DockType.center,
    geoType: geometryType.point,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制线",
    name: "drawpolyline",
    icon: Icons.polylineIcon,
    Dock: DockType.center,
    geoType: geometryType.polyline,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制多边形",
    name: "drawpolygon",
    icon: Icons.Polygon2DIcon,
    Dock: DockType.center,
    geoType: geometryType.polygon,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制圆",
    name: "drawcircle",
    icon: Icons.CircleIcon,
    Dock: DockType.center,
    geoType: geometryType.circle,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "绘制矩形",
    name: "drawrectangle",
    icon: Icons.RectangleIcon,
    Dock: DockType.center,
    geoType: geometryType.rectangle,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
  {
    label: "导入坐标",
    name: "inputcoord",
    icon: Icons.RectangleIcon,
    Dock: DockType.center,
    geoType: geometryType.image,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: undefined, 
  },
];
 
//*****************************  treeType   end  *****************************/
export type OnemapConstructorOptions = {
  
  MapControlName: string | any;
  Options: Options | any;
  Components?: ComponentsType | any;
  MapLayerTree?: (LayerContentTabType & MapTreeTabType) | any;
  LayerTreeDrawTools?: Array<IToolItemType> | any;
  MapTools?:
    | {
        CustomTools?: Array<IToolItemType> | any;
        CustomToolSize?: { width: number; height: number } | any;
      }
    | any;
  MultScreenTree?: MapTreeTabType | any;
  MapDrawTools?: Array<IToolItemType> | any;
};