import { type Component } from "vue";

import {
  mapType as _mapType,
  type Options as _Options,
  type IPosition as _IPosition,
  type IMapLayer as _IMapLayer,
  type IToolItemType as _IToolItemType,
	CommonEventEnum,
} from "./Icommon";
/**  ******* 规则 *******
 *  该文件中的是定义的接口 枚举等，接口命名规则 I+首字母大写，
 *  如：IMapLayer，其他如枚举，首字母小写，如：mapType；
 *  地图组件初始化参数 Option 除外，为了与其他产品保持一致
 *
 * IOnemap.ts  0. 定义类型文件
 * RootMap.ts  1. TOP父类
 * arcMap.ts   1.1 arcgis实现方法子类
 * czmMap.ts   1.2 cesium实现方法子类
 * Onemap.ts   1.3 暴露给用户、CesiumMAP组件的子类
 */

export {
  mapType,
  geometryType,
  type IToolItemType,
	CommonEventEnum,
  type Options,
  type IPosition,
  BaseMapType,
  type IMapLayer,
  IFavoriteType,
  serviceType,
  DockType,
  ILayerType,
  type IComponent,
  variableType,
  type ICustomTabItem,
} from "./Icommon";
//#region  ArcGIS 部分 ，需整理接口 ，再并入对应的到处类型去

//#endregion

/**地图委托事件类型，
 * （回调）是Map事件的一种实现方式,app是组件对象、关键字段，args操作的数据对对象
 * 使用方法参考props // map:Cesium.Viewer | MapView
 */
export type IMapEvent = (
  app: Component | String | any,
  args: _Options | _IPosition | number | any
) => any;
/**
 * 地图事件
 * MapReadyEvent(mapControlName:String)
 * MapRealtimeEvent(mapControlName:String, position: any)
 * MapClickEvent(mapControlName:String,_point: any, _options: any)
 * SwitchMapEvent(mapControlName:String, MapType: mapType)
 */
export const mapEmits = [
  "BaseMapReadyEvent",
  "MapReadyEvent",
  "MapRealtimeEvent",
  "MapClickEvent",
  // "SwitchMapEvent",
];
export type mapEmitsEventType = (
  mapControlName: String,
  avg1?: any,
  avg2?: any,
  options?: any
) => any;
export type toolButtonEmitsEventType = (
  toolItem: any,
  state: any,
  mapControlName?: any
) => any;
/**
 * 数据源Array<IMapLayer> 的过滤器函数类型定义
 */
// export interface MaplayerFilterType { (Source: Array<IMapLayer>, mapType?: mapType,isSublayer?: boolean): any;}
export type IMaplayerFilter = (
  Source: _IMapLayer,
  mapType?: _mapType,
  isSublayer?: boolean
) => any;

/**
 * 地图服务、图层参数 ， 与图层服务相关的参数接口
 */

/**
 * 范围参数接口
 */
export interface IExtent {
  /**
   * west
   */
  xmin: number;
  /**
   * south
   */
  ymin: number;
  /**
   * east
   */
  xmax: number;
  /**
   * north
   */
  ymax: number;
}

/**
 * 空间查询单条数据的数据结构
 */
export interface IQueryItem {
  /**
   * 字段信息
   */
  attributes: any;

  /**
   * 坐标对象
   */
  geometry: any;
}

/**
 * 空间查询字段数据结构
 */
export interface IQueryField {
  /**
   * 字段名称
   */
  name: string;
  /**
   * 字段类型
   */
  type: string;
  /**
   * 字段别名
   */
  alias: string;
}

/**
 * 空间查询结果数据结构
 */
interface _IQueryResult {
  /**
   * 查询结果的要素对象列表
   */
  features: IQueryItem[];
  /**
   * 字段列表
   */
  fields: IQueryField[];

  /**
   * 查询结果的图形类型
   */
  geometryType: string;

  /**
   * 坐标系
   */
  spatialReference?: {
    wkid: number;
  };
}

export type QueryResult = _IQueryResult;

export interface ILightType1 {
  scenename?: string;
  groupname?: string;
  lightname?: string;
  lightType?: ScenelightType;
  color?: string;
  intensity?: number;
  radius?: number;
  outerConeDegrees?: number;
  innerConeDegrees?: number;

  lightBarType?: ScenelightType;
  position: {
    x: number;
    y: number;
    z: number;
  };
  directionPoint: {
    x: number;
    y: number;
    z: number;
  };
}
export enum ScenelightType {
  pointLight = "pointLight",
  spotLight = "spotLight",
  LightBar = "LightBar",
  LightImage = "LightImage",
  LightBarVertical = "LightBarVertical",
  LightBarHorizontal = "LightBarHorizontal",
  lightmodeldynamics = "dynamics",
  lightmodelstatic = "static",
  lightmodelimage = "image",
}
export interface ILightType {
  /**  序号  */
  index?: number;
  /** 光源类型 */
  lightType: string;
  /** 场景名称 */
  scenename: string;
  /** 光源组名称 */
  groupname: string;
  /** 光源名称 */
  lightname: string;
  /** 光源效果(ScenelightType) */
  lightEffectName: string;
  /** 光源效果(ScenelightType) */

  lightEffects: [
    {
      index: number;
      modelname: string;
      lightSource: string;
      lightprops: string;
    }
  ];
  //{
  /** staticlight(静态光源),dynamicslight(动态光源),imagelight(灯光动画), */
  //modellist: [];
  /** index(序号),modelname(模式名称),lightSource(资源路径\光源颜色),lightprops(模型ID\间隔时间) */
  //modelTitle: [];
  //};
  /** 光源强度 */
  intensity: number;
  /** 发光半径 */
  radius: number;
  /** 聚灯外角 */
  outerConeDegrees: number;
  /** 聚灯内角 */
  innerConeDegrees: number;
  /** 光源对象标签：position 或ModelID */
  lightobjecttag: string;
  /** 聚灯位置 */
  position: {
    x: number;
    y: number;
    z: number;
  };
  /** 聚灯照向 */
  directionPoint: {
    x: number;
    y: number;
    z: number;
  };
  /** 灯带类型:LightBarHorizontal (水平灯带),LightBarVertical(竖直灯带) */
  lightBarType: ScenelightType;
  /** 灯带半径 */
  lightBarRadius: number;
  /** 开启灯带发光 */
  lightBarOpenRay: boolean;
  /** 首尾是否闭合 */
  lightBarRingClose: boolean;
  /** 灯带顶部高程 */
  lightBarTopHight: number;
}

export enum RoamType {
  FirstPersion = "FirstPersion",
  PathRoam = "PathRoam",
  ViewPointRoam = "ViewPointRoam",
  FirstPersionAngle = "FirstPersionAngle",
  ThirdPersonAngle = "ThirdPersonAngle",
  FreedomAngle = "FreedomAngle",
}
export interface IRoamerType {
  roamType: RoamType;
  roamWayName: string;
  viewPointName: string;
  viewspaceTime: number;
  roamerModelName: string;
  roamerModelPath: string;
  roamerModelScale: string;
  roamModelScale: string;
  roamerAngle: string;
  roamerAngleX: number;
  roamerAngleY: number;
  roamerAngleZ: number;
  roamRunLoop: boolean;
  roamRunTime: number;
  roamPath: string;
  openGravity: boolean;
  openCollision: boolean;
  cameraFovAngle: number;
  roamSpeed: number;
}

export interface ITdtResultType {
  resultType: string;
  status: any;
  count: number;
  keyword: string;
  Data: Array<ITdtDataItemType>;
}
export interface ITdtDataItemType {
  id?: string;
  title?: string;
  address?: string;
  lonlat: { x: number; y: number };
  phone?: string;
  poiType?: string;
  resultType?: string;
  stationData?: any;
  prompt?: any;
}

/**
 * 底栏信息数据结构
 */
export class BottomInfoData {
  /**
   * 审图号
   */
  checkImageNumber?: string =
    "天地图-tianditu.gov.cn-审图号：GS（2024）0568号 | 桂S（2024）01-53号";

  /**
   * 涉密警告信息
   */
  hint?: string = "提示:地图数据属于内部数据，请勿截图、拍照外传！";

  /**
   * 经纬度和高度信息
   */
  coordinates?: _IPosition = { x: 0, y: 0, z: 0 };

  /**
   * 坐落信息
   */
  position?: string = "";

  /**
   * 公司信息
   */
  company?: string = "";

  /**
   * 最佳比例
   */
  bestRatio?: string = "";

  /**
   * 比例尺
   */
  scale?: string = "";

  /**
   * 自定义信息展示
   */
  customInfo?: string = "";
}
