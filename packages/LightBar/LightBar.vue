<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item title="添加灯带" name="addLightBar">
      <el-form :model="lightBarInfor" class="light-bar-class">
        <el-form-item label="灯带类型">
          <el-radio-group v-model="lightBarInfor.type">
            <el-radio label="HorizontalExtent">水平灯带</el-radio>
            <el-radio label="VerticalExtent">竖直灯带</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="lightBarInfor.type === 'HorizontalExtent'"
          label="灯带首尾闭合"
        >
          <el-switch v-model="lightBarInfor.close" />
        </el-form-item>
        <el-form-item
          v-if="lightBarInfor.type === 'HorizontalExtent'"
          label="绘制灯带"
        >
          <el-button type="primary" @click="drawLineTool.start()">
            绘制
          </el-button>
        </el-form-item>
        <el-form-item
          v-if="lightBarInfor.type === 'VerticalExtent'"
          label="灯带顶部高程"
        >
          <el-input-number
            v-model="lightBarInfor.topHeight"
            :min="0"
            :step="0.01"
          />
        </el-form-item>
        <el-form-item
          v-if="lightBarInfor.type === 'VerticalExtent'"
          label="拾取灯带底部点"
        >
          <el-button type="primary" @click="drawPointTool.start()">
            拾取
          </el-button>
        </el-form-item>
      </el-form>
    </el-collapse-item>
    <el-collapse-item title="全局设置" name="globeSetup">
      <el-form :model="lightBarGlobeInfor" class="light-bar-class">
        <el-form-item label="灯带半径">
          <el-input-number
            v-model="lightBarGlobeInfor.radius"
            :min="0"
            :step="0.01"
            @change="nightProgramme.update(lightBarGlobeInfor.radius)"
          />
        </el-form-item>
        <el-form-item label="灯带颜色">
          <el-color-picker
            v-model="lightBarGlobeInfor.color"
            @change="colorChange()"
          />
        </el-form-item>
        <el-form-item label="开启灯带发光">
          <el-switch
            v-model="lightBarGlobeInfor.enable"
            @change="lightEnable()"
          />
        </el-form-item>
        <el-form-item label="发光强度">
          <el-input-number
            v-model="lightBarGlobeInfor.bloomStrength"
            :min="0"
            :step="0.01"
            @change="
              nightProgramme.bloomStrength = lightBarGlobeInfor.bloomStrength
            "
          />
        </el-form-item>
        <el-form-item label="发光半径">
          <el-input-number
            v-model="lightBarGlobeInfor.bloomRadius"
            :min="0"
            :max="1"
            :step="0.01"
            @change="
              nightProgramme.bloomRadius = lightBarGlobeInfor.bloomRadius
            "
          />
        </el-form-item>
      </el-form>
    </el-collapse-item>
    <el-collapse-item title="灯带列表" name="lightBars">
      <el-collapse v-model="lightActiveNames" accordion class="collapse-item">
        <el-collapse-item
          v-for="light in horizontalExtents"
          title="水平灯带"
          :name="light.key"
          :key="light.key"
        >
          <el-collapse
            v-model="lightActiveNames2"
            accordion
            class="collapse-item"
          >
            <el-collapse-item
              v-for="(point, pointIndex) in light.value"
              :title="'顶点' + pointIndex"
              :name="point.key"
              :key="point.key"
            >
              <el-form class="light-bar-class">
                <el-form-item label="经度">
                  <el-input-number
                    v-model="point.value[0]"
                    :min="-180"
                    :max="180"
                    :step="0.01"
                    @change="horizontalExtentsUpdate()"
                  />
                </el-form-item>
                <el-form-item label="维度">
                  <el-input-number
                    v-model="point.value[1]"
                    :min="-90"
                    :max="90"
                    :step="0.01"
                    @change="horizontalExtentsUpdate()"
                  />
                </el-form-item>
                <el-form-item label="高程">
                  <el-input-number
                    v-model="point.value[2]"
                    :step="0.01"
                    @change="horizontalExtentsUpdate()"
                  />
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-collapse-item>

        <el-collapse-item
          v-for="light in verticalExtents"
          title="竖直灯带"
          :name="light.key"
          :key="light.key"
        >
          <el-form class="light-bar-class">
            <el-form-item label="底部点经度">
              <el-input-number
                v-model="light.bottomPoint[0]"
                :min="-180"
                :max="180"
                :step="0.01"
                @change="verticalExtentsUpdate()"
              />
            </el-form-item>
            <el-form-item label="底部点维度">
              <el-input-number
                v-model="light.bottomPoint[1]"
                :min="-90"
                :max="90"
                :step="0.01"
                @change="verticalExtentsUpdate()"
              />
            </el-form-item>
            <el-form-item label="底部点高程">
              <el-input-number
                v-model="light.bottomPoint[2]"
                :step="0.01"
                @change="verticalExtentsUpdate()"
              />
            </el-form-item>
            <el-form-item label="顶部高程">
              <el-input-number
                v-model="light.topHeight"
                :step="0.01"
                @change="verticalExtentsUpdate()"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts">
import { onUnmounted, ref, defineComponent, type PropType } from "vue";
import {
  Cartesian3,
  Cartographic,
  Math as CesiumMath,
  Color,
  createGuid,
} from "@onemapkit/cesium";
import { DrawTool, DrawMod } from "@onemapkit/cesium-tools";
import NightProgramme from "./tools";
import {
  type Options as __Options,
  getOnemap, 
} from "../onemapkit";

const props = () => ({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  }, 
  horizontalExtents: {
    type: Object as PropType<number[][][]>,
    default: [],
    require: false,
  },
  verticalExtents: {
    type: Object as PropType<{ bottomPoint: number[]; topHeight: number }[]>,
    default: [],
    require: false,
  },
});

interface HorizontalExtent {
  key: string;
  value: HorizontalExtentValue[];
}

interface HorizontalExtentValue {
  key: string;
  value: number[];
}

interface VerticalExtent {
  key: string;
  bottomPoint: number[];
  topHeight: number;
}

export default defineComponent({
  name: "LightBar",
  // 入参
  props: props(),

  setup(props, { expose }) {
    const _Onemap = getOnemap(props.MapControlName);
     
    const activeNames = ref([]);
    const lightActiveNames = ref([]);
    const lightActiveNames2 = ref([]);

    const viewer = _Onemap.MapViewer;
    const nightProgramme = new NightProgramme(viewer);

    const lightBarInfor = ref({
      type: "HorizontalExtent",
      topHeight: 140,
      close: false,
    });

    const lightBarGlobeInfor = ref({
      radius: 0.2,
      color: "#03ffff",
      bloomStrength: 1.5, // 发光强度
      bloomRadius: 0.59, // 发光半径
      enable: true, // 是否开启发光
    });

    const horizontalExtents = ref([] as HorizontalExtent[]); // 水平灯带

    // 根据初始传入的参数更新水平灯带
    if (props.horizontalExtents) {
      const newHorizontalExtents: HorizontalExtent[] = [];
      props.horizontalExtents.forEach((he) => {
        const newHe: HorizontalExtentValue[] = [];
        he.forEach((point) => {
          newHe.push({
            key: createGuid(),
            value: point,
          });
        });
        newHorizontalExtents.push({
          key: createGuid(),
          value: newHe,
        });
      });
      horizontalExtents.value = newHorizontalExtents;
      horizontalExtentsUpdate();
    }

    const verticalExtents = ref([] as VerticalExtent[]); // 竖直灯带

    // 根据初始传入的参数更新竖直灯带
    if (props.verticalExtents) {
      verticalExtents.value = props.verticalExtents.map((ve) => {
        return {
          key: createGuid(),
          bottomPoint: ve.bottomPoint,
          topHeight: ve.topHeight,
        };
      });
      verticalExtentsUpdate();
    }

    // 使用线工具来绘制水平灯带
    const drawLineTool = new DrawTool(viewer, DrawMod.Line, {});
    drawLineTool.drawEndEvent.addEventListener((points: Cartesian3[]) => {
      const cartographics = points.map((p: Cartesian3) => {
        const cartographic = Cartographic.fromCartesian(p);
        return {
          key: createGuid(),
          value: [
            CesiumMath.toDegrees(cartographic.longitude),
            CesiumMath.toDegrees(cartographic.latitude),
            cartographic.height,
          ],
        };
      });
      // 如果开启首尾闭合，则在点末尾追加第一个点
      if (lightBarInfor.value.close) {
        cartographics.push(cartographics[0]);
      }
      horizontalExtents.value.push({
        key: createGuid(),
        value: cartographics,
      });
      horizontalExtentsUpdate();
    });

    // 使用点工具来绘制底部点
    const drawPointTool = new DrawTool(viewer, DrawMod.Point, {});
    drawPointTool.drawEndEvent.addEventListener((point: Cartesian3) => {
      const cartographic = Cartographic.fromCartesian(point);
      verticalExtents.value.push({
        key: createGuid(),
        bottomPoint: [
          CesiumMath.toDegrees(cartographic.longitude),
          CesiumMath.toDegrees(cartographic.latitude),
          cartographic.height,
        ],
        topHeight: lightBarInfor.value.topHeight,
      });
      verticalExtentsUpdate();
    });

    // 颜色改变
    function colorChange() {
      nightProgramme.appearance.material.uniforms.u_color =
        Color.fromCssColorString(lightBarGlobeInfor.value.color);
    }

    // 发光开启/关闭
    function lightEnable() {
      if (lightBarGlobeInfor.value.enable) {
        nightProgramme.createPostProcessStage();
      } else {
        nightProgramme.remocePostProcessStage();
      }
    }

    // 水平灯带内容更新
    function horizontalExtentsUpdate() {
      nightProgramme.horizontalExtents = horizontalExtents.value;
      nightProgramme.update(lightBarGlobeInfor.value.radius);
    }

    // 竖直灯带内容更新
    function verticalExtentsUpdate() {
      nightProgramme.verticalExtents = verticalExtents.value;
      nightProgramme.update(lightBarGlobeInfor.value.radius);
    }

    // 获取水平灯带的内容
    function getHorizontalExtents() {
      const newHorizontalExtents: number[][][] = [];
      horizontalExtents.value.map((he) => {
        const newHe: number[][] = [];
        he.value.forEach((points) => {
          newHe.push(points.value);
        });
        newHorizontalExtents.push(newHe);
      });
      return newHorizontalExtents;
    }

    // 获取竖直灯带的内容
    function getVerticalExtents() {
      const newVerticalExtents: { bottomPoint: number[]; topHeight: number }[] =
        verticalExtents.value.map((ve) => {
          return {
            bottomPoint: ve.bottomPoint,
            topHeight: ve.topHeight,
          };
        });
      return newVerticalExtents;
    }

    // 组件销毁
    onUnmounted(() => {
      if (viewer && (viewer as any)._cesiumWidget && nightProgramme) {
        nightProgramme.destroy();
      }
    });

    // 抛出到外面的方法
    expose({
      getHorizontalExtents,
      getVerticalExtents,
    });

    return {
      activeNames,
      lightActiveNames,
      lightActiveNames2,
      lightBarInfor,
      lightBarGlobeInfor,
      horizontalExtents,
      verticalExtents,
      drawLineTool,
      drawPointTool,
      nightProgramme,
      colorChange,
      lightEnable,
      horizontalExtentsUpdate,
      verticalExtentsUpdate,
    };
  },
});
</script>

<style lang="less" scoped>
.light-bar-class {
  padding: 16px 10px 0px 34px;
}
.collapse-item {
  padding: 0px 10px 0px 34px;
}
</style>
