import {
  Math as CesiumMath,
  Cartesian2,
  Cartesian3,
  Cartographic,
  PostProcessStage,
  PostProcessStageSampleMode,
  PostProcessStageComposite,
  Viewer,
  Primitive,
  MaterialAppearance,
  GeometryInstance,
  PolylineVolumeGeometry,
  Matrix4,
  Transforms,
  CylinderGeometry,
  Material,
  Color,
  Geometry,
} from "@onemapkit/cesium";

const singleStageFrag = `
uniform sampler2D colorTexture;
in vec2 v_textureCoordinates;

void main() {
    // 如果没选中 
    if(!czm_selected()) {
        discard;
    }
    vec4 texel = texture(colorTexture, v_textureCoordinates);        
    out_FragColor = texel;
}
`;

const shadersSeparableBlur = `
uniform sampler2D colorTexture;
uniform vec2 direction;
in vec2 v_textureCoordinates;

float gaussianPdf(in float x, in float sigma) {
    return 0.39894 * exp( -0.5 * x * x / ( sigma * sigma)) / sigma;
}

void main() {
    vec2 invSize = 1.0 / czm_viewport.zw;
    float fSigma = float(SIGMA);
    float weightSum = gaussianPdf(0.0, fSigma);
    vec3 diffuseSum = texture( colorTexture, v_textureCoordinates).rgb * weightSum;
    for( int i = 1; i < KERNEL_RADIUS; i ++ ) {
        float x = float(i);
        float w = gaussianPdf(x, fSigma);
        vec2 uvOffset = direction * invSize * x;
        vec3 sample1 = texture( colorTexture, v_textureCoordinates + uvOffset).rgb;
        vec3 sample2 = texture( colorTexture, v_textureCoordinates - uvOffset).rgb;
        diffuseSum += (sample1 + sample2) * w;
        weightSum += 2.0 * w;
    }
    out_FragColor = vec4(diffuseSum / weightSum, 1.0);
}
`;

const finshStageFrag = `
#extension GL_OES_standard_derivatives : enable
uniform sampler2D blurTexture1;
uniform sampler2D blurTexture2;
uniform sampler2D blurTexture3;
uniform sampler2D blurTexture4;
uniform sampler2D colorTexture;
uniform float bloomStrength;
uniform float bloomRadius;


in vec2 v_textureCoordinates;

float bloomFactors1 = 1.0;
float bloomFactors2 = 0.6;
float bloomFactors3 = 0.2;
float bloomFactors4 = 0.1;

float lerpBloomFactor(const in float factor, float bloomRadius) {
	float mirrorFactor = 1.2 - factor;
	return mix(factor, mirrorFactor, bloomRadius);
}

void main() {
    vec4 color = texture(colorTexture, v_textureCoordinates);
	color += bloomStrength * ( lerpBloomFactor(bloomFactors1, bloomRadius) * texture(blurTexture1, v_textureCoordinates) +
	    lerpBloomFactor(bloomFactors2, bloomRadius) * texture(blurTexture2, v_textureCoordinates) +
	    lerpBloomFactor(bloomFactors3, bloomRadius) * texture(blurTexture3, v_textureCoordinates) +
	    lerpBloomFactor(bloomFactors4, bloomRadius) * texture(blurTexture4, v_textureCoordinates)
    );

    if(color.r > 1.0) color.r = 1.0;
    if(color.g > 1.0) color.g = 1.0;
    if(color.b > 1.0) color.b = 1.0;

    out_FragColor = color;
}
`;

function computeCircle(radius: number) {
  const positions:any = [];
  for (let i = 0; i < 360; i++) {
    const radians = CesiumMath.toRadians(i);
    positions.push(
      new Cartesian2(radius * Math.cos(radians), radius * Math.sin(radians))
    );
  }
  return positions;
}

function getNightProgrammePositions(positions: Cartesian3[], radius: number) {
  const newPositions = positions.map((p) => {
    const cartographic = Cartographic.fromCartesian(p);
    return Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      cartographic.height - radius
    );
  });

  if (newPositions[0].equals(newPositions[newPositions.length - 1])) {
    // 如果首尾相连，则返回首尾相连的格式
    const first = Cartesian3.midpoint(
      newPositions[newPositions.length - 1],
      newPositions[newPositions.length - 2],
      new Cartesian3()
    );

    const final = Cartesian3.midpoint(
      newPositions[0],
      newPositions[1],
      new Cartesian3()
    );

    return [first, ...newPositions, final];
  } else {
    // 如果首尾不相连，则直接返回
    return newPositions;
  }
}

function parseDefines(shader: any) {
  let defines: any = [];
  for (const key in shader.defines) {
    if (shader.defines.hasOwnProperty(key)) {
      const val = shader.defines[key];
      defines.push("#define " + key + " " + val);
    }
  }
  defines = defines.join("\n") + "\n";
  if (shader.fragmentShader) {
    shader.fragmentShader = defines + shader.fragmentShader;
  }
  if (shader.vertexShader) {
    shader.vertexShader = defines + shader.vertexShader;
  }
  return shader;
}

// 获取卷积处理的后处理(name,中心半径,纹理缩放尺寸)
function createBlurStage(
  name: string,
  kernelRadius: number,
  textureScale: number
) {
  const blurDirectionX = new Cartesian2(1.0, 0.0);
  const blurDirectionY = new Cartesian2(0.0, 1.0);

  const separableBlurShader = {
    defines: {
      KERNEL_RADIUS: kernelRadius,
      SIGMA: kernelRadius,
    },
    fragmentShader: shadersSeparableBlur,
  };
  parseDefines(separableBlurShader);

  // X方向上的卷积
  const blurX = new PostProcessStage({
    name: name + "_x_direction",
    fragmentShader: separableBlurShader.fragmentShader,
    textureScale: textureScale,
    uniforms: {
      direction: blurDirectionX,
    },
    sampleMode: PostProcessStageSampleMode.LINEAR,
  });

  // Y方向上的卷积
  const blurY = new PostProcessStage({
    name: name + "_y_direction",
    fragmentShader: separableBlurShader.fragmentShader,
    textureScale: textureScale,

    uniforms: {
      direction: blurDirectionY,
    },
    sampleMode: PostProcessStageSampleMode.LINEAR,
  });

  return new PostProcessStageComposite({
    name: name,
    stages: [blurX, blurY],
    inputPreviousStageTexture: true,
  });
}

export default class NightProgramme {
  private _viewer: Viewer;
  private _primitive: Primitive | undefined;
  private _bloomStrength: number;
  private _bloomRadius: number;
  private _update: boolean;
  private _removeCallback: any;
  private _selected: any[];
  private _singleStage: any;

  public appearance: MaterialAppearance;
  public horizontalExtents: any[];
  public verticalExtents: any[];
  public postProcessStageComposite: any;

  constructor(viewer: Viewer) {
    this._viewer = viewer;
    this._bloomStrength = 1.479;
    this._bloomRadius = 0.589;
    this.appearance = new MaterialAppearance({
      flat: true,
      translucent: false,
      material: new Material({
        translucent: false,
        fabric: {
          type: "NightProgramme",
          uniforms: {
            u_color: Color.AQUA,
          },
          source: `
              czm_material czm_getMaterial(czm_materialInput materialInput){
                czm_material material = czm_getDefaultMaterial(materialInput);
                material.diffuse = u_color.rgb;
                material.alpha = 1.0;
                return material;
              }`,
        },
      }),
      fragmentShaderSource: `
            void main(){
              czm_materialInput materialInput;
              czm_material material = czm_getMaterial(materialInput);
              out_FragColor = vec4(material.diffuse, material.alpha);
            }
          `,
    });

    this.horizontalExtents = [];
    this.verticalExtents = [];
    this._update = false;
    this._selected = [];

    // 一个每帧轮训尝试获取pickId的操作，获取到即停止
    this._removeCallback = this._viewer.scene.postRender.addEventListener(
      () => {
        if (this._update) {
          // 只高亮范围线
          this._selected = [];
          this._singleStage.selected = this._selected;
          (this._primitive as any)._pickIds.forEach((pickId: any) => {
            this._selected.push({
              pickId: pickId,
            });
          });

          if (
            this.horizontalExtents.length > 0 &&
            this.verticalExtents.length > 0 &&
            this._selected.length === 0
          ) {
            // 当存在灯带，但是没有获取到灯带的Pickid时，不结束更新操作，继续循环更新，直到获取到灯带为止
            this._update = true;
          } else {
            this._update = false;
            this._singleStage.selected = this._selected;
          }
        }
      }
    );

    this.createPostProcessStage();
  }

  update(radius: number) {
    if (this._primitive) {
      this._viewer.scene.primitives.remove(this._primitive);
    }
    const geometryInstances: GeometryInstance[] = [];

    this.verticalExtents.forEach((ve: any) => {
      const { bottomPoint, topHeight } = ve;
      const length = topHeight - bottomPoint[2];
      const positionOnEllipsoid = Cartesian3.fromDegrees(
        bottomPoint[0],
        bottomPoint[1],
        bottomPoint[2]
      );

      const modelMatrix = Matrix4.multiplyByTranslation(
        Transforms.eastNorthUpToFixedFrame(positionOnEllipsoid),
        new Cartesian3(0.0, 0.0, length * 0.5),
        new Matrix4()
      );

      const greenCylinder = new GeometryInstance({
        geometry: CylinderGeometry.createGeometry(
          new CylinderGeometry({
            length: length,
            topRadius: radius,
            bottomRadius: radius,
          })
        ) as Geometry,
        modelMatrix: modelMatrix,
      });

      geometryInstances.push(greenCylinder);
    });

    this.horizontalExtents.forEach((he: any) => {
      const positions: Cartesian3[] = [];
      he.value.forEach((point: any) => {
        positions.push(Cartesian3.fromDegrees(point.value[0], point.value[1], point.value[2]));
      });
      const tubeGeometry = new GeometryInstance({
        geometry: new PolylineVolumeGeometry({
          polylinePositions: getNightProgrammePositions(positions, radius),
          shapePositions: computeCircle(radius),
        }),
      });

      geometryInstances.push(tubeGeometry);
    });

    this._primitive = new Primitive({
      geometryInstances: geometryInstances,
      appearance: this.appearance,
      asynchronous: false,
    });

    this._viewer.scene.primitives.add(this._primitive);
    this._update = true;
  }

  /**
   * 泛光的强度
   *
   * @type {Number}
   */
  get bloomStrength() {
    return this._bloomStrength;
  }
  set bloomStrength(value) {
    this._bloomStrength = value;
    this.postProcessStageComposite.bloomStrength = value;
  }

  /**
   * 泛光的范围
   *
   * @type {Number}
   */
  get bloomRadius() {
    return this._bloomRadius;
  }
  set bloomRadius(value) {
    this._bloomRadius = value;
    this.postProcessStageComposite.bloomRadius = value;
  }

  remocePostProcessStage() {
    if (this.postProcessStageComposite) {
      this._viewer.postProcessStages.remove(this.postProcessStageComposite);
      this.postProcessStageComposite = undefined;
    }
  }

  createPostProcessStage() {
    this.remocePostProcessStage();

    // 用于获取指定物体的颜色
    this._singleStage = new PostProcessStage({
      name: "step1",
      fragmentShader: singleStageFrag,
      sampleMode: PostProcessStageSampleMode.LINEAR,
    });

    this._singleStage.selected = this._selected;

    // 创建4个不同程度的卷积模糊
    const blurStage1 = createBlurStage("blur1", 3, 1);
    const blurStage2 = createBlurStage("blur2", 5, 0.5);
    const blurStage3 = createBlurStage("blur3", 7, 0.25);
    const blurStage4 = createBlurStage("blur4", 18, 0.25);

    // 对指定物体颜色进行第一重卷积
    const sdf = new PostProcessStageComposite({
      name: "sdf",
      stages: [this._singleStage, blurStage1],
      inputPreviousStageTexture: true,
    });

    // 第二重卷积
    const sdf2 = new PostProcessStageComposite({
      name: "sdf2",
      stages: [sdf, blurStage2],
      inputPreviousStageTexture: true,
    });

    // 第三重卷积
    const sdf3 = new PostProcessStageComposite({
      name: "sdf3",
      stages: [sdf2, blurStage3],
      inputPreviousStageTexture: true,
    });

    // 第四重卷积
    const sdf4 = new PostProcessStageComposite({
      name: "sdf4",
      stages: [sdf3, blurStage4],
      inputPreviousStageTexture: true,
    });

    const fish = new PostProcessStage({
      name: "fishshadersds",
      uniforms: {
        blurTexture1: blurStage1.name,
        blurTexture2: blurStage2.name,
        blurTexture3: blurStage3.name,
        blurTexture4: blurStage4.name,
        bloomStrength: () => {
          return this._bloomStrength;
        },
        bloomRadius: () => {
          return this._bloomRadius;
        },
      },
      fragmentShader: finshStageFrag,
    });

    // 原始颜色叠加四重卷积的指定颜色
    this.postProcessStageComposite = new PostProcessStageComposite({
      name: "sdfs",
      stages: [sdf, sdf2, sdf3, sdf4, fish],
      inputPreviousStageTexture: false,
    });
    this._viewer.postProcessStages.add(this.postProcessStageComposite);
  }

  destroy() {
    this.remocePostProcessStage();
    this._removeCallback();
    if (this._primitive) {
      this._viewer.scene.primitives.remove(this._primitive);
    }
  }
}
