<template>
  <el-tooltip :content="propertyswitch ? '关闭属性点选' : '开启属性点选'" placement="left" effect="light">
    <div class="map-switch map-righ-button" @click="handlePropertyswitch">
      <i v-html="propertyswitch ? Icons.InfomationOn : Icons.InfomationOff"></i>
      <span style="font-size: 12px; caret-color: transparent">属性</span>
    </div>
  </el-tooltip>
  <PopPanel v-if="_propertyPanel" :title="'查询结果'" :width="450" :height="360" :top="20" :right="75"
    @close="Propertyclose">
    <div>{{ content }}</div>
    <PropertyPanel></PropertyPanel>
  </PopPanel>
</template>

<script setup lang="ts">
import { ref, defineProps, onMounted } from "vue";
import {
  Icons,
  getOnemap,
  PopPanel,
  PropertyPanel,
  MessageBox,
} from "../onemapkit";
import * as base from "../base/base";
const propertyswitch = ref(false);
const _propertyPanel = ref(false);

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
});

//1.初始化 Onemap、Options
const _Onemap = getOnemap(props.MapControlName);

const content = ref("");
let _MouseClick: any = (avg: any) => {
  MessageBox({
    props: {
      title: "提示",
      onConfirm: (_result: boolean) => {
        // result.value = _result ? "选择的确定" : "选择的取消或关闭弹窗";
      },
    },
    content: `点击的屏幕坐标:${avg.position.x},${avg.position.x}`,
  });

  // if (avg.position.x < 200) {
  //   _propertyPanel.value = true;
  // } else {
  //   _propertyPanel.value = false;
  // }
  // content.value = avg.position.x + "," + avg.position.y;
};
const handlePropertyswitch = () => {
  propertyswitch.value = !propertyswitch.value;
  props.PropStore.propertyVisible.value = propertyswitch.value
  console.log('props.PropStore', props.PropStore);

  // if (propertyswitch.value) {
  //   _Onemap.setMapEventHandler(base.MouseEventType.LEFT_CLICK, {
  //     isThrottle: false,
  //     handlerName: "_MouseClickAttr",
  //     handler: _MouseClick,
  //   });
  // } else {
  //   _Onemap.removeMapEventHandler(base.MouseEventType.LEFT_CLICK, {
  //     handler: _MouseClick,
  //     handlerName: "_MouseClickAttr",
  //   });
  // }
};
const Propertyclose = () => {
  _propertyPanel.value = false;
};
onMounted(() => {
  _Onemap.removeMapEventHandler(base.MouseEventType.LEFT_CLICK, {
    handler: _MouseClick,
    handlerName: "_MouseClickAttr",
  });

  propertyswitch.value = true;
  props.PropStore.propertyVisible.value = true;

  props.PropStore.setStorage({
    isLocalStorage: true,
    storagekey: 'isDrawing',
    variableType: 1,
    initStoragevalue: false,
  })

});
</script>
<style lang="less" scoped>
.common-tool-panel {
  .map-switch {
    margin-top: 8px;
    width: 45px;
    margin-left: 8px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
    text-align: center;
    cursor: pointer;
  }
}
</style>
