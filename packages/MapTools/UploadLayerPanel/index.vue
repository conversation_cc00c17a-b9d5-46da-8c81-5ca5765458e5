<template>
  <div :id="panelID" class="upload-layer-panel">
    <el-form
      ref="formMapServer"
      :rules="formsRules"
      id="onlineForm"
      :model="forms"
      size="small"
    >
      <el-form-item label="应用场景" prop="viewType">
        <el-select
          size="small"
          :teleported="false"
          v-model="forms.viewType"
          placeholder="请选择应用场景"
        >
          <el-option
            v-for="(item, index) in viewTypes"
            :key="'options' + index"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务类型" prop="serverType">
        <el-select
          size="small"
          :teleported="false"
          v-model="forms.serverType"
          placeholder="请选择服务类型"
        >
          <el-option
            v-for="(item, index) in serverTypes"
            :key="'options' + index"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务地址" prop="serverUrl">
        <el-input
          size="small"
          class="serverUrl-input uppload-layer-input"
          placeholder="输入服务地址"
          v-model="forms.serverUrl"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="图层  I D " prop="serverID">
        <el-input
          size="small"
          class="serverUrl-input uppload-layer-input"
          placeholder="输入图层id"
          v-model="forms.serverID"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="图层名称" prop="serverName">
        <el-input
          size="small"
          class="serverName-input uppload-layer-input"
          placeholder="输入图层名称"
          v-model="forms.serverName"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="坐标系统" prop="epsgVal">
        <el-select
          size="small"
          :teleported="false"
          v-model="forms.epsgVal"
          placeholder="请选择坐标系"
        >
          <el-option
            v-for="item in coords"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="显示范围"
        class="flex-row"
        required
        style="margin-bottom: 5px"
      >
        <el-form-item prop="maxScale">
          <el-input
            size="small"
            class="EPSG-input uppload-layer-input-scale"
            placeholder="最大比例尺"
            v-model.number="forms.maxScale"
            clearable
          >
          </el-input>
        </el-form-item>
        <span>--</span>
        <el-form-item prop="minScale">
          <el-input
            size="small"
            class="EPSG-input uppload-layer-input-scale"
            placeholder="最小比例尺"
            v-model.number="forms.minScale"
            clearable
          >
          </el-input>
        </el-form-item>
      </el-form-item>
      <!-- <el-form-item label="添加收藏" style="margin-top:10px;">
                    <el-switch v-model="forms.save" size="small" @change="handleSaveChange"></el-switch>
                </el-form-item> -->
    </el-form>
    <el-button
      :loading="checkLoad"
      @click="testLoad"
      size="small"
      type="primary"
      >{{ checkLoad ? "正在验证..." : "添加" }}</el-button
    >
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, type PropType, defineProps } from "vue";
import { OnemapClass, serviceType } from "../../onemapkit";
import { ElMessage, ElMessageBox } from "element-plus";
const props = defineProps({
  Onemap: {
    type: OnemapClass,
    default: () => {
      let tmp = new OnemapClass();
      tmp.isMapReady = ref(false);
      return tmp;
    },
    require: true,
  },
  Store: {
    type: Object as PropType<{
      layers: any;
      Props: any;
    }>,
    default: { layers: null, Props: null },
    require: true,
  },
});

const panelID = "UploadLayerPanel";

const coords = ref([
  {
    label: "2000地理坐标系（4490）",
    value: 4490,
  },
  {
    label: "2000平面坐标系_有带号（4524）",
    value: 4524,
  },
  {
    label: "2000平面坐标系_无带号（4545）",
    value: 4545,
  },
  {
    label: "WGS84地理坐标系（4326）",
    value: 4326,
  },
  {
    label: "墨卡托投影（3857）",
    value: 3857,
  },
  {
    label: "西安80坐标系（2360）",
    value: 2360,
  },
  {
    label: "北京54坐标系（2412）",
    value: 2412,
  },
]);

const forms = ref({
  viewType: "arcgis",
  serverSource: "Arcgis Server",
  serverType: serviceType.ArcgisTileLayer,
  serverUrl: "",
  serverName: "",
  serverID: "",
  epsgVal: 4490,
  elevationMode: "on-the-ground",
  opacity: 100,
  maxScale: 500,
  minScale: 100000,
  save: false,
});

const formMapServer = ref();

// const selectTypes = ref([]);
const checkLoad = ref(false);

// 校验表单
const formsRules = {
  viewType: [{ required: true, message: "请选择应用场景", trigger: "blur" }],
  serverSource: [
    { required: true, message: "请选择服务来源", trigger: "blur" },
  ],
  serverType: [{ required: true, message: "请选择服务类型", trigger: "blur" }],
  serverUrl: [{ required: true, message: "请选择服务地址", trigger: "blur" }],
  serverID: [{ required: true, message: "请选择图层ID", trigger: "blur" }],
  serverName: [{ required: true, message: "请选择图层名称", trigger: "blur" }],
  epsgVal: [{ required: true, message: "请选择坐标系统", trigger: "blur" }],
  elevationMode: [
    { required: true, message: "请选择贴地类型", trigger: "blur" },
  ],
  opacity: [{ required: true, message: "请选择透明度", trigger: "blur" }],
  maxScale: [
    { required: true, message: "请输入最大显示范围", trigger: "blur" },
    { type: "number", message: "最大显示范围必须为数字值" },
  ],
  minScale: [
    { required: true, message: "请输入最小显示范围", trigger: "blur" },
    { type: "number", message: "最小显示范围必须为数字值" },
  ],
} as any;

const testLoad = () => {
  if (forms.value.viewType !== props.Onemap.MapType.value) {
    ElMessageBox.alert("应用场景与当前地图视图类型不一致");
    return;
  }

  formMapServer.value.validate(async (valid: any) => {
    if (valid) {
      let serverUrl = forms.value.serverUrl;
      let epsgVal = forms.value.epsgVal;
      let serverType = forms.value.serverType;
      let minScale = forms.value.minScale;
      let maxScale = forms.value.maxScale;

      if (maxScale >= minScale) {
        ElMessage.error("显示范围最小值不能大于最大值");
        return;
      }

      if (serverType == serviceType.ArcgisTileLayer && epsgVal == 4524) {
        ElMessageBox.alert("不能载入4524坐标系的切片图层");
        return;
      }
      checkLoad.value = true;
      try {
        let lyResult = await props.Onemap.AddLayer({
          layerid: forms.value.serverID,
          name: forms.value.serverName,
          useMapModel: forms.value.viewType == 'arcgis' ? ["arcgis", "cesium"] : ["cesium"],
          visible: true,
          serviceType: forms.value.serverType,
          url: serverUrl,
        });
        console.log('lyResult',lyResult);
        if(lyResult && lyResult.includes('rror' as any)){
          ElMessageBox.alert("图层加载验证失败，请检查参数是否正确");
        }
        checkLoad.value = false;
      } catch (error) {
        ElMessageBox.alert("图层加载验证失败，请检查设置是否正确");
        checkLoad.value = false;
      }
    }
  });
};

// type = 1为2D，2位3D，3为都支持
// const serverTypes = ref([
//     {
//         value: "tile",
//         label: "切片图层",
//         type: 3,
//     },
//     {
//         value: "feature",
//         label: "要素图层",
//         type: 3,
//     },
//     {
//         value: "image",
//         label: "影像图层",
//         type: 3,
//     },
//     {
//         value: "mapImage",
//         label: "地图图像图层",
//         type: 3,
//     },
//     {
//         value: "elevation",
//         label: "地形图层",
//         type: 2,
//     },
//     {
//         value: "scene",
//         label: "手工模型",
//         type: 2,
//     },
//     {
//         value: "imageryTileLayer",
//         label: "影像切片图层",
//         type: 3,
//     },
//     {
//         value: "vectorTileLayer",
//         label: "矢量切片图层",
//         type: 3,
//     },
//     {
//         value: "buildingSceneLayer",
//         label: "建筑模型",
//         type: 2,
//     },
//     {
//         value: "integratedMeshLayer",
//         label: "倾斜模型",
//         type: 2,
//     },
//     {
//         value: "pointCloudLayer",
//         label: "点云模型",
//         type: 2,
//     },
// ]);
const serverTypes = ref([
  {
    value: "ArcgisTileLayer",
    label: "切片图层",
    type: 3,
  },
  {
    value: "ArcgisMapImageLayer",
    label: "地图图像图层",
    type: 1,
  },
  {
    value: "Cesium3DTiles",
    label: "倾斜模型Cesium服务",
    type: 3,
  },
  {
    value: "Arcgis3DTiles",
    label: "倾斜模型Arcgis服务",
    type: 3,
  },
  {
    value: "Super3DTiles",
    label: "倾斜模型SuperMap服务",
    type: 3,
  }
]);

const viewTypes = ref([
  {
    value: "arcgis",
    label: "2D",
  },
  {
    value: "cesium",
    label: "3D",
  },
]);
//@ts-ignore
const viewChange = (type:any) => {
  console.log('type');
  
};

onMounted(() => {
  // viewChange(props.Onemap.MapType);
});
onUnmounted(() => {
  //domx?.destroy();
});
</script>
<style lang="scss" scoped>
// @import "@/styles/util-panel.scss";

.upload-layer-panel {
  padding: 10px;
}
.flex-row {
  display: flex;
  .uppload-layer-input-scale {
    width: 140px;
  }
}
.el-form-item {
  margin-bottom: 10px;
}
</style>
