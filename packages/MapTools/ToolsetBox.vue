<template>
  <el-container style="height: 100%">
    <el-main style="margin: 0px; padding: 0px">
      <el-scrollbar>
        <!-- 2.1 工具箱 Item -->
        <div
          class="general-utils-container"
          v-for="key in Array.from(CustomToolGroups.keys())"
          :key="key"
        >
          <div class="more-util-list__title" style="padding-bottom: 10px">
            {{ key }}
          </div>
          <el-space wrap>
            <ubadge
              v-for="tool in CustomToolGroups.get(key)"
              ParentDom="mapviewerContainer"
              :sender="tool.name"
              :hidden="isShowBadgeIcon"
              :value="
                getFixToolButton(tool)
                  ? Icons.IconHTML.badgeerro
                  : Icons.IconHTML.badgeright
              "
              :type="getFixToolButton(tool) ? 'success' : 'danger'"
              @BadgeClickEvent="_BadgeClickEvent(tool)"
            >
              <div
                class="more-util-list-item"
                @click.stop="ClickToolButotnItemEvent(tool)"
              >
                <i
                  :class="[
                      'more-util-list-item__icon',
                      (tool as any)?.isActive ? 'is-active' : '',
                    ]"
                  v-html="(tool as any)?.icon"
                ></i>
                <div
                  :class="[
                      'more-util-list-item__name',
                      (tool as any).isActive ? 'is-active' : '',
                    ]"
                >
                  {{ (tool as any).label }}
                </div>
              </div>
            </ubadge>
          </el-space>
        </div>
      </el-scrollbar>
    </el-main>
    <el-footer style="margin: 0px; padding: 0px; height: 30px">
      <!-- 2.2 自定义工具栏 按钮-->
      <div style="display: flex; text-align: right">
        <div
          style="
            width: 100%;
            padding-top: 8px;
            padding-left: 10px;
            display: flex;
            text-align: right;
            color: rgb(234, 71, 11);
          "
        >
          {{ ChangeTooltip }}
        </div>
        <div @click="onCustomUtilClick">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="将常用的功能按钮配置到快捷工具栏"
            placement="bottom"
          >
            <el-button
              text
              bg
              style="margin-right: 10px; width: 120px"
              type="info"
              >{{
                isShowBadgeIcon ? "关闭快捷工具配置" : "开启快捷工具配置"
              }}</el-button
            >
          </el-tooltip>
        </div>
      </div>
    </el-footer>
  </el-container>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import {
  type IToolItemType,
  getOnemap,
  getOption,
  Icons,
  ubadge,
  Popwindow,
} from "../onemapkit";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  CustomToolGroups: {
    type: Map<string, Array<IToolItemType>>,
    default: [],
  },
});
const _inOnemap = getOnemap(props.MapControlName);
const _inOptions = getOption(props.MapControlName);

/** 工具箱按钮图标 */
const isShowBadgeIcon = ref(false);
/** 改变工具箱时的提示信心 */
const ChangeTooltip = ref();

/** 判断是不是在Fix中 */
const getFixToolButton = (item: any): any => {
  return (FixCustomToolButton.value as any).some(
    (itm: any) => itm.name == item.name
  );
};
const FixCustomToolButton = computed(() => {
  return (_inOptions?.MapTools?.Toolset as any).filter(
    (item: any) =>
      (item?.useMapModel == undefined ||
        item?.useMapModel.some((itm: any) => itm == _inOnemap.MapType.value)) &&
      (item.isFix ||
        (props.PropStore?.ToolQuickItem as any).some(
          (itm: any) => itm == item.name
        ))
  );
});
/** 工具箱中 Item 右上角的配置按钮事件 */
const _BadgeClickEvent = (avg: any) => {
  ChangeTooltip.value = "";
  if (avg.isFix) {
    ChangeTooltip.value = avg.label + "-为默认工具.";
    return;
  }
  const idx = props.PropStore.ToolQuickItem.indexOf(avg.name);
  if (idx == -1) {
    if (props.PropStore.ToolQuickItem.length < 6) {
      props.PropStore.ToolQuickItem.push(avg.name);
      avg.isQuick = true;
    } else {
      ChangeTooltip.value = "最多放6个工具按钮.";
    }
  } else {
    props.PropStore.ToolQuickItem.splice(idx, 1);
    avg.isQuick = false;
  }
};


/** 更新工具按钮是否处于 */
const ClickToolButotnState = (customTool: IToolItemType, isState: Boolean) => {
  customTool.isActive = isState;
  if (customTool.ToolButtonClickEvent) {
    customTool.ToolButtonClickEvent({
      sender: customTool,
      param: {
        isActive: isState,
      },
    });
  }
};
const ClickToolButotnItemEvent = (customTool: IToolItemType) => {
  if (customTool.isActive) return;
  console.log('点击',bindProps(customTool.name, customTool));
  Popwindow({
    component: customTool.component as any,
    props: bindProps(customTool.name, customTool),
    // rootid:"mapviewerContainer",
    attrs: {
      title: customTool.label,
      width:customTool.defaultSize?.width,
      height:customTool.defaultSize?.height,
      // ShowMinimized: false,
      onClose: (avg: any) => {
        ClickToolButotnState(customTool, false);
        console.log("弹窗关闭回调函数,avg:", avg);
      },
    },
  });
  ClickToolButotnState(customTool, true);
  emit('openCallBack');
};

const emit = defineEmits(['openCallBack']);

function bindProps(toolName: String, tool: IToolItemType): any {
  console.log("当前按钮名称：", toolName);
  let componentProps = {};
  // if (tool.isPropStore) {
    //@ts-ignore
    componentProps["PropStore"] = props.PropStore;
  // }
  // if (tool.isLayerStore) {
    //@ts-ignore
    componentProps["LayerStore"] = props.LayerStore;
  // }
  if (tool.isOptions) {
    //@ts-ignore
    componentProps["Options"] = tool.OptionStore;
  }
  return componentProps;
} 

/** 自定义快捷工具箱设置 */
const onCustomUtilClick = () => {
  isShowBadgeIcon.value = !isShowBadgeIcon.value;
  ChangeTooltip.value = "";
};
</script>

<style lang="scss" scoped>
@import "./ToolsBox";
</style>
