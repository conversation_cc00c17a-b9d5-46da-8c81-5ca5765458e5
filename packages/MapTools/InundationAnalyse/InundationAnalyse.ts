import * as Cesium from "@onemapkit/cesium";
import { Icons } from "../../onemapkit";
import DrawTool from "./DrawTool";

// 获取贴底线Primitive
function getLinePrimitive(
  positions: Cesium.Cartesian3[],
  uniforms: any,
  width: number,
  materialType: string
): Cesium.GroundPolylinePrimitive {
  return new Cesium.GroundPolylinePrimitive({
    geometryInstances: new Cesium.GeometryInstance({
      geometry: new Cesium.GroundPolylineGeometry({
        positions,
        width,
      }),
    }),
    appearance: new Cesium.PolylineMaterialAppearance({
      material: Cesium.Material.fromType(materialType, uniforms),
    }),
    asynchronous: false,
    classificationType: Cesium.ClassificationType.BOTH,
  });
}

// 根据圆形的圆上一点确定圆的边界线点
function getCirclePoint(positions: Cesium.Cartesian3[]) {
  // 创建geometry
  const cartographic1 = Cesium.Cartographic.fromCartesian(positions[0]);
  const cartographic2 = Cesium.Cartographic.fromCartesian(positions[1]);
  const height = cartographic1.height;
  cartographic1.height = 0;
  cartographic2.height = 0;
  const cartesian1 = Cesium.Cartographic.toCartesian(cartographic1);
  const cartesian2 = Cesium.Cartographic.toCartesian(cartographic2);

  const radius = Cesium.Cartesian3.distance(cartesian1, cartesian2);

  const ellipseGeometry = new Cesium.EllipseOutlineGeometry({
    center: cartesian1,
    semiMajorAxis: radius,
    semiMinorAxis: radius,
    height,
  }) as any;

  const options = {
    center: ellipseGeometry._center,
    semiMajorAxis: ellipseGeometry._semiMajorAxis,
    semiMinorAxis: ellipseGeometry._semiMinorAxis,
    ellipsoid: ellipseGeometry._ellipsoid,
    rotation: ellipseGeometry._rotation,
    height: ellipseGeometry._height,
    granularity: ellipseGeometry._granularity,
    numberOfVerticalLines: ellipseGeometry._numberOfVerticalLines,
  };

  let cep = (Cesium as any).EllipseGeometryLibrary.computeEllipsePositions(
    options,
    false,
    true
  ).outerPositions;

  cep = (Cesium as any).EllipseGeometryLibrary.raisePositionsToHeight(
    cep,
    options,
    false
  );

  const outLinePositions = [];

  for (let i = 0; i < cep.length; i += 3) {
    outLinePositions.push(
      new Cesium.Cartesian3(cep[i], cep[i + 1], cep[i + 2])
    );
  }

  return outLinePositions;
}

const updateDataValue = new Cesium.Cartesian3();
const rectangleScratch = new Cesium.Rectangle();
const nwScratch = new Cesium.Cartographic();

function constructRectangle(geometry: any, computedOptions: any) {
  const ellipsoid = geometry._ellipsoid;
  const height = computedOptions.height;
  const width = computedOptions.width;
  const northCap = computedOptions.northCap;
  const southCap = computedOptions.southCap;

  let rowHeight = height;
  let widthMultiplier = 2;
  let size = 0;
  let corners = 4;
  if (northCap) {
    widthMultiplier -= 1;
    rowHeight -= 1;
    size += 1;
    corners -= 2;
  }
  if (southCap) {
    widthMultiplier -= 1;
    rowHeight -= 1;
    size += 1;
    corners -= 2;
  }
  size += widthMultiplier * width + 2 * rowHeight - corners;

  const positions = new Float64Array(size * 3);

  let posIndex = 0;
  let row = 0;
  let col;
  const position = updateDataValue;
  if (northCap) {
    (Cesium as any).RectangleGeometryLibrary.computePosition(
      computedOptions,
      ellipsoid,
      false,
      row,
      0,
      position
    );
    positions[posIndex++] = position.x;
    positions[posIndex++] = position.y;
    positions[posIndex++] = position.z;
  } else {
    for (col = 0; col < width; col++) {
      (Cesium as any).RectangleGeometryLibrary.computePosition(
        computedOptions,
        ellipsoid,
        false,
        row,
        col,
        position
      );
      positions[posIndex++] = position.x;
      positions[posIndex++] = position.y;
      positions[posIndex++] = position.z;
    }
  }

  col = width - 1;
  for (row = 1; row < height; row++) {
    (Cesium as any).RectangleGeometryLibrary.computePosition(
      computedOptions,
      ellipsoid,
      false,
      row,
      col,
      position
    );
    positions[posIndex++] = position.x;
    positions[posIndex++] = position.y;
    positions[posIndex++] = position.z;
  }

  row = height - 1;
  if (!southCap) {
    // if southCap is true, we dont need to add any more points because the south pole point was added by the iteration above
    for (col = width - 2; col >= 0; col--) {
      (Cesium as any).RectangleGeometryLibrary.computePosition(
        computedOptions,
        ellipsoid,
        false,
        row,
        col,
        position
      );
      positions[posIndex++] = position.x;
      positions[posIndex++] = position.y;
      positions[posIndex++] = position.z;
    }
  }

  col = 0;
  for (row = height - 2; row > 0; row--) {
    (Cesium as any).RectangleGeometryLibrary.computePosition(
      computedOptions,
      ellipsoid,
      false,
      row,
      col,
      position
    );
    positions[posIndex++] = position.x;
    positions[posIndex++] = position.y;
    positions[posIndex++] = position.z;
  }

  return positions;
}

function getRectanglePoint(positions: Cesium.Cartesian3[]) {
  // 创建geometry
  const cartographic1 = Cesium.Cartographic.fromCartesian(positions[0]);
  const height = cartographic1.height;

  const rectangleGeometry = new Cesium.RectangleOutlineGeometry({
    rectangle: Cesium.Rectangle.fromCartesianArray(positions),
    height: height,
  });

  const rectangle = (rectangleGeometry as any)._rectangle;
  const ellipsoid = (rectangleGeometry as any)._ellipsoid;
  const computedOptions = (
    Cesium as any
  ).RectangleGeometryLibrary.computeOptions(
    rectangle,
    (rectangleGeometry as any)._granularity,
    (rectangleGeometry as any)._rotation,
    0,
    rectangleScratch,
    nwScratch
  );

  let cep = constructRectangle(rectangleGeometry, computedOptions);
  cep = (Cesium as any).PolygonPipeline.scaleToGeodeticHeight(
    cep,
    height,
    ellipsoid,
    false
  );

  const outLinePositions = [];

  for (let i = 0; i < cep.length; i += 3) {
    outLinePositions.push(
      new Cesium.Cartesian3(cep[i], cep[i + 1], cep[i + 2])
    );
  }

  return outLinePositions;
}

function getLinePrimitiveByType(
  type: string,
  positions: Cesium.Cartesian3[],
  uniforms: any,
  width: number,
  materialType: string
) {
  let newPrimitive;

  switch (type) {
    case "polygon":
      newPrimitive = getLinePrimitive(
        [...positions, positions[0]],
        uniforms,
        width,
        materialType
      );
      break;
    case "circle":
      const circlePositions = getCirclePoint(positions);
      newPrimitive = getLinePrimitive(
        [...circlePositions, circlePositions[0]],
        uniforms,
        width,
        materialType
      );
      break;
    case "rectangle":
      const rectanglePoint = getRectanglePoint(positions);
      newPrimitive = getLinePrimitive(
        [...rectanglePoint, rectanglePoint[0]],
        uniforms,
        width,
        materialType
      );
      break;
  }

  return newPrimitive;
}

function getInundationPolygonInfor(
  type: string,
  positions: Cesium.Cartesian3[],
  height: number
) {
  let outlinePositions: Cesium.Cartesian3[] = [];
  switch (type) {
    case "polygon":
      outlinePositions = positions;
      break;
    case "circle":
      outlinePositions = getCirclePoint(positions);
      break;
    case "rectangle":
      outlinePositions = getRectanglePoint(positions);
      break;
  }

  const total = positions.length;
  let x = 0;
  let y = 0;
  let z = 0;
  const newPositions: Cesium.Cartesian3[] = [];
  outlinePositions.forEach((p: Cesium.Cartesian3) => {
    const cartographic = Cesium.Cartographic.fromCartesian(p);
    const lat = cartographic.latitude;
    const lon = cartographic.longitude;
    x += Math.cos(lat) * Math.cos(lon);
    y += Math.cos(lat) * Math.sin(lon);
    z += Math.sin(lat);
    newPositions.push(
      Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        height
      )
    );
  });
  x /= total;
  y /= total;
  z /= total;
  const centerLon = Math.atan2(y, x);
  const hyp = Math.sqrt(x * x + y * y);
  const centerLat = Math.atan2(z, hyp);

  return {
    newPositions,
    centerLon,
    centerLat,
  };
}

export default class InundationAnalyse {
  private _viewer: Cesium.Viewer;
  private _primitives: Cesium.PrimitiveCollection;
  private _waterPrimitives: Cesium.PrimitiveCollection;
  private _drawTool: DrawTool;
  private _removeEvent: Function[];
  private _timer: any;

  public waterRange: any[];
  public heightUpdateEvent: Cesium.Event;

  public height: number;
  public maxHeight: number;
  public speed: number;

  private _appearance: Cesium.MaterialAppearance;
  private _material: Cesium.Material;
  constructor(viewer: Cesium.Viewer) {
    this._viewer = viewer;
    this._primitives = new Cesium.PrimitiveCollection();
    this._waterPrimitives = new Cesium.PrimitiveCollection();
    viewer.scene.primitives.add(this._primitives);
    viewer.scene.primitives.add(this._waterPrimitives);
    this._drawTool = new DrawTool(viewer);
    this._removeEvent = [];

    this.waterRange = [];

    this.height = 70;
    this.speed = 1;
    this.maxHeight = 200;

    this.heightUpdateEvent = new Cesium.Event();

    this._material = new Cesium.Material({
      fabric: {
        type: "InundationAnalyseWater",
        uniforms: {
          normalMap: Icons.waterNormals,
          waterColor: Cesium.Color.fromCssColorString("#b4a285"),
        },
        source: `
          uniform sampler2D normalMap;

          czm_material czm_getMaterial(czm_materialInput materialInput){
            czm_material material = czm_getDefaultMaterial(materialInput);
            vec2 st = materialInput.st;

            float frequency = 8000.0;
            float animationSpeed = 0.06;
            float amplitude = 1.0;
            float specularIntensity = 0.5;
            float fadeFactor = 1.0;

            vec4 baseWaterColor = waterColor;

            float time = czm_frameNumber * animationSpeed;

            // fade is a function of the distance from the fragment and the frequency of the waves
            float fade = max(1.0, (length(materialInput.positionToEyeEC) / 10000000000.0) * frequency * fadeFactor);

            float specularMapValue = 1.0;

            // note: not using directional motion at this time, just set the angle to 0.0;
            vec4 noise = czm_getWaterNoise(normalMap, materialInput.st * frequency, time, 0.0);
            vec3 normalTangentSpace = noise.xyz * vec3(1.0, 1.0, (1.0 / amplitude));

            // fade out the normal perturbation as we move further from the water surface
            normalTangentSpace.xy /= fade;

            normalTangentSpace = normalize(normalTangentSpace);

            // get ratios for alignment of the new normal vector with a vector perpendicular to the tangent plane
            float tsPerturbationRatio = clamp(dot(normalTangentSpace, vec3(0.0, 0.0, 1.0)), 0.2, 1.0);

            tsPerturbationRatio = pow(tsPerturbationRatio, 2.0);

            // fade out water effect as specular map value decreases
            material.alpha = baseWaterColor.a;

            material.diffuse = baseWaterColor.rgb;

            // diffuse highlights are based on how perturbed the normal is
            material.diffuse *= tsPerturbationRatio;

            material.normal = normalize(materialInput.tangentToEyeMatrix * normalTangentSpace);

            material.specular = specularIntensity;
            material.shininess = 200.0;
            
            return material;
          }`,
      },
      translucent: false,
    });
    this._appearance = new Cesium.MaterialAppearance({
      material: this._material,
      fragmentShaderSource: `
        in vec3 v_positionEC;
        in vec3 v_normalEC;
        in vec3 v_tangentEC;
        in vec3 v_bitangentEC;
        in vec2 v_st;

        void main()
        {
            vec3 positionToEyeEC = -v_positionEC;
            mat3 tangentToEyeMatrix = czm_tangentToEyeSpaceMatrix(v_normalEC, v_tangentEC, v_bitangentEC);

            vec3 normalEC = normalize(v_normalEC);
        #ifdef FACE_FORWARD
            normalEC = faceforward(normalEC, vec3(0.0, 0.0, 1.0), -normalEC);
        #endif

            czm_materialInput materialInput;
            materialInput.normalEC = normalEC;
            materialInput.tangentToEyeMatrix = tangentToEyeMatrix;
            materialInput.positionToEyeEC = positionToEyeEC;
            materialInput.st = v_st;
            czm_material material = czm_getMaterial(materialInput);

        #ifdef FLAT
            out_FragColor = vec4(material.diffuse + material.emission, material.alpha);
        #else
            out_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC);
        #endif
        }

      `,
      vertexShaderSource: `
        in vec3 position3DHigh;
        in vec3 position3DLow;
        in vec3 normal;
        in vec3 tangent;
        in vec3 bitangent;
        in vec2 st;
        in float batchId;

        out vec3 v_positionEC;
        out vec3 v_normalEC;
        out vec3 v_tangentEC;
        out vec3 v_bitangentEC;
        out vec2 v_st;

        void main()
        {
            vec4 p = czm_computePosition();

            v_positionEC = (czm_modelViewRelativeToEye * p).xyz;      // position in eye coordinates
            v_normalEC = czm_normal * normal;                         // normal in eye coordinates
            v_tangentEC = czm_normal * tangent;                       // tangent in eye coordinates
            v_bitangentEC = czm_normal * bitangent;                   // bitangent in eye coordinates
            v_st = st;

            gl_Position = czm_modelViewProjectionRelativeToEye * p;
        }

      `,
      flat: false,
    });
  }

  /**
   * 水面的颜色
   */
  get waterColor() {
    return this._material.uniforms.waterColor;
  }
  set waterColor(waterColor) {
    this._material.uniforms.waterColor = waterColor;
  }

  // 一个私有的绘制方法
  _draw(type: string,mouseMoveEvent: Function, drawEndEvent: Function) {
    this._drawTool.stop();
    this._removeEvent.forEach((curFunction) => {
      curFunction();
    });
    this._removeEvent = [];

    // 移动的事件
    const removeMouseMoveEvent = this._drawTool.mouseMoveEvent.addEventListener(
      (infor) => {
        mouseMoveEvent(infor);
      }
    );

    // 绘制结束的事件
    const removeDrawEndEvent = this._drawTool.drawEndEvent.addEventListener(
      (infor) => {
        drawEndEvent(infor);
        this._removeEvent.forEach((curFunction) => {
          curFunction();
        });
        this._removeEvent = [];
      }
    );
    this._drawTool.start(type);
    this._removeEvent.push(
      removeMouseMoveEvent,
      removeDrawEndEvent
    );
  }

  _removePrimiriveByID(id: string, temporary: boolean) {
    // 鼠标移动的事件，这里用于显示鼠标移动时的示意图形
    const primitivesLength = this._primitives.length;

    for (let i = 0; i < primitivesLength; i++) {
      const primitive = this._primitives.get(i);
      if (primitive.id === id && primitive.temporary === temporary) {
        this._primitives.remove(primitive);
        break;
      }
    }
  }

  removeByID(id: string) {
    this._removePrimiriveByID(id, false);

    const primitivesLength2 = this._waterPrimitives.length;

    for (let i = 0; i < primitivesLength2; i++) {
      const primitive = this._waterPrimitives.get(i);
      if (primitive.id === id) {
        this._waterPrimitives.remove(primitive);
        break;
      }
    }

    const index = this.waterRange.findIndex((r) => r.id === id);

    if (index > -1) {
      this.waterRange.splice(index, 1);
    }
  }

  addGraphic(type: string, id: string, positions: Cesium.Cartesian3[]) {
    const newPrimitive = getLinePrimitiveByType(
      type,
      positions,
      { color: Cesium.Color.RED },
      2,
      Cesium.Material.ColorType
    );

    (newPrimitive as any).id = id;
    (newPrimitive as any).temporary = false;
    this._primitives.add(newPrimitive);
    this.waterRange.push({
      type,
      positions,
      id,
    });
  }

  drawGraphic(type: string, id: string, drawEnd: Function) {
    // 清除所有临时图形
    const primitivesLength = this._primitives.length;
    for (let i = 0; i < primitivesLength; i++) {
      const primitive = this._primitives.get(i);
      if (primitive.temporary) {
        this._primitives.remove(primitive);
      }
    }

    this._draw(
      type,
      (positions: Cesium.Cartesian3[]) => {
        // 鼠标移动的事件，这里用于显示鼠标移动时的示意图形
        this._removePrimiriveByID(id, true);

        const newPrimitive = getLinePrimitiveByType(
          type,
          positions,
          { color: Cesium.Color.WHITESMOKE, gapColor: Cesium.Color.RED },
          2,
          Cesium.Material.PolylineDashType
        );
        (newPrimitive as any).id = id;
        (newPrimitive as any).temporary = true;
        (newPrimitive as any).positions = positions;

        this._primitives.add(newPrimitive);
      },
      (positions: Cesium.Cartesian3[]) => {
        let curPosition = positions;
        if (type === "circle" || type === "rectangle") {
          const primitivesLength = this._primitives.length;
          for (let i = 0; i < primitivesLength; i++) {
            const primitive = this._primitives.get(i);
            if (primitive.id === id) {
              curPosition = (primitive as any).positions;
              break;
            }
          }
        }
        this._removePrimiriveByID(id, true);

        if ((type === "circle" || type === "rectangle") && curPosition.length < 2) {
          return;
        }

        this.addGraphic(type, id, curPosition);
        drawEnd(type, curPosition);
      }
    );
  }

  updateWaterHeight(newHeight: number) {
    const primitivesLength = this._waterPrimitives.length;

    for (let i = 0; i < primitivesLength; i++) {
      const primitive = this._waterPrimitives.get(i);
      const newRwpCa = Cesium.Cartesian3.fromRadians(
        (primitive as any)._originWorldPositionLon,
        (primitive as any)._originWorldPositionLat,
        newHeight
      );
      const move = Cesium.Cartesian3.subtract(
        newRwpCa,
        (primitive as any)._originWorldPosition,
        new Cesium.Cartesian3()
      );
      const moveMatrix4 = Cesium.Matrix4.fromTranslation(move);
      primitive.modelMatrix = moveMatrix4;
    }
  }

  updateWaterPolygon() {
    this._waterPrimitives.removeAll();
    this.waterRange.forEach((infor) => {
      const { type, positions, id } = infor;
      // 通过类型的对应的点信息，获取要绘制的水面的信息
      const inundationPolygonInfor = getInundationPolygonInfor(
        type,
        positions,
        this.height
      );

      const primitive = new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({
          geometry: Cesium.CoplanarPolygonGeometry.fromPositions({
            vertexFormat: new Cesium.VertexFormat({
              position: true,
              normal: true,
              st: true,
              tangent: true,
              bitangent: true,
            }),
            positions: inundationPolygonInfor.newPositions,
          }),
        }),
        appearance: this._appearance,
        asynchronous: false,
      });

      (primitive as any).id = id;

      (primitive as any)._originWorldPosition = Cesium.Cartesian3.fromRadians(
        inundationPolygonInfor.centerLon,
        inundationPolygonInfor.centerLat,
        this.height
      );

      (primitive as any)._originWorldPositionLon =
        inundationPolygonInfor.centerLon;
      (primitive as any)._originWorldPositionLat =
        inundationPolygonInfor.centerLat;

      this._waterPrimitives.add(primitive);
    });
  }

  restart() {
    if (Cesium.defined(this._timer)) {
      clearInterval(this._timer);
    }
    this.updateWaterPolygon();
  }

  stop() {
    if (Cesium.defined(this._timer)) {
      clearInterval(this._timer);
    }
    this._timer = undefined;
  }

  start() {
    this._timer = setInterval(() => {
      if (this.maxHeight > this.height) {
        this.height += this.speed / 100.0;
        this.updateWaterHeight(this.height);
        this.heightUpdateEvent.raiseEvent(this.height as any);
      }
    }, 10);
  }

  clearAllGraphic() {
    this.stop();
    this._drawTool.stop();
    this._waterPrimitives.removeAll();
    this._primitives.removeAll();
    this.waterRange = [];
  }

  locate(id: string) {
    const infor = this.waterRange.find((r) => r.id === id);
    if (infor) {
      if (infor.type === "circle") {
        // 如果是圆形，则根据圆心和圆上一点生成包围盒
        const center = infor.positions[0];
        const onCircle = infor.positions[1];
        const length = Cesium.Cartesian3.distance(center, onCircle);
        const boundingSphere = new Cesium.BoundingSphere(center, length);
        this._viewer.camera.flyToBoundingSphere(boundingSphere);
      } else {
        // 如果是多边形或者矩形，则根据点集合生成包围盒
        const boundingSphere = Cesium.BoundingSphere.fromPoints(
          infor.positions
        );
        this._viewer.camera.flyToBoundingSphere(boundingSphere);
      }
    }
  }

  destoy() {
    this.stop();
    this._drawTool.destroy();
    this._material.destroy();
    this._viewer.scene.primitives.remove(this._primitives);
    this._viewer.scene.primitives.remove(this._waterPrimitives);
    Cesium.destroyObject(this);
  }
}
