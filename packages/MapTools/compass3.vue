<template>
  <div class="compass-container" v-show="mapViewType === '3D' ? true : false">
    <span
      ref="compassRef"
      class="pointer"
      :class="mapViewType === '2D' ? 'is-disabled' : ''"
      title="恢复正北方向"
      @click="onReset"
    ></span>
    <span
      class="arrow-left"
      :class="mapViewType === '2D' ? 'is-disabled' : ''"
      title="逆时针转动45°"
      @click="onRotate(-1)"
    ></span>
    <span
      class="arrow-right"
      :class="mapViewType === '2D' ? 'is-disabled' : ''"
      title="顺时针转动45°"
      @click="onRotate(1)"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, onUnmounted,defineEmits,defineProps, } from "vue";
import {
  mapType,
  Icons,
  compass2,
  OnemapEvent,
  getOnemap,
  getOption,
  base,
} from "../onemapkit";
import * as Cesium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";
import arcgisScale from "../../doc/dev/MapLayerTreePop/LayerContent/LayerResPanel/arcgisScale";
import getExtent from "../utils/cesium-tools/czmGetExtent";
import getCenter from "../utils/cesium-tools/czmGetCenter";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});

const _Onemap = getOnemap(props.MapControlName);
const mapViewType = ref("");
// 罗盘实例
const compassRef = ref<HTMLElement | null>(null);
// 指针旋转角度
const angle = ref(0);

const compass3 = ref<compass2 | null>(null);
// watch(
//   () => _Onemap.MapType.value,
//   (newVal: string) => {
//     if (newVal) {
//       //console.log("change ", newVal);
//       if (newVal == "cesium") {
//         mapViewType.value = "3D";
//         if(compass3.value!=null){
//             let compassExists = compass3.value.isDestroyed();
//             console.log("是否已销毁 compass3",compassExists," compass3.value ",compass3.value)
//             if(compassExists){
//                 console.log("再建 compass3");
//                 compass3.value=new compass2(_Onemap.MapViewer,{zIndex: 999,bottom:640});
//                 console.log("再建 compass3  look ",compass3.value);
//                 _Onemap.MapViewer.scene.postRender.addEventListener(()=>{setGyrosAngle(_Onemap.MapViewer,compassRef.value);})
//                 _Onemap.MapViewer.beforeDestroyEvent.addEventListener(() => {
//                 if(compass3.value!=null){
//                    console.log("再建 compass3里的,摧毁 compass3")
//                    change3D.value=!change3D.value;
//                    compass3.value.destroy();
//                    _Onemap.MapViewer.scene.postRender.removeEventListener(_Onemap.MapViewer,compassRef.value);
//                 }
//         });
//             }
//             // console.log("再建2 compass3  look ");
//             // compass3.value=new compass2(_Onemap.MapViewer,{zIndex: 999,bottom:640});
//             //  _Onemap.MapViewer.scene.postRender.addEventListener(()=>{setGyrosAngle(_Onemap.MapViewer,compassRef.value);})
//         }

//       } else if (newVal == "arcgis") {
//         mapViewType.value = "2D";
//         console.log("compass3 change 2D");
//         angle.value = 0;

//       }
//     }
//   }
// );
watch(
  () => _Onemap.isMapReady.value,
  (val?: boolean) => {
    if (val) {
      if (_Onemap.MapType.value == "cesium") {
        mapViewType.value = "3D";
        console.log("初始化 3D");
        if(compass3.value==null){
          console.log("初始化 compass3");
        compass3.value=new compass2(_Onemap.MapViewer,{zIndex: 999,bottom:640});
        _Onemap.MapViewer.scene.postRender.addEventListener(()=>{setGyrosAngle(_Onemap.MapViewer,compassRef.value);})
        }else if(compass3.value!=null){
          let compassExists = compass3.value.isDestroyed();
            //console.log("是否已销毁 compass3",compassExists," compass3.value ",compass3.value)
            if(compassExists){
              //console.log("新的 compass3");
              compass3.value=new compass2(_Onemap.MapViewer,{zIndex: 999,bottom:640});
              _Onemap.MapViewer.scene.postRender.addEventListener(()=>{setGyrosAngle(_Onemap.MapViewer,compassRef.value);})
            }
        }

        _Onemap.MapViewer.beforeDestroyEvent.addEventListener(() => {
           if(compass3.value!=null){
               // 地图对象销毁时销毁组件内创建的工具
            //   let compassExists = compass3.value.isDestroyed();
            //   if(compassExists){
            //     console.log("摧毁 compass3")
            //      compass3.value.destroy();
            //     _Onemap.MapViewer.scene.postRender.removeEventListener(_Onemap.MapViewer,compassRef.value);

            //  }
           // console.log("摧毁 compass3")
            compass3.value.destroy();
            _Onemap.MapViewer.scene.postRender.removeEventListener(_Onemap.MapViewer,compassRef.value);
            }
        });
      }

    }else{
      mapViewType.value = "2D";
      console.log("初始化 2D");
      angle.value = 0;
    }
  }
);



// 指针复位
const onReset = () => {
  if (mapViewType.value == "") {
    console.log("3 罗盘初始化");
    if (_Onemap.MapType.value == "cesium") {
      mapViewType.value = "3D";
    } else {
      mapViewType.value = "2D";
      return false;
    }
  }
  if (mapViewType.value === "2D") {
    return false;
  }
  //let curHeading=Cesium.Math.toDegrees(_Onemap.MapViewer.camera.heading);
  angle.value = Math.abs(angle.value) > 180 ? 360 : 0;
//   console.log("指北 ", angle.value);
if(compass3.value!=null){
  compass3.value.setNorth();
  
}

};

/**
 * 旋转
 *
 * @param {*} direction 旋转方向 1 顺时针 -1 逆时针
 */
const onRotate = (direction: number) => {
  if (mapViewType.value == "") {
    //console.log("3 罗盘初始化2");
    if (_Onemap.MapType.value == "cesium") {
      mapViewType.value = "3D";
    } else {
      mapViewType.value = "2D";
      return false;
    }
  }
  if (mapViewType.value === "2D") {
    return false;
  }
//   angle.value = direction > 0 ? angle.value -45 : angle.value + 45;
  
  if(direction>0){
    // console.log("1 顺时针");
    if(compass3.value!=null){
    compass3.value.rotateRight();
    }
  }
  if(direction<0){
    // console.log("-1 逆时针 ");
    if(compass3.value!=null){
    compass3.value.rotateLeft();
    }
  }
 


};


function setGyrosAngle(viewer: Cesium.Viewer, gyro: any) {
  if (viewer.scene.mode === Cesium.SceneMode.SCENE3D) {
    gyro.style.transform =
      "scale(0.8) rotate3d(1,0,0," +
      (Cesium.Math.toDegrees(viewer.camera.pitch) + 90) +
      "deg) " +
      "rotate3d(0,1,0," +
      Cesium.Math.toDegrees(viewer.camera.roll) +
      "deg) " +
      "rotate3d(0,0,1," +
      -Cesium.Math.toDegrees(viewer.camera.heading) +
      "deg)";

  } 
}

onMounted(() => {
 
  
//   compass3.value=new compass2(_Onemap.MapViewer,{zIndex: 999,});

});


onUnmounted(() => {
  if(_Onemap.MapViewer?._cesiumWidget){
    if(compass3.value!=null){
    let compassExists = compass3.value.isDestroyed();
    if(compassExists){
        compass3.value.destroy();
        _Onemap.MapViewer.scene.postRender.removeEventListener(_Onemap.MapViewer,compassRef.value);

    }
    }
  }
  
});
</script>
<style lang="scss" scoped>

.compass-container {
  background: url("/assets/images/compass-bg.png") no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
  width: 52px;
  height: 52px;
  border-radius: 24px;
  overflow: hidden;
  position: relative;

  .compass {
    width: 100%;
    display: block;
  }

  .pointer,
  .arrow-left,
  .arrow-right {
    &.is-disabled {
      cursor: not-allowed;
    }
  }

  .pointer {
    position: absolute;
    z-index: 5;
    top: 10px;
    color: #717171;
    width: 12px;
    height: 34px;
    left: 19px;
    background: url("/assets/images/pointer.png") no-repeat;
    background-size: 100% 100%;
  }

  .arrow {
    &-left,
    &-right {
      position: absolute;
      z-index: 5;
      top: 13px;
      font-size: 24px;
      color: #717171;
      cursor: pointer;
      width: 8px;
      height: 28px;
      image-rendering: -moz-smooth; /* Firefox */
      image-rendering: -o-smooth; /* Opera */
      image-rendering: -webkit-optimize-contrast; /*Webkit (non-standard naming) */
      image-rendering: smooth;
      // -ms-interpolation-mode: nearest-neighbor; /* IE (non-standard property) */
    }

    &-left {
      left: 3.5px;
      background: url("/assets/images/arrow-left.png") no-repeat;
      background-size: 100% 100%;

      &.is-disabled {
        &:hover {
          background: url("/assets/images/arrow-left.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      &:hover {
        background: url("/assets/images/arrow-left-active.png") no-repeat;
        background-size: 100% 100%;
      }
    }

    &-right {
      right: 3px;
      background: url("/assets/images/arrow-right.png") no-repeat;
      background-size: 100% 100%;

      &.is-disabled {
        &:hover {
          background: url("/assets/images/arrow-right.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      &:hover {
        background: url("/assets/images/arrow-right-active.png") no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>