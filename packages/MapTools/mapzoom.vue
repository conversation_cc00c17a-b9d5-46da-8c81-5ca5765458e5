<template>
  <div class="  map-switch map-zoom map-righ-button">
    <Icons.zoomAddIcon title="放大" class="zoom-icon" @click="zoomIn" />
    <Icons.zoomSubIcon title="缩放" class="zoom-icon" @click="zoomOut" />
  </div>
</template>

<script lang="ts" setup>
import { getOnemap, Icons } from "../onemapkit";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
});

const _inOnemap = getOnemap(props.MapControlName);

const zoomIn = () => {
  _inOnemap.zoomIn();
};

const zoomOut = () => {
  _inOnemap.zoomOut();
};
</script>

<style lang="scss" scoped> 
  .map-switch {
    position: absolute;
    margin-top: 8px;
    width: 45px;
    margin-left: 8px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
    text-align: center;
    cursor: pointer;
    .map-zoom {
        position: absolute;
        bottom: 0px;
        right: 0px;
        font-size: 12px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1px;
        cursor: pointer; 
      }
      .zoom-icon {
        margin: 4px 0;
        width: 28px;
        height: 24px;
      } 
  }
  
</style>
