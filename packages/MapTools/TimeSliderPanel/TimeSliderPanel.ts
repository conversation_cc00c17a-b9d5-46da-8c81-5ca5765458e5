
import {
    ref,
  } from "vue"; 

const fullExtent = ref({
    xmin: 105.71685125,
    ymin: 21.802522646484373,
    xmax: 110.99028875,
    ymax: 24.354097353515623,
    heading: 0,
    pitch: -90,
    roll: 0,
    wkid: 4490,
} as any)


export async function getesriObj (_esriObj: any,list: any, appConfigData:any ) {
  // const [_SpatialReference, _Map, _Extent, _ArcGISTiledMapServiceLayer] = await loadModules([
  //   "esri/SpatialReference",
  //   "esri/map",
  //   "esri/geometry/Extent",
  //   "esri/layers/ArcGISTiledMapServiceLayer",
  // ]);
    let arcGisMap = new _esriObj.Map("Mapviewer", {
        autoResize: true,
        fitExtent: true,
        logo: false,
        slider: false,
        navigationMode: "css-transforms",
        spatialReference: new _esriObj.SpatialReference({ wkid: 4490 }),
      });


  
      list.forEach((item: any)=>{
        if(item.order == 1) {
          let basemapLayer = new _esriObj.ArcGISTiledMapServiceLayer(`${appConfigData?.BaseUrl}${item.url}`, {
            id: item.id,
            visible: true
          })
          arcGisMap.addLayer(basemapLayer);
        } else {
          let basemapLayer = new _esriObj.ArcGISTiledMapServiceLayer(`${appConfigData?.BaseUrl}${item.url}`, {
            id: item.id,
            visible: false
          })
          arcGisMap.addLayer(basemapLayer);
        }
      })
      let ex = new _esriObj.Extent({
        xmin: fullExtent.value.xmin,
        ymin: fullExtent.value.ymin,
        xmax: fullExtent.value.xmax,
        ymax: fullExtent.value.ymax,
        spatialReference: { wkid: fullExtent.value.wkid },
      });
      arcGisMap.setExtent(ex);
      arcGisMap.setZoom(8)
     return arcGisMap

}