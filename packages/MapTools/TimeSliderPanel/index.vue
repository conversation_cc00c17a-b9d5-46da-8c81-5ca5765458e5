<template>
  <div class="content">
    <div class="container" id="parentContainer">
    <div id="Mapviewer" class="map-container"></div> 
      <div class="timeline">
        <el-slider
          v-model="currentYear"
          :min="minYear"
          :max="maxYear"
          :marks="marks"
        />
        <div class="control-bottom">
          <el-button type="primary" class="play-btn" @click="lastPlay">
            <el-icon><el-icon-arrowLeft /></el-icon>
          </el-button>
          <el-button type="primary" class="play-btn-cen" @click="changePlay">
            <el-icon v-if="!isPlaying"><el-icon-videoPlay /></el-icon>
            <el-icon v-else><el-icon-videoPause /></el-icon>
          </el-button>
          <el-button type="primary" class="play-btn" @click="nextPlay">
            <el-icon><el-icon-arrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  onMounted,
  ref,
  defineProps,
  watch, 
} from "vue";
import layersList from "./layers.json";

import {getesriObj} from "./TimeSliderPanel";
import {
  getOnemap, 
} from "../../../packages/onemapkit";




const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: false,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});

const _inOnemap = getOnemap(props.MapControlName);



let historyImagesData = ref([] as any[]);
let currentYear = ref(0 as number);
const minYear = ref(0 as number);
const maxYear = ref(0 as number);
const marks = ref({
  // 2011: '2011年',
});
const isPlaying = ref(false);
const interval = ref(null as any);
const currentIndex = ref(0 as number);

const currentItem = ref({} as any);
const mapView = ref(null as any)

watch(
  () => currentIndex.value,
  () => {
    currentItem.value = historyImagesData.value[currentIndex.value];
    currentYear.value = currentItem.value.year;
    // _propsExt.onChangeBaseMap(currentItem.value);
    if (currentIndex.value == 0) {
      isPlaying.value = false;
      clearInterval(interval.value);
    }
    historyImagesData.value.forEach((item,index)=>{
      if(item.name ==  currentItem.value.name) {
        let cerrentLayer = mapView.value.getLayer(`layer${index}`)
          cerrentLayer.setVisibility(true)
      } else {
        let otherLayer = mapView.value.getLayer(`layer${index}`)
        // console.log(otherLayer,`layer${index}`)
        otherLayer.hide()
      }
    })
    // console.log(mapView.value,'-----currentItem.value')
  }
);


onMounted(async () => {
  //@ts-ignore
  mapView.value = await getesriObj(_inOnemap?._esriObj,layersList, props.PropStore.appConfigData)
  let res = layersList;
  historyImagesData.value = res
    .filter((item: any) => item.year)
    .sort((a: any, b: any) => a.year - b.year);
  minYear.value = historyImagesData.value[0].year;
  maxYear.value =
    historyImagesData.value[historyImagesData.value.length - 1].year;
  currentYear.value = historyImagesData.value[0].year;
  currentItem.value = historyImagesData.value[currentIndex.value];
  let arr = historyImagesData.value;
  let obj: { [key: string]: string } = {};
  for (let i = 0; i < arr.length; i++) {
    obj[arr[i].year] = arr[i].year + "年";
  }
  marks.value = obj;
});

const lastPlay = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  } else {
    currentIndex.value = historyImagesData.value.length - 1;
  }
};

const nextPlay = () => {
  if (currentIndex.value < historyImagesData.value.length - 1) {
    currentIndex.value++;
  } else {
    currentIndex.value = 0;
  }
};

const changePlay = () => {
  isPlaying.value = !isPlaying.value;
  if (isPlaying.value) {
    interval.value = setInterval(() => {
      nextPlay();
    }, 3000);
  } else {
    clearInterval(interval.value);
  }
};

</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
}
.container {
  width: 100%;
  height: 600px;
  position: relative;
}
.timeline {
  width: 600px;
  display: flex;
  flex-direction: column;
  padding: 30px 50px;
  border: 1px solid #ccc;
  position: absolute;
  left: 20px;
  bottom: 20px;
  background-color: #fff;
  // margin-left: 10%;
}
.control-bottom {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 35px;
}
:deep(.el-icon) {
  font-size: 20px;
  color: #fff;
}
.play-btn-cen {
  margin-left: 12px;
}
#onemaptopdiv {
  & .common-tool-panel {
    display: none;
  }
}

#Mapviewer {
  // width: 1482px;
  // height: 600px;
  width: 100%;
  height: 100%;
}

</style>
