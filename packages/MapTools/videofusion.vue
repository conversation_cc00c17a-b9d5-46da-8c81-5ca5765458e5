<template>
  <div class="from-item-dom">
    <el-input size="small" style="width: 200px" placeholder="输入关键字查询" />
    <el-button size="small" style="margin: 1px 1px 1px 5px" type="success" plain
      >导入</el-button
    >
    <el-button size="small" style="margin: 1px 1px 1px 5px" type="warning" plain
      >导出</el-button
    >
  </div>
  <el-table :data="tableData" height="250" style="width: 100%">
    <el-table-column type="expand">
      <template #default="scope">
        <div style="text-align: right">
          <div style="margin: 0px 20px">
            <el-button :type="_type" @click="EditOkVideo(scope.row)">{{
              _editok
            }}</el-button>
          </div>
          <SliderInput
            label="&nbsp;&nbsp;&nbsp;远平面"
            style="margin: 5px 0px"
            :input-width="120"
            :size="'small'"
            :min="0"
            :max="10"
            :controls="false"
            :placeholder="'0'"
          ></SliderInput>
          <SliderInput
            label="&nbsp;&nbsp;&nbsp;近平面"
            style="margin: 5px 0px"
            :input-width="120"
            :size="'small'"
            :min="0"
            :max="10"
            :controls="false"
            :placeholder="'0'"
          ></SliderInput>
          <SliderInput
            label="&nbsp;&nbsp;&nbsp;视场角"
            style="margin: 5px 0px"
            :input-width="120"
            :size="'small'"
            :min="0"
            :max="10"
            :controls="false"
            :placeholder="'0'"
          ></SliderInput>
          <SliderInput
            label="视频高度"
            style="margin: 5px 0px"
            :input-width="120"
            :size="'small'"
            :min="0"
            :max="10"
            :controls="false"
            :placeholder="'0'"
          ></SliderInput>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="名称" class-name="left-align">
    </el-table-column>
    <el-table-column prop="operations" label="操作" :width="170" align="center">
      <template #default="scope">
        <el-button
          size="small"
          text
          type="primary"
          style="margin: 2px 6px; padding: 0px"
          @click="OpenVideo(scope.row)"
          >开启</el-button
        >
        <el-button
          size="small"
          text
          type="primary"
          style="margin: 2px 6px; padding: 0px"
          @click="CloseVideo(scope.row)"
          >关闭</el-button
        >

        <el-button
          size="small"
          text
          type="primary"
          style="margin: 2px 6px; padding: 0px"
          @click="DeleteVideo(scope.row)"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getOnemap, OnemapClass, SliderInput } from "../onemapkit";

import * as CesiumTools from "@onemapkit/cesium-tools";
// import * as Cesium from "@onemapkit/cesium";
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: false,
  },
  tableData: {
    type: Object,
    default: [
      {
        id: "111",
        name: "name",
        data: {
          url: "",
          position: [108.36302329916886, 22.85606469127191, 200],
          vidioCenter: [108.35872999625839, 22.858585345883608, 80],
          closePlane: 1,
          farPlane: 1000,
          fov: 45,
          videoWidth: 1.5,
        },
      },
    ],
    require: true,
  },
});
const VidioArray = new Map<string, any>();
const _editok = ref("视频投影位置调整");
const _type = ref("primary");
//1.初始化 Onemap、Options
const _Onemap: OnemapClass = getOnemap(props.MapControlName);

function OpenVideo(data: any) {
  VidioArray.set(
    data.id,
    new CesiumTools.VideoShadow(_Onemap.MapViewer, {
      position: data.data.position,
      viewCenter: data.data.vidioCenter,
      url: data.data.url,
    })
  );
}

function CloseVideo(data: any) {
  VidioArray.set(data.id, null);
  VidioArray.delete(data.id);
}
//@ts-ignore
function EditOkVideo(data: any) {
  _editok.value =
    _editok.value == "视频投影位置调整"
      ? "确定当前调整位置"
      : "视频投影位置调整";
  _type.value = _editok.value == "确定当前调整位置" ? "success" : "primary"; 
}
function DeleteVideo(data: any) {
  if (VidioArray.has(data.id)) {
    VidioArray.set(data.id, null);
    VidioArray.delete(data.id);
  }
  let idx = props.tableData.indexOf((itm: any) => itm.id == data.id);
  props.tableData.splice(idx, 1);
}

// const _tableData = computed(() => {});
// const IniData = () => {
//   tableData.forEach((obj: any) => {});
// };
</script>
<style scoped>
.from-item-dom {
  width: 100%;
  align-items: center;
  text-align: right;
}
.left-align {
  align: "center";
  .cell {
    text-align: left;
  }
}
</style>
