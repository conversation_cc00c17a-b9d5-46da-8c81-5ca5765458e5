<template>
  <div class="compass-container">
    <span
      ref="compassRef"
      class="pointer"
      :class="mapViewType === '2D' ? 'is-disabled' : ''"
      title="恢复正北方向"
      @click="onReset"
    ></span>
    <span
      class="arrow-left"
      :class="mapViewType === '2D' ? 'is-disabled' : ''"
      title="逆时针转动45°"
      @click="onRotate(-1)"
    ></span>
    <span
      class="arrow-right"
      :class="mapViewType === '2D' ? 'is-disabled' : ''"
      title="顺时针转动45°"
      @click="onRotate(1)"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, onUnmounted,defineEmits } from "vue";
import {
  mapType,
  Icons,
  OnemapEvent,
  getOnemap,
  getOption,
  base,
} from "../onemapkit";
import * as Cesium from "@onemapkit/cesium";
import arcgisScale from "../../doc/dev/MapLayerTreePop/LayerContent/LayerResPanel/arcgisScale";
import getExtent from "../utils/cesium-tools/czmGetExtent";
import getCenter from "../utils/cesium-tools/czmGetCenter";
const cameraWatcher = ref<ReturnType<typeof watch> | null>(null);
const emit=defineEmits(['headingChanged']);

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});

const initLocation = {
  xmax: 109.64409,
  ymax: 24.06955,
  xmin: 107.28664,
  ymin: 22.19061,
  heading: 0,
  pitch: -90,
  roll: 0,
  wkid: 4490,
  toolbar: "定位到南宁",
};
//1.初始化 Onemap、Options
const _Onemap = getOnemap(props.MapControlName);
const mapViewType = ref("");

// 罗盘实例
const compassRef = ref<HTMLElement | null>(null);
// 指针旋转角度
const angle = ref(0);
const mapPoint: any = computed(
  () => props.PropStore.bottomInfoData.value.coordinates
);

let cameraChangeEvent: any = undefined;
watch(
  () => _Onemap.isMapReady.value,
  (val?: boolean) => {
    if (val) {
      if (_Onemap.MapType.value == "cesium") {
        cameraChangeEvent = _Onemap.MapViewer.camera.changed.addEventListener(
          () => {
            // 更新罗盘的指针方向
            let curHeading = Cesium.Math.toDegrees(
              _Onemap.MapViewer.camera.heading
            );
            angle.value = curHeading;
            setCompassStyles(angle.value, "0s");
          }
        );
		//    mouseMoveEvent = Onemap.setMapEventHandler(base.MouseEventType.MOUSE_MOVE, {
		// 	handlerName: "mouseMoveEvent",
		// 	isThrottle: true,
		// 	delayTime: 5,
		// 	handler: (avg: any) => {
		// 		if (Onemap.MapType.value == mapType.cesium) {
		// 			const realtimePoint = Onemap.getMapRealtimePoint(avg.endPosition);
		// 			props.PropStore.bottomInfoData.value.coordinates = realtimePoint;
		// 		} 
		// 	}
		// })
      }
    }
  }
);

watch(
  () => angle.value,
  (newVal: number) => {
    if (newVal) {
      if (_Onemap.MapType.value == "cesium") {
        if(newVal==360){
          newVal=0;
        }
        if(newVal>360){
          newVal=newVal-360;
        }
        //console.log(" angle change ",newVal)
        //props.PropStore.bottomInfoData.value.coordinates.heading=newVal;
      }
    }
  }
);

onMounted(() => {
  // if (props.PropStore && props.PropStore.bottomInfoData) {
  // 	bestResolution.value = props.PropStore.bottomInfoData.value.bestRatio ? props.PropStore.bottomInfoData.value.bestRatio : 1000
  // }

});

onUnmounted(() => {
  if (cameraChangeEvent) {
    // 销毁底栏时，销毁监听的事件
    cameraChangeEvent();
  }
  
});
watch(
  () => _Onemap.MapType.value,
  (newVal: string) => {
    if (newVal) {
      //console.log("change ", newVal);
      if (newVal == "cesium") {
        mapViewType.value = "3D";
      } else if (newVal == "arcgis") {
        mapViewType.value = "2D";
        //console.log("change");
        angle.value = 0;
        setCompassStyles(angle.value, "0s");
      }
    }
  }
);

// 指针复位
const onReset = () => {
  if (mapViewType.value == "") {
    console.log("罗盘初始化");
    if (_Onemap.MapType.value == "cesium") {
      mapViewType.value = "3D";
    } else {
      mapViewType.value = "2D";
      return false;
    }
  }
  if (mapViewType.value === "2D") {
    return false;
  }
  //let curHeading=Cesium.Math.toDegrees(_Onemap.MapViewer.camera.heading);
  angle.value = Math.abs(angle.value) > 180 ? 360 : 0;
  //console.log("指北 ", angle.value);

  _Onemap.MapViewer.camera.setView({
    orientation: {
      heading: Cesium.Math.toRadians(angle.value), // heading
      pitch: _Onemap.MapViewer.camera.pitch, // pitch
      roll: 0, // roll
    },
  });
  setCompassStyles(angle.value);
};

//  const setView=(viewData:any,duration=1.5)=>{
//   _Onemap.MapViewer.scene.camera.flyTo({
//       destination: new Cesium.Cartesian3(viewData.x,viewData.y,viewData.z),
//       orientation: {
//         heading: Cesium.Math.toRadians(viewData.heading), // heading
//         pitch:viewData.pitch, // pitch
//         roll: Cesium.Math.toRadians(viewData.roll), // roll
//       },
//     });
//  }

/**
 * 旋转
 *
 * @param {*} direction 旋转方向 1 顺时针 -1 逆时针
 */
const onRotate = (direction: number) => {
  if (mapViewType.value == "") {
    console.log("罗盘初始化2");
    if (_Onemap.MapType.value == "cesium") {
      mapViewType.value = "3D";
    } else {
      mapViewType.value = "2D";
      return false;
    }
  }
  if (mapViewType.value === "2D") {
    return false;
  }
  angle.value = direction > 0 ? angle.value -45 : angle.value + 45;
  //console.log("旋转后 ", angle.value);
  _Onemap.MapViewer.camera.setView({
    orientation: {
      heading: Cesium.Math.toRadians(angle.value), // heading
      pitch: _Onemap.MapViewer.camera.pitch, // pitch
      roll: 0, // roll
    },
  });
  setCompassStyles(angle.value);
  emit('headingChanged',angle.value)
};
/**
 * 设置指针样式
 * @param {*} angle 旋转角度
 * @param {*} duration 动画持续时间
 */
const setCompassStyles = (angle: number, duration = "0.2s") => {
  const transform = `rotateZ(${-angle}deg)`;
  if(compassRef.value){
  const compass = compassRef.value;
  compass.style.transitionDuration=duration;
  compass.style.transform=transform;
  // compass.style.webkitTransform= transform;
  // compass.style.mozTransform= transform;
  // compass.style["transition-duration"] = duration;
  // compass.style["transform"] = transform;

  // compass.style["-webkit-transform"] = transform;
  // compass.style["-moz-transform"] = transform;
  }

};
</script>
<style lang="scss" scoped>
.compass-container {
  background: url("/assets/images/compass-bg.png") no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
  width: 52px;
  height: 52px;
  border-radius: 24px;
  overflow: hidden;
  position: relative;

  .compass {
    width: 100%;
    display: block;
  }

  .pointer,
  .arrow-left,
  .arrow-right {
    &.is-disabled {
      cursor: not-allowed;
    }
  }

  .pointer {
    position: absolute;
    z-index: 5;
    top: 10px;
    color: #717171;
    width: 12px;
    height: 34px;
    left: 20px;
    background: url("/assets/images/pointer.png") no-repeat;
    background-size: 100% 100%;
  }

  .arrow {
    &-left,
    &-right {
      position: absolute;
      z-index: 5;
      top: 13px;
      font-size: 24px;
      color: #717171;
      cursor: pointer;
      width: 8px;
      height: 28px;
      image-rendering: -moz-smooth; /* Firefox */
      image-rendering: -o-smooth; /* Opera */
      image-rendering: -webkit-optimize-contrast; /*Webkit (non-standard naming) */
      image-rendering: smooth;
      // -ms-interpolation-mode: nearest-neighbor; /* IE (non-standard property) */
    }

    &-left {
      left: 3.5px;
      background: url("/assets/images/arrow-left.png") no-repeat;
      background-size: 100% 100%;

      &.is-disabled {
        &:hover {
          background: url("/assets/images/arrow-left.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      &:hover {
        background: url("/assets/images/arrow-left-active.png") no-repeat;
        background-size: 100% 100%;
      }
    }

    &-right {
      right: 3px;
      background: url("/assets/images/arrow-right.png") no-repeat;
      background-size: 100% 100%;

      &.is-disabled {
        &:hover {
          background: url("/assets/images/arrow-right.png") no-repeat;
          background-size: 100% 100%;
        }
      }

      &:hover {
        background: url("/assets/images/arrow-right-active.png") no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
