<template>
  <pop-panel
    :title="'批量分析'"
    :width="460"
    :height="420"
    :top="20"
    :right="75"
  >
    <template #content>
      <analysis-panel
        :MapControlName="props.MapControlName"
        :PropStore="props.PropStore"
        :LayerStore="props.LayerStore"
      />
    </template>
  </pop-panel>
</template>
<script lang="ts" setup>
import { defineProps } from "vue";
import PopPanel from "../../PopPanel/PopPanel.vue";
import AnalysisPanel from "./AnalysisPanel.vue";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});
</script>
<style lang="scss" scoped></style>
