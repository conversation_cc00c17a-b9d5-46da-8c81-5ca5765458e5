<template>
    <div :class="['toolbox']">
        <div class="toolbox-wrapper">
          <div class="tool-list">
            <div class="tool-list-wrapper"> 
            </div>
          </div>
        </div>
      </div>
</template>

<script setup lang="ts">
// import { ref, onMounted, watch, computed } from "vue";
// import { ElMessageBox } from "element-plus";
import { 
  getOnemap,
  getOption, 
} from "../onemapkit";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  Options: {
    type: Object as any,
    default: null,
  },
});
//@ts-ignore
const _inOnemap = getOnemap(props.MapControlName);
//@ts-ignore
const _inOptions = getOption(props.MapControlName);



</script>