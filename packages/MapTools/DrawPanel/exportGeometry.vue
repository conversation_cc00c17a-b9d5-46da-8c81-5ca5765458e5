<template>
	<el-dialog title="坐标导出" width="350px" :modelValue="visible" destroy-on-close @close="onClose()">
		<el-form :model="exportFormModel" :rules="exportCheckRules">
			<el-form-item label="格式" label-width="80px" prop="format" :required="true">
				<el-select size="small" v-model="exportFormModel.format" placeholder="请选择格式">
					<el-option v-for="item in exportFormat" :key="item.value" :label="item.label" :value="item.value"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="坐标类型" label-width="80px" prop="coord" :required="true">
				<el-select size="small" v-model="exportFormModel.coord" placeholder="请选择坐标系">
					<el-option v-for="item in coords" :key="item.value" :label="item.label" :value="item.value"></el-option>
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onClose()" size="small">取 消</el-button>
				<el-button type="primary" v-loading="exportLoading" @click="exportCroodData()" size="small">导出</el-button>
			</span>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { defineProps, defineEmits, ref } from "vue";

const emits = defineEmits(["close"]);
const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	graphics: {
		type: Array,
		default: [],
	},
	exportType: {
		type: String,
		default: "1",
	}
});
const exportFormModel = ref({ format: "geojson", coord: 4490 });
const exportFormat = ref([
	{
		value: "txt",
		label: "txt单图形坐标文件",
		show: "one",
	},
	{
		value: "txtMultiple",
		label: "信息集团坐标文本（*.txt）支持多个地块",
		show: "all",
	},
	{
		label: "geojson",
		value: "geojson",
	},
	{
		label: "arcgisJson",
		value: "arcgis_json",
	},
]);

const exportCheckRules = {
	format: [{ required: true, message: "请输入导出格式", trigger: "blur" }],
	coord: [{ required: true, message: "请选择坐标系", trigger: "blur" }],
};
const coords = ref([
	{
		label: "2000地理坐标系（4490）",
		value: 4490,
	},
	{
		label: "2000平面坐标系_有带号（4524）",
		value: 4524,
	},
	{
		label: "2000平面坐标系_有带号（4525）",
		value: 4525,
	},
	{
		label: "2000平面坐标系_无带号（4545）",
		value: 4545,
	},
	{
		label: "WGS84地理坐标系（4326）",
		value: 4326,
	},
	{
		label: "墨卡托投影（3857）",
		value: 3857,
	},
	{
		label: "西安80坐标系（2360）",
		value: 2360,
	},
	{
		label: "北京54坐标系（2412）",
		value: 2412,
	},
]);

const exportLoading = ref(false);

const onClose = () => {
	emits("close");
};

/**
 * 导出props.graphics数据，props.graphics数组中的每个元素都是一个图形，遍历数组，每个图形导出一个文件，文件名为图形的名称
 */
const exportCroodData = () => {
	exportLoading.value = true;
	const { format } = exportFormModel.value;
	if (format === "txt") {
		downloadTxt();
	} else if (format === "txtMultiple") {
		downloadTxt();
	} else if (format === "geojson") {
		downloadGeojson();
	} else if (format === "arcgis_json") {
		downloadArcgisjson();
	}
	exportLoading.value = false;
	onClose();
};

/**
 * 导出text坐标数据
 */
const downloadTxt = async () => {
	for (let i = 0; i < props.graphics.length; i++) {
		const graphic: any = props.graphics[i];
		const { geometry, attributes } = graphic;
		const { name } = attributes;
		if (geometry.type === "polygon" || geometry.type === "circle" || geometry.type === "rectangle") {
			const { rings } = geometry;
			const ring = rings[0];
			const txt = ring.map((item: any) => {
				return `${item[0]} ${item[1]}`;
			});
			const blob = new Blob([txt.join("\n")], { type: "text/plain;charset=utf-8" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `${name}.txt`;
			a.click();
			URL.revokeObjectURL(url);
		} else if (geometry.type === "polyline") {
			const { paths } = geometry;
			const path = paths[0];
			const txt = path.map((item: any) => {
				return `${item[0]} ${item[1]}`;
			});
			const blob = new Blob([txt.join("\n")], { type: "text/plain;charset=utf-8" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `${name}.txt`;
			a.click();
			URL.revokeObjectURL(url);
		} else if (geometry.type === "point") {
			const { x, y } = geometry;
			const txt = `${x} ${y}`;
			const blob = new Blob([txt], { type: "text/plain;charset=utf-8" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `${name}.txt`;
			a.click();
			URL.revokeObjectURL(url);
		} else if (geometry.type === "extent") {
			const { xmin, ymin, xmax, ymax } = geometry;
			const txt = `${xmin} ${ymin}\n${xmax} ${ymax}`;
			const blob = new Blob([txt], { type: "text/plain;charset=utf-8" });
			const url = URL.createObjectURL(blob);
			const a = document.createElement("a");
			a.href = url;
			a.download = `${name}.txt`;
			a.click();
			URL.revokeObjectURL(url);
		}
	}
};

/**
 * 导出Geojson坐标数据
 */
const downloadGeojson = async () => {
	const geojson = {
		type: "FeatureCollection",
		features: [] as any[],
	};
	for (let i = 0; i < props.graphics.length; i++) {
		const graphic: any = props.graphics[i];
		const { geometry, attributes } = graphic;
		const { name } = attributes;
		if (geometry.type === "polygon" || geometry.type === "circle" || geometry.type === "rectangle") {
			const { rings } = geometry;
			const ring = rings[0];
			const coordinates = ring.map((item: any) => {
				return [item[0], item[1]];
			});
			const feature = {
				type: "Feature",
				properties: {
					name,
				},
				geometry: {
					type: "Polygon",
					coordinates: [coordinates],
				},
			};
			geojson.features.push(feature);
		} else if (geometry.type === "polyline") {
			const { paths } = geometry;
			const path = paths[0];
			const coordinates = path.map((item: any) => {
				return [item[0], item[1]];
			});
			const feature = {
				type: "Feature",
				properties: {
					name,
				},
				geometry: {
					type: "LineString",
					coordinates: coordinates,
				},
			};
			geojson.features.push(feature);
		} else if (geometry.type === "point") {
			const { x, y } = geometry;
			const feature = {
				type: "Feature",
				properties: {
					name,
				},
				geometry: {
					type: "Point",
					coordinates: [x, y],
				},
			};
			geojson.features.push(feature);
		}
		const blob = new Blob([JSON.stringify(geojson)], { type: "text/plain;charset=utf-8" });
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `${name}.geojson`;
		a.click();
		URL.revokeObjectURL(url);
	}
};


	/**
	 * 导出ArcgisJson坐标数据
	 */
	const downloadArcgisjson = async () => {
		const arcgisjson = {
			displayFieldName: "",
			fieldAliases: {},
			geometryType: "",
			spatialReference: {
				wkid: 4490,
				latestWkid: 4490,
			},
			fields: [],
			features: [] as any[],
		};
		for (let i = 0; i < props.graphics.length; i++) {
			const graphic: any = props.graphics[i];
			const { geometry, attributes } = graphic;
			const { name } = attributes;
			if (geometry.type === "polygon" || geometry.type === "circle" || geometry.type === "rectangle") {
				const { rings } = geometry;
				const ring = rings[0];
				const coordinates = ring.map((item: any) => {
					return [item[0], item[1]];
				});
				const feature = {
					attributes: {
						name,
					},
					geometry: {
						rings: [coordinates],
						spatialReference: {
							wkid: 4490,
							latestWkid: 4490,
						},
					},
				};
				arcgisjson.features.push(feature);
			} else if (geometry.type === "polyline") {
				const { paths } = geometry;
				const path = paths[0];
				const coordinates = path.map((item: any) => {
					return [item[0], item[1]];
				});
				const feature = {
					attributes: {
						name,
					},
					geometry: {
						paths: [coordinates],
						spatialReference: {
							wkid: 4490,
							latestWkid: 4490,
						},
					},
				};
				arcgisjson.features.push(feature);
			} else if (geometry.type === "point") {
				const { x, y } = geometry;
				const feature = {
					attributes: {
						name,
					},
					geometry: {
						x,
						y,
						spatialReference: {
							wkid: 4490,
							latestWkid: 4490,
						},
					},
				};
				arcgisjson.features.push(feature);
			} else if (geometry.type === "extent") {
				const { xmin, ymin, xmax, ymax } = geometry;
				const coordinates = [
					[xmin, ymin],
					[xmax, ymax],
				];
				const feature = {
					attributes: {
						name,
					},
					geometry: {
						rings: [coordinates],
						spatialReference: {
							wkid: 4490,
							latestWkid: 4490,
						},
					},
				};
				arcgisjson.features.push(feature);
			}
		}
		const blob = new Blob([JSON.stringify(arcgisjson)], { type: "text/plain;charset=utf-8" });
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `${name}.json`;
		a.click();
		URL.revokeObjectURL(url);
	};

</script>