<template>
  <pop-panel
    v-if="popShow"
    :title="'绘制'"
    :width="500"
    :height="200"
    :top="20"
    :right="75"
    :modal="false"
    @close="close"
  >
    <template #content>
      <div class="draw-panel">
        <el-button
          class="draw-btn"
          size="small"
          title="绘制点"
          @click="handleDraw('point')"
        >
          <Icons.PointIcon class="draw-icon" />
        </el-button>
        <el-button
          class="draw-btn"
          size="small"
          title="绘制线"
          @click="handleDraw('polyline')"
        >
          <Icons.lineIcon class="draw-icon" />
        </el-button>
        <el-button
          class="draw-btn"
          size="small"
          title="绘制多边形"
          @click="handleDraw('polygon')"
        >
          <Icons.Polygon2DIcon class="draw-icon" />
        </el-button>
        <el-button
          class="draw-btn"
          size="small"
          title="绘制圆"
          @click="handleDraw('circle')"
        >
          <Icons.CircleIcon class="draw-icon" />
        </el-button>
        <el-button
          class="draw-btn"
          size="small"
          title="绘制矩形"
          @click="handleDraw('rectangle')"
        >
          <Icons.RectangleIcon class="draw-icon" />
        </el-button>
        <el-button
          v-if="(props.Onemap as any)?.MapType == mapType.cesium"
          class="draw-btn"
          size="small"
          title="绘制体"
          @click="handleDraw('polygon3d')"
        >
          <Icons.Polygon3DIcon class="draw-icon" />
        </el-button>
      </div>
      <el-tabs class="tabs" v-model="activeName">
        <el-tab-pane label="绘制" name="draw">
          <draw-panel 
          :graphicData="graphicData" 
          :Onemap="Onemap"
          :PropStore="PropStore"
          :LayerStore="LayerStore"
          :ToolStore="ToolStore"
        />
        </el-tab-pane>
        <el-tab-pane label="收藏" name="collect" v-if="false">
          <collect-panel />
        </el-tab-pane>
      </el-tabs>
    </template>
  </pop-panel>
</template>
<script lang="ts" setup>
import { 
  ref, 
  watch,
  type PropType 
} from "vue";
import { Icons, mapType } from "../../onemapkit";
import PopPanel from "../../PopPanel";
import { defaultSymbol } from "./const";
import DrawPanel from "./DrawPanel.vue";
import CollectPanel from "./CollectPanel.vue";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  Onemap: {
    type: Object as PropType<any>,
    default: undefined,
  },
  ToolStore: {
    type: Object as any,
    default: null,
  },
});
const activeName = ref("draw");
const drawState = ref(false); // 绘制状态

const popShow = ref(true);

// 图形列表
const graphicData = ref<any[]>([]);

// 图形id
let graphicId = 0;

watch(
  () => props.Onemap.MapType,
  () => {
    // 切换2D/3D地图时，重置绘制状态
    drawState.value = false;
  }
);

const handleDraw = (type: string) => {
  switch (type) {
    case "point":
      props.Onemap.drawPoint(handleDrawEnd);
      break;
    case "polyline":
      props.Onemap.drawLine(handleDrawEnd);
      break;
    case "circle":
      props.Onemap.drawCircle(handleDrawEnd);
      break;
    case "rectangle":
      props.Onemap.drawRectangle(handleDrawEnd);
      break;
    case "polygon":
      props.Onemap.drawPolygon(handleDrawEnd);
      break;
    default:
      break;
  }
};

/**
 * 完成绘制回调
 */
const handleDrawEnd = (geometry: any) => {
  let symbol: any = null;
  switch (geometry.type) {
    case "point":
      symbol = defaultSymbol.point;
      break;
    case "polyline":
      symbol = defaultSymbol.polyline;
      break;
    case "circle":
    case "rectangle":
    case "polygon":
      symbol = defaultSymbol.polygon;
      break;
    default:
      break;
  }

  let id = ++graphicId;
  const attributes = {
    id: id,
    name: "图形" + id,
  };

  let graphic = {
    attributes: attributes,
    geometry: geometry,
    symbol: symbol,
  };

  graphicData.value.push(graphic);

  props.Onemap.drawGraphic(attributes, geometry, symbol);
};

/**
 * 2D绘制实现
 * @param type
 */

//@ts-ignore
const handleDraw2D = (type: string) => {
  switch (type) {
    case "point":
      props.Onemap.drawPoint(handleDrawEnd);
      break;
    case "polyline":
      props.Onemap.drawLine(handleDrawEnd);
      break;
    case "circle":
      props.Onemap.drawCircle(handleDrawEnd);
      break;
    case "rectangle":
      props.Onemap.drawRectangle(handleDrawEnd);
      break;
    case "polygon":
      props.Onemap.drawPolygon(handleDrawEnd);
      break;
    default:
      break;
  }
};

/**
 * 3D绘制实现
 * @param type
 */

//@ts-ignore
const handleDraw3D = (type: string) => {
  switch (type) {
    case "point":
      break;
    case "polyline":
      break;
    case "circle":
      break;
    case "rectangle":
      break;
    case "polygon":
      break;
    default:
      break;
  }
};

const close = () => {
  popShow.value = false;
};
</script>
<style lang="scss" scoped>
.draw-panel {
  margin: 10px 0 0 10px;
  .draw-btn {
    width: 34px;
    height: 20px;
    margin-left: 3px;

    .draw-icon {
      width: 20px;
      height: 20px;
      transform: scale(0.8);
    }
  }
}

.tabs {
  margin: 10px 0 0 10px;
}
</style>
