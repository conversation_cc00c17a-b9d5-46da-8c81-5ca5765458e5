import proj4 from "proj4";

/**
 * 将wkid为4490的geometry转换为指定坐标系，坐标系可选4524、4525、4545、4326、3857、2360、2412，支持点、线和面的转换
 */
export function projectGeometry(geometry: any, toWkid: number) {
	if (!geometry || !geometry.spatialReference) {
		return geometry;
	}
	let fromWkid = geometry.spatialReference.wkid;
	if (fromWkid === toWkid) {
		return geometry;
	}
	let type = geometry.type;
	let result = null;
	switch (type) {
		case "point":
			result = projectPoint(geometry, toWkid);
			break;
		case "polyline":
			result = projectPolyline(geometry, toWkid);
			break;
		case "polygon":
			result = projectPolygon(geometry, toWkid);
			break;
		default:
			break;
	}
	return result;
}

/**
 * 将wkid为4490的点转换为指定坐标系，坐标系可选4524、4525、4545、4326、3857、2360、2412
 */
export function projectPoint(point: any, toWkid: number) {
	if (!point || !point.spatialReference) {
		return point;
	}
	let fromWkid = point.spatialReference.wkid;
	if (fromWkid === toWkid) {
		return point;
	}
	let result = proj4(String(proj4(String(fromWkid))), String(proj4(String(toWkid))), [point.x, point.y]);
	return {
		x: result[0],
		y: result[1],
		spatialReference: {
			wkid: toWkid,
		},
	};
}

/**
 * 将wkid为4490的线转换为指定坐标系，坐标系可选4524、4525、4545、4326、3857、2360、2412
 */
export function projectPolyline(polyline: any, toWkid: number) {
	if (!polyline || !polyline.spatialReference) {
		return polyline;
	}
	let fromWkid = polyline.spatialReference.wkid as number;
	if (fromWkid === toWkid) {
		return polyline;
	}
	let paths = polyline.paths;
	let result:any = [];
	for (let i = 0; i < paths.length; i++) {
		let path = paths[i];
		let newPath:any = [];
		for (let j = 0; j < path.length; j++) {
			let point = path[j];
			let newPoint = proj4(String(proj4(String(fromWkid))), String(proj4(String(toWkid))), [point[0], point[1]]);
			newPath.push(newPoint);
		}
		result.push(newPath);
	}
	return {
		paths: result,
		spatialReference: {
			wkid: toWkid,
		},
	};
}

/**
 * 将wkid为4490的面转换为指定坐标系，坐标系可选4524、4525、4545、4326、3857、2360、2412
 */
export function projectPolygon(polygon: any, toWkid: number) {
	if (!polygon || !polygon.spatialReference) {
		return polygon;
	}
	let fromWkid = polygon.spatialReference.wkid;
	if (fromWkid === toWkid) {
		return polygon;
	}
	let rings = polygon.rings;
	let result:any = [];
	for (let i = 0; i < rings.length; i++) {
		let ring = rings[i];
		let newRing:any = [];
		for (let j = 0; j < ring.length; j++) {
			let point = ring[j];
			let newPoint = proj4(String(proj4(String(fromWkid))), String(proj4(String(toWkid))), [point[0], point[1]]);
			newRing.push(newPoint);
		}
		result.push(newRing);
	}
	return {
		rings: result,
		spatialReference: {
			wkid: toWkid,
		},
	};
}