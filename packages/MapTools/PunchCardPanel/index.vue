<template>
  <div class="util-panel punch-card-panel">
    <div class="util-panel__content punch-card-panel__content">
      <div class="flex fz-12">
        <div class="left">
          <div class="row block">
            <div class="label">
              <label><span style="color: red">*</span>打卡时间</label>
              <a
                style="color: #007bff; float: right; text-decoration: underline"
                @click="doQuery"
                >刷新</a
              >
            </div>
            <el-config-provider
              :locale="localeTy"
            >
            <el-date-picker
              v-model="date"
              type="date"
              class="w-full-i el-icon__reset"
              style="width: 100%;"
              :teleported="false"
              placeholder="请选择"
            />
            </el-config-provider>
            <p v-if="!date" class="fz-12" style="color: red; margin: 6px">
              *打卡时间不能为空
            </p>
          </div>
          <div class="row">
            <div class="label">
              <label>打卡时段</label>
              <sortOrder
                :alias="sortAlias"
                :textValue="'CREATEDATE'"
                @updateSort="updateSort"
              ></sortOrder>
            </div>
            <div class="check-row">
              <el-checkbox
                size="small"
                :disabled="customTime"
                v-model="allTime"
                label="所有时段"
              />
            </div>
            <el-checkbox-group size="small" v-model="timeCheckList">
              <div class="check-row" v-for="time in timeList" :key="time.label">
                <el-checkbox
                  :label="time.label"
                  :disabled="allTime || customTime"
                />
                <span
                  v-if="time.description"
                  :class="{
                    punchin: time.description === '上班',
                    punchout: time.description === '下班',
                  }"
                  >({{ time.description }})</span
                >
              </div>
            </el-checkbox-group>
            <div class="check-row">
              <el-checkbox
                size="small"
                v-model="customTime"
                @change="changeCustomTime"
                label="自定义时段"
              />
            </div>
            <div v-if="customTime">
              <el-config-provider
                :locale="localeTy"
              >
                <el-time-picker
                  class="w-full-i el-icon__reset"
                  format="HH:mm"
                  is-range
                  v-model="customTimeRange"
                  start-placeholder="开始"
                  end-placeholder="结束"
                  @blur="handleCustomTimeBlur"
                ></el-time-picker>
              </el-config-provider>
            </div>
          </div>
          <div class="row">
            <div class="label">
              <label>打卡人姓名</label>
              <sortOrder
                :alias="sortAlias"
                :textValue="'USERNAME'"
                @updateSort="updateSort"
              ></sortOrder>
            </div>
            <el-input
              clearable
              v-model="username"
              placeholder="请输入"
              class="el-icon__reset"
              @keydown.enter="doQuery"
              @clear="doQuery"
            >
              <template #append>
                <el-button @click="doQuery">
                  <el-icon><Search/></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          <div class="row">
            <div class="label">
              <label>打卡人部门</label>
              <sortOrder
                :alias="sortAlias"
                :textValue="'DEPARTMENTNAME'"
                @updateSort="updateSort"
              ></sortOrder>
            </div>
            <el-select v-model="deptname" placeholder="请选择" clearable>
              <el-option
                v-for="dept in deptList"
                :key="dept"
                :label="dept"
                :value="dept"
              />
            </el-select>
          </div>
          <div class="row">
            <div class="label">
              <label>打卡范围</label>
            </div>
            <el-select v-model="regionName" placeholder="请选择" clearable>
              <el-option
                v-for="region in regionNameList"
                :key="region"
                :label="region"
                :value="region"
              />
            </el-select>
            <div class="check-row" style="margin-top: 10px">
              <el-checkbox
                size="small"
                v-model="showValid"
                label="范围内打卡"
              />
            </div>
            <div class="check-row">
              <el-checkbox
                size="small"
                v-model="showInvalid"
                label="范围外打卡"
              />
            </div>
          </div>
        </div>
        <div class="center">
          <div class="center-main">
            <ul class="record-list" v-if="listData.length">
              <li
                v-for="(record, index) in listData"
                :key="record.attributes.OBJECTID"
                @click="handleListItemClick(record)"
                class="record-item"
                :class="{
                  active:
                    curRecord &&
                    curRecord.attributes.OBJECTID ===
                      record.attributes.OBJECTID,
                }"
              >
                <span class="record-list-index">{{ index + 1 }}.</span>
                <span class="record-list-avator">{{
                  record.attributes.USERNAME
                }}</span>
                -
                <span class="record-list-name"
                  >{{ record.attributes.DEPARTMENTNAME }} ({{
                    timeFormat(record.attributes.CREATEDATE)
                  }})</span
                >
              </li>
            </ul>
            <el-empty
              class="punch-no-data"
              v-else
              description="暂无数据"
            ></el-empty>
          </div>
          <div class="center-footer">
            <el-pagination
              small
              background
              hide-on-single-page
              layout="pager"
              :pager-count="5"
              :total="recordCount"
              :page-size="page.pageSize"
              @current-change="changePage"
              v-model:current-page="page.pageIndex"
            />
            <p class="count">
              <span style="display: inline-flex; margin-right: 4px">
                <span>每页 </span>
                <el-select
                  class="page-size-select"
                  v-model="page.pageSize"
                  size="small"
                  placeholder=""
                >
                  <el-option
                    v-for="item in pageSizeOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
                <span> 条 </span>
              </span>
              <span>共 </span>
              <span style="color: #007bff">{{ recordCount }}</span>
              <span> 条记录</span>
            </p>
          </div>
        </div>
        <div class="right">
          <div class="info-table" v-if="curRecord">
            <div class="table-row">
              <div class="table-col th">姓名</div>
              <div class="table-col">{{ curRecord.attributes.USERNAME }}</div>
            </div>
            <div class="table-row">
              <div class="table-col th">部门名称</div>
              <div class="table-col">
                {{ curRecord.attributes.DEPARTMENTNAME }}
              </div>
            </div>
            <div class="table-row">
              <div class="table-col th">x</div>
              <div class="table-col">{{ curRecord.attributes.X }}</div>
            </div>
            <div class="table-row">
              <div class="table-col th">y</div>
              <div class="table-col">{{ curRecord.attributes.Y }}</div>
            </div>
            <div class="table-row">
              <div class="table-col th">打卡时间</div>
              <div class="table-col">
                {{ timeFormat(curRecord.attributes.CREATEDATE,"YYYY-MM-DD HH:mm:ss") }}
              </div>
            </div>
          </div>
          <el-empty
            class="punch-no-data"
            v-else
            description="暂无详情"
          ></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  onMounted,
  reactive,
  defineProps,
  shallowRef,
  watch,
  ref,
  toRaw,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { Search } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as _Query from "@arcgis/core/rest/query";
import sortOrder from "./sortOrder.vue";
import LayerQuery from "../../utils/common-tools/layerQuery";
import { getOnemap, mapType, Icons } from "../../onemapkit";
import Query from "@arcgis/core/rest/support/Query.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";

const props = defineProps({
  // 面板
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
});

const _inOnemap = getOnemap(props.MapControlName);
const mapData:any = ref();
const mapUrl = ref('')
const localeTy = ref(zhCn)

onMounted( () => {
  console.log('打卡信息的props.LayerStore',props.LayerStore);
  
  let lyList = props.LayerStore._MapLayers.filter((ly:any)=>{
    return ly.layerTagId == 'PUNCH_CARD'
  })
  if(lyList && lyList.length){
    mapData.value = lyList[0]
    mapUrl.value = lyList[0].options.url
  }
  let oneDatejs = dayjs()
  date.value = oneDatejs.format("YYYY-MM-DD")
  _initOptions();
  _getCurNearTimeRange();
  doQuery();
});
onBeforeUnmount(() => {
  if(mapData.value){
    _inOnemap.RemoveLayerById(mapData.value)
  }
  if (_inOnemap.MapType.value == mapType.arcgis) {
    let ly = _inOnemap.GetLayerByID('temp_locate_graphics_layer')
    if(ly){
      ly.clear()
    }
    _inOnemap.MapViewer.graphics.clear()
  }
  _inOnemap.removeAllGraphicsByLayerId('PunchRecordClick')
});

// 左侧查询
const date = ref();
const timeCheckList:any = ref([]);
const timeList = reactive([
  {
    value: 1,
    label: "07:20-08:00",
    nearTimeRange: "07:00-12:00",
    description: "上班",
  },
  {
    value: 2,
    label: "12:00-13:00",
    nearTimeRange: "12:00-15:00",
    description: "下班",
  },
  {
    value: 3,
    label: "14:00-15:00",
    nearTimeRange: "15:00-18:00",
    description: "上班",
  },
  {
    value: 4,
    label: "18:00-24:00",
    nearTimeRange: "18:00-24:00",
    description: "下班",
  },
  {
    value: -1,
    label: "其他时段",
  },
]);

const username = ref("");
const allTime = ref(false);
const deptList = ref([]);
const deptname = ref("");
const regionList = shallowRef([]);
const regionNameList = ref();
const regionName = ref("");
const showValid = ref(true);
const showInvalid = ref(false);

// 自定义时段
const customTime = ref(false);
const customTimeRange = ref(null);
const handleCustomTimeBlur = () => {
  if (customTime.value && customTimeRange.value) {
    doQuery();
  }
};

const changeCustomTime = (val:boolean)=>{
  if (!val) {
    doQuery();
  }
}

const curQueryParams:any = ref({});
const curRegion = ref(null);
let num = 1
const onPunchRecordClick = (data:any)=> {
  console.log('点击的data',data);
  _inOnemap.removeAllGraphicsByLayerId('PunchRecordClick')
	if (data) {
		const geometry = toRaw(data).geometry;
		if (geometry.type == 'point') {
      const symbol = Icons.geoSymbol.point;
      symbol.color = [0,0,0,0];
      symbol.size = 8;
      symbol.outline.color = [0, 0, 255, 1];
      symbol.outline.width = 2;
      _inOnemap.drawGraphic(
        {id: 'geo1720658761781_point_' + num},
        {
          x: geometry.x,
          y: geometry.y,
          z: '90',
          spatialReference: { wkid: 4490 },
          type: 'point'
        },
        symbol,
        false,
        'PunchRecordClick'
      );
      _inOnemap.gotoPoint(
        {
          x: geometry.x,
          y: geometry.y,
          z: 0,
          wkid: geometry.spatialReference.wkid,
        },
        _inOnemap.MapType.value == mapType.cesium? 12 : 16,
        false
      );
      num++
		}
		else if (geometry.type == 'polyline' || geometry.type == 'polygon') {
      let symbol = {
        type: "simple-line",
          color: [98, 172, 249, 1],
          style:'solid',
          width: 5
      };
      _inOnemap.drawGraphic(
        data.attributes,
        geometry,
        symbol,
        true,
        'PunchRecordClick'
      );
      if(geometry.extent){
        _inOnemap.gotoExtent(geometry.extent)
      }
    }
		else {
			return
		}
  }
}

const isAddLayer = ref(false);
watch([curQueryParams, curRegion],() => {
  console.log('mapData.value',mapData.value);
  
  if (!isAddLayer.value && mapData.value) {     
      _inOnemap.AddLayer(mapData.value)
      isAddLayer.value = true;
  }
},{
  deep:true
}
);

const recordCount = ref(0);
const curRecordIdMap = ref({});
const listData:any = shallowRef([]);

// 分页
const page = reactive({
  pageSize: 50,
  pageIndex: 1,
});
const pageSizeOptions = ref([50, 100, 200, 500]);

// 排序
const orderByFields:any = ref()
const sortAlias = reactive(["ASC", "DESC"]);
const orderFields:any = reactive({
  CREATEDATE: "",
  USERNAME: "",
  DEPARTMENTNAME: "",
});
const updateSort = (val:any)=>{
  orderFields[val[0]] = val[1]
  const res = Object.keys(orderFields).reduce((ans:any, item:any) => {
    return orderFields[item] ? [...ans, `${item} ${orderFields[item]}`] : ans;
  }, []);
  orderByFields.value = res.length ? res : ["CREATEDATE"];
  console.log('orderByFields.value',orderByFields.value);
  doQuery();
}
// const orderByFields = computed(() => {
//   const res = Object.keys(orderFields).reduce((ans:any, item:any) => {
//     return orderFields[item] ? [...ans, `${item} ${orderFields[item]}`] : ans;
//   }, []);
//   return res.length ? res : ["CREATEDATE"];
// });
// watch(orderByFields.value, () => {
//   console.log('改变orderByFields.value',orderByFields.value);
  
  
// },{
//   deep: true
// });

const queryMap = (
  url:string,
  params = {},
  taskMethod = "executeQueryJSON",
  requestOptions = {}
) => {
  if(taskMethod == 'executeQueryJSON'){
    return _Query.executeQueryJSON(
      encodeURI(url),
      {
        ...params,
      },
      requestOptions
    )
  }else{
    return _Query.executeForCount(
      encodeURI(url),
      {
        ...params,
      },
      requestOptions
    );
  }
};
const _fixDatetime = (date:any) => {
  let diff = 8 * 1000 * 60 * 60;
  let d = new Date(date).getTime() - diff;
  return d;
};

const _queryRecordByPage = (params:any) => {
  listData.value = [];
  let dkUrl = mapUrl.value + "/0";
  let pageSize = page.pageSize;
  let pageIndex = page.pageIndex;
  let beginIndex = pageSize * (pageIndex - 1);
  queryMap(
    dkUrl,
    {
      ...params,
      resultOffset: beginIndex,
      resultRecordCount: pageSize,
      outFields: ["*"],
      returnGeometry: true
    },
    "executeQueryJSON",
    {
      query: {
        resultOffset: beginIndex,
        resultRecordCount: pageSize,
        t: Math.random()
      }
    }
  ).then((res:any) => {
    if(!res){
      return
    }
    let features = res.features || [];
    let recordIdMap:any = {};
    features.forEach(function (item:any) {
      let id = item.attributes.OBJECTID;
      item.attributes.CREATEDATE = _fixDatetime(item.attributes.CREATEDATE);
      recordIdMap[id] = item;
    });
    listData.value = features;
    curRecordIdMap.value = recordIdMap;
    curRecord.value = listData.value[0]

  });
};
const doQuery = () => {
  if (!date.value) {
    ElMessage.error("打卡时间不能为空");
    return;
  }
  let dateVal = dayjs(date.value).format("YYYY-MM-DD");
  let userName = username.value;
  let deptName = deptname.value;
  let region = regionName.value;
  let dkUrl:string = mapUrl.value + "/0";
  const neverCondition = "1 = 2";

  const nextDay = dayjs(dateVal).add(1, "day").format("YYYY-MM-DD");

  let typeFilter = "1=1";
  if (showValid.value && !showInvalid.value) {
    typeFilter = "Type=1";
  }
  if (!showValid.value && showInvalid.value) {
    typeFilter = "Type=0";
  }
  if (!showValid.value && !showInvalid.value) {
    ElMessage.error("请至少勾选一个打卡范围进行查询");
    typeFilter = neverCondition;
  }

  let where = typeFilter;

  // 时间范围
  let timeRangeVals;
  let dateTimeFilter;
  if (customTime.value) {
    timeRangeVals = customTimeRange.value || [];
    timeRangeVals = timeRangeVals.map((item:any) => dayjs(item).format("HH:mm"));
    if (timeRangeVals.length) {
      dateTimeFilter = ` (CREATEDATE >= TIMESTAMP'${dateVal} ${timeRangeVals[0]}' AND CREATEDATE < TIMESTAMP'${dateVal} ${timeRangeVals[1]}')`;
    }
  } else {
    timeRangeVals = allTime.value ? [] : timeCheckList.value;
    dateTimeFilter = timeRangeVals.reduce((res:any, item:any) => {
      let curRes;
      let one = timeList.find((time:any) => time.label === item)
      if (one && one.value === -1) {
        let cdt = `CREATEDATE >= TIMESTAMP'${dateVal}' AND CREATEDATE < TIMESTAMP'${nextDay}'`;
        curRes = timeList
          .filter((item:any) => item.value !== -1)
          .map((item:any) => item.label)
          .reduce((res:any, item:any) => {
            let ranges = item.split("-");
            if (ranges[1] === "24:00") {
              ranges[1] = "";
            }
            let endTime = ranges[1] ? `${dateVal} ${ranges[1]}:59` : nextDay;
            let curRes2 = ` AND NOT (CREATEDATE >= TIMESTAMP'${dateVal} ${ranges[0]}' AND CREATEDATE < TIMESTAMP'${endTime}')`;
            return res + curRes2;
          }, cdt);
      } else {
        let ranges = item.split("-");
        if (ranges[1] === "24:00") {
          ranges[1] = "";
        }
        let endTime = ranges[1] ? `${dateVal} ${ranges[1]}:59` : nextDay;
        curRes = ` (CREATEDATE >= TIMESTAMP'${dateVal} ${ranges[0]}' AND CREATEDATE < TIMESTAMP'${endTime}')`;
      }
      return res ? `${res} OR  ${curRes}` : curRes;
    }, "");
  }

  if (!dateTimeFilter) {
    dateTimeFilter = `CREATEDATE >= TIMESTAMP'${dateVal}' AND CREATEDATE < TIMESTAMP'${nextDay}'`;
  }

  if (!allTime.value && !timeRangeVals.length && !customTime.value) {
    ElMessage.error("请至少勾选一个时段进行查询");
    dateTimeFilter = neverCondition;
  }

  if (dateTimeFilter) {
    where += ` AND (${dateTimeFilter})`;
  }

  if (userName) {
    where += `AND USERNAME like '%${userName}%'`;
  }

  if (deptName) {
    where += ` AND DEPARTMENTNAME = '${deptName}'`;
  }

  where = where.indexOf(neverCondition) !== -1 ? neverCondition : where;

  let queryParams:any = {
    returnGeometry: true,
    outSpatialReference: { wkid: 4490 },
    orderByFields: orderByFields.value,
    where: where,
  };

  if (region) {
    let regionItem:any = regionList.value.find((item:any) => {
      return item.attributes.NAME === region;
    });
    if (regionItem && regionItem.geometry) {
      queryParams.geometry = regionItem.geometry;
      curRegion.value = regionItem;
    }
  }

  // 保存查询条件，过滤地图要素和搜索结果一致
  curQueryParams.value = queryParams;

  queryMap(dkUrl, queryParams, "executeForCount", {
    query: {
      t: Math.random(),
    }
  }).then(function (res:any) {
    recordCount.value = res || 0;
    page.pageIndex = 1;
    _queryRecordByPage(queryParams);
  });
};

watch(
  [
    date,
    allTime,
    timeCheckList,
    deptname,
    regionName,
    showValid,
    showInvalid,
    () => page.pageSize,
  ],
  () => {
    doQuery();
  }
);

//#region 4. 地图切换后要重绘图形
watch(
  () => _inOnemap.isMapReady.value,
  (newval) => {
    if (newval) {
      onPunchRecordClick(curRecord.value)
      nextTick(()=>{

        if (mapData.value) {     
            _inOnemap.AddLayer(mapData.value)
        }
      })
    }
  }
);

const changePage = (val:any) => {
  _queryRecordByPage(curQueryParams.value);
}

const curRecord:any = shallowRef();
const handleListItemClick = (record:any) => {
  let raw = JSON.parse(JSON.stringify(record))
  raw.geometry.type = record.geometry.type
  curRecord.value = toRaw(raw);
  onPunchRecordClick(raw)
};

// 获取最近的时间段
const _getCurNearTimeRange = () => {
  const timeNow = dayjs().format("HH:mm");
  const curTimeRange:any = timeList.find((item:any) => {
    const nearTimeRanges = item?.nearTimeRange?.split("-");
    return (
      nearTimeRanges &&
      timeNow >= nearTimeRanges[0] &&
      timeNow < nearTimeRanges[1]
    );
  });
  if(curTimeRange){
    timeCheckList.value.push(curTimeRange.label);
  }
};
const _initOptions = () => {
  if (!mapUrl.value) {
    return;
  }
  const getQuery = new LayerQuery(mapUrl.value);
    // 获取范围
  getQuery.query(
    1,
    ["*"],
    "1=1",
    1,
    500,
    true,
    'Mapserve,arcgis',
    null,
    (res: any) => {
      console.log('获取范围response',res);
      regionList.value = res.features;
      regionNameList.value = regionList.value.map((item:any) => item.attributes.NAME)
    }
  )
    // 获取部门
  const query = new Query();
  query.outFields = ["DEPARTMENTNAME"]
  query.where = "1=1";
  query.returnGeometry = false
  query.returnDistinctValues = true
  const featureLayer = new FeatureLayer({
    url: `${mapUrl.value}/0`,
  });
  featureLayer.queryFeatures(query).then((res:any)=>{
    deptList.value = res.features.map((item:any) => item.attributes.DEPARTMENTNAME)
  })
};

// 时间格式化
const timeFormat = (val:any, formater = "HH:mm") => {
  return dayjs(val).format(formater);
};
</script>
<style lang="scss" scoped>
// @import "~@/styles/util-panel.scss";
.punch-card-panel {
  width: 600px;

  &__content {
    padding: 10px;
  }
}

.row {
  margin-bottom: 10px;
  .label {
    margin-bottom: 6px;
    label {
      font-size: 12px;
      font-weight: bold;
      color: teal;
    }
  }
}

.check-row {
  margin-bottom: 6px;
}
.fz-12 {
  font-size: 12px;
}

.flex {
  display: flex;
}

$maxHeight: 320px;
.left {
  width: 180px;
  padding-right: 10px;
  border-right: 1px solid #ccc;
  max-height: $maxHeight;
  overflow: auto;
}

.punchout,
.punchin {
  position: relative;
  top: -2px;
  font-size: 12px;
  margin-left: 4px;
}
.punchin {
  color: rgb(13, 116, 0);
}
.punchout {
  color: rgb(207, 19, 0);
}

.center {
  position: relative;
  width: 220px;
  border-right: 1px solid #ccc;
  max-height: $maxHeight;
  overflow: hidden;
  $footerHeight: 66px;
  &-main {
    padding: 0 6px;
    // padding-bottom: 10px;
    height: calc($maxHeight - $footerHeight);
    overflow-y: auto;
  }
  &-footer {
    height: $footerHeight;
    padding-top: 6px;
    border-top: 1px solid #efefef;
    .count {
      font-size: 12px;
      margin: 0;
      margin-top: 10px;
      padding-right: 10px;
      text-align: right;
    }
  }
}

.record-item {
  cursor: pointer;
  font-size: 12px;
  color: #444;
  line-height: 1.6;
  padding-left: 4px;
  &.active {
    background-color: #cdf6ff !important;
  }
  &:hover {
    background-color: #f2f5fc;
  }
}
.record-list{
  padding: 0;
}
.record-list-avator {
  // color: #007bff;
  // font-weight: bold;
  color: #000;
}

.right {
  width: 180px;
  padding: 6px;
  max-height: $maxHeight;
  overflow: auto;
}

$tableBorder: 1px solid #0000;
.info-table {
  border: $tableBorder;
  font-size: 12px;
  color: #333;

  .th {
    font-weight: bold;
    max-width: 60px;
  }
}
.table-row {
  display: flex;
  & + & {
    border-top: $tableBorder;
  }
  &:nth-child(even) {
    background-color: #f5f5f5;
    .table-col:nth-child(1) {
      background-color: #eaf3fa;
    }
  }
}
.table-col {
  padding: 3px;
  flex: 1;
  & + & {
    border-left: $tableBorder;
  }
}

.page-size-select {
  position: relative;
  top: -3px;
  width: 60px;
  margin: 0 3px;
}

// :deep(.el-month-table td.current:not(.disabled) .cell){
//   // color: #fff!important;
//   color: #f00!important;
// }

.punch-no-data {
  padding: 20px 0;
  .el-empty__image {
    width: 100px;
  }
  .el-empty__description {
    p {
      font-size: 13px;
    }
  }
}
:deep(.el-year-table td.today .cell){
    color: #fff;
    background: #409eff;
  }
:deep(.el-month-table td.today .cell){
  color: #fff;
  background: #409eff;
}
:deep(.el-month-table td.current:not(.disabled) .cell){
  color: #409eff;
  background: #fff;
}
:deep(.el-year-table td.current:not(.disabled) .cell){
  color: #409eff;
  background: #fff;
}
.w-full-i{
  width: 100%!important;
}
</style>

