import * as Cesium from "@onemapkit/cesium";

/** 获取当前地图范围
 * @param { Cesium.Viewer} viewer
 * @returns [west,south,east,north]
 */
export default function getCenterPosition(viewer: Cesium.Viewer) {
  {
    // 保存相机的完整状态
    const originalPosition = viewer.camera.position.clone();
    const originalDirection = viewer.camera.direction.clone();
    const originalUp = viewer.camera.up.clone();

    const centerResult = viewer.camera.pickEllipsoid(
      new Cesium.Cartesian2(
        viewer.canvas.clientWidth / 2,
        Cesium.Math.toDegrees(viewer.camera.pitch) > -60
          ? viewer.canvas.clientHeight
          : viewer.canvas.clientHeight / 2
      )
    );
    if (!centerResult) {
      return [107.5, 22.4, 108.5, 23.2];
    }
    const distance = Cesium.Cartesian3.distance(
      centerResult,
      viewer.scene.camera.positionWC
    );
    const initialHeading = viewer.camera.heading;
    viewer.camera.lookAt(
      centerResult,
      new Cesium.HeadingPitchRange(initialHeading, -90, distance)
    );
    //获取当前三维地图范围
    const rectangle: any = viewer.camera.computeViewRectangle();
    //地理坐标（弧度）转经纬度坐标
    const extent = [
      Cesium.Math.toDegrees(rectangle.west),
      Cesium.Math.toDegrees(rectangle.south),
      Cesium.Math.toDegrees(rectangle.east),
      Cesium.Math.toDegrees(rectangle.north),
    ];
    // 恢复相机控制 
    viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);

    // 恢复相机的完整状态
    viewer.camera.setView({
      destination: originalPosition,
      orientation: {
        direction: originalDirection,
        up: originalUp
      }
    });

    return extent;
  }
}
