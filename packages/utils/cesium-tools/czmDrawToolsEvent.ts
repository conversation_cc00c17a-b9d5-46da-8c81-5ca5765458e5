// import * as CesiumTools from "@onemapkit/cesium-tools";
import * as Cesium from "@onemapkit/cesium";

export function DrawToolItemEvent(
  MapViewer: Cesium.Viewer,
  geoDataKeys: Array<string>,
  geoDataValue: Map<string, any>,
  Option: any
): any {
  switch (Option.toolname) {
    case "delete":
      { 
        geoDataKeys.splice(geoDataKeys.indexOf(Option.row.id), 1);
        geoDataValue.delete(Option.row.id);
        //@ts-ignore
        MapViewer["DrawBrush"].RemoveGeometryById(Option.row.id);
      }
      break;
    case "endedit":
      {
        //@ts-ignore
        MapViewer["DrawBrush"].quitEdit();
      }
      break;
    case "edit":
      {
        //@ts-ignore
        MapViewer["DrawBrush"].reEdit(Option.row);
      }
      break;
    case "location":
      {
        const _state = MapViewer.scene.requestRenderMode;
        MapViewer.scene.requestRenderMode = false;
        MapViewer.camera.flyToBoundingSphere(
          Cesium.BoundingSphere.fromPoints(
            geoDataValue.get(Option.row).geometry.data
          )
        );
        MapViewer.scene.requestRenderMode = _state;
      }
      break;
    case "collect":
      {
      }
      break;
    case "downCoord":
      {
      }
      break;
    case "spatialanalysis":
      {
      }
      break;
    case "setsymbol":
      {
      }
      break;
  }
}
