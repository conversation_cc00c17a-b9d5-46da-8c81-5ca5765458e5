import * as Cesium from "@onemapkit/cesium";

export let entities: Array<Cesium.Entity> = [];

export class MeasureHandler {
  viewer: any;
  constructor(view: any) {
    this.viewer = view;
  }
  active: boolean = false; // 是否激活
  measureMapHandler: any; // 绘制的MapHandler
  primitives: Array<string> = [];
  // 激活地图操作事件
  AttachEvent() {
    if (!this.active) {
      this.measureMapHandler = new Cesium.ScreenSpaceEventHandler(
        this.viewer.scene.canvas
      );
      this.active = true;
    }
  }
  // 解绑地图操作事件
  DetachEvent() {
    if (this.active) {
      this.measureMapHandler.destroy();
      this.active = false;
    }
  }

  /**
   * 两个笛卡尔坐标的空间距离
   * @description 返回两个笛卡尔坐标的空间距离
   * @param cartesian3x
   * @param cartesian3y
   * @return Number
   */
  useMeasureDistance(cartesian3x: any, cartesian3y: any) {
    if (!cartesian3x || !cartesian3y) return 0;
    return Cesium.Cartesian3.distance(cartesian3x, cartesian3y);
  }

  /**
   * @description 计算两个笛卡尔坐标的高度差
   * @param cartesian3x
   * @param cartesian3y
   * @return Number
   */
  useMeasureHeight(cartesian3x: any, cartesian3y: any) {
    let cartographic1 = Cesium.Cartographic.fromCartesian(cartesian3x);
    let alt1 = cartographic1.height; // 高度
    let cartographic2 = Cesium.Cartographic.fromCartesian(cartesian3y);
    let alt2 = cartographic2.height; // 高度
    let height = Math.abs(alt1 - alt2);
    return height;
  }

  /**
   * @description 计算两个笛卡尔坐标的水平投影距离
   * @param cartesian3x
   * @param cartesian3y
   * @return Number
   */
  useMeasureHorizontalDistance(cartesian3x: any, cartesian3y: any) {
    let distance = this.useMeasureDistance(cartesian3x, cartesian3y);
    let height = this.useMeasureHeight(cartesian3x, cartesian3y);
    let horizontalDistance = Math.sqrt(distance ** 2 - height ** 2);
    return horizontalDistance;
  }

  /**
   * @description 计算笛卡尔坐标的方位角
   * @param cartesian3x
   * @param cartesian3y
   * @return Number
   */
  useMeasureAngle(cartesian3x: any, cartesian3y: any) {
    let cartographicx = Cesium.Cartographic.fromCartesian(cartesian3x);
    let cartographicy = Cesium.Cartographic.fromCartesian(cartesian3y);
    let lng_x = Cesium.Math.toDegrees(cartographicx.longitude);
    let lat_x = Cesium.Math.toDegrees(cartographicx.latitude);
    let lng_y = Cesium.Math.toDegrees(cartographicy.longitude);
    let lat_y = Cesium.Math.toDegrees(cartographicy.latitude);

    //以a点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
    const localToWorld_Matrix = Cesium.Transforms.eastNorthUpToFixedFrame(
      Cesium.Cartesian3.fromDegrees(lng_x, lat_x)
    );
    //求世界坐标到局部坐标的变换矩阵
    const worldToLocal_Matrix = Cesium.Matrix4.inverse(
      localToWorld_Matrix,
      new Cesium.Matrix4()
    );
    //a点在局部坐标的位置，其实就是局部坐标原点
    const localPosition_A = Cesium.Matrix4.multiplyByPoint(
      worldToLocal_Matrix,
      Cesium.Cartesian3.fromDegrees(lng_x, lat_x),
      new Cesium.Cartesian3()
    );
    //B点在以A点为原点的局部的坐标位置
    const localPosition_B = Cesium.Matrix4.multiplyByPoint(
      worldToLocal_Matrix,
      Cesium.Cartesian3.fromDegrees(lng_y, lat_y),
      new Cesium.Cartesian3()
    );

    //弧度
    const angle = Math.atan2(
      localPosition_B.x - localPosition_A.x,
      localPosition_B.y - localPosition_A.y
    );
    //角度
    let theta = angle * (180 / Math.PI);
    if (theta < 0) {
      theta = theta + 360;
    }
    return theta;
  }

  /**
   * @description 获取两个笛卡尔点位的方向角 参数 azimuthAngle 为方位角 默认为0
   * @param azimuthAngle
   */
  useGetBearingAngle(azimuthAngle: number = 0) {
    let bearingAngle = "正北方向";
    let angle = 0;
    if (
      azimuthAngle === 0 ||
      azimuthAngle === 90 ||
      azimuthAngle === 180 ||
      azimuthAngle === 270
    ) {
      if (azimuthAngle === 0) {
        bearingAngle = "正北方向";
      } else if (azimuthAngle === 90) {
        bearingAngle = "正东方向";
      } else if (azimuthAngle === 180) {
        bearingAngle = "正南方向";
      } else if (azimuthAngle === 270) {
        bearingAngle = "正西方向";
      }
    } else if (azimuthAngle < 90) {
      bearingAngle = "北偏东";
      angle = azimuthAngle;
    } else if (azimuthAngle > 90 && azimuthAngle < 180) {
      bearingAngle = "南偏东";
      angle = 180 - azimuthAngle;
    } else if (azimuthAngle > 180 && azimuthAngle < 270) {
      bearingAngle = "南偏西";
      angle = azimuthAngle - 180;
    } else if (azimuthAngle > 270 && azimuthAngle < 360) {
      bearingAngle = "北偏西";
      angle = 360 - azimuthAngle;
    }
    return { bearingAngle, angle };
  }

  /**
   * @description 创建一个cesium实时绘制线要素 即会因给的cartesian3数组的变化而变化
   * @param positions
   */
  useCreateLine(
    positions: any,
    color: string = "#409eff",
    width: number = 3,
    isGround?: true
  ) {
    let polyline = this.viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(function () {
          return positions.value;
        }, false),
        show: true,
        // disableDepthTestDistance: Number.POSITIVE_INFINITY,
        // material: Cesium.Color.LIGHTSKYBLUE,
        material: Cesium.Color.fromCssColorString(color),
        width: width,
        clampToGround: isGround,
        classificationType: Cesium.ClassificationType.BOTH,
        cornerType: Cesium.CornerType.MITERED,
        depthFailMaterial: Cesium.Color.fromCssColorString(color)//在绘制线不贴地时，线被建筑物遮挡设置这个属性
      },
    });
    entities.push(polyline);
    return polyline;
  }

  //创建多边形
  useCreatePolygon(
    cartesian3: any,
    //@ts-ignore
    fill: boolean = true,
    //@ts-ignore
    outlineColor: string = "#409eff"
  ) {
    return this.viewer.entities.add({
      // name: name,
      position: new Cesium.CallbackProperty(function () {
        return Cesium.BoundingSphere.fromPoints(cartesian3.value).center;
      }, false),
      polyline: new Cesium.PolylineGraphics({
        positions: new Cesium.CallbackProperty(function () {
          let polylinePositions = [...cartesian3.value, cartesian3.value[0]];
          return polylinePositions;
        }, false),
        show: true,
        material: Cesium.Color.fromCssColorString(outlineColor),
        width: 3,
        clampToGround: true,
        classificationType: Cesium.ClassificationType.BOTH,
      }),
      polygon: new Cesium.PolygonGraphics({
        hierarchy: new Cesium.CallbackProperty(function () {
          return new Cesium.PolygonHierarchy(cartesian3.value);
        }, false),
        // clampToGround: true,
        material: Cesium.Color.WHITESMOKE.withAlpha(0.3),
        // disableDepthTestDistance: Number.POSITIVE_INFINITY,
        show: true,
        fill: true,
        outline: true,
      }),
      label: new Cesium.LabelGraphics({
        text: "",
        font: "20px Helvetica",
        fillColor: Cesium.Color.RED,
        // backgroundColor: Cesium.Color.fromCssColorString('#FF6347'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 3,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      }),
    });
  }

  //创建圆
  useCreateCircle(
    center: any, // 圆心坐标（Cartesian3）
    radius: number, // 半径
    fill: boolean = true,
    outlineColor: string = "#409eff"
  ) {
    const ellipseGeometry = new Cesium.EllipseOutlineGeometry({
      center: center,
      semiMajorAxis: radius,
      semiMinorAxis: radius,
      granularity: Cesium.Math.RADIANS_PER_DEGREE / 2
    }) as any;

    const options = {
      center: ellipseGeometry._center,
      semiMajorAxis: ellipseGeometry._semiMajorAxis,
      semiMinorAxis: ellipseGeometry._semiMinorAxis,
      ellipsoid: ellipseGeometry._ellipsoid,
      rotation: ellipseGeometry._rotation,
      height: ellipseGeometry._height,
      granularity: ellipseGeometry._granularity,
      numberOfVerticalLines: ellipseGeometry._numberOfVerticalLines,
    };

    let cep = (Cesium as any).EllipseGeometryLibrary.computeEllipsePositions(
      options,
      false,
      true
    ).outerPositions;

    cep = (Cesium as any).EllipseGeometryLibrary.raisePositionsToHeight(
      cep,
      options,
      false
    );

    let outLinePositions = [];

    if (radius == 0) {
      outLinePositions = [];
    }
    else {
      for (let i = 0; i < cep.length; i += 3) {
        outLinePositions.push(
          new Cesium.Cartesian3(cep[i], cep[i + 1], cep[i + 2])
        );
      }
    }

    // const outline = new Cesium.Entity({
    //   position: center,
    //   ellipse: new Cesium.EllipseGraphics({
    //     semiMajorAxis: new Cesium.CallbackProperty(function () {
    //       //@ts-ignore
    //       return outline.myRadius;
    //     }, false), // 半径即为长轴和短轴的值
    //     semiMinorAxis: new Cesium.CallbackProperty(function () {
    //       //@ts-ignore
    //       return outline.myRadius;
    //     }, false), // 半径即为长轴和短轴的值
    //     show: true,
    //     fill: false, // 是否填充圆形
    //     outline: true, // 是否显示轮廓线
    //     outlineColor: Cesium.Color.fromCssColorString(outlineColor), // 轮廓线颜色
    //     outlineWidth: 100, // 轮廓线宽度cesium的bug，设置宽度无效
    //   }),
    // })
    // const outline = new Cesium.Entity({
    //   polyline: {
    //     positions: new Cesium.CallbackProperty(function () {
    //       //@ts-ignore
    //       return outline.myPositions;
    //     }, false),
    //     width: 3,
    //     clampToGround: true,
    //     material: Cesium.Color.fromCssColorString(outlineColor),
    //   }

    // })

    //@ts-ignore
    const circle = new Cesium.Entity({
      position: center,
      polyline: {
        positions: new Cesium.CallbackProperty(function () {
          //@ts-ignore
          return circle.myPositions;
        }, false),
        width: 3,
        clampToGround: true,
        material: Cesium.Color.fromCssColorString(outlineColor),
      },
      ellipse: new Cesium.EllipseGraphics({
        semiMajorAxis: new Cesium.CallbackProperty(function () {
          //@ts-ignore
          return circle.myRadius;
        }, false), // 半径即为长轴和短轴的值
        semiMinorAxis: new Cesium.CallbackProperty(function () {
          //@ts-ignore
          return circle.myRadius;
        }, false),
        granularity: Cesium.Math.RADIANS_PER_DEGREE / 2,
        material: Cesium.Color.WHITESMOKE.withAlpha(0.3), // 填充颜色
        show: true,
        fill: fill, // 是否填充圆形
      }),
      label: new Cesium.LabelGraphics({
        text: "",
        font: "20px Helvetica",
        fillColor: Cesium.Color.RED,
        // backgroundColor: Cesium.Color.fromCssColorString('#FF6347'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 3,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      }),
    });
    //@ts-ignore
    circle.myPositions = [...outLinePositions, outLinePositions[0]];
    //@ts-ignore
    circle.myRadius = radius
    // this.viewer.entities.add(outline);
    this.viewer.entities.add(circle);
    return circle;
  }


  //创建矩形
  useCreateRectangle(
    startPoint: any,
    endPoint: any,
    //@ts-ignore
    fill: boolean = true,
    //@ts-ignore
    outlineColor: string = "#409eff"
  ) {
    // const outline = new Cesium.Entity({
    //   rectangle: {
    //     coordinates: new Cesium.CallbackProperty(function () {
    //       //@ts-ignore
    //       return outline.myCoor;
    //     }, false),
    //     fill: false,
    //     outline: true,  // 显示轮廓
    //     outlineColor: Cesium.Color.fromCssColorString(outlineColor),// 轮廓颜色
    //     outlineWidth: 100,  // 轮廓宽度
    //   }
    // })
    // 计算矩形的四个角点
    const rectangleTemp = Cesium.Rectangle.fromCartesianArray([startPoint, endPoint]);
    let outLinePositions: Cesium.Cartesian3[] = [];
    if (startPoint == endPoint) {
      outLinePositions = []
    }
    else {
      outLinePositions = Cesium.Rectangle.subsample(rectangleTemp); // 获取矩形的四个角点
    }
    // 使用 Cesium.Polyline 绘制矩形轮廓
    // const outline = new Cesium.Entity({
    //   polyline: {
    //     positions: new Cesium.CallbackProperty(function () {
    //       //@ts-ignore
    //       return outline.myCoor;
    //     }, false),
    //     width: 3, // 轮廓宽度
    //     material: Cesium.Color.fromCssColorString(outlineColor), // 轮廓颜色
    //     clampToGround: true, // 如果希望轮廓贴地
    //   }
    // });

    //@ts-ignore
    const rectangle = new Cesium.Entity({
      //@ts-ignore
      position: new Cesium.CallbackProperty(function () {
        //@ts-ignore
        return rectangle.center;
      }, false),
      polyline: {
        positions: new Cesium.CallbackProperty(function () {
          //@ts-ignore
          return rectangle.myOutLinePositions;
        }, false),
        width: 3, // 轮廓宽度
        material: Cesium.Color.fromCssColorString(outlineColor), // 轮廓颜色
        clampToGround: true, // 如果希望轮廓贴地
      },
      rectangle: new Cesium.RectangleGraphics({
        coordinates: new Cesium.CallbackProperty(function () {
          //@ts-ignore
          return rectangle.myRectangleCoor;
        }, false),
        fill: fill, // 填充颜色
        // 填充颜色
        material: Cesium.Color.WHITESMOKE.withAlpha(0.3), // 轮廓宽度
      }),
      label: new Cesium.LabelGraphics({
        text: "",
        font: "20px Helvetica",
        fillColor: Cesium.Color.RED,
        // backgroundColor: Cesium.Color.fromCssColorString('#FF6347'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 3,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      }),
    })
    //@ts-ignore
    rectangle.myOutLinePositions = [...outLinePositions, outLinePositions[0]];
    //@ts-ignore
    rectangle.myRectangleCoor = Cesium.Rectangle.fromCartesianArray([startPoint, endPoint]);
    //@ts-ignore
    rectangle.center = new Cesium.Cartesian3(
      (startPoint.x + endPoint.x) / 2,
      (startPoint.y + endPoint.y) / 2,
      (startPoint.z + endPoint.z) / 2
    );
    this.viewer.entities.add(rectangle);
    return rectangle
  }

  // 创建一条虚线
  useCreateDottedLine(
    positions: any[],
    color: string = "#FF8C00",
    width: number = 20,
    //@ts-ignore
    gapColor: string = "white"
  ) {
    let polyline = this.viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(function () {
          return positions;
        }, false),
        show: true,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        // material: Cesium.Color.LIGHTSKYBLUE,
        material: new Cesium.PolylineDashMaterialProperty({
          color: Cesium.Color.fromCssColorString(color),
          // gapColor: Cesium.Color.fromCssColorString(gapColor),
          dashLength: 60,
        }),
        width: width,
        clampToGround: true,
        classificationType: Cesium.ClassificationType.BOTH,
      },
    });
    entities.push(polyline);
    return polyline;
  }

  useCreateCorridor(
    positions: any[],
    color: string = "#409eff",
    width: number
  ) {
    let corridor = this.viewer.entities.add({
      corridor: {
        positions: new Cesium.CallbackProperty(function () {
          return positions;
        }, false),
        show: true,
        width: width,
        material: Cesium.Color.fromCssColorString(color),
      },
    });
    entities.push(corridor);
    return corridor;
  }

  useCreateDottedCorridor(
    positions: any[],
    //@ts-ignore
    color: string = "#FF8C00",
    width: number = 6000,
    //@ts-ignore
    gapColor: string = "white"
  ) {
    let corridor = this.viewer.entities.add({
      corridor: {
        positions: new Cesium.CallbackProperty(function () {
          return positions;
        }, false),
        show: true,
        width: width,
        material: Cesium.Color.LIGHTSKYBLUE,
      },
    });
    entities.push(corridor);
    return corridor;
  }

  /**
   * @description 返回两个笛卡尔坐标的中点 (笛卡尔坐标)
   * @param cartesian3x
   * @param cartesian3y
   */
  getCartesian3sCenter(cartesian3x: any, cartesian3y: any) {
    if (!cartesian3x || !cartesian3y) return;
    return new Cesium.Cartesian3(
      (cartesian3x.x + cartesian3y.x) / 2,
      (cartesian3x.y + cartesian3y.y) / 2,
      (cartesian3x.z + cartesian3y.z) / 2
    );
  }

  useCreateTextLabel(
    position: any,
    text: string,
    name: string = "",
    color: string = "#FF0000"
  ) {
    if (!position) return;
    let label = this.viewer.entities.add({
      name: name || "",
      position: position,
      label: {
        text: text || "",
        font: "20px Helvetica",
        showBackground: true,
        fillColor: Cesium.Color.fromCssColorString(color),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 3,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        eyeOffset: new Cesium.Cartesian3(0.0, 10000, -1000),
      },
    });
    entities.push(label);
    return label;
  }

  useCreateLabel(
    position: Cesium.Cartesian3,
    text: string,
    name: string = "",
    color: string = "#FF0000"
  ) {
    if (!position) return;
    let label = this.viewer.entities.add({
      name: name || "",
      position: position,
      label: {
        text: text || "",
        font: "20px Helvetica",
        fillColor: Cesium.Color.fromCssColorString(color),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 3,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.RIGHT,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        eyeOffset: new Cesium.Cartesian3(0.0, 0.0, 0),
        pixelOffset: new Cesium.Cartesian2(45, 20),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
    });
    entities.push(label);
    return label;
  }
}
