import * as Cesium from "@onemapkit/cesium";

export default async function getHeight(
  viewer: Cesium.Viewer,
  lon: number,
  lat: number,
  isModel: boolean = true
): Promise<number> {
  if (isModel) {
    //  let height = viewer.scene.sampleHeight(
    //   new Cesium.Cartographic(
    //     Cesium.Math.toRadians(lon),//转换成弧度
    //     Cesium.Math.toRadians(lat),//弧度
    //   )
    // );
    //     const positions = [
    //       Cesium.Cartographic.fromDegrees(86.925145, 27.988257),
    //       Cesium.Cartographic.fromDegrees(87.0, 28.0)
    //      ];

    //      const terrainProvider = await Cesium.createWorldTerrainAsync();
    // const positions = [lon, lat];
    // const updatedPositions =    Cesium.sampleTerrainMostDetailed(terrainProvider, [Cesium.Cartographic.fromDegrees(lon, lat)]);

    // 使用Cesium的sampleTerrainMostDetailed函数查询3D模型上的高程
    await Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [
      Cesium.Cartographic.fromDegrees(lon, lat),
    ])
      .then(function (updatedPositions) {
        // updatedPositions 是Cartographic对象的数组
        if (updatedPositions.length > 0) {
          console.log("1111111111111111111111");
          return updatedPositions[0].height; // 高程
        } else {
          console.log("2222222222222222222222");
          return 0;
        }
      })
      //@ts-ignore
      .catch(function (error) {
        console.log("3333333333333333333");
        return 0;
      });
  } else {
    // 使用Cesium的sampleTerrain函数查询高程
    await Cesium.sampleTerrain(viewer.terrainProvider, 12, [
      Cesium.Cartographic.fromDegrees(lon, lat),
    ])
      .then(function (updatedPositions) {
        // updatedPositions 是Cartographic对象的数组
        if (updatedPositions.length > 0) {
          console.log("4444444444444444");
          return updatedPositions[0].height; // 高程
        } else {
          console.log("5555555555555555555555");
          return 0;
        }
      })
      //@ts-ignore
      .catch(function (error) {
        console.log("6666666666666666666");
        return 0;
      });
  }

  console.log("57777777777777777775");
  return 0;

  // var positions = Cesium.Cartographic.fromDegrees(lon, lat);
  // Cesium.when(
  //   new Cesium.sampleTerrain(window.cfglobe._viewer.terrainProvider, level, [
  //     positions,
  //   ]),
  //   function (updatedPositions) {
  //     self.height = updatedPositions[0].height;
  //   }
  // );
}
