import * as CesiumTools from "@onemapkit/cesium-tools";
import * as Cesium from "@onemapkit/cesium";
import { geometryType } from "../../onemapkit";
/**
 * 绘制点
 * @param callback 回调函数
 */
export function DrawGrephicPoint(viewer: Cesium.Viewer, callback: Function) {
  const drawPoint = new CesiumTools.DrawTool(
    viewer,
    CesiumTools.DrawMod.Point,
    { ground: true, classificationType: Cesium.ClassificationType.BOTH }
  );
  //   drawPoint.mouseMoveEvent.addEventListener((data) => {});
  drawPoint.drawEndEvent.addEventListener((data) => {
    callback(geometryType.point, data);
  });
  drawPoint.start();
}
/**
 * 绘制点
 * @param callback 回调函数
 */
export function DrawGrephicPolyline2d(
  viewer: Cesium.Viewer,
  DrawEndCallback: Function,
  DrawingCallback: Function
) {
  const drawLine = new CesiumTools.DrawTool(viewer, CesiumTools.DrawMod.Line, {
    ground: true,
    classificationType: Cesium.ClassificationType.BOTH,
  });
  if (DrawingCallback) {
    drawLine.drawEvent.addEventListener((data) => {
      DrawingCallback(geometryType.polyline, data);
    });
  }
  drawLine.drawEndEvent.addEventListener((data) => {
    DrawEndCallback(geometryType.polyline, data);
  });
  drawLine.start();
}

/**
 * 绘制点
 * @param callback 回调函数
 */
export function DrawGrephicPolygon2d(
  viewer: Cesium.Viewer,
  DrawEndCallback: Function,
  DrawingCallback: Function
) {
  const drawLine = new CesiumTools.DrawTool(
    viewer,
    CesiumTools.DrawMod.Polygon,
    {
      ground: true,
      classificationType: Cesium.ClassificationType.BOTH,
    }
  );
  if (DrawingCallback) {
    drawLine.drawEvent.addEventListener((data) => {
      DrawingCallback(geometryType.polyline, data);
    });
  }
  drawLine.drawEndEvent.addEventListener((data) => {
    DrawEndCallback(geometryType.polyline, data);
  });
  drawLine.start();
}
