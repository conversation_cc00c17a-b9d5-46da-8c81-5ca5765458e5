import * as Cesium from "@onemapkit/cesium";
export class ImageryLayerClass {
  public Viewer: Cesium.Viewer = undefined as any;
  public ImageLayerCollection = new Map<string, Array<string>>([
    /** 1.Imagery */
    ["BaseImageLayer", []],
    ["ImageryLayer", []],
    ["PolygonLayer", []],
    ["PointLineLayer", []],
    ["OtherLayer", []],
  ]);

  //#region 1.Imagery  ImageryLayerCollection

  /**
   * 将图层置顶，ImageLayers（这里的ImageryLayer是带有layerType属性值的图层）
   * @param  {Cesium.ImageryLayer} [params] BaseImageLayer,ImageryLayer,PolygonLayer,PointLineLayer,OtherLayer
   */
  public setTopIndex(params: any) {
    const topidx = this.getMaxIndex(params.layerType);
    const inFun = (_params: any) => {
      let tmpidx = this.Viewer.imageryLayers.indexOf(params);
      if (tmpidx < topidx) {
        this.Viewer.imageryLayers.raise(_params);
        inFun(_params);
      }

      if (tmpidx > topidx) {
        this.Viewer.imageryLayers.lower(_params);
        inFun(_params);
      }
    };
  }

  /**
   * 将图层底部，ImageLayers
   * @param  {Cesium.ImageryLayer} [params] BaseImageLayer,ImageryLayer,PolygonLayer,PointLineLayer,OtherLayer
   */
  public setBottomIndex(params: any) {
    const topidx = this.getMinIndex(params.layerType);
    const inFun = (_params: any) => {
      let tmpidx = this.Viewer.imageryLayers.indexOf(params);
      if (tmpidx < topidx) {
        this.Viewer.imageryLayers.raise(_params);
        inFun(_params);
      }

      if (tmpidx > topidx) {
        this.Viewer.imageryLayers.lower(_params);
        inFun(_params);
      }
    };
  }
  /**
   * 上移图层 ，ImageLayers（这里的ImageryLayer是带有layerType属性值的图层）
   * @param  {Cesium.ImageryLayer} [params] BaseImageLayer,ImageryLayer,PolygonLayer,PointLineLayer,OtherLayer
   */
  public moveUpIndex(params: any) {
    const topidx = this.getMaxIndex(params.layerType);
    let tmpidx = this.Viewer.imageryLayers.indexOf(params);
    if (tmpidx < topidx) {
      this.Viewer.imageryLayers.lower(params);
    }
  }
  /**
   * 下移图层 ，ImageLayers（这里的ImageryLayer是带有layerType属性值的图层）
   * @param  {Cesium.ImageryLayer} [params] BaseImageLayer,ImageryLayer,PolygonLayer,PointLineLayer,OtherLayer
   */
  public moveDownIndex(params: any) {
    const bottmidx = this.getMinIndex(params.layerType);
    let tmpidx = this.Viewer.imageryLayers.indexOf(params);
    if (tmpidx > bottmidx) {
      this.Viewer.imageryLayers.lower(params);
    }
  }

  /** 获取最大 index ImageLayers
   * [BaseImageLayer,ImageryLayer,PolygonLayer,PointLineLayer,OtherLayer]
   * @param {ILayerType} layerType
   * @returns
   */
  public getMaxIndex(layerType: string): any {
    switch (layerType) {
      case "BaseImageLayer":
        return this.ImageLayerCollection.get(layerType as string)?.length;
      case "ImageryLayer":
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get(layerType as string)?.length || 0)
        );
      case "PolygonLayer":
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get("ImageryLayer")?.length || 0) +
          (this.ImageLayerCollection.get(layerType as string)?.length || 0)
        );
      case "PointLineLayer":
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get("ImageryLayer")?.length || 0) +
          (this.ImageLayerCollection.get("PolygonLayer")?.length || 0) +
          (this.ImageLayerCollection.get(layerType as string)?.length || 0)
        );
      default:
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get("ImageryLayer")?.length || 0) +
          (this.ImageLayerCollection.get("PolygonLayer")?.length || 0) +
          (this.ImageLayerCollection.get("PointLineLayer")?.length || 0) +
          (this.ImageLayerCollection.get("OtherLayer")?.length || 0)
        );
    }
  }
  /** 获取最小 index ImageLayers
   * [BaseImageLayer,ImageryLayer,PolygonLayer,PointLineLayer,OtherLayer]
   * @param {ILayerType} layerType
   * @returns
   */
  public getMinIndex(layerType: string): any {
    switch (layerType) {
      case "BaseImageLayer":
        return 0;
      case "ImageryLayer":
        return this.ImageLayerCollection.get("BaseImageLayer")?.length || 0;
      case "PolygonLayer":
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get("ImageryLayer")?.length || 0)
        );
      case "PointLineLayer":
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get("ImageryLayer")?.length || 0) +
          (this.ImageLayerCollection.get("PolygonLayer")?.length || 0)
        );
      default:
        return (
          (this.ImageLayerCollection.get("BaseImageLayer")?.length || 0) +
          (this.ImageLayerCollection.get("ImageryLayer")?.length || 0) +
          (this.ImageLayerCollection.get("PolygonLayer")?.length || 0) +
          (this.ImageLayerCollection.get("PointLineLayer")?.length || 0)
        );
    }
  }
  public layerAddedEvent = (params: any) => {
    if (
      this.ImageLayerCollection.get(params.layerType as string)?.some(
        (itm) => itm == params.layerid
      ) == false
    ) {
      this.ImageLayerCollection.get(params.layerType as string)?.push(
        params.layerid
      );
    }
    this.setTopIndex(params);
  };
  public layerRemovedEvent = (params: any) => {
    let idx: any = this.ImageLayerCollection.get(
      params.layerType as string
    )?.findIndex((itm) => itm == params.layerid);
    if (idx > -1) {
      this.ImageLayerCollection.get(params.layerType as string)?.splice(idx, 1);
    }
  };
  //#endregion

  /**
   * PrimitiveLayer
   * @param {Viewer} viewer 视图对象
   * @param {Object} [options]
   * @param {Cesium.Event} [options.layerAddedEvent=undefined] 默认的 layerAddedEvent
   * @param {Cesium.Event} [options.layerRemovedEvent=undefined] 默认的 layerRemovedEvent
   */
  constructor(viewer: Cesium.Viewer, options: any = {}) {
    this.Viewer = viewer;
    //#region 1.初始化非标准化的 imageryLayers
    if (options?.layerAddedEvent) {
      this.layerAddedEvent = options.layerAddedEvent;
    }
    if (options?.layerRemovedEvent) {
      this.layerRemovedEvent = options.layerRemovedEvent;
    }

    /** 已经存在的 */
    let _length = this.Viewer.imageryLayers.length;
    for (let i = 0; i < _length; ++i) {
      this.layerAddedEvent(this.Viewer.imageryLayers.get(i));
    }
    //#endregion

    //#region 2.Add/Removed Event
    this.Viewer.imageryLayers.layerAdded.addEventListener((arg) => {
      this.layerAddedEvent(arg);
    });
    this.Viewer.imageryLayers.layerRemoved.addEventListener((arg) => {
      this.layerRemovedEvent(arg);
    });
    //#endregion
  }
}
