import proj4 from 'proj4';
/**
 * 基于proj4.js的坐标转换工具，目前支持4490、4524、4545、4326、3857、2360、2412坐标系相互转换
 */

// EPSG:4490 - China Geodetic Coordinate System 2000
proj4.defs('EPSG:4490', '+proj=longlat +ellps=GRS80 +no_defs +type=crs');

// EPSG:4524 - CGCS2000 / 3-degree Gauss-Kruger CM 105E
proj4.defs("EPSG:4524","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs");

// EPSG:4545 - CGCS2000 / 3-degree Gauss-Kruger zone 15
proj4.defs("EPSG:4545","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs");

// EPSG:4326 - WGS 84
proj4.defs("EPSG:4326","+proj=longlat +datum=WGS84 +no_defs +type=crs");

// EPSG:3857 - WGS 84 / Pseudo-Mercator
proj4.defs("EPSG:3857","+proj=merc +a=6378137 +b=6378137 +lat_ts=0 +lon_0=0 +x_0=0 +y_0=0 +k=1 +units=m +nadgrids=@null +wktext +no_defs +type=crs");

// EPSG:2360 - NAD83 / Pennsylvania South
proj4.defs("EPSG:2360","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=IAU76 +units=m +no_defs +type=crs");

// EPSG:2412 - JAD69 / Jamaica Grid
proj4.defs("EPSG:2412","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=krass +towgs84=11.911,-154.833,-80.079,0,0,0,0 +units=m +no_defs +type=crs");

// 定义几何类型
type Point = { type: "point", x: number, y: number };
type Polyline = { type: "polyline", paths: [number, number][] };
type Polygon = { type: "polygon", rings: [number, number][] };
type Circle = { type: "circle", rings: [number, number][] };
type Rectangle = { type: "rectangle", rings: [number, number][] };

// 定义可能的几何类型
type Geometry = Point | Polyline | Polygon | Circle | Rectangle;

// 定义GeoJSON坐标类型
type GeoJSONCoordinate = [number, number] | [number, number, number] | GeoJSONCoordinate[];

// 定义转换函数
function convertToGeoJSONCoordinates(geometrys: any, sourceProjection: number, targetProjection: number): GeoJSONCoordinate {
  let coordinates: GeoJSONCoordinate;
  let geometry = geometrys as Geometry;
	// 根据几何类型进行坐标转换
	switch (geometry.type.toLocaleLowerCase()) {
    case "point":
      if (geometrys.coordinates) {
        coordinates = transformCoordinates(
          [geometrys.coordinates[0], geometrys.coordinates[1]],
          `EPSG:${sourceProjection}`,
          `EPSG:${targetProjection}`
        );
      } else {
        geometry = geometry as Point;
        coordinates = transformCoordinates(
          [geometry.x, geometry.y],
          `EPSG:${sourceProjection}`,
          `EPSG:${targetProjection}`
        );
      }
      break;
    case "polyline":
      geometry = geometry as Polyline;
      coordinates = geometry.paths.map((path) =>
        path.map((coordinatePair: any) =>
          transformCoordinates(
            coordinatePair,
            `EPSG:${sourceProjection}`,
            `EPSG:${targetProjection}`
          )
        )
      );
      break;
    case "rectangle":
    case "circle":
    case "polygon":
    case "multipolygon":
      if (geometrys.coordinates) {
        coordinates = geometrys.coordinates.map((ring: any) =>
          ring.map((coordinatePair: any) =>
            transformCoordinates(
              coordinatePair,
              `EPSG:${sourceProjection}`,
              `EPSG:${targetProjection}`
            )
          )
        );
      } else {
        geometry = geometry as Polygon;
        coordinates = geometry.rings.map((ring) =>
          ring.map((coordinatePair: any) =>
            transformCoordinates(
              coordinatePair,
              `EPSG:${sourceProjection}`,
              `EPSG:${targetProjection}`
            )
          )
        );
      }
      break;
    default:
      throw new Error(`Unsupported geometry type`);
  }

  return coordinates;
}

// 定义坐标转换函数
function transformCoordinates(
  coordinates: [number, number],
  sourceProjection: string,
  targetProjection: string
): [number, number] {
	let source = sourceProjection
	let target = targetProjection
	if (!source.includes('EPSG')) {
		source = `EPSG:${sourceProjection}`
	}
	if (!target.includes('EPSG')) {
		target = `EPSG:${targetProjection}`
	}

  // 如果坐标系相等直接返回
  if(source === target){
    return coordinates;
  }

  // 检查是否proj4支持源坐标系和目标坐标系
  if (!proj4.defs(source)) {
    throw new Error(`Unsupported source projection: ${source}`);
  }
  if (!proj4.defs(target)) {
    throw new Error(`Unsupported target projection: ${target}`);
  }

  const [x, y] = proj4(source, target, coordinates);
  return [x, y];
}

export {
	convertToGeoJSONCoordinates,
	transformCoordinates
}