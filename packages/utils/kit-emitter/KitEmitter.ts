import mitt from 'mitt';
import type { KitEventTypes } from './types';

const emitter = mitt<KitEventTypes>();

export const KitEmitter = {
    onKit: <E extends keyof KitEventTypes>(event: E, callback: (data: KitEventTypes[E]) => void) => {
        emitter.on(event, callback as any);
    },

    emitKit: <E extends keyof KitEventTypes>(event: E, data: KitEventTypes[E]) => {
        emitter.emit(event, data);
    },

    offKit: <E extends keyof KitEventTypes>(event: E, callback?: (data: KitEventTypes[E]) => void) => {
        emitter.off(event, callback as any);
    },

    clearAll: () => {
        emitter.all.clear();
    },
};

export default KitEmitter;
