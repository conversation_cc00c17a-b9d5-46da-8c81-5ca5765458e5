import { markRaw, ref } from "vue";
import * as <PERSON>sium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";
// import throttle from "lodash/throttle";
import {
  mapType,
  type IPosition,
  type IMapLayer,
  Utils,
  type ITdtResultType,
  type ITdtDataItemType,
  CommonEventEnum,
} from "../onemapkit";

import * as base from "../base/base";

import arcMap from "./arcmap3";
import czmMap from "./czmmap";
import getExtent from "./cesium-tools/czmGetExtent";

// import { loadlic, _loadlogo } from "../license/allowlic";

export class OnemapClass {
  public isEnable: boolean = false;
  public test: any = undefined;
  public MapControlName: string = "mainMapControl";
  protected _impl: any = undefined;

  /** OnemapClass构造函数
   * @param {mapType} _mapType 可选参数，如果此处不带参数就必须在实例化后用setMapType方法赋值
   */
  constructor(_mapType?: mapType) {
    if (_mapType) {
      this.MapType.value = _mapType;
      this._impl =
        _mapType == mapType.cesium
          ? markRaw(new czmMap())
          : markRaw(new arcMap());

      // loadlic
      //   .then((lic: any) => {
      //     this.isEnable = lic;
      //     _loadlogo();
      //   })
      //   .catch(() => {
      //     this.isEnable = false;
      //     _loadlogo();
      //   });
    }
  }
  /**
   *销毁impl对象
   * @param {mapType | String} _mapType
   */
  public implDestroy(_mapType: String) {
    try {
      if (this.MapViewer) {
        this.MapViewer.destroy();
        if (_mapType == mapType.cesium) {
          Cesium.destroyObject(this._impl);
        }
        this._impl = null;
      }
    } catch (e) {
      Cesium.destroyObject(this._impl);
      this._impl = null;
      console.log("Viewer对象释放异常！");
    }
  }

  //#region 0.OnemapClass 类基本属性

  /** arcgis 3 使用，获取esriobj */
  private _esriObj: any = null;
  public get EsriObj() {
    return this._esriObj;
  }
  public get Arcgis3Tools() {
    if (this.MapType.value == mapType.arcgis) {
      return this._impl?.arcgisTools;
    } else {
      return undefined;
    }
  }
  public get isImplNull() {
    return this._impl == null ? true : false;
  }
  public get MapViewer() {
    return this._impl?.MapViewer;
  }
  /** 挂载Cesium.Viewer对象或ArcGIS的Map对象
   * @param {Cesium.Viewer | Map} _mapViewer
   * @param {Object } _esriObj ArcgisJS的组件集对象，当在二维模式下必带参数
   */
  public setMapViewer(_mapViewer: any, _esriObj?: any) {
    this._esriObj = _esriObj;
    (this._impl as any)?.setMapViewer(_mapViewer, _esriObj);
  }
  // public MapRealtimeHandler: Function = undefined as any;
  /** 该变量只是地图改变的响应式状态，不能作为OnemapClass的MapType属性赋值，
   * 否则无法正常实例化OnemapClass，赋值请用setMapType 或实例化时带mapType参数 */
  public MapType = ref(mapType.arcgis);
  /**
   * 改变地图后，去实例化对应实现类
   * @param MapType mapType
   */
  public setMapType(_inMapType: mapType) {
    this.MapType.value = _inMapType;
    this._impl =
      this.MapType.value == mapType.cesium
        ? markRaw(new czmMap())
        : markRaw(new arcMap());
  }

  public set MaximumMemory(val: any) {
    this._impl.MaximumMemory = val;
  }
  public get MaximumMemory(): CesiumTools.Cesium3DTilesMaximumTotalMemoryUsageControl {
    return this._impl?.MaximumMemory;
  }

  //#endregion

  //#region 1.地图加载 isMapReady MapReadyHandler
  /** 存放地图是否加载完毕状态参数 */
  public isMapReady = ref(false);
  /** 在Map组件中调用
   */
  public MapReadyHandler = undefined as any;

  /**
   * Cesium数据加载完毕后的事件
   * @param {Object} [_Options] 参数
   * @param {boolean} [_Options.depthTestAgainstTerrain = false ] 深度检测
   * @param {number} [_Options.MaxMemoryNum = 512] 缓存大小
   * @param {number} [_Options.maximumScreenSpaceError = 2 ] 抗锯齿
   * @param {boolean} [_Options.highDynamicRange = false ] 高动态范围渲染
   * @param {boolean} [_Options.skyAtmosphere = false ] 高动态范围渲染
   * @param {boolean} [_Options.skyBox = false ] 天空盒
   * @param {boolean} [_Options.fxaa = false ] 抗锯齿
   * @param {boolean} [_Options.fog = false ] 雾
   */
  public setMapReadyEvent = (_Options: any) => {
    /** 基本参数设置 */
    this.MapViewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
    );
    this.MapViewer.scene.globe.depthTestAgainstTerrain =
      _Options?.depthTestAgainstTerrain || false;
    //设置缓存
    this.MaximumMemory.maximumTotalMemoryUsage = _Options?.MaxMemoryNum || 512;
    //片抗锯齿功能
    this.MapViewer.scene.fxaa = _Options.fxaa || false;
    //数值越高，性能越好，但视觉质量越差。默认值为2。该值在0.66~1.33之间地图清晰度最高。
    this.MapViewer.scene.globe.maximumScreenSpaceError =
      _Options.maximumScreenSpaceError || 2;

    //改变地图灰度系数
    //let layer0 = viewer.scene.imageryLayers.get（0）；layer0.gamma = 0.66；
    //改变当前地图的组织结构
    // layer.minificationFilter-Cesium.TextyreMinificationFiler.NEAREST;
    // layer.magnificationFilter=Cesium.TextureMagnificationFilter.NEAREST
    //minificationFilter，magnificationFilter表示缩小和放大瓦片数据的过滤方式。
    //默认值为LINEAR线性结构，大部分地图调整为最近方式过滤能够有效提升地图清晰度。

    this.MapViewer.scene.highDynamicRange = _Options.fxaa || false; //关闭高动态范围渲染
    this.MapViewer.scene.skyAtmosphere.show = _Options.skyAtmosphere || false; //关闭大气
    this.MapViewer.scene.skyBox.show = _Options.skyBox || false; //关闭天空盒
    this.MapViewer.scene.fog.enabled = _Options.fog || false; //关闭雾
    // 地图加载完毕后的回调
    if (_Options.MapReadyEvent) {
      _Options.MapReadyEvent(this?.MapViewer, {
        mapType: this.MapViewer instanceof Cesium.Viewer ? "cesium" : "arcgis",
      });
    }
    if (_Options?.mapextent) {
      this?.fullExtent(_Options?.mapextent);
    }
  };
  //#endregion

  //#region 2.地图图层存储

  /** 地图加载异步事件的回调函数集合 */
  public LayerCallbackCollection: Map<string, Map<string, Function>> = new Map<
    string,
    Map<string, Function>
  >();
  /** 注册地图加载异步事件回调函数
   * @param {Object} options 参数选项
   * @param {string | Array<string>} options.layerid 需要挂在回调函数图层的layerid 或Array<layerid>集合
   * @param {string} options.CallBackName 注册的回调函数名称
   * @param { (layer: any, option: IMapLayer) => any} options.CallBack 异步回调函数
   */
  public AddLayerCallBack(options: {
    layerid: string | Array<string>;
    CallBackName: string;
    CallBack: (layer: any, option: IMapLayer) => any;
  }) {
    const fun = (
      _layerid: string,
      _CallBackName: string,
      _CallBack: (layer: any, option: IMapLayer) => any
    ) => {
      if (this.LayerCallbackCollection.has(_layerid) == false) {
        this.LayerCallbackCollection.set(_layerid, new Map<string, Function>());
      }
      this.LayerCallbackCollection.get(_layerid)?.set(
        _CallBackName,
        _CallBack as any
      );
    };
    if (typeof options.layerid === "string") {
      fun(options.layerid as any, options.CallBackName, options.CallBack);
    } else {
      (options.layerid as any).forEach((lyrid: string) => {
        fun(lyrid, options.CallBackName, options.CallBack);
      });
    }
  }
  /**
   * 移除地图加载异步事件回调函数
   * @param {String} layerid 图层的layerid ，如果省略就移除所有图层对象绑定的所有异步函数对象
   * @param {String} CallBackName 异步函数名称，如果省略就移除该图层对象绑定的所有异步函数对象
   */
  public RemoveLayerCallBack(layerid?: string, CallBackName?: string) {
    if (layerid) {
      if (CallBackName) {
        if (this.LayerCallbackCollection.has(layerid)) {
          this.LayerCallbackCollection.get(layerid)?.delete(CallBackName);
        }
      } else {
        this.LayerCallbackCollection.delete(layerid);
      }
    } else {
      this.LayerCallbackCollection.clear();
    }
  }

  public get ImageryLayerClass() {
    return this._impl?.ImageryLayerClass ?? null;
    // return this.MapViewer["ImageLayers"];
  }

  /**
   * 二维状态下可以。当前图层加载完成的状态值
   */
  public get loaded(): Map<
    string,
    { type: string; layerid: string; success: boolean }
  > {
    if (this.MapType.value == mapType.arcgis) {
      return this._impl?.loaded;
    } else {
      return false as any;
    }
  }

  /*
  在 TypeScript 中，你可以使用 Proxy 来监听变量的变化，直到它等于某个值时返回 true。以下是一个简单的示例：
typescript复制代码
function waitForValue(obj: { [key: string]: any }, key: string, targetValue: any): Promise<boolean> {
    return new Promise((resolve) => {
        const handler = {
            set(target: any, prop: string, value: any) {
                target[prop] = value;
                if (prop === key && value === targetValue) {
                    resolve(true);
                }
                return true;
            }
        };

        const proxy = new Proxy(obj, handler);
        return proxy;
    });
}

// 示例用法
const myObject = { status: 'waiting' };
const proxyObject = waitForValue(myObject, 'status', 'done').then((result) => {
    console.log(result); // 当 status 变为 'done' 时会输出 true
});

// 变更对象的值
setTimeout(() => {
    myObject.status = 'done';
}, 2000);
这个例子使用 Proxy 监听对象属性的变化。你可以根据需要调整和扩展。
*/
  //******************************************** */
  public createReactive<T extends object>(obj: T): T {
    return new Proxy(obj, {
      set(target, property, value) {
        console.log(`Property ${String(property)} changed to ${value}`);
        target[property as keyof T] = value;
        return true;
      },
    });
  }

  public get ArcGisDynamicServices() {
    return this._impl?._ArcGisDynamicServices;
  }
  //存储上一模式的图层显示顺序
  public OldImageLayerCollection: Map<string, Array<string>> = undefined as any;

  //#endregion

  //#region 3.地图事件注册 -------------------->EventType = MouseEventType.MOUSE_STOP || MouseEventType.MOUSE_STOP2
  public get Handler() {
    return this._impl?.Handler;
  }
  /** 注册地图事件
   * @param { Cesium.ScreenSpaceEventType | base.MouseEventType | string } EventType 事件类型
   * @param {Object | any} options 事件处理参数
   * @param {Function} [options.handler] 事件处理逻辑
   * @param {boolean} [options.isThrottle = false] 事件处理是否节流参数
   * @param {string} [options.handlerName] 事件处理函数名
   * @param {number} [options.delayTime = 300] 处理延迟(毫秒)，EventType = MouseEventType.MOUSE_STOP || MouseEventType.MOUSE_STOP2
   * @param {Function} [options.RealtimeHandler] 实时事件处理逻辑，EventType = MouseEventType.MOUSE_STOP || MouseEventType.MOUSE_STOP2
   * @returns 处理对象，可用于 removeMapEventHandler 函数移除
   */
  public setMapEventHandler(
    EventType: Cesium.ScreenSpaceEventType | base.MouseEventType | string,
    options?: any
  ): any {
    return this._impl?.setMapEventHandler(EventType, options);
  }
  /** 移除地图事件
   * @param {Cesium.ScreenSpaceEventType | base.MouseEventType | string} EventType 事件类型
   * @param {Object | any} options 事件处理参数
   * @param {string} [options.handlerName] 注册的事件处理函数
   * @param {Object | any} options.Handler 移除地图事件对象
   */
  public removeMapEventHandler(
    EventType: Cesium.ScreenSpaceEventType | base.MouseEventType | Number,
    options?: any
  ) {
    return this._impl?.removeMapEventHandler(EventType, options);
  }
  /** 清除地图事件
   * @param {Map<string | base.MouseEventType |  Cesium.ScreenSpaceEventType, any>} [events = _inHandlerMap]  默认为内部变量 _inHandlerMap
   */
  public clearMapEventHandler(
    events?: Map<
      string | base.MouseEventType | Cesium.ScreenSpaceEventType,
      any
    >
  ) {
    return this._impl?.clearMapEventHandler(events);
  }

  //#region Onemap组件相关的事件
  /** 注册Onemap事件
   * @param {Object} options 参数
   * @param {string} options.group 事件实现组名
   * @param {string} options.key 事件逻辑名称
   * @param {Function} options.event 事件逻辑实现
   */
  public setOnemapEventHandler(options: {
    group: string;
    key: string;
    event: any;
  }): any {
    return this._impl?.setOnemapEventHandler(options);
  }
  /** 删除Onemap事件
   * @param {Object} options 参数
   * @param {string} options.group 事件实现组名
   * @param {string} options.key 事件逻辑名称
   */
  public removeOnemapEventHandler(options: {
    group: string;
    key?: string;
  }): any {
    return this._impl?.setOnemapEventHandler(options);
  }
  /** 执行Onemap事件
   * @param {Object} options 参数
   * @param {string} options.group 事件实现组名
   * @param {string} options.key 事件逻辑名称
   * @param {string} options.callback 事件逻辑名称
   */
  public executeOnemapEvent(options: {
    group: string;
    key?: string;
    callback?: Function;
  }) {
    return this._impl?.executeOnemapEvent(options);
  }
  //#endregion

  //#region Maplayer 与图层资源相关的事件
  /** 注册Maplayer与图层资源相关的事件
   * @param {Object} options 参数
   * @param {string} options.key 事件逻辑名称
   * @param {Function} options.event 事件逻辑实现
   */
  public setMaplayerEventHandler(options: {
    key: string;
    event?: Function | any;
  }): any {
    return this._impl?.setMaplayerEventHandler(options);
  }
  /** 删除Maplayer与图层资源相关的事
   * @param {string} key 事件逻辑名称
   */
  public removeMaplayerEventHandler(key?: string): any {
    return this._impl?.removeMaplayerEventHandler(key);
  }
  /** 执行Maplayer与图层资源相关的事
   * @param {Object} options 参数
   * @param {string} options.key 发起事件名称
   * @param {string} options.eventkey 事件逻辑名称
   * @param {string} options.param 事件参数
   * @param {string} options.callback 事件逻辑名称
   */
  public executeMaplayerEvent(options: {
    key: string;
    eventkey?: string;
    param?: any;
    callback?: Function;
  }) {
    return this._impl?.executeMaplayerEvent(options);
  }

  // 自定义事件组 begin
  /**
   * 自定义事件, 谨慎使用，组件释放的时候一定要清理掉
   * 可以在多个组件中添加监听同一事件，方便组件之间的数据同步
   */
  protected _EventMap = new Map();

  /**
   * 添加事件监听器
   * @param event 事件
   * @param listener 监听器
   */
  addEventListener(event: CommonEventEnum | string, listener: Function) {
    if (!this._EventMap.has(event)) {
      this._EventMap.set(event, new Set());
    }
    this._EventMap.get(event).add(listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件
   * @param listener 监听器，为空则移除所有监听器
   */
  removeEventListener(event: CommonEventEnum | string, listener?: Function) {
    if (this._EventMap.has(event)) {
      if (listener) {
        this._EventMap.get(event).delete(listener);
      } else {
        this._EventMap.delete(event);
      }
    }
  }

  /**
   * 触发事件
   * @param event 事件
   * @param data 事件参数
   */
  dispatchEvent(event: CommonEventEnum | string, data?: any) {
    if (this._EventMap.has(event)) {
      this._EventMap.get(event).forEach((listener: Function) => listener(data));
    }
  }
  // 自定义事件组 end

  //#endregion

  public isMapClick: boolean = false;
  /** 鼠标点击事件实现函数 (event: any) => void */
  public _MapClickEvent: (event: any) => void = undefined as any;
  private _mapclickevent: any = undefined;
  /** 注册鼠标点击监听事件 */
  public setMapClickHandler = (option?: {
    handlerName: string;
    handler: (avg: any) => any;
  }): any => {
    if (option) {
      return this.setMapEventHandler(Cesium.ScreenSpaceEventType.LEFT_CLICK, {
        isThrottle: false,
        handlerName: option.handlerName,
        handler: option.handler,
      });
    } else {
      if (this._MapClickEvent == undefined) {
        console.info("yusy>Onemap._MapClickEvent函数未定义");
        return;
      }

      this._mapclickevent = this.setMapEventHandler(
        Cesium.ScreenSpaceEventType.LEFT_CLICK,
        {
          isThrottle: false,
          handlerName: "defaultmapleftclickevent",
          handler: this._MapClickEvent,
        }
      );
      return this._mapclickevent;
    }
  };
  public removeMapClickHandler = (option?: {
    handlerName: string;
    handler?: (avg: any) => void;
  }) => {
    if (option) {
      this.removeMapEventHandler(Cesium.ScreenSpaceEventType.LEFT_CLICK, {
        handlerName: option.handlerName,
        handler: option.handler,
      });
    } else {
      this.removeMapEventHandler(Cesium.ScreenSpaceEventType.LEFT_CLICK, {
        handlerName: "defaultmapleftclickevent",
        handler: this._mapclickevent,
      });
    }
  };
  /** 获取实时鼠标点击点坐标
   * @param {Cesium.Cartesian2} CartPoint
   * @returns
   */
  public getMapClickPoint(CartPoint: Cesium.Cartesian2): any {
    return this._impl?.getMapClickPoint(CartPoint);
  }

  /**
   * 获取实时鼠标移动点坐标
   * @param {Cesium.Cartesian2} CartPoint
   * @returns
   */
  public getMapRealtimePoint(CartPoint: Cesium.Cartesian2): any {
    if (this._impl.getMapRealtimePoint)
      return this._impl.getMapRealtimePoint(CartPoint);
  }

  //#endregion

  //#region  4.图层操控:图层加载 图层顺序调整 地图状态操控
  public get LayerPromise(): Map<
    string,
    {
      option: IMapLayer;
      Promise: Promise<any>;
    }
  > {
    return this._impl?.LayerPromise;
  }
  /** 获取图层的Promise对象
   * @param {string} [layerid] layerid
   * @returns {Promise<any>} Promise<any>
   */
  public getLayerPromise(layerid: string): Promise<any> {
    return this._impl?.getLayerPromise(layerid);
  }
  //#region 4.1 图层加载

  /**综合图层加载方法,叶节点或叶节点的SubLayers属性中的IMapLayer对象集合
   * @param {IMapLayer} options Option 类型
   * @param {({ layer: Promise<any>, option: IMapLayer })=>any} callback 回调函数
   * @returns Promise<Array<{ maplayer: Object | undefined; option: IMapLayer }>>
   */
  AddLayer(
    options: IMapLayer,
    callback?: any
  ): Promise<
    Array<{
      maplayer: Object | undefined;
      option: IMapLayer;
      layerid: string;
      success: boolean;
      type: string;
    }>
  > {
    if (options?.children) {
      Utils.ThrowError("不是叶节点对象");
      return new Promise((resolve) => {
        resolve([
          {
            maplayer: undefined,
            option: options,
            layerid: options.layerid as any,
            success: false,
            type: "add",
          },
        ]);
      });
    }
    const rtn = this._impl?.AddLayer(options, callback);
    this.dispatchEvent(CommonEventEnum.AddLayer, options);
    return rtn;
  }
  /**加载由叶节点构成的IMapLayer对象数组
   * @param {Array<IMapLayer>} options
   * @param {({ layer: Promise<any>, option: IMapLayer })=>any} callback 回调函数
   * @returns {Promise<Array<{ maplayer: Object | undefined; option: IMapLayer }>>} Promise<Array<{ maplayer: Object | undefined; option: IMapLayer }>>
   */
  AddLayers(
    options: Array<IMapLayer>,
    callback?: any
  ): Promise<
    Array<{
      maplayer: Object | undefined;
      option: IMapLayer;
      layerid: string;
      success: boolean;
      type: string;
    }>
  > {
    const rtn = this._impl?.AddLayers(options, callback);
    this.dispatchEvent(CommonEventEnum.AddLayer, options);
    return rtn;
  }
  /** 根据id移除影像图层
   * @param {IMapLayer} id
   * @returns any
   */
  RemoveLayerById(id?: any): any {
    const rtn = this._impl?.RemoveLayerById(id);
    this.dispatchEvent(CommonEventEnum.RemoveLayer, id);
    return rtn;
  }
  /** 根据id获取影像
   * @param {IMapLayer | String} id
   * @returns any
   */
  GetLayerByID(id?: any): any {
    return this._impl?.GetLayerByID(id);
  }
  /** 判断图层是不是存在
   * @param {IMapLayer | String} layer IMapLayer对象
   * @returns {boolean}
   */
  isExistLayer(layer: any): boolean {
    return this._impl?.GetLayerByID(layer);
  }
  //#endregion
  //#region 4.2 图层顺序调整函数 setTopIndex setBottomIndex...
  /** 图层置顶
   * @param maplayer
   */
  setTopIndex(maplayer: IMapLayer) {
    this._impl?.setTopIndex(maplayer);
  }
  /** 图层置底
   * @param maplayer
   */
  setBottomIndex(maplayer: IMapLayer) {
    this._impl?.setBottomIndex(maplayer);
  }
  /** 上移一个图层
   * @param maplayer
   */
  moveUpIndex(maplayer: IMapLayer) {
    this._impl?.moveUpIndex(maplayer);
    this.dispatchEvent(CommonEventEnum.MoveLayer, {maplayer, action: "up"});
  }
  /** 下移一个图层
   * @param maplayer
   */
  moveDownIndex(maplayer: IMapLayer) {
    this._impl?.moveDownIndex(maplayer);
    this.dispatchEvent(CommonEventEnum.MoveLayer, {maplayer, action: "down"});
  }
  /** 推拽图层
   * @param sourcemaplayer
   * @param endmaplayer
   */
  moveDragIndex(sourcemaplayer: IMapLayer, endmaplayer: IMapLayer) {
    this._impl?.moveDragIndex(sourcemaplayer, endmaplayer);
    this.dispatchEvent(CommonEventEnum.MoveLayer, {sourcemaplayer, endmaplayer, action: "moveDrag"});
  }
  //#endregion

  //#region  4.3 地图状态操控 zoomIn zoomOut zoomToLayer fullExtent getMapExtent ...

  /** 定位图层
   * @param {IMapLayer} layer
   */
  zoomToLayer(layer: any) {
    this._impl?.zoomToLayer(layer);
  }
  /** 地图定位到初始化范围,二三维切换的时候地图范围保持一致
   */
  fullExtent(extent?: any) {
    this._impl?.fullExtent(extent);
  }

  /** 获取底图当前范围，通常与 fullExtent 一起使用
   */
  getMapExtent(): any {
    return this._impl?.getMapExtent();
  }
  /**
   * 获取底图当前范围，通常与 fullExtent 一起使用
   */
  getExtent(): any {
    if (this.MapType.value == mapType.cesium) {
      //获取当前属于范围
      const center = getExtent(this.MapViewer);
      return {
        xmin: center[0],
        ymin: center[1],
        xmax: center[2],
        ymax: center[3],
      };
    } else {
      return this.MapViewer?.extent;
    }
  }
  /** 定位到范围
   * @param extent 范围
   */
  gotoExtent(extent: any) {
    this._impl?.gotoExtent(extent);
  }
  /**
   * 定位到指定点位
   * @param point 目标点位，包含（longitude和latitude）或（x,y）属性
   * @param zoom 缩放级别
   * @param showIcon 是否显示图标
   */
  gotoPoint(
    point?: IPosition,
    zoom: number = 14,
    showIcon: Boolean = true,
    url?: string
  ) {
    this._impl?.gotoPoint(point, zoom, showIcon, url);
  }
  zoomToPoint(point?: IPosition) {
    this._impl?.zoomToPoint(point, point?.zoom);
  }
  /** 指定坐标加点标签 */
  createPopX(x: any, y: any, z: any, markerUrl: string,) {
    this._impl?.createPopX(x, y, z, markerUrl);
  }
  /** 地图放大
   */
  zoomIn() {
    this._impl?.zoomIn();
  }
  /** 地图缩放
   */
  zoomOut() {
    this._impl?.zoomOut();
  }

  //#endregion

  //#endregion

  //#region 5.画图专题 DrawBrush

  /** 默认的综合画图方法,Darw事件
   * 具体在Arcgis和cesium里去实现
   * @param {IToolItemType} sender  ToolItem对象
   * @param {Array<string>} geoDataKeys
   * @param {Map<string, Object>} geoDataValue
   * @param {any} args
   * @returns any
   */
  public get DrawBrush() {
    return this._impl?.DrawBrush;
  }
  /** 暂时没有启用，  Cesium 是否开启画图工具，在使用的时候开启，不适用的时候应关闭
   * @param {Object} [options]
   * @param {Number} [options.defaultLineWidth=2] 默认的线宽
   * @param {Cesium.Color} [options.defaultLineColor=Cesium.Color.RED] 默认的线颜色
   * @param {Number} [options.defaultEditLineWidth=2] 默认的编辑状态线宽
   * @param {Number} [options.showDepthFailAppearance=true] 是否显示深度测试失败的纹理
   * @param {Cesium.Color} [options.defaultEditLineColor=Cesium.Color.YELLOW] 默认的编辑状态线颜色
   * @param {Cesium.Color} [options.defaultPlaneColor=Cesium.Color.fromCssColorString("rgba(0, 0, 0, 0.2)")] 默认的面颜色
   * @param {Cesium.Color} [options.defaultEditPlaneColor=Cesium.Color.fromAlpha(Cesium.Color.YELLOW, 0.5)] 默认的编辑状态面颜色
   * @returns any
   */
  public setDrawBrush(options?: any) {
    this._impl?.setDrawBrush(options);
  }

  /** 综合画图时间方法，也可以直接用 drawPoint drawLine ... 等方法
   * @param {Function} CallBack  画完之后的回调函数
   * @param {Object} [option]
   * @param {geometryType} [option.geoType] 几何对象类型
   * @param {string} [option.id] 几何对象ID
   * @param {string} [option.attributes] 几何对象属性
   * @returns any
   */
  DrawGeometryEvent(CallBack: any, option?: any): any {
    return this._impl?.DrawGeometryEvent(CallBack, option);
  }
  /** 批量综合绘图方法，根据Geometry
   * 具体在Arcgis和cesium里去实现
   * @param { Map<string, any>} geoDataValue
   * @returns any
   */
  DrawGraphicEvent(geoDataValue: Map<string, any>): any {
    return this._impl?.DrawGraphicEvent(geoDataValue);
  }
  /** 画图工具结果列表中的按钮事件实现
   * @param geoDataKeys
   * @param geoDataValue
   * @param RowData
   * @param toolitem
   * @param handleDrawEnd
   * @returns
   */
  DrawToolListItemEvent(
    geoDataKeys: Array<string>,
    geoDataValue: Map<string, any>,
    Option: any
  ): any {
    geoDataKeys.splice(geoDataKeys.indexOf(Option.row.id), 1);
    geoDataValue.delete(Option.row.id);
    //@ts-ignore
    _inOnemap.DrawBrush.RemoveGeometryById(Option.row.id);

    // return this._impl?.DrawToolListItemEvent(geoDataKeys, geoDataValue, Option);
  }
  drawLabel(options: any): any {
    return this._impl?.drawLabel(options);
  }

  /** 绘制图形
   * @param attributes 图形属性
   * @param geometry 图形对象
   * @param symbol 图形样式
   */
  drawGraphic(
    attributes: any,
    geometry: any,
    symbol: any,
    isDrawLabel = true,
    layerId?: string
  ) {
    this._impl?.drawGraphic(attributes, geometry, symbol, isDrawLabel, layerId);
  }

  /** 综合方法,根据key列表删除Map中的几何要素
   * 具体在Arcgis和cesium里去实现
   * @param {Array<string>} geoDataKeys
   * @returns any
   */
  DeleteGraphicEvent(geoDataKeys: Array<string>): any {
    return this._impl?.DeleteGraphicEvent(geoDataKeys);
  }

  /** 清屏 */
  clearScreen() {
    this._impl.clearScreen();
  }

  //#endregion

  //#region 6.天地图地图查询
  public QueryTdtPoi(option: {
    keyword?: string;
    level?: number;
    mapBound?: string;
    queryType?: number;
    count?: number;
    start?: number;
    type?: string;
    tk?: string;
  }): Promise<ITdtResultType> {
    let extent = this.getMapExtent();
    return this._impl?.QueryTdtPoi({
      ...option,
      mapBound: `${extent.west},${extent.south},${extent.east},${extent.north}`,
    });
  }

  /** 创建POI图层
   * @param {string} name  POI图层的名字
   * @returns {Cesium.CustomDataSource} TdtPoiDataLayer
   */
  public CreatePoiGraphicLayer(name?: string): any {
    return this._impl?.CreatePoiGraphicLayer(name);
  }
  public CreatePoiGraphics(option: {
    poilayer: any;
    PoiData: Array<ITdtDataItemType>;
    CurrentList: Array<ITdtDataItemType>;
  }): any {
    return this._impl?.CreatePoiGraphics(option);
  }
  public CreatePoiGraphicLayer1(option: {
    name?: string;
    PoiData: Array<ITdtDataItemType>;
    CurrentList: Array<ITdtDataItemType>;
  }): any {
    return this._impl?.CreatePoiGraphicLayer(option);
  }
  public MapPoiClickEvent(option: {
    poiLayer: any;
    clickItemID: string;
    position: any;
    Image: any;
    graphic: any;
    CurrentList: Array<ITdtDataItemType>;
  }) {
    return this._impl?.MapPoiClickEvent(option);
  }
  //#endregion

  //#region  7.比例尺

  /**
   * 缩放到指定比例
   * @param scale 比例
   */
  setScale(scale: number) {
    this._impl?.setScale(scale);
  }
  /** 获取当前可见比例 */
  getScale() {
    return this._impl?.getScale();
  }
  //#endregion

  //
  //
  //
  //#region  ========================= 暴露出去的方法 start =========================
  //
  //
  //

  CreateLayerPromise(layeroption: Array<IMapLayer>): Array<
    Promise<{
      maplayer: Object | undefined;
      option: IMapLayer;
    }>
  > {
    return this._impl?.CreateLayerProvider(layeroption);
  }
  /**
   * 只根据 IMapLayer 创建 ImageLayer 图层，但并不加载显示
   * @param layer IMapLayer
   * @returns 创建失败就返回false，否则就返回  Layer
   */
  CreateLayerProvider(layer: IMapLayer): Cesium.ImageryLayer | boolean | any {
    if (layer?.subLayers) {
      Utils.ThrowError(
        "参数不能有‘subLayers’属性，请传入‘subLayers’的值",
        layer
      );
    }
    if (
      layer.useMapModel == undefined ||
      layer.useMapModel.some((item) => item == this.MapType.value)
    ) {
      return this._impl?.CreateLayerProvider(layer);
    } else {
      return false;
    }
  }
  /**
   * 创建 CesiumTools.ImageryLayerGroud
   * @param layers IMapLayer
   * @returns CesiumTools.ImageryLayerGroud
   */
  CreateGroupLayer(layers: IMapLayer): any {
    if (layers?.subLayers?.length || 0 > 1) {
      return this._impl?.CreateGroupLayer(layers);
    } else {
      console.log("只有一个图层，不能创建 GroupLayer 对象");
      return false;
    }
  }
  /**
   * 向 MapViewer 添加 ImageryLayerGroud 对象，本质就是ImageryLayer数组
   * @param layers IMapLayer
   * @returns false / imageryLayerGroud
   */
  AddGroupLayer(layers: IMapLayer): any {
    if ((layers.isGroup == true && layers?.subLayers?.length) || 0 > 1) {
      return this._impl?.AddGroupLayer(layers);
    } else {
      console.log("只有一个图层，不能创建 GroupLayer 对象");
      return false;
    }
  }

  /**
   * 切换底图,与AddLayer的区别：1、添加ImagLayer，2、置为Bottom，3、会替换已有的底图
   * @param {IMapLayer} _layers 新底图
   * @param {IMapLayer} [_oldLayer = false] 旧底图
   * @returns any
   */
  changeBasemap(
    _layers: IMapLayer,
    _oldLayer: IMapLayer = false as any,
    callback?: any
  ): Promise<{ maplayer: Object | undefined; option: IMapLayer }[]> {
    if (callback && _layers?.layerid) {
      this.AddLayerCallBack({
        layerid: _layers.layerid,
        CallBackName: "default",
        CallBack: callback,
      });
    }
    return this._impl?.changeBasemap(
      _layers,
      _oldLayer,
      this.LayerCallbackCollection
    );
  }

  /**
   * 地图绘制点
   * @param callback 地图绘制点位后的回调函数，参数为绘制的点，类型为IPosition
   */
  drawPoint(callback: Function, options: any) {
    this._impl?.drawPoint(callback, options);
  }
  /**
   * 地图绘制线
   * @param callback 地图绘制线后的回调函数，参数为绘制的线，类型为IPosition[]
   */
  drawLine(callback: Function, options: any) {
    this._impl?.drawLine(callback, options);
  }
  /**
   * 地图绘制面
   * @param callback 地图绘制面后的回调函数，参数为绘制的面，类型为IPosition[]
   */
  drawPolygon(callback: Function, options: any) {
    this._impl?.drawPolygon(callback, options);
  }
  /**
   * 地图绘制圆
   * @param callback 地图绘制圆后的回调函数，参数为绘制的圆，类型为IPosition
   */
  drawCircle(callback: Function, options: any) {
    this._impl?.drawCircle(callback, options);
  }
  /**
   * 地图绘制矩形
   * @param callback 地图绘制矩形后的回调函数，参数为绘制的矩形，类型为IPosition[]
   */
  drawRectangle(callback: Function, options: any) {
    this._impl?.drawRectangle(callback, options);
  }

  //#endregion ========================= 暴露出去的方法 end =========================

  //
  //
  //
  //
  //
  //
  //
  //
  //
  //#region   //////////////////////////////////////////////////////////  待清理  //////////////////////////////////////////////////////////

  updateCurrentExtent() {
    this._impl?.updateCurrentExtent();
  }

  /** IMapEvent对象 */
  public MapClickQueryEvent: Function = undefined as any;
  public LayerCheckEvent: Function = undefined as any;
  public SwitchMapEvent: Function = undefined as any;
  public SetPropertyEvent: Function = undefined as any;

  // 外部函数调用列表
  public functionList: Map<string, Function> = new Map<string, Function>();

  /**
   * Cesium地形服务 SuperMap地形服务 ArcGIS地形服务
   * @param terrain path或 Cesium地形服务、SuperMap地形服务、ArcGIS地形服务等地形类型
   */
  AddTerrain(terrain: IMapLayer) {
    this._impl?.AddTerrain(terrain);
  }

  /**
   * 根据地址和服务属性创建图层对象 只根据 IMapLayer 创建 ImageLayer 图层对象，但并不加载显示
   * @param url 服务地址
   * @param properties 服务属性
   */
  CreateLayer(
    url: IMapLayer | String,
    properties?: any
  ): Promise<Cesium.Cesium3DTileset> | any | boolean {
    return this._impl?.CreateLayer(url, properties);
  }

  getImageryLayer(layer: IMapLayer): any {
    return this._impl?.getImageryLayer(layer);
  }
  /** 转换点数组坐标系
   * @param fromWkid 源坐标系
   * @param toWkid 目标坐标系
   * @param points 点数组
   * @returns
   */
  projectPoints(fromWkid: number, toWkid: number, points: IPosition[]) {
    return this._impl?.projectPoints(fromWkid, toWkid, points);
  }

  ChangeGeometrySymbolEvent(symbol: any, geoDataValue: any): any {
    return this._impl?.ChangeGeometrySymbolEvent(symbol, geoDataValue);
  }

  /**
   * 综合量算方法
   * @param geoType 量算图形类型
   * @param geoUnit 量算单位
   * @param callback 量算结束后的回调函数，参数为量算结果，类型为IMeasureResult
   */
  measureHandler(
    geoType: string | any,
    geoUnit: string = "meter",
    callback: Function
  ): any {
    return this._impl?.measureHandler(geoType, geoUnit, callback);
  }

  /**
   * 清除量算绘制的图形
   * @param callback 绘制结束后的回调函数，参数为绘制的点位信息
   */
  clearMeasure(isClear: true) {
    this._impl.clearMeasure(isClear);
  }

  /** 清除绘制工具绘制的所有图形
   */
  clearAllDrawGraphic() {
    this._impl?.clearAllDrawGraphic();
  }


  /**
   * 综合量算绘制方法
   * @param geoType 量算图形类型
   * @param geoUnit 量算单位
   * @param callback 量算结束后的回调函数，参数为量算结果，类型为IMeasureResult
   * @param option option
   */
  drawHandler(
    geoType: string | any,
    geoUnit: string = "meter",
    callback: Function,
    option?: any
  ): any {
    return this._impl?.drawHandler(geoType, geoUnit, callback, option??{});
  }

  /**
   * 清除量算绘制的图形
   * @param callback 绘制结束后的回调函数，参数为绘制的点位信息
   */
  clearDrawGraphic(isClear: true) {
    this._impl.clearDrawGraphic(isClear);
  }

  /**
   * 退出绘制(drawHandler)状态
   */
  deactivateDrawingMode() {
    this._impl?.deactivateDrawingMode();
  }

  public get DrawWidget() {
    return this._impl?.DrawWidget;
  }

  /**
   * 根据id清除绘制工具的图形
   * @param id 图形id
   */
  clearDrawGraphicById(id: string) {
    this._impl.clearDrawGraphicById(id);
  }

  /**
   * 地图绘制点
   * @param callback 地图绘制点位后的回调函数，参数为绘制的点，类型为IPosition
   */
  drawImagePoint(
    geometry: any,
    isClear: boolean,
    graphicLayerId: string,
    markerUrl?: string,
    order?: number,
    color?: any,
    attributes?: any,
    width: number = 20,
    height: number = 18
  ) {
    this._impl?.drawImagePoint(
      geometry,
      isClear,
      graphicLayerId,
      markerUrl,
      order,
      color,
      attributes,
      width,
      height,
    );
  }

  /**
   * 更新图形的符号化
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {String} graphicId - 图形ID
   * @param {Any} symbol - 符号化Json对象
   */
  setGraphicSymbol(layerId: string, graphicId: string, symbol: any) {
    this._impl?.setGraphicSymbol(layerId, graphicId, symbol);
  }
  /**
   * 根据Grapics的属性id更新Graphics名称
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {String} graphicId - 图形ID
   * @param {String} name - 图形名称
   */
  setGraphicName(layerId: string, graphicId: string, name: string) {
    this._impl?.setGraphicName(layerId, graphicId, name);
  }

  //二维退出绘制状态
  removeDrawStatus(): any {
    return this._impl.removeDrawStatus();
  }

  /**
   * 根据图层id和图形id数组，缩放到图形范围
   * @param layerId 图层id
   * @param graphicIds 图形id数组
   */
  zoomToGraphics(layerId: string, graphicIds: string[]) {
    this._impl?.zoomToGraphics(layerId, graphicIds);
  }
  /**
   * 调整图层叠加顺序
   * @param layer 当前要移动的图层
   * @param action top: 置顶 bottom: 置底 up: 上移 down: 下移
   */
  reorderLayer(data: any, action: string) {
    this._impl?.reorderLayer(data, action);
    this.dispatchEvent(CommonEventEnum.ReorderLayer, {data, action});
  }
  /**
   * 根据图层id和图形id数组删除一个或多个图形
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {Array} graphicIds - 图形ID列表
   */
  removeGraphics(layerId: string, graphicIds: string[]) {
    this._impl?.removeGraphics(layerId, graphicIds);
  }

  /**
   * 根据图层id删除该图层内所有图形
   */
  removeAllGraphicsByLayerId(layerId: string) {
    this._impl?.removeAllGraphicsByLayerId(layerId);
  }

  getGraphicsLayerByLayerId(layerId: string) {
    return this._impl?.getGraphicsLayerByLayerId(layerId);
  }

  /**
 * 根据图形来定位到它的中心点
 * @param geometry
 * @param expand 放大倍率
 */
  zoomByGeometry(geometry: any[], expand: number = 1.8) {
    this._impl?.zoomByGeometry(geometry, expand);
  }


  /**
   * 获取图层的透明度
   * @param layer
   * @returns
   */
  getLayerOpacity(layer: IMapLayer) {
    return this._impl?.getLayerOpacity(layer);
  }
  /**
   * 设置图层的透明度
   * @param layer
   * @param opacity
   */
  setLayerOpacity(layer: IMapLayer, opacity: number) {
    this._impl?.setLayerOpacity(layer, opacity);
  }

  /**
   * 设置图层显隐
   * @param layerid
   * @param visible
   */
  setLayerVisible(layerid: string, visible: boolean) {
    this._impl?.setLayerVisible(layerid, visible);
    this.dispatchEvent(CommonEventEnum.LayerVisible, {layerid, visible});
  }

  /**
   * 设置可见比例
   * @param layerid
   * @param minScale
   * @param maxScale
   */
  setLayerScale(layerid: string, minScale: number, maxScale: number) {
    // 只有arcgis需要单独设置
    if (this.MapType.value == mapType.arcgis) {
      this._impl.setLayerScale(layerid, minScale, maxScale);
    }
  }

  /**
   * 批量移除图层
   * @param layers
   */
  removeLayers(layers: Array<IMapLayer>) {
    this._impl?.removeLayers(layers);
  }

  //#endregion
}

export function SwitchMapEvent(callback: any) {
  callback();
}
/** 用户自定义回调函数 */
export const OnemapEvent = {
  /**
   * 点击图层数后，向服务端发送API请求解析服务地址
   * @example LayerCheckServiceAPI(newLayer:IMapLayer,oldLayer:IMapLayer):any
   */
  LayerCheckServiceAPI: undefined as any,
  /**
   * 底图切换的回调函数
   * @example ChangeBaseMapAPI(newLayer:IMapLayer,oldLayer:IMapLayer):any
   */
  ChangeBaseMapAPI: undefined as any,
  SplitSwitchVisible: undefined as any,
  SwitchMapCallBack: [] as Function[],
  /** 点击图层树中节点后处理逻辑
    LayerTreeCheckEvent(isCheck:boolen,data:IMapLayer):any
  */
  LayerTreeCheckEvent: undefined as any,
  /** 地图加载完成时间函数 */
  MapReadyEvent: undefined as any,
  /** 地图实时回调函数 */
  MapRealtimeEvent: undefined as any,
  /** 地图点选事件 */
  MapClickEvent: undefined as any,
  InitBaseMapEvent: undefined as any,
  ExportExcelAPI: undefined as any,	// 导出Excel文件
  ArcGISConfig: undefined as any,	// ArcGIS配置请求的回调
  FlyDataAPI: undefined as any,	// 飞行漫游接口
  HideHightAPI: undefined as any,
  FavoritesAPI: undefined as any, // 收藏接口
  getMaticListAPI: undefined as any, // 专题列表接口
};

/**
 * 地图初始化函数
 * @param {OnemapClass} [_onemap]  OnemapClass参数
 * @param {Object} [options]
 * @param {string} [options.MapControlName='mainMapControl'] 绑定底图视图的名称,默认主视图名称是mainMapControl
 * @param {string} [options.BASE_URL='ThirdParty']
 * @param {ILayerType} [options.BaseMapLayer] 底图参数
 * @param {string} [options.TerrainUrl] 地形参数
 * @param {Object} [options.mapextent] 初始化底图范围
 * @param {number} [options.mapextent.xmin] 经度
 * @param {number} [options.mapextent.ymin = 21.624205573999646] 纬度
 * @param {number} [options.mapextent.xmax = 110.55005559052246] 经度
 * @param {number} [options.mapextent.ymax = 24.67104274606849] 纬度
 * @param {number} [options.mapextent.heading = 0]
 * @param {number} [options.mapextent.pitch = -90]
 * @param {number} [options.mapextent.roll = 0]
 * @example
 * {
      MapControlName: "mainMapControl",
      BASE_URL: "ThirdParty",
      BaseMapLayer: BaseMapLayerUrl(),
      TerrainUrl: ServiceUrl.TerrainUrl,
      mapextent: {
        xmin: 105.9738504337227,
        ymin: 21.624205573999646,
        xmax: 110.55005559052246,
        ymax: 24.67104274606849,
        heading: 0,
        pitch: -90,
        roll: 0,
      }
    }
*/
export const InitMapControl = (_onemap: any, options: any) => {
  base.InitMapControl(_onemap, options);
};
/**
 * 获取底图
 * @param {string} [mapid = "mainMapControl"]
 * @returns OnemapClass
 */
export function getOnemap(mapid: string = "mainMapControl"): OnemapClass {
  return base.getOnemap(mapid);
}
/**
 * 获取所有子视图
 * @returns  Map<string, OnemapClass>
 */
export function getSubOnemap(): any {
  return base.getSubOnemap();
}

/**
 * 获取底图参数
 * @param {string} [mapid = "mainMapControl"] 绑定底图视图的名称,默认主视图名称是mainMapControl
 * @returns
 */
export function getOption(mapid: string = "mainMapControl"): any {
  return base.getOption(mapid);
}
/**
 * 销毁子视图对象(与主地图组件相关的Onemap、LayerStore、PropStoreOptions对象是不能删除的)
 * @param {string} [mapid = "mainMapControl"] 绑定底图视图的名称,默认主视图名称是mainMapControl
 */
export function deleteOnemap(mapid?: string): any {
  base.deleteOnemap(mapid);
}

export function getOptions(options?: Cesium.Viewer.ConstructorOptions): any {
  return base.getOptions(options);
}
