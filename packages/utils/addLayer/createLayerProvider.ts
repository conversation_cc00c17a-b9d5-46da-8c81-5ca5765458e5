import * as Cesium from "@onemapkit/cesium";
import * as CesiumTools from "@onemapkit/cesium-tools";
import { serviceType, Utils, type IMapLayer } from "onemapkit"; 
import { getCookie } from "../../utils/utils";
import { cloneDeep } from "lodash";

export function CreateLayerProvider(
  layer: IMapLayer,isPromise:boolean=true
):
  | Cesium.ImageryLayer
  | Promise<Cesium.CesiumTerrainProvider>
  | Promise<Cesium.Cesium3DTileset>
  | CesiumTools.S3MTilesLayer
  | any {
  try {
    switch (layer.serviceType) {
      case serviceType.ArcgisMapImageLayer: {
        return createArcgisMapImageProvider(layer);
      }
      case serviceType.ArcgisDynamicLayer: {
        return createArcgisDynamicProvider(layer);
      }
      case serviceType.ArcgisFeatureLayer: {
        return createArcgisFeatureProvider(layer);
      }
      case serviceType.ArcgisTileLayer: {
        return createArcgisTileProvider(layer);
      }
      case serviceType.ArcgisWebTiledLayer: {
        return createArcgisWebTiledProvider(layer);
      }
      case serviceType.ArcgisVectorTileLayer: {
        return createArcgisVectorTileProvider(layer);
      }

      case serviceType.ArcgisTilesMap: {
        return createArcgisTilesMapProvider(layer);
      }
      case serviceType.tiandiMap: {
        return createTiandiMapProvider(layer,isPromise);
      }
      case serviceType.gaodeMap: {
        return createGaodeMapProvider(layer);
      }
      case serviceType.Arcgis3DTiles: {
        return createArcgis3DTilesProvider(layer);
      }
      case serviceType.Cesium3DTiles: {
        return createCesium3DTilesProvider(layer);
      }
      case serviceType.Super3DTiles: {
        return createSuper3DTilesProvider(layer);
      }
      case serviceType.CesiumTerrain: {
        return createCesiumTerrainProvider(layer);
      }
      case serviceType.SingleImage: {
        return createSingleImageProvider(layer);
      }
      default: {
        return false;
      }
    }
  } catch {
    return false;
  }
}

function innerReadMap(_layer: IMapLayer): any { 
	const url = Utils.makeTokenURL(_layer.url as any);
  const rtnLyr = CesiumTools.ArcGisMapServerImageryProvider.fromUrl(
    url as any,
    _layer?.options?.mapoption
  );
  return rtnLyr;
}

/**
 * arcgis矢量切片图层
 * @param _layer 
 * @returns 
 */
function innerReadVectorMap(_layer: IMapLayer): any {
	let tokenName = localStorage.getItem("TokenName");
	if (!tokenName) {
		tokenName = "sessionid";
	}
	
	let params = cloneDeep(_layer?.options?.mapoption);
	if (!params) {
		params = {
			maximumNativeLevel: 19,
			headers: {
				Authorization: `bearer ${getCookie(tokenName)}`
			}
		}
	} else {
		params.headers = {
			Authorization: `bearer ${getCookie(tokenName)}`
		}
		params.maximumNativeLevel = 19;
	}
	const layer = CesiumTools.ArcGISPBFImageryProvider.fromUrl(
		_layer.url as any,
		_layer.url + "/resources/styles/root.json",
		params
	) as any;
	return layer;
}

/**
 * 创建ArcgisMapImageProvider对象，如：serviceType = ArcgisMapImageLayer
 * @param {IMapLayer} layer
 * @returns Promise<CesiumTools.ArcGisMapServerImageryProvider>
 */
export function createArcgisMapImageProvider(layer: IMapLayer): any {
  return innerReadMap(layer);
}
/**
 * 创建createArcgisDynamicProvider对象，如：serviceType = ArcgisDynamicLayer
 * @param {IMapLayer} layer
 * @returns Promise<CesiumTools.ArcGisMapServerImageryProvider>
 */
export function createArcgisDynamicProvider(layer: IMapLayer): any {
  return innerReadMap(layer);
}
/**
 * 创建createArcgisFeatureProvider对象，如：serviceType = ArcgisFeatureLayer
 * @param {IMapLayer} layer
 * @returns Promise<CesiumTools.ArcGisMapServerImageryProvider>
 */
export function createArcgisFeatureProvider(layer: IMapLayer): any {
  return innerReadMap(layer);
}
/**
 * 创建createArcgisTileProvider对象，如：serviceType = ArcgisTileLayer
 * @param {IMapLayer} layer
 * @returns Promise<CesiumTools.ArcGisMapServerImageryProvider>
 */
export function createArcgisTileProvider(layer: IMapLayer): any {
  return innerReadMap(layer);
}
/**
 * 创建ArcgisWebTiledProvider对象，如：serviceType = ArcgisWebTiledLayer
 * @param {IMapLayer} layer
 * @returns Promise<CesiumTools.ArcGisMapServerImageryProvider>
 */
export function createArcgisWebTiledProvider(layer: IMapLayer): any {
  return innerReadMap(layer);
}

export function createArcgisVectorTileProvider(layer: IMapLayer): any {
  return innerReadVectorMap(layer);
}

/**
 * 创建creatArcgisTilesMapProvider对象，如：serviceType = ArcgisTilesMap
 * @param {IMapLayer} layer
 * @returns CesiumTools.UrlTemplateImageryProvider
 */
export function createArcgisTilesMapProvider(layer: IMapLayer): any {
  let rect: any;
  if (layer?.options?.ProviderOptions?.rectangle) {
    rect = Cesium.Rectangle.fromDegrees(
      layer?.options?.ProviderOptions?.rectangle?.west,
      layer?.options?.ProviderOptions?.rectangle?.south,
      layer?.options?.ProviderOptions?.rectangle?.east,
      layer?.options?.ProviderOptions?.rectangle?.north
    );
  } else if (layer?.extent) {
    rect = Cesium.Rectangle.fromDegrees(
      layer?.extent?.west,
      layer?.extent?.south,
      layer?.extent?.east,
      layer?.extent?.north
    );
  } 
  const prm = new Promise((resolve) => {
    const rtnLyr = new CesiumTools.UrlTemplateImageryProvider({
      url: layer.url,
      ...layer?.options?.ProviderOptions,
      rectangle: rect,
    }); 
    resolve(rtnLyr);
  });  
  return prm;
}

/**
 * 创建createTiandiMapProvider对象，如：serviceType = tiandiMap
 * @param {IMapLayer} layer
 * @returns Cesium.WebMapTileServiceImageryProvider
 */
export function createTiandiMapProvider(layer: IMapLayer,isPromise:boolean=true): any {
  const regexLevel = /([Tt][Ii][Ll][Ee][Mm][Aa][Tt][Rr][Ii][Xx])={([Ll][Ee][Vv][Ee][Ll])}/g;
  const regexRow = /([Tt][Ii][Ll][Ee][Rr][Oo][Ww])={([Rr][Oo][Ww])}/g;
  const regexCol = /([Tt][Ii][Ll][Ee][Cc][Oo][Ll])={([Cc][Oo][Ll])}/g;
  if (regexLevel.test(layer.url!)) {
    layer.url = layer.url!.replace(regexLevel, "tilematrix={TileMatrix}");
    layer.url = layer.url!.replace(regexRow, "tilerow={TileRow}");
    layer.url = layer.url!.replace(regexCol, "tilecol={TileCol}");
  }
  const tileMatrixLabels = [
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
  ];
  let _inOption = {
    ...{
      style: "default",
      format: "tiles",
      subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
      minimumLevel: 0,
      maximumLevel: 16,
      // tilingScheme: new Cesium.GeographicTilingScheme(),
      // tilingScheme: new Cesium.WebMercatorTilingScheme(),
    },
    ...(layer?.options as any)?.ProviderOptions,
    tileMatrixLabels: tileMatrixLabels.slice(
      (layer?.options as any)?.ProviderOptions?.minimumLevel || 0,
      (layer?.options as any)?.ProviderOptions?.maximumLevel || 16
    ),
  };
	
  let rtnLyr: any;
  if ((layer?.options as any)?.isWebMercator) {
		_inOption.tileMatrixLabels.splice(0, 1);
		_inOption.tilingScheme = new Cesium.GeographicTilingScheme();
		const url = Utils.makeTokenURL(layer.url as any);
		rtnLyr = new Cesium.WebMapTileServiceImageryProvider({
			url: url as any,
			..._inOption,
		});
  } else {
		const url = Utils.makeTokenURL(layer.url as any);
    rtnLyr = new Cesium.UrlTemplateImageryProvider({
      url: url as any,
      ..._inOption,
    });
  } 
  if(isPromise){ 
    return new Promise((resolve) => {
      resolve(rtnLyr);
    });
  }else{
    return rtnLyr;
  }
} 
/**
 * 创建creatArcgisTilesMapProvider对象，如：serviceType = ArcgisTilesMap
 * @param {IMapLayer} layer
 * @returns CesiumTools.UrlTemplateImageryProvider
 */
export function createGaodeMapProvider(layer: IMapLayer): any {
	const url = Utils.makeTokenURL(layer.url as any);
  const rtnLyr = new CesiumTools.GaoDeImageryProvider({
    templateUrl: url,
    param: "lang=zh_cn&style=6&ltype=0&scl=0&size=0",
    ...(layer?.options as any)?.ProviderOptions,
  }); 
  return new Promise((resolve) => {
    resolve(rtnLyr);
  });
}

/**
 * 创建 createArcgis3DTilesProvider 对象，如：serviceType = Arcgis3DTiles
 * @param {IMapLayer} layer
 * @returns {Promise<Cesium.I3SDataProvider>}
 */
export function createArcgis3DTilesProvider(layer: IMapLayer): any {
  let rtnLyr = Cesium.I3SDataProvider.fromUrl(layer?.url || "", {
    cesium3dTilesetOptions: {
      skipLevelOfDetail: true,
      skipLevels: 1,
      baseScreenSpaceError: 1024,
      skipScreenSpaceErrorFactor: 32,
    },
    ...(layer?.options as any)?.ProviderOptions,
  }); 
  return rtnLyr;
}
/**
 * 创建 createCesium3DTilesProvider 对象，如：serviceType = Cesium3DTiles
 * @param {IMapLayer} layer
 * @returns {Promise<Cesium.Cesium3DTileset>}
 */
export function createCesium3DTilesProvider(layer: IMapLayer): any {
	const url = Utils.makeTokenURL(layer.url as any);
  let rtnLyr = Cesium.Cesium3DTileset.fromUrl(url || "", {
    skipLevelOfDetail: true,
    skipLevels: 1,
    baseScreenSpaceError: 1024,
    skipScreenSpaceErrorFactor: 32,
    ...(layer?.options as any)?.ProviderOptions, 
  }); 
  return rtnLyr;
}

export function createSuper3DTilesProvider(layer: IMapLayer): any {
  let rtnLyr = new CesiumTools.S3MTilesLayer({
    context: (layer as any)?.context,
    url: layer?.url || "",
  });
  return new Promise((resolve) => {
    resolve(rtnLyr);
  });
}

/**
 * 创建 createCesiumTerrainProvider 对象，如：serviceType = CesiumTerrain
 * @param {IMapLayer} layer
 * @returns {Promise<CesiumTerrainProvider>}
 */
export function createCesiumTerrainProvider(layer: IMapLayer) {
	const url = Utils.makeTokenURL(layer.url as any);
  const rtnLyr = Cesium.CesiumTerrainProvider.fromUrl(
    url || "",
    (layer?.options as any)?.ProviderOptions
  ); 
  return rtnLyr;
}
/**
 * 创建 createSingleImageProvider 对象，如：serviceType = SingleImage
 * @param {IMapLayer} layer
 * @returns {Promise<Cesium.SingleTileImageryProvider>}
 */
export function createSingleImageProvider(layer: IMapLayer) {
	const url = Utils.makeTokenURL(layer.url as any);
  const rtnLyr = Cesium.SingleTileImageryProvider.fromUrl(
    url || "./assets/earth.jpg",
    (layer?.options as any)?.ProviderOptions
  ); 
  return rtnLyr;
}
