/**
 * @file 通用图层查询类
 * @description 该类用于查询图层数据，支持分页查询，支持查询条件，支持查询指定字段，，
 * @description 因为该功能与Map是否为二维还是三维无关，所以放在common-tools中，并且使用ArcGIS JS高版本的API进行查询
 * @description This class is used to query layer data, supports pagination, query conditions, and specified fields.
 * @description Because this feature is independent of whether the Map is 2D or 3D, it is placed in common-tools and uses the ArcGIS JS high version API for querying.
 * @version 0.0.1
 * @date 2023-1205
 */

import Query from "@arcgis/core/rest/support/Query.js";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";
import FeatureSet from "@arcgis/core/rest/support/FeatureSet.js";
import Geometry from "@arcgis/core/geometry/Geometry.js";
import request from "../axios";
import {
  type QueryResult
} from "../../interface/IOnemap";
import { ElMessage } from "element-plus";

class LayerQuery {
  private serviceUrl: string;

  constructor(serviceUrl: string) {
    this.serviceUrl = serviceUrl;
  }

  private async _arcgisQueryAsync(
    subLayerId: number,
    fields: string[] | null,
    where: string,
    pageNum: number,
    pageSize: number,
    geometry?: Geometry | null,
    returnGeometry: boolean = true,
    hasPagination: boolean = true,  // 是否分页
  ): Promise<FeatureSet> {
    const featureLayer = new FeatureLayer({
      url: `${this.serviceUrl}/${subLayerId}`,
    });

    const query = new Query();
    query.outFields = fields || ["*"];
    query.where = where;
    query.returnGeometry = returnGeometry
    if (hasPagination) {
      query.start = (pageNum - 1) * pageSize;
      query.num = pageSize;
    }
    if (geometry) {
      query.geometry = geometry;
    }

    return await featureLayer.queryFeatures(query);
  }

  async queryTotalCountAsync(
    subLayerId: number, // 子图层ID
    where: string,  // 查询条件
    serviceType: string | null,  // 服务类型
    geometry?: Geometry | null,  // 查询范围
  ): Promise<number> {
    // 如果地图服务为ArcGIS的服务
    if (serviceType && serviceType.toLocaleLowerCase().includes("arcgis")) {
      const featureLayer = new FeatureLayer({
        url: `${this.serviceUrl}/${subLayerId}`,
      });

      const query: any = new Query();
      query.where = where;
      query.fields = "*";
      if (geometry) {
        query.geometry = geometry;
      }

      const result = await featureLayer.queryFeatureCount(query);
      return result;
    } else {
      return -1;
    }
  }

  async query(
    subLayerId: number, // 子图层ID
    fields: string[] | null,  // 查询字段
    where: string,  // 查询条件
    pageNum: number,  // 页码
    pageSize: number, // 每页条数
    returnGeometry: boolean = true, // 是否返回几何信息
    serviceType: string, // 服务类型
    geometry?: Geometry | null,  // 查询范围
    callback?: (result?: QueryResult, _isPage?: boolean) => void
  ): Promise<void> {
		try {
			// 如果地图服务为ArcGIS的服务
			if (serviceType && serviceType.toLocaleLowerCase().includes("arcgis")) {
				// 通过请求服务，判断是否支持分页
				let res = await request.get({ url: `${this.serviceUrl}/${subLayerId}?f=json` })
				let hasPage = false
				if (res && res.advancedQueryCapabilities && (res.advancedQueryCapabilities.supportsPagination == true)) {
					hasPage = true;
				}
				await this._arcgisQueryAsync(subLayerId, fields, where, pageNum, pageSize, geometry, returnGeometry, hasPage).then(
					(result) => {
						if (callback) {
							callback(result, hasPage);
						}
					}
				);
			} else {
				if (callback) {
					callback(new FeatureSet(), false);
				}
			}
		} catch(err:any) {
			console.log(err);
			if (err && err.message && err.message.includes('Timeout')) {
				ElMessage.error("请求超时，查询数据量过多，请调整查询条件")
			}
			if (callback) {
				callback(undefined, false);
			}
		}
  }
}

export default LayerQuery;