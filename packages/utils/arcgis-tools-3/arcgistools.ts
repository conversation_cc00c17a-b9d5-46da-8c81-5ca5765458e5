/**
 * @file Arcgis 3.x 通用方法
 * <AUTHOR>
 * @version 0.0.1
 * @date 2023-11-03
 */
import { Locate } from "./locate";
import { Layer } from "./layer";
import { Draw } from "./draw";
import { Measure } from "./measure";
import { DrawWidget } from "./drawWidget/drawHandler";
import { geometryType } from "onemapkit";
import * as projection from "./projection";
import _ from "lodash";
/**
 * Arcgis 3.x 通用方法
 */
export class Arcgis3Tools {
  /**
   * 视图对象
   * @private
   */
  _map: any = null;
  /**
   * esri对象
   */
  _esri: any = null;
  /**
   * 定位对象
   */
  _locate: any = null;
  /**
   * 图层对象
   */
  _layer: any = null;
  /**
   * 绘制对象
   */
  _draw: any = null;

  /**
   * 测量对象
   */
  _measure: any = null;

  /**
   * 绘制对象
   */
  _drawWidget: any = null;
  /**
   * 构造函数
   * @param {any} map - 二维的地图视图(MapView)
   */
  constructor(map: any, esri: any) {
    this._map = map;
    this._esri = esri;
    if (esri) {
      this._locate = new Locate(map, esri);
      this._layer = new Layer(map, esri);
      this._draw = new Draw(map, esri);
      this._measure = new Measure(map, esri);
      this._drawWidget = new DrawWidget(map, esri);
    }
  }
  /**
   * 初始化esri对象 用于异步加载
   */
  // async initEsriObject() {
  //   await loadEsri((data: any) => {
  //     this._esri = data;
  //   });
  // }
  //#region 天地图相关方法
  /**
   * 创建天地图4490的WebTileLayer
   * @param url 天地图图层的url
   * @param id 自定义图层id
   * @returns
   */
  //@ts-ignore
  createTiandituLayer4490(url: string, id: string, lods: Array<any>) {
    if (this._esri == null) {
      throw new Error(
        "please initEsriObject first, check the initEsriObject method"
      );
    }
    //return tianditu.createTiandituLayer4490(this._esri, url, id, lods);
  }
  //#endregion

  //#region 图层相关方法
  /**
   * 根据url创建Arcgis图层对象，异步方法
   * @param {string} url - 图层的url
   * @param {Object} properties - 图层参数，详细可参考arcgis相关图层参数
   * @returns layer图层对象
   */
  async createLayer(options: any) {
    return await this._layer?.createLayer(options);
  }
  /**
   * 给图层设置透明度
   * @param layer
   * @param opacity
   */
  setLayerOpacity(layer: any, opacity: number) {
    this._layer?.setLayerOpacity(layer, opacity);
  }
  //#endregion

  //#region 定位相关方法
  /**
   * 定位到指定点位
   * @param point 目标点位,geojson对象
   * @param zoom 定位缩放级别
   * @param showIcon 是否显示定位图标
   * @param markerUrl 图标路径
   * @param width 图标宽度
   * @param height 图标高度
   * @returns
   */
  gotoPoint(
    point: any,
    zoom: number,
    showIcon: Boolean,
    iconUrl: string,
    iconWidth: string,
    iconHeight: string
  ) {
    return this._locate?.gotoPoint(
      point,
      zoom,
      showIcon,
      iconUrl,
      iconWidth,
      iconHeight
    );
  }

  fullExtent(extend?: any) {

    console.log("=========extend", extend)
    let fullExtent = new this._esri.Extent({
      xmax: extend?.xmax || 110.55005559052246,
      xmin: extend?.xmin || 105.9738504337227,
      ymax: extend?.ymax || 24.67104274606849,
      ymin: extend?.ymin || 21.624205573999646,
      spatialReference: new this._esri.SpatialReference({
        wkid: extend?.wkid || 4490,
      }),
    });
    this.gotoExtent(fullExtent, 1);
  }
  gotoExtent(extent: any, ratio: number) {
    this._locate?.gotoExtent(extent, ratio);
  }

  drawImagePoint(
    geometry: any,
    isClear: boolean,
    graphicLayerId: string,
    markerUrl?: string,
    order?: number,
    color?: any,
    attributes?: any,
    width: number = 20,
    height: number = 18
  ) {
    this._locate?.drawGraphic(
      geometry,
      isClear,
      graphicLayerId,
      markerUrl,
      order,
      color,
      width,
      height,
      attributes,
    );
  }
  //#endregion

  //#region 投影相关方法
  projectPoints(
    fromProjection: number,
    toProjection: number,
    points: Array<Array<number>>
  ) {
    return projection.projectPoints(fromProjection, toProjection, points);
  }
  //#endregion

  //#region 绘制相关方法
  /**
   * 开始绘制点
   * @param callback 回调函数，返回绘制的点坐标{x:0,y:0,wkid:4490}
   */
  drawPoint(attributes: any, callback: Function) {
    this._draw?.drawPoint(attributes, callback);
  }
  /**
   * 开始绘制线
   * @param callback 回调函数，返回绘制的坐标
   */
  drawLine(attributes: any, callback: Function) {
    this._draw?.drawLine(attributes, callback);
  }
  /**
   * 开始绘制面
   * @param callback 回调函数，返回绘制的坐标
   */
  drawPolygon(attributes: any, callback: Function) {
    this._draw?.drawPolygon(attributes, callback);
  }
  /**
   * 开始绘制圆
   * @param callback 回调函数，返回绘制的坐标
   */
  drawCircle(attributes: any, callback: Function) {
    this._draw?.drawCircle(attributes, callback);
  }
  /**
   * 开始绘制矩形
   * @param callback 回调函数，返回绘制的坐标
   */
  drawRectangle(attributes: any, callback: Function) {
    this._draw?.drawRectangle(attributes, callback);
  }

  /**
   *  //退出编辑状态
   * @param callback 回调函数，返回绘制的坐标
  */
  removeDrawStatus() {
    return this._draw?.removeDrawStatus();
  }

  /**
   * 绘制图形
   * @param attributes 图形属性
   * @param geometry 图形对象
   * @param symbol 图形样式
   */
  drawGraphic(
    attributes: any,
    geometry: any,
    symbol: any,
    isDrawLabel = true,
    layerId?: string
  ) {
    this._draw?.drawGraphic(attributes, geometry, symbol, isDrawLabel, layerId);
  }
  /**
   * 更新图形的符号化
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {String} graphicId - 图形ID
   * @param {Any} symbol - 符号化Json对象
   */
  setGraphicSymbol(layerId: string, graphicId: string, symbol: any) {
    this._draw?.setGraphicSymbol(layerId, graphicId, symbol);
  }
  /**
   * 根据Grapics的属性id更新Graphics名称
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {String} graphicId - 图形ID
   * @param {String} name - 图形名称
   */
  setGraphicName(layerId: string, graphicId: string, name: string) {
    this._draw?.setGraphicName(layerId, graphicId, name);
  }
  /**
   * 定位到一个或多个图形，如果图形坐标为非4326坐标系，则需要先做转换才能定位
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {Array} graphicIds - 图形ID列表
   */
  zoomToGraphics(layerId: string, graphicIds: string[]) {
    this._draw?.zoomToGraphics(layerId, graphicIds);
  }
  /**
   * 根据图层id和图形id数组删除一个或多个图形
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   * @param {Array} graphicIds - 图形ID列表
   */
  removeGraphics(layerId: string, graphicIds: string[]) {
    this._draw?.removeGraphics(layerId, graphicIds);
  }
  /**
   * 根据图层id删除所有图形
   * @param {String} layerId - 图层ID,如果不传则默认调整Map的Graphics图层上的图形
   */
  removeAllGraphicsByLayerId(layerId: string) {
    this._draw?.removeAllGraphicsByLayerId(layerId);
  }
  getGraphicsLayerByLayerId(layerId: string) {
    return this._draw?.getGraphicsLayerByLayerId(layerId);
  }

  /**
   * 清除所有图形
   */
  clearGraphics(geoDataKeys = null) {
    if (!geoDataKeys) {
      this._map?.graphics?.clear();
    }
  }

  /**
   * 综合量算方法
   * @param geoType 量算图形类型
   * @param geoUnit 量算单位
   * @param callback 量算结束后的回调函数，参数为量算结果，类型为IMeasureResult
   */
  measureHandler(
    geoType: string | any,
    geoUnit: string = "meter",
    callback: Function
  ): any {
    this._measure?.removeClickHandle()
    switch (geoType) {
      case "line": {
        this._measure?.drawMeasureLine(geoUnit, false, callback);
        break;
      }
      case "linesegment": {
        this._measure?.drawMeasureLine(geoUnit, true, callback);
        break;
      }
      case "polygon": {
        this._measure?.drawMeasurePolygon(geoUnit, callback);
        break;
      }
    }
  }

  clearMeasure(): void {
    this._measure?.clearMeasure();
  }
  //#endregion

  drawHandler(
      geoType: string | any,
      geoUnit: string = "meter",
      callback: Function,
      option?: any
  ){
    this._drawWidget?.removeClickHandle()
    const typeMap: Record<string, { prefix: string, method: string, type: string }> = {
      point: { prefix: "pt", method: "drawPoint", type: geometryType.point },
      polyline: { prefix: "pl", method: "drawMeasureLine", type: geometryType.polyline },
      polygon: { prefix: "pg", method: "drawMeasurePolygon", type: geometryType.polygon },
      circle: { prefix: "cc", method: "drawMeasureCircle", type: geometryType.circle },
      rectangle: { prefix: "rc", method: "drawMeasureRectangle", type: geometryType.rectangle }
    };
    
    const config = typeMap[geoType];
    if (!config) return;
    
    const key = option?.id || `${config.prefix}${new Date().getTime()}`;
    const attributes = { id: key, geoUnit, ...(option || {}) };
    
    this._drawWidget?.[config.method](attributes, (_geometry: any) => {
      _geometry.type = config.type;
      const result = {
        id: key,
        label: key,
        type: _geometry.type,
        data: _geometry,
        ..._geometry.result
      }
      callback?.({
        id: key,
        geometry: _geometry,
        result: result,
        label: key,
        type: _geometry.type
      });
    });
  }

  clearAllDrawGraphic(): void {
    this._drawWidget?.clearDrawWidget();
  }

  /**
   * 退出绘制状态
   */
  deactivateDrawingMode() {
    this._drawWidget?.deactivateDrawingMode();
  }

  DrawToolsEvent(
    geoDataKeys: Array<string>,
    geoDataValue: Map<string, any>,
    RowData: any,
    toolitem: any,
    //@ts-ignore
    handleDrawEnd?: Function
  ): any {
    switch (toolitem.name) {
      case "delete":
        {
          geoDataKeys.splice(geoDataKeys.indexOf(RowData.row), 1);
          (geoDataValue.get(RowData.row) as any)?.editor.clear();
          (geoDataValue.get(RowData.row) as any)?.editor.destroy();
          geoDataValue.delete(RowData.row);
        }
        break;
      case "endedit":
        {
        }
        break;
      case "edit":
        {
        }
        break;
      case "location":
        {
        }
        break;
      case "collect":
        {
        }
        break;
      case "downCoord":
        {
        }
        break;
      case "spatialanalysis":
        {
        }
        break;
      case "setsymbol":
        {
        }
        break;
    }
  }
}
