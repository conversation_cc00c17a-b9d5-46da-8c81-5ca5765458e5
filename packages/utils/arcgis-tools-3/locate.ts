/**
 * @file Arcgis 3.x 定位相关通用方法
 * <AUTHOR>
 * @version 0.0.1
 * @date 2023-11-04
 */
import { graphicLayerIds } from "./interface";
/**
 * Arcgis 4.x 定位相关通用方法
 */
export class Locate {
  /**
   * 视图对象
   * @private
   */
  _map: any = null;
  _esri: any = null;
  /**
   * 构造函数
   * @param  map - 二维的地图视图(Map)
   */
  constructor(map: any, esri: any) {
    this._map = map;
    this._esri = esri;
  }
  gotoPoint(
    point?: any,
    zoom: number = 14,
    //@ts-ignore
    showIcon: Boolean = true,
    markerUrl: string = "/arcgis/assets/images/position/tag.png",
    height: number = 20,
    width: number = 18
  ) {
    if (!point) {
      return;
    }

    //提取点坐标
    const x = Number(point.x !== undefined ? point.x : point.longitude);
    const y = Number(point.y !== undefined ? point.y : point.latitude);


    let geometry = new this._esri.Point({
      x: x,
      y: y,
      spatialReference: { wkid: point.wkid }
    });

    console.log(point, geometry);
    if (geometry) {
      // if(geometry)
      this.drawGraphic(
        geometry,
        true,
        graphicLayerIds.locateGraphicsLayer,
        markerUrl,
        9999,
        null,
        width,
        height
      );
      this.gotoGeometry(geometry, zoom);
    }
  }
  drawPoint(
    geojson: any,
    //@ts-ignore
    markerUrl: string = "/arcgis/assets/images/position/tag.png",
    //@ts-ignore
    height: number = 20,
    //@ts-ignore
    width: number = 18
  ) {
    //转换geojson数据为arcgis api 3.x的点对象
    //@ts-ignore
    let geometry = new this._esri.Point(
      geojson.coordinates[0],
      geojson.coordinates[1],
      new this._esri.SpatialReference({ wkid: 4490 })
    );
  }
  drawGraphic(
    geometry: any,
    isClear = true,
    graphicLayerId = graphicLayerIds.drawGraphicsLayer,
    markerUrl = "/arcgis/assets/images/position/tag.png",
    order = 9999,
    color: any,
    width: number,
    height: number,
    attributes?: any,
  ) {
    if (geometry.onlyDraw) {
      geometry = new this._esri.Point(
        Number(geometry.x),
        Number(geometry.y),
        new this._esri.SpatialReference({ wkid: geometry.wkid })
      );
    }
    let graphicsLayer = this._map.getLayer(graphicLayerId);
    if (!graphicsLayer) {
      graphicsLayer = new this._esri.GraphicsLayer({ id: graphicLayerId });
      this._map.addLayer(graphicsLayer);
    }
    this._map.reorderLayer(graphicsLayer, order);
    if (isClear) {
      graphicsLayer.clear();
    }
    if (geometry.spatialReference.wkid == 4490) {
      let graphic = this.createGraphic(
        geometry,
        markerUrl,
        color,
        width,
        height,
        attributes
      );
      if (graphic) {
        graphicsLayer.add(graphic);
      }
    } else {
    }
  }
  createGraphic(
    geometry: any,
    markerUrl = "/arcgis/assets/images/tag.png",
    color: any,
    width = 20,
    height = 18,
    attributes?: any
  ) {
    if (geometry.type == "point") {
      let picSymbol = new this._esri.PictureMarkerSymbol({
        url: markerUrl,
        height: height,
        width: width,
        type: "esriPMS",
      });
      return new this._esri.Graphic(geometry, picSymbol, attributes);
    } else if (geometry.type == "polyline") {
      let LineSymbol = new this._esri.SimpleLineSymbol(
        this._esri.SimpleLineSymbol.STYLE_SOLID,
        new this._esri.Color(color ?? [0, 255, 255]),
        2
      );
      return new this._esri.Graphic(
        new this._esri.Polyline(geometry),
        LineSymbol,
        attributes
      );
    } else if (geometry.type == "multipoint") {
      let graphics = [] as any;
      geometry.points.forEach((point: any) => {
        let picSymbol = new this._esri.PictureMarkerSymbol({
          url: markerUrl,
          height: height,
          width: width,
          type: "esriPMS",
        });
        graphics.add(new this._esri.Graphic(point, picSymbol, attributes));
      });
      return graphics;
    } else if (geometry.type == "polygon") {
      let fillSymbol = new this._esri.SimpleFillSymbol().setColor(
        new this._esri.Color([173, 220, 220, 0.35])
      );
      fillSymbol.setOutline(
        new this._esri.SimpleLineSymbol(
          this._esri.SimpleLineSymbol.STYLE_SOLID,
          new this._esri.Color(color ? color : [0, 255, 255]),
          2
        )
      );
      const polygon = new this._esri.Polygon(geometry);
      return new this._esri.Graphic(polygon, fillSymbol, attributes);
    } else {
      return null;
    }
  }
  //地图绽放至对象
  gotoGeometry(geometry: any, zoomOrRation?: number) {
    if (geometry.type == "point") {
      this._map.centerAndZoom(geometry, zoomOrRation || 17);
    } else if (geometry.type == "multipoint") {
      if (geometry.points) {
        if (geometry.points.length == 1) {
          let x = 0,
            y = 0;
          if (geometry.points[0].x && geometry.points[0].y) {
            x = geometry.points[0].x;
            y = geometry.points[0].y;
          } else if (geometry.points[0][0] && geometry.points[0][1]) {
            x = geometry.points[0][0];
            y = geometry.points[0][1];
          }

          let pt = new this._esri.Point(
            [x, y],
            new this._esri.SpatialReference({ wkid: 4490 })
          );
          console.log(pt);
          this._map.centerAndZoom(pt, 17);
        } else if (geometry.points.length == 2) {
          let polyline = new this._esri.Polyline(
            new this._esri.SpatialReference({ wkid: 4490 })
          );
          polyline.addPath(geometry.points);
          if (polyline) {
            console.log(polyline);
            let extent = polyline.getExtent();
            if (extent) {
              this._map.setExtent(extent.expand(zoomOrRation || 1.8));
            }
          }
        } else {
          let extent = geometry.getExtent();
          if (extent) {
            this._map.setExtent(extent.expand(zoomOrRation || 1.8));
          }
        }
      }
    } else {
      if (!geometry.getExtent) {
        if (geometry.type == "polygon") {
          geometry = new this._esri.Polygon(geometry);
        } else if (geometry.type == "polyline") {
          geometry = new this._esri.Polyline(geometry);
        }
      }

      let extent = geometry.getExtent();
      if (extent) {
        this._map.setExtent(extent.expand(zoomOrRation || 1.8));
      }
    }
  }
  /**
   * 地图缩放至范围
   * @param extent
   */
  gotoExtent(extent: any, ratio: number) {
    this._map.setExtent(extent, ratio);
  }
}
