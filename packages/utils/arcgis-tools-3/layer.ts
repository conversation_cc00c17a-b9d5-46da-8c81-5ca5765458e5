/**
 * @file Arcgis 3.x 二维图层相关通用方法
 * <AUTHOR>
 * @version 0.0.1
 * @date 2023-11-01
 */
import { getCookie } from "./utils";
import * as IOnemap from "../../interface/IOnemap";
import * as base from "../../base/base";
/**
 * Arcgis 3.x 二维图层相关通用方法
 */
export class Layer {
	/**
	 * 视图对象
	 * @private
	 */
	_map: any = null;
	_esri: any = null;
	_lods: any = [
		{ level: 0, resolution: 1.406250000000238, scale: 590995186.1176 },
		{ level: 1, resolution: 0.703125000000119, scale: 295497593.0588 },
		{ level: 2, resolution: 0.3515625000000595, scale: 147748796.5294 },
		{ level: 3, resolution: 0.17578125000002975, scale: 73874398.2647 },
		{ level: 4, resolution: 0.08789062500001488, scale: 36937199.13235 },
		{ level: 5, resolution: 0.04394531250000744, scale: 18468599.566175 },
		{ level: 6, resolution: 0.02197265625000372, scale: 9234299.7830875 },
		{ level: 7, resolution: 0.01098632812500186, scale: 4617149.89154375 },
		{ level: 8, resolution: 0.00549316406250093, scale: 2308574.945771875 },
		{
			level: 9,
			resolution: 0.002746582031250465,
			scale: 1154287.4728859374,
		},
		{
			level: 10,
			resolution: 0.0013732910156252325,
			scale: 577143.7364429687,
		},
		{
			level: 11,
			resolution: 0.0006866455078126162,
			scale: 288571.86822148436,
		},
		{
			level: 12,
			resolution: 0.0003433227539063081,
			scale: 144285.93411074218,
		},
		{
			level: 13,
			resolution: 0.00017166137695315406,
			scale: 72142.96705537109,
		},
		{
			level: 14,
			resolution: 0.00008583068847657703,
			scale: 36071.483527685545,
		},
		{
			level: 15,
			resolution: 0.000042915344238288514,
			scale: 18035.741763842772,
		},
		{
			level: 16,
			resolution: 0.000021457672119144257,
			scale: 9017.870881921386,
		},
		{
			level: 17,
			resolution: 0.000010728836059572129,
			scale: 4508.935440960693,
		},
		{
			level: 18,
			resolution: 0.000005364418029786064,
			scale: 2254.4677204803465,
		},
		{
			level: 19,
			resolution: 0.000002682209014893032,
			scale: 1127.2338602401733,
		},
		{
			level: 20,
			resolution: 0.000001341104507446516,
			scale: 563.6169301200866,
		},
		{
			level: 21,
			resolution: 6.70552253723258e-7,
			scale: 281.8084650600433,
		},
		{
			level: 22,
			resolution: 3.35276126861629e-7,
			scale: 140.9042325300217,
		},
		{
			level: 23,
			resolution: 1.676380634308145e-7,
			scale: 70.45211626501083,
		},
		{
			level: 24,
			resolution: 8.381903171540725e-8,
			scale: 35.22605813250541,
		},
		{
			level: 25,
			resolution: 4.190951585770363e-8,
			scale: 17.61302906625271,
		},
		{
			level: 26,
			resolution: 2.0954757928851814e-8,
			scale: 8.806514533126353,
		},
	];
	factoryLayer = {
		tileInfo: {
			rows: 256,
			cols: 256,
			compressionQuality: 0,
			dpi: 96,
			origin: { x: -180, y: 90 },
			spatialReference: { wkid: 4490, latestWkid: 4490 },
			lods: this._lods,
		},
		defaultTileInfo: {
			cols: 256,
			rows: 256,
			compressionQuality: 0,
			dpi: 96,
			origin: { x: -180, y: 90 },
			spatialReference: { wkid: 4490, latestWkid: 4490 },
			lods: this._lods,
			format: "PNG",
		},
		tileInfoConfig: {
			rows: 256,
			cols: 256,
			compressionQuality: 0,
			dpi: 90.71428571427429,
			origin: { x: -180, y: 90 },
			spatialReference: { wkid: 4490 },
			lods: this._lods,
		} as any,
		_layer: {},
		defaultWkid: 4490,
		create: async function (options: any, esri: any) {
			if (
				Object.getOwnPropertyNames(this._layer).includes(options.serviceType)
			) {
				try {
					let imageParameters = new esri.ImageParameters();
					imageParameters.customParameters = {
						sessionid: getCookie("sessionid"),
					};
					options.options = options.options || {};
					options.options.id = options.layerid;
					options.options.imageParameters = imageParameters;
					let layer: any = null;
					if (
						options.serviceType == IOnemap.serviceType.ArcgisWebTiledLayer ||
						options.serviceType == IOnemap.serviceType.tiandiMap
					) {
						if (options.options?.lods) {
							this.tileInfoConfig.lods = options.options.lods;
						}
						let config = {
							id: options.layerid,
							tileInfo: new esri.TileInfo(this.tileInfoConfig),
							spatialReference: { wkid: Number(this.defaultWkid) },
							fullExtent: {
								xmin: -180,
								ymin: -90,
								xmax: 180,
								ymax: 90,
								spatialReference: { wkid: Number(this.defaultWkid) },
							}
						} as any;
						if (options.url && options.url.indexOf("{subDomain}") >= 0) {
							config.subDomains = [
								"t0",
								"t1",
								"t2",
								"t3",
								"t4",
								"t5",
								"t6",
								"t7",
							];
						}

						const reverseRegexLevel = /([Tt][Ii][Ll][Ee][Mm][Aa][Tt][Rr][Ii][Xx])={TileMatrix}/g;
						const reverseRegexRow = /([Tt][Ii][Ll][Ee][Rr][Oo][Ww])={TileRow}/g;
						const reverseRegexCol = /([Tt][Ii][Ll][Ee][Cc][Oo][Ll])={TileCol}/g;

						if (reverseRegexLevel.test(options.url!)) {
							options.url = options.url!.replace(reverseRegexLevel, "$1={level}");
							options.url = options.url!.replace(reverseRegexRow, "$1={row}");
							options.url = options.url!.replace(reverseRegexCol, "$1={col}");
						}
						layer = new esri.WebTiledLayer(options.url, config);
					} /* 4326 加载
          else if (options.serviceType == IOnemap.serviceType.tiandiMap) {
            var tileInfo = new esri.TileInfo({
              dpi: 90.71428571427429,
              rows: 256,
              cols: 256,
              compressionQuality: 0,
              origin: {
                x: -180,
                y: 90,
              },
              spatialReference: {
                wkid: 4326,
              },
              lods: [
                {
                  level: 2,
                  levelValue: 2,
                  resolution: 0.3515625,
                  scale: 147748796.52937502,
                },
                {
                  level: 3,
                  levelValue: 3,
                  resolution: 0.17578125,
                  scale: 73874398.264687508,
                },
                {
                  level: 4,
                  levelValue: 4,
                  resolution: 0.087890625,
                  scale: 36937199.132343754,
                },
                {
                  level: 5,
                  levelValue: 5,
                  resolution: 0.0439453125,
                  scale: 18468599.566171877,
                },
                {
                  level: 6,
                  levelValue: 6,
                  resolution: 0.02197265625,
                  scale: 9234299.7830859385,
                },
                {
                  level: 7,
                  levelValue: 7,
                  resolution: 0.010986328125,
                  scale: 4617149.8915429693,
                },
                {
                  level: 8,
                  levelValue: 8,
                  resolution: 0.0054931640625,
                  scale: 2308574.9457714846,
                },
                {
                  level: 9,
                  levelValue: 9,
                  resolution: 0.00274658203125,
                  scale: 1154287.4728857423,
                },
                {
                  level: 10,
                  levelValue: 10,
                  resolution: 0.001373291015625,
                  scale: 577143.73644287116,
                },
                {
                  level: 11,
                  levelValue: 11,
                  resolution: 0.0006866455078125,
                  scale: 288571.86822143558,
                },
                {
                  level: 12,
                  levelValue: 12,
                  resolution: 0.00034332275390625,
                  scale: 144285.93411071779,
                },
                {
                  level: 13,
                  levelValue: 13,
                  resolution: 0.000171661376953125,
                  scale: 72142.967055358895,
                },
                {
                  level: 14,
                  levelValue: 14,
                  resolution: 8.58306884765625e-5,
                  scale: 36071.483527679447,
                },
                {
                  level: 15,
                  levelValue: 15,
                  resolution: 4.291534423828125e-5,
                  scale: 18035.741763839724,
                },
                {
                  level: 16,
                  levelValue: 16,
                  resolution: 2.1457672119140625e-5,
                  scale: 9017.8708819198619,
                },
                {
                  level: 17,
                  levelValue: 17,
                  resolution: 1.0728836059570313e-5,
                  scale: 4508.9354409599309,
                },
                {
                  level: 18,
                  levelValue: 18,
                  resolution: 5.3644180297851563e-6,
                  scale: 2254.4677204799655,
                },
                {
                  level: 19,
                  levelValue: 19,
                  resolution: 2.68220901489257815e-6,
                  scale: 1127.23386023998275,
                },
                {
                  level: 20,
                  levelValue: 2,
                  resolution: 1.341104507446289075e-6,
                  scale: 563.616930119991375,
                },
              ],
            });
            layer = new esri.WebTiledLayer(options.url, {
              subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
              tileInfo: tileInfo,
            });
            console.log("================test", layer);subDomains
          }*/ else if (
						options.serviceType == IOnemap.serviceType.ArcgisTilesMap
					) {
						let _Options = options;
						this._layer[options.serviceType] = esri.declare(
							"ArcgisTilesMap",
							esri.TiledMapServiceLayer,
							{
								id: undefined,
								constructor: function (url: any, args: any) {
									this.id = _Options.layerid;
									base.ArcgisTilesMap(this, url, args, esri.TileInfo);
									this.loaded = true;
									this.onLoad(this);
								},
							}
						);
						layer = new this._layer[options.serviceType](
							options.url,
							options?.options?.arcOption
						);
					} else {
						layer = new this._layer[options.serviceType](`${options.url}`, {
							...options.options,
						});
					}
					if (
						layer.setImageFormat &&
						options.serviceType != IOnemap.serviceType.ArcgisTilesMap
					) {
						layer.setImageFormat("png32");
					}
					layer.on("error", function (response: any) {
						console.log(response);
						if (
							[400, 500, 401].includes(response.error.code) ||
							response.target.loadError
						) {
							console.log(
								"图层【%s】加载错误: %s",
								options.name,
								response.error.message
							);
						}
					});
					layer["layerid"] = options?.layerid;
					layer["layerType"] = options?.layerType;
					layer["isDynamicMapLayer"] = options?.isDynamicMapLayer;
					return layer;
				} catch (e) {
					console.log(e, options);
					return null;
				}
			}

			return null;
		},

		//@ts-ignore
		createByAttributes: function (options: any, properties: any) {
			if (
				options.attributes &&
				Object.getOwnPropertyNames(this._layer).includes(
					options.attributes.layerType
				)
			) {
				try {
					let layer = this._layer[options.attributes.layerType](options.url, {
						...options.attributes,
					});
					layer.setVisibility(false);

					if (layer.setImageFormat) {
						layer.setImageFormat("png32");
					}
					layer.on("error", function (response: any) {
						console.log(response);
						if (
							[400, 500, 401].includes(response.error.code) ||
							response.target.loadError
						) {
							console.log(
								"图层【%s】加载错误: %s",
								options.name,
								response.error.message
							);
						}
					});

					layer["layerid"] = options?.layerid;
					layer["layerType"] = options?.layerType;
					layer["isDynamicMapLayer"] = options?.isDynamicMapLayer;
					return layer;
				} catch (e) {
					console.log(e);
					return null;
				}
			}
			return null;
		},
	} as any;
	/**
	 * 构造函数
	 * @param  map - 二维的地图视图(Map)
	 */
	constructor(map: any, esri: any) {
		this._map = map;
		this._esri = esri;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisVectorTileLayer] =
			esri.ArcGISDynamicMapServiceLayer;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisMapImageLayer] =
			esri.ArcGISDynamicMapServiceLayer;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisDynamicLayer] =
			esri.ArcGISDynamicMapServiceLayer;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisTileLayer] =
			esri.ArcGISTiledMapServiceLayer;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisFeatureLayer] =
			esri.FeatureLayer;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisWebTiledLayer] =
			esri.WebTiledLayer;
		this.factoryLayer._layer[IOnemap.serviceType.tiandiMap] =
			esri.WebTiledLayer;
		this.factoryLayer._layer[IOnemap.serviceType.ArcgisTilesMap] =
			esri.ArcGISTiledMapServiceLayer;
	}
	/**
	 * 创建图层并返回 WebTiledLayer WMSLayer ArcGISTiledMapServiceLayer
	 * @param {*} options 图层服务
	 */
	async createLayer(options: any) {
		return await this.factoryLayer.create(options, this._esri);
	}
	setLayerOpacity(layer: any, opacity: number) {
		let ly = this._map.getLayer(layer.layerid);
		if (ly) {
			ly.setOpacity(opacity);
		}
	}
}
