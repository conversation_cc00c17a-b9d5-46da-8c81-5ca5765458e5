import Cookies from "js-cookie"
export const getCookie = (name: string) => {

  let token = localStorage.getItem(name);
  if (token) {
    return token;
  }
  if (Cookies.get(name)) {
    return Cookies.get(name)
  }
  else {
    return Cookies.get("sessionid")
  }
  // if (document.cookie.length > 0) {
  //   let cStart = document.cookie.indexOf(name + "=");
  //   if (cStart != -1) {
  //     cStart = cStart + name.length + 1;
  //     let cEnd = document.cookie.indexOf(";", cStart);
  //     if (cEnd == -1) cEnd = document.cookie.length;
  //     return decodeURIComponent(document.cookie.substring(cStart, cEnd));
  //   }
  // }
  // return "";
};