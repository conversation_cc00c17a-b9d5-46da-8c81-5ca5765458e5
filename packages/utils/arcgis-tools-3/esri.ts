import * as esriLoader from "esri-loader"; 
export const ArcGIS = { esri: undefined as any }; 
export function loadEsri(callback: Function) {
  let esriObj: any = null;
  if (!esriObj) {
    esriLoader.loadModules(["dojo/_base/declare"]).then(([declare]) => { 
      esriLoader
        .loadModules([
          "dojo/dom",
          "esri/map",
          "esri/graphic",
          "dojo/_base/config",
          "esri/basemaps",
          "esri/layers/TiledMapServiceLayer",
          "esri/layers/ArcGISTiledMapServiceLayer",
          "esri/layers/ArcGISDynamicMapServiceLayer",
          "esri/layers/FeatureLayer",
          "esri/layers/ArcGISImageServiceVectorLayer",
          "esri/layers/ArcGISImageServiceLayer",
          "esri/layers/WMSLayer",
          "esri/layers/WMSLayerInfo",
          // "esri/layers/BaseTileLayer",
          "esri/SpatialReference",
          "esri/geometry/Circle",
          "esri/geometry/Extent",
          "esri/geometry/Point",
          "esri/geometry/Polygon",
          "esri/geometry/Polyline",
          "esri/geometry/Geometry",
          "esri/geometry/webMercatorUtils",
          "esri/dijit/Measurement",
          "dojo/on",
          "esri/Color",
          "dojo/topic",
          "esri/layers/DynamicMapServiceLayer",
          "esri/symbols/SimpleMarkerSymbol",
          "esri/symbols/FillSymbol",
          "esri/symbols/LineSymbol",
          "esri/symbols/SimpleFillSymbol",
          "esri/symbols/SimpleLineSymbol",
          "esri/symbols/TextSymbol",
          "esri/toolbars/draw",
          "esri/tasks/query",
          "esri/tasks/QueryTask",
          "esri/layers/WebTiledLayer",
          "esri/layers/TileInfo",
          "esri/graphicsUtils",
          "esri/geometry/geometryEngine",
          // "esri/geometry/Geodesic",
          "esri/tasks/ProjectParameters",
          "esri/tasks/BufferParameters",
          "esri/tasks/GeometryService",
          "esri/tasks/IdentifyParameters",
          "esri/tasks/IdentifyResult",
          "esri/tasks/IdentifyTask",
          "esri/dijit/BasemapLayer",
          "esri/layers/GraphicsLayer",
          "esri/config",
          "esri/geometry/jsonUtils",
          "esri/symbols/PictureMarkerSymbol",
          "esri/geometry/Multipoint",
          "esri/layers/MapImageLayer",
          "esri/symbols/Font",
          "esri/dijit/Legend",
          "esri/tasks/PrintTask",
          "esri/tasks/PrintParameters",
          "esri/dijit/Print",
          "esri/tasks/PrintTemplate",
          "esri/dijit/LayerSwipe",
          "esri/request",
          "esri/layers/ImageParameters",
          "esri/symbols/jsonUtils",
          "esri/geometry/geodesicUtils",
          "esri/units",
          "dojo/domReady!",
          "dojo/_base/declare",
          //=========
          "esri/renderers/SimpleRenderer",

        ])
        .then(
          ([
            dom,
            Map,
            Graphic,
            dojoConfig,
            basemaps,
            TiledMapServiceLayer,
            ArcGISTiledMapServiceLayer,
            ArcGISDynamicMapServiceLayer,
            FeatureLayer,
            ArcGISImageServiceVectorLayer,
            ArcGISImageServiceLayer,
            WMSLayer,
            WMSLayerInfo,
            // BaseTileLayer,
            // declare ,
            SpatialReference,
            Circle,
            Extent,
            Point,
            Polygon,
            Polyline,
            Geometry,
            webMercatorUtils,
            Measurement,
            esriOn,
            Color,
            _topic,
            DynamicMapServiceLayer,
            SimpleMarkerSymbol,
            FillSymbol,
            LineSymbol,
            SimpleFillSymbol,
            SimpleLineSymbol,
            TextSymbol,
            Draw,
            Query,
            QueryTask,
            WebTiledLayer,
            TileInfo,
            graphicsUtils,
            geometryEngine,
            // Geodesic,
            ProjectParameters,
            BufferParameters,
            GeometryService,
            IdentifyParameters,
            IdentifyResult,
            IdentifyTask,
            BasemapLayer,
            GraphicsLayer,
            esriConfig,
            jsonUtils,
            PictureMarkerSymbol,
            Multipoint,
            MapImageLayer,
            Font,
            Legend,
            PrintTask,
            PrintParameters,
            Print,
            PrintTemplate,
            LayerSwipe,
            esriRequest,
            ImageParameters,
            symbolJsonUtils,
            geodesicUtils,
            Units,
            //=======
            SimpleRenderer
          ]) => {
            const esri = {
              dom: dom,
              Map: Map,
              Graphic: Graphic,
              dojoConfig: dojoConfig,
              basemaps: basemaps,
              TiledMapServiceLayer: TiledMapServiceLayer,
              ArcGISTiledMapServiceLayer: ArcGISTiledMapServiceLayer,
              ArcGISDynamicMapServiceLayer: ArcGISDynamicMapServiceLayer,
              FeatureLayer: FeatureLayer,
              ArcGISImageServiceVectorLayer: ArcGISImageServiceVectorLayer,
              ArcGISImageServiceLayer: ArcGISImageServiceLayer,
              SpatialReference: SpatialReference,

              WMSLayer: WMSLayer,
              WMSLayerInfo: WMSLayerInfo,
              //@ts-ignore
              // ArcgisTilesMap:ArcgisTilesMap,
              // BaseTileLayer:BaseTileLayer,
              declare : declare ,
              Circle: Circle,
              Extent: Extent,
              Point: Point,
              Polygon: Polygon,
              Polyline: Polyline,
              Geometry: Geometry,
              webMercatorUtils: webMercatorUtils,
              Measurement: Measurement,
              esriOn: esriOn,
              Color: Color,
              _topic: _topic,
              DynamicMapServiceLayer: DynamicMapServiceLayer,
              SimpleMarkerSymbol: SimpleMarkerSymbol,
              FillSymbol: FillSymbol,
              LineSymbol: LineSymbol,
              SimpleFillSymbol: SimpleFillSymbol,
              SimpleLineSymbol: SimpleLineSymbol,
              TextSymbol: TextSymbol,
              Draw: Draw,
              Query: Query,
              QueryTask: QueryTask,
              WebTiledLayer: WebTiledLayer,
              TileInfo: TileInfo,
              graphicsUtils: graphicsUtils,
              geometryEngine: geometryEngine,
              // Geodesic: Geodesic,
              ProjectParameters: ProjectParameters,
              BufferParameters: BufferParameters,
              GeometryService: GeometryService,
              IdentifyParameters: IdentifyParameters,
              IdentifyResult: IdentifyResult,
              IdentifyTask: IdentifyTask,
              BasemapLayer: BasemapLayer,
              GraphicsLayer: GraphicsLayer,
              esriConfig: esriConfig,
              jsonUtils: jsonUtils,
              PictureMarkerSymbol: PictureMarkerSymbol,
              Multipoint: Multipoint,
              MapImageLayer: MapImageLayer,
              Font: Font,
              Legend: Legend,
              PrintTask: PrintTask,
              PrintParameters: PrintParameters,
              Print: Print,
              PrintTemplate: PrintTemplate,
              LayerSwipe: LayerSwipe,
              esriRequest: esriRequest,
              ImageParameters: ImageParameters,
              symbolJsonUtils: symbolJsonUtils,
              geodesicUtils: geodesicUtils,
              Units: Units,
              //==========
              SimpleRenderer:SimpleRenderer
            }; 
            // 注册获取要素的方法 
            if (esriConfig.defaults?.io) {
              esriConfig.defaults.io.corsEnabledServers.push(
                "zhdtjc.dev.nnric.net"
              );
              esriConfig.defaults.io.corsEnabledServers.push(
                "zhdtjc-develop.nnland.cn"
              );
            }
            esriObj = esri;
            ArcGIS.esri = esriObj; 
            if (callback !== undefined) {
              callback(esri);
            }
          }
        );
    });
  } else {
    if (callback !== undefined) {
      callback(esriObj);
    }
  }
}
