import proj4 from "proj4";
import wkids from "./wkids.json";
export const projectPoint = (
  fromProjection: number,
  toProjection: number,
  point: Array<number>
) => {
  const wkidData = wkids as any;
  if (point && point.length > 0 && fromProjection === toProjection) {
    return point;
  }
  try {
    if (!wkidData[fromProjection] && !wkidData[toProjection]) {
      return null;
    }

    return proj4(wkidData[fromProjection], wkidData[toProjection], point);
  } catch (e) {
    console.log(e);
    return null;
  }
};
export const projectPoints = (
  fromProjection: number,
  toProjection: number,
  points: Array<Array<number>>
) => {
  const wkidData = wkids as any;
  if (points && points.length > 0 && fromProjection === toProjection) {
    return points;
  }
  let outPoints = [] as any;
  try {
    points.forEach((point) => {
      outPoints.push(proj4(wkidData[fromProjection], wkidData[toProjection], point));
    });
    if (!wkidData[fromProjection] && !wkidData[toProjection]) {
      return null;
    }
    return outPoints;
  } catch (e) {
    console.log(e);
    return null;
  }
};
export const projectPath = (
  fromProjection: number,
  toProjection: number,
  path: [number, number][]
) => {
  if (path && path.length > 0 && fromProjection === toProjection) {
    return path;
  }
  try {
    const wkidData = wkids as any;
    if (!wkidData[fromProjection] && !wkidData[toProjection]) {
      return null;
    }
    const projection = proj4(wkidData[fromProjection], wkidData[toProjection]);
    let outPath = path.map(([x, y]) => {
      const result = projection.forward([x, y]);
      return result;
    });
    return outPath;
  } catch (e) {
    console.log(e);
    return null;
  }
};
