import { markRaw, nextTick } from "vue";
import { cloneDeep } from "lodash";

import {
  OnemapClass,
  Utils,
  mapType,
  type IMapLayer,
} from "../onemapkit";
import * as base from "../base/base";
import * as Cesium from "@onemapkit/cesium";

Cesium.Viewer.prototype.destroy = function () {
	// 在销毁之前触发一个自定义的事件
	(this as any).beforeDestroyEvent.raiseEvent();
	
	let i;
	if (
	  Cesium.defined(this.screenSpaceEventHandler) &&
	  !this.screenSpaceEventHandler.isDestroyed()
	) {
	  this.screenSpaceEventHandler.removeInputAction(
		Cesium.ScreenSpaceEventType.LEFT_CLICK
	  );
	  this.screenSpaceEventHandler.removeInputAction(
		Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
	  );
	}
  
	// Unsubscribe from data sources
	const dataSources = this.dataSources;
	const dataSourceLength = dataSources.length;
	for (i = 0; i < dataSourceLength; i++) {
	  (this as any)._dataSourceRemoved(dataSources, dataSources.get(i));
	}
	(this as any)._dataSourceRemoved(
	  undefined,
	  (this as any)._dataSourceDisplay.defaultDataSource
	);
  
	(this as any)._container.removeChild((this as any)._element);
	(this as any)._element.removeChild((this as any)._toolbar);
  
	(this as any)._eventHelper.removeAll();
  
	if (Cesium.defined((this as any)._geocoder)) {
	  (this as any)._geocoder = (this as any)._geocoder.destroy();
	}
  
	if (Cesium.defined((this as any)._homeButton)) {
	  (this as any)._homeButton = (this as any)._homeButton.destroy();
	}
  
	if (Cesium.defined((this as any)._sceneModePicker)) {
	  (this as any)._sceneModePicker = (this as any)._sceneModePicker.destroy();
	}
  
	if (Cesium.defined((this as any)._projectionPicker)) {
	  (this as any)._projectionPicker = (this as any)._projectionPicker.destroy();
	}
  
	if (Cesium.defined((this as any)._baseLayerPicker)) {
	  (this as any)._baseLayerPicker = (this as any)._baseLayerPicker.destroy();
	}
  
	if (Cesium.defined((this as any)._animation)) {
	  (this as any)._element.removeChild((this as any)._animation.container);
	  (this as any)._animation = (this as any)._animation.destroy();
	}
  
	if (Cesium.defined((this as any)._timeline)) {
	  (this as any)._timeline.removeEventListener(
		"settime",
		(e: any) => {
		  const clock = e.clock;
		  clock.currentTime = e.timeJulian;
		  clock.shouldAnimate = false;
		},
		false
	  );
	  (this as any)._element.removeChild((this as any)._timeline.container);
	  (this as any)._timeline = (this as any)._timeline.destroy();
	}
  
	if (Cesium.defined((this as any)._fullscreenButton)) {
	  (this as any)._fullscreenSubscription.dispose();
	  (this as any)._element.removeChild(
		(this as any)._fullscreenButton.container
	  );
	  (this as any)._fullscreenButton = (this as any)._fullscreenButton.destroy();
	}
  
	if (Cesium.defined((this as any)._vrButton)) {
	  (this as any)._vrSubscription.dispose();
	  (this as any)._vrModeSubscription.dispose();
	  (this as any)._element.removeChild((this as any)._vrButton.container);
	  (this as any)._vrButton = (this as any)._vrButton.destroy();
	}
  
	if (Cesium.defined((this as any)._infoBox)) {
	  (this as any)._element.removeChild((this as any)._infoBox.container);
	  (this as any)._infoBox = (this as any)._infoBox.destroy();
	}
  
	if (Cesium.defined((this as any)._selectionIndicator)) {
	  (this as any)._element.removeChild(
		(this as any)._selectionIndicator.container
	  );
	  (this as any)._selectionIndicator = (
		this as any
	  )._selectionIndicator.destroy();
	}
  
	if ((this as any)._destroyClockViewModel) {
	  (this as any)._clockViewModel = (this as any)._clockViewModel.destroy();
	}
	(this as any)._dataSourceDisplay = (this as any)._dataSourceDisplay.destroy();
	(this as any)._cesiumWidget = (this as any)._cesiumWidget.destroy();
  
	if ((this as any)._destroyDataSourceCollection) {
	  (this as any)._dataSourceCollection = (
		this as any
	  )._dataSourceCollection.destroy();
	}
  
	return Cesium.destroyObject(this);
  };

// import {loadlic, _loadlogo } from "../license/allowlic";
// import { cloneDeep } from "lodash";
export function czmsetup(_inOptions: any, _Onemap: OnemapClass, emits: any, _PropStore: any): any {
	/*
		Cesium.Ion.defaultAccessToken =
			"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.IhkngSXwFixfux7Z4HBWmAw-pFhqfDzmyUO0v3lLy_4";
	*/
	//#region 1. 必要参数解析

	// 1.2 从MapLayer中解析 Cesium 的底图参数 BaseMapLayer
	if (_inOptions.BaseMapLayer == null) {
		Utils.ThrowError("底图参数设置有误,创建底图失败。", _inOptions);
	}
	// 1.3 从Options中 baseLayer  Promise<Cesium.ImageryLayer> 参数
	let BaseResult: any = [];
	function innerBaseMap(_Onemap: OnemapClass): any {
		let tmpBaseMapLayer: any;
		if (_inOptions.BaseMapLayer == false) return false as any;
		tmpBaseMapLayer = cloneDeep(_inOptions.BaseMapLayer);
		tmpBaseMapLayer["subLayers"] = cloneDeep(
			Utils.getSubLayers(tmpBaseMapLayer?.subLayers, mapType.cesium)
		);

		if (tmpBaseMapLayer["subLayers"].length > 1) {
			const _LayerProvider: any = [];
			tmpBaseMapLayer.subLayers?.forEach((x: any) => {
				_LayerProvider.push(_Onemap.AddLayer(x));
			});
			Promise.all(_LayerProvider).then((_layers: any) => {
				// 按照subLayers的顺序，将_layers添加到tmplayer中
				for (let i = tmpBaseMapLayer.subLayers?.length - 1; i >= 0; i--) {
					try {
						const layer = _layers.find((x: any) => x[0].layerid == tmpBaseMapLayer.subLayers[i].layerid)[0];
						if (layer) {
							BaseResult.push(layer);
							_Onemap.setBottomIndex(layer.option);
						}
					} catch (error) {
						console.error(error);
					}
				};
			});
		} else {
			_Onemap.AddLayer(tmpBaseMapLayer?.subLayers[0]);
		}
	}
	function mapReadyEvent() {
		_Onemap.isMapReady.value = true;
		//是一个默认函数，用户可以自定义的
		_Onemap.setMapReadyEvent(_inOptions);
		_MapReadyEvent(_Onemap.MapControlName, BaseResult);
		if (_inOptions?.mapextent) {
			_Onemap.fullExtent(_inOptions?.mapextent)
		}
	}

	_Onemap?.setMapViewer(
		markRaw(
			new Cesium.Viewer(
				_inOptions.mapContainer??"mapviewerContainer",
				_inOptions?.Options
					? Object.assign(
						base.getOptions(),
						_inOptions.Options
					)
					: base.getOptions()
			)
		)
	);

	// 添加底图
	innerBaseMap(_Onemap);

	//#endregion
	//#region 3 设置地形服务 terrainProvider
	const _MapReadyEvent = (
		mapControlName: String,
		avg: {
			maplayer: any;
			option: IMapLayer;
		}
	) => {
		emits("MapReadyEvent", mapControlName, avg);

	};

	//地图点选事件
	new Cesium.ScreenSpaceEventHandler(
		_Onemap.MapViewer.scene.canvas
	).setInputAction((e: any) => {
		handleMouseClick(e);
	}, Cesium.ScreenSpaceEventType.LEFT_CLICK);

	const handleMouseClick = (event: any) => {
		// if (MapClickEvent) {
		const viewer = _Onemap.MapViewer;
		// 屏幕坐标转世界坐标
		let ray = viewer.camera.getPickRay(event.position);
		let cartesian = viewer.scene.globe.pick(ray, viewer.scene);
		// 将笛卡尔坐标转换为地理坐标
		let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
		// 将弧度转为度的十进制度表示
		/** 当前鼠标点击点的经纬度,地表高度分别是 lon, lat, height*/
		let lon = Cesium.Math.toDegrees(cartographic.longitude);
		let lat = Cesium.Math.toDegrees(cartographic.latitude);
		/** xmax xmin ymin  ymax 分别对应 east west south north*/
		let radianRectangle =
			viewer.camera.computeViewRectangle() as Cesium.Rectangle;
		let west = Cesium.Math.toDegrees(radianRectangle.west);
		let east = Cesium.Math.toDegrees(radianRectangle.east);
		let south = Cesium.Math.toDegrees(radianRectangle.south);
		let north = Cesium.Math.toDegrees(radianRectangle.north);
		//开始构造点选参数
		let clientWidth = viewer.scene.canvas.clientWidth / 2;
		let clientHeight = viewer.scene.canvas.clientHeight / 2;
		let mapOptions = {
			width: clientWidth,
			height: clientHeight,
			extent: {
				xmax: east,
				xmin: west,
				ymax: north,
				ymin: south,
				spatialReference: { wkid: 4490 },
			},
		};
		let mapPoint = { x: lon, y: lat, wkid: 4490 };
		emits("MapClickEvent", _Onemap.MapControlName, mapPoint, mapOptions);
		// }
	};

	//  3.2 设置地形
	let terrain: any = undefined;
	if (_inOptions.TerrainUrl) {
		console.log("加载地形", _inOptions.TerrainUrl);
		// 加载地形
		Cesium.CesiumTerrainProvider.fromUrl(_inOptions.TerrainUrl, {
			requestVertexNormals: true,
		})
			.then((_terrain: any) => {
				console.log("加载地形成功", _terrain);
				_Onemap.MapViewer.scene.terrainProvider = _terrain;
				terrain = _terrain;
			})
			.catch((ex) => {
				console.log("加载地形失败", ex);
				_Onemap.MapViewer.scene.terrainProvider = new Cesium.EllipsoidTerrainProvider();
			});
	} else {
		_Onemap.MapViewer.scene.terrainProvider = new Cesium.EllipsoidTerrainProvider();
	}

	// 监听瓦片加载进度,当首批瓦片加载完成时触发地图就绪事件
	const handleTileLoadProgress = (() => {
		let maxTileCount = 0;
		return (queuedTileCount: number) => {
			maxTileCount = Math.max(maxTileCount, queuedTileCount);
			if (maxTileCount > queuedTileCount) {
				mapReadyEvent();
				_Onemap.MapViewer.scene.globe.tileLoadProgressEvent.removeEventListener(handleTileLoadProgress);
			}
		};
	})();

	_Onemap.MapViewer.scene.globe.tileLoadProgressEvent.addEventListener(handleTileLoadProgress);


	//#endregion
	//#region 4 地图实时回调函数 地图点选事件 后期应以知道对应的按钮事件中去
	{
		// 	console.log('===========三维_Onemap',_Onemap);
		// 	_Onemap.MapRealtimeInterval = 300;
		// 	_Onemap.MapRealtimeDistance = 5;
		// 	// 地图实时回调函数
		// 	_Onemap._MapRealtimeEvent = (mapPoint: any, options: any) => {
		// 	  emits("MapRealtimeEvent", options.MapControlName, mapPoint);
		// 	};
		// 	//地图点选事件
		// 	_Onemap._MapClickEvent = (mapPoint: any, options: any) => {
		// 	  emits("MapClickEvent", mapPoint, options);
		// 	};
	}
	//#endregion
}
