import { ref } from "vue";
import {
  mapType,
  type IPosition,
  OnemapClass,
  type mapEmitsEventType,
  Utils,
  type IMapLayer,
} from "../onemapkit";
import throttle from "lodash/throttle";
import { loadEsri } from "../utils/arcgis-tools-3";
import { cloneDeep } from "lodash";
import { loadScript, loadCss, setDefaultOptions } from "esri-loader";
import {loadlic, _loadlogo } from "../license/allowlic";

declare global {
  interface Window {
    dojoConfig: any;
    ARCGIS_BASE_URL: string;
  }
}

const esriObj: any = ref(null);
//记录鼠标位置
let mousePosition: IPosition = {
  x: 0,
  y: 0,
  z: 0,
  wkid: 4490,
  zoom: 0,
  scale: 0,
};

export function arcsetup(
  _inOptions: any,
  _Onemap: OnemapClass,
  emits: any,
  _LayerStore: any
): Promise<any> {
  window.dojoConfig = {
    parseOnLoad: true,
    async: true,
    tlmSiblingOfDojo: false,
    has: {
      "extend-esri": 1,
    },
    locale: "zh-cn",
    baseUrl: window.ARCGIS_BASE_URL + "/dojo",
    packages: [],
  };

  setDefaultOptions({
    url: window.ARCGIS_BASE_URL + "/init.js",
    css: window.ARCGIS_BASE_URL + "/esri/css/esri.css",
  });

  loadScript({ url: window.ARCGIS_BASE_URL + "/init.js" });
  loadCss(window.ARCGIS_BASE_URL + "/esri/css/esri.css");

  //绑定事件 --- 地图实时回调函数
  const _MapRealtimeEvent: mapEmitsEventType = (
    mapControlName: String,
    mapPoint: any
  ) => {
    emits("MapRealtimeEvent", mapControlName, mapPoint);
  };
  const _MapClickEvent: mapEmitsEventType = (
    mapControlName: String,
    mapPoint: any,
    _options: any
  ) => {
    emits("MapClickEvent", mapControlName, mapPoint, _options);
  };

  //@ts-ignore
  const onMapEvents = (props: any, map: any) => {
    esriObj.value?.esriOn(map, "mouse-move", handleMousePositionThrottled);
    esriObj.value?.esriOn(map, "click", handleMapClick);
  };
  const handleMapClick = (event: any) => {
    let mapPoint = {
      x: event.mapPoint.x,
      y: event.mapPoint.y,
      wkid: event.mapPoint.spatialReference.wkid,
    };
    let mapOptions = {
      width: _Onemap.MapViewer.width,
      height: _Onemap.MapViewer.height,
      extent: {
        xmin: _Onemap.MapViewer.extent.xmin,
        xmax: _Onemap.MapViewer.extent.xmax,
        ymin: _Onemap.MapViewer.extent.ymin,
        ymax: _Onemap.MapViewer.extent.ymax,
        spatialReference: {
          wkid: _Onemap.MapViewer.extent.spatialReference.wkid,
        },
      },
    };
    _MapClickEvent(_inOptions.MapControlName, mapPoint, mapOptions);
  };

  const handleMousePositionThrottled = throttle((event: any) => {
    mousePosition = {
      x: event.mapPoint.x ? Number(parseFloat(event.mapPoint.x).toFixed(5)) : 0,
      y: event.mapPoint.y ? Number(parseFloat(event.mapPoint.y).toFixed(5)) : 0,
      z: 0,
      wkid: event.mapPoint.spatialReference
        ? event.mapPoint.spatialReference.wkid
        : 4490,
    };

    _MapRealtimeEvent(_inOptions.MapControlName, mousePosition);
  }, 200);

	return new Promise(async (resolve, _reject) => {
		loadEsri(async (data: any) => {
			//这个是ArcGIS特有的步骤
    esriObj.value = data;
    esriObj.value.esriRequest.setRequestPreCallback(function (options: any) {
      options.headers = options.headers || {};
      options.headers["sessionid"] = Utils.getCookie("sessionid");
      return options;
    });
    //create arcgis map object
		const params = {
      minZoom:1,
      //maxScale:282,
      ...(typeof _inOptions.maxScale === 'number' ? { maxScale: _inOptions.maxScale } : {}),
      // 仅在 _inOptions.center.x 存在时才设置 center
      ...(typeof _inOptions.center?.x === 'number' && typeof _inOptions.center?.y === 'number' ? { center: [_inOptions.center.x, _inOptions.center.y] } : {}),
     // 仅在 _inOptions.zoom 存在时才设置 zoom
      ...(typeof _inOptions.zoom === 'number' ? { zoom: _inOptions.zoom } : {}),
      autoResize: true,
      fitExtent: true,
      logo: false,
      slider: false,
      navigationMode: "css-transforms",
      spatialReference: new esriObj.value.SpatialReference({ wkid: 4490 }),
    }

		let extent = null;
		if(_inOptions.mapextent){
			extent = new esriObj.value.Extent(
				_inOptions.mapextent.xmax,
				_inOptions.mapextent.xmin,
				_inOptions.mapextent.ymax,
				_inOptions.mapextent.ymin,
				new esriObj.value.SpatialReference({ wkid: 4490 })
			)
		}
    let center=null
    if(_inOptions.center){
      center= [_inOptions.center.x,_inOptions.center.y]
    }

    const arcGisMap = new esriObj.value.Map(_inOptions.mapContainer??"mapviewerContainer",
			Object.assign( extent ? {
				extent: extent
			} : {}, params)
		);

    //这里实例化与MapView相关的对象
    _Onemap.setMapViewer(arcGisMap, esriObj.value);
    //调试用
    (window as any).arcgismap = arcGisMap;

    // 1.3 从Options中 baseLayer  参数
    function innerBaseMap() {
      if (_inOptions.BaseMapLayer == false) return;
      let tmpBaseMapLayer = cloneDeep(_inOptions.BaseMapLayer);
      tmpBaseMapLayer["subLayers"] = cloneDeep(
        Utils.getSubLayers(tmpBaseMapLayer.subLayers, mapType.arcgis)
      );
      if (tmpBaseMapLayer["subLayers"].length == 0) return;
      //加载完地图后返回的地图对象集合

      return _Onemap.AddLayer(tmpBaseMapLayer);
    }
    //地图加载完毕
    innerBaseMap()?.then((baselayer: any) => {
			// 依照_inOptions.BaseMapLayer的subLayer来调整图层顺序
			for(let i = _inOptions.BaseMapLayer.subLayers.length - 1; i >= 0; i--) {
				const layer = baselayer[0].find((x: any) => x.layerid == _inOptions.BaseMapLayer.subLayers[i].layerid);
				if (layer) {
					_Onemap.setBottomIndex(layer.option);

					const _layer = arcGisMap.getLayer(layer.layerid);
					_layer?.setVisibility(layer.option.visible);
				}
			}
      _Onemap.isMapReady.value = true;
      _MapReadyEvent(_inOptions.MapControlName, baselayer);

      if(_inOptions?.mapextent){
        _Onemap?.fullExtent(_inOptions?.mapextent || undefined);
      }
      if (_Onemap.MapReadyHandler) _Onemap.MapReadyHandler();
    });

    //二三维视窗范围同步
    //地图加载完之后，调用相关回调
    const _MapReadyEvent = (
      mapControlName: String,
      avg: {
        maplayer: any;
        option: IMapLayer;
      }
    ) => {
      emits("MapReadyEvent", mapControlName, avg);
      // loadlic
      // .then((lic: any) => {
      //   _Onemap.isEnable = lic;
      //   _loadlogo();
      // })
      // .catch(() => {
      //   _Onemap.isEnable = false;
      //   _loadlogo();
      // });
    };

    _Onemap.MapViewer?.on("load", function () {
      //保存地图加载完毕的状态
			onMapEvents(undefined, arcGisMap);

			resolve({ MapViewer: _Onemap.MapViewer, EsriObj: esriObj.value });
      console.log("arcgis map is load");
      /* 转移至底图加载Promise对象中去
      _MapReadyEvent(_inOptions.MapControlName, {
        maplayer: baselayer,
        option: _inOptions.BaseMapLayer,
      });*/

      // _Onemap.Handler = this.esriObj;
      });
    });
	});
}
