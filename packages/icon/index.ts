import * as IconHTML from "./iconhtml";
// export default Icons;

import PointIcon from "./icon/point.svg";
import lineIcon from "./icon/line.svg";
import polylineIcon from "./icon/polyline.svg";
import Polygon2DIcon from "./icon/polygon2d.svg";
import Polygon3DIcon from "./icon/polygon3d.svg";
import CircleIcon from "./icon/circle.svg";
import RectangleIcon from "./icon/rectangle.svg";
import dataExchange from "./icon/dataExchange.svg";
import clear from "./icon/clear.svg";
import deleteion from "./icon/deleteion.svg";
import FolderNormal from "./icon/folder.svg";
import FolderOpen from "./icon/folder-open.svg";
import positionOpen from "./icon/set_active.svg";
import positionDown from "./icon/set.svg";
import zoomAddIcon from "./icon/zoom_add.svg";
import zoomSubIcon from "./icon/zoom_sub.svg";
import importIcon from "./icon/import.svg";
import kuangxuandayinIcon from "./icon/kuangxuandayin.svg";
import shitudayin01Icon from "./icon/shitudayin_01.svg";
import exportImgIcon from "./icon/export-img.svg";
import measureArea from "./icon/measure_area.svg";
import juanlian from "./icon/juanlian.svg";
import fenping from "./icon/fenping.svg";
import dakaxinxi from "./icon/dakaxinxi.svg";
import zaixianfuwu from "./icon/zaixianfuwu.svg";
import shangchuanzuobiao from "./icon/shangchuanzuobiao.svg";
import shixian from "./icon/shixian.svg";
import dixiakongjian from "./icon/dixiakongjian.svg";
import shiyu from "./icon/shiyu.svg";
import yanmo from "./icon/yanmo.svg";
import qingping from "./icon/qingping.svg";
import dingwei1 from "./icon/dingwei1.svg";
import tucengxuanze from "./icon/tucengxuanze.svg";
import liangsuan from "./icon/liangsuan.svg";
import fenxi from "./icon/fenxi.svg";
import robot from "./icon/robot.svg";
import newDialog from "./icon/new-dialog.svg";
import { uploadCoordType, polygonSymbol_onland, lineType, fillType, defaultSymbol } from "./const";

import {
  ResCatalogIcon,
  OpenFolderIcon,
  CloseFolderIcon,
  LayerIcon,
  NewIcon,
  CollectIcon,
  LocationIcon,
  OpacityIcon,
  ScaleIcon,
  ThematicRoot,
  ThematicTitle,
  InfomationOff,
  InfomationOn,
} from "./MainIcon.json";

import {
  point,
  polyline,
  polygon2d,
  polygon3d,
  rectangle,
  circle,
  draw,
  measure,
  locate,
  clearjosn,
  Switch3D,
  Switch2D,
} from "./toolIcon.json";

export {
  deleteion,
  dataExchange,
  PointIcon,
  polylineIcon,
  lineIcon,
  Polygon2DIcon,
  Polygon3DIcon,
  CircleIcon,
  RectangleIcon,
  uploadCoordType,
  polygonSymbol_onland,
  lineType,
  fillType,
  defaultSymbol,
  clear,
  IconHTML,
  ResCatalogIcon,
  OpenFolderIcon,
  CloseFolderIcon,
  LayerIcon,
  NewIcon,
  CollectIcon,
  LocationIcon,
  OpacityIcon,
  ScaleIcon,
  ThematicRoot,
  ThematicTitle,
  point,
  polyline,
  polygon2d,
  polygon3d,
  rectangle,
  circle,
  draw,
  measure,
  locate,
  clearjosn,
  Switch3D,
  Switch2D,
  InfomationOff,
  InfomationOn,
  FolderNormal,
  FolderOpen,
  positionOpen,
  positionDown,
  zoomAddIcon,
  zoomSubIcon,
  importIcon,
  kuangxuandayinIcon,
  shitudayin01Icon,
  exportImgIcon,
  measureArea,
  juanlian,
  fenping,
  dakaxinxi,
  zaixianfuwu,
  shangchuanzuobiao,
  shixian,
  dixiakongjian,
  shiyu,
  yanmo,
  qingping,
  dingwei1,
  tucengxuanze,
  liangsuan,
  fenxi,
  robot,
  newDialog,
};
export {
  help,
  HelpFilled,
  Tools,
  InfoFilled,
  WarningFilled,
  QuestionFilled,
  Monitor,
  Orange,
  Cpu,
  OfficeBuilding,
  DataAnalysis,
  Operation,
  PieChart,
  DataBoard,
  DataLine,
  TrendCharts,
  Connection,
  Setting,
  Odometer,
  alisetting,
  alisetting1,
  aliMonitor,
  aliControlModule,
  aliSingleChannel,
  aliNavigationSignal,
  aliPositionReceiver,
  aliTamperProof,
  aliSwitchboard,
  aliControlEquipment,
  aliElectricSwitch,
  aliSubsystem,
  aliHighspeedSwitch,
  aliPanoramicMonitoring,
  aliOperationsReport,
  aliParameterSettings,
  aliToolManagement,
  aliExceptionCapture,
  aliVideoMonitoring,
  aliContactMonitoring,
  aliAlarmManagement,
  aliLedgerManagement,
  aliEye,
  aliEyeFill,
  aliTools,
  aliSetting3,
  aliAbnormalOperationWarning,
  aliMonitor2,
  aliSafetyWork,
  aliMonitoringCenterl,
  aliExchange,
  aliRobot,
  aliSecurityLine,
  aliECI,
  aliEIP,
} from "./debugicon";
export { geoSymbol } from "../utils/common-tools/geoSymbol";
export { waterNormals } from "./images/waterNormals";



export {
  LabelImageBlue,
  LabelImageRed,
  locationPurple,
  locationCyan,
  locationRed,
  locationBlue,
  locationOrange,
  siteBlue,
  siteGreed,
  clusterGreed2,
  clusterGreed3,
  clusterGreed4,
  clusterGreed5,
  clusterGreed6,
  clusterGreed7,
  clusterGreed8,
  clusterGreed9,
  clusterGreed10,
  clusterGreed20,
  clusterGreed30,
  clusterGreed40,
  clusterGreed50,
  clusterBlue2,
  clusterBlue3,
  clusterBlue4,
  clusterBlue5,
  clusterBlue6,
  clusterBlue7,
  clusterBlue8,
  clusterBlue9,
  clusterBlue10,
  clusterBlue20,
  clusterBlue30,
  clusterBlue40,
  clusterBlue50,
  LabelRed,
  LabelRed0,
  LabelRed1,
  LabelRed2,
  LabelRed3,
  LabelRed4,
  LabelRed5,
  LabelRed6,
  LabelRed7,
  LabelRed8,
  LabelRed9,
  LabelBlue0,
  LabelBlue1,
  LabelBlue2,
  LabelBlue3,
  LabelBlue4,
  LabelBlue5,
  LabelBlue6,
  LabelBlue7,
  LabelBlue8,
  LabelBlue9,LabelBlue,
  clusterBlue,clusterGreed
} from "./images/labelimage";

// {point,polyline,polygon2d,polygon3d,rectangle,circle}
