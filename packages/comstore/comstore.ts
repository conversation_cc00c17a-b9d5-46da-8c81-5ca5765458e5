import { ref, reactive, type Ref, } from "vue";
import {
  type IMapLayer,
  type IPosition,
  Utils,
  BaseMapType,
  serviceType,
  mapType,
  type IToolItemType,
} from "../onemapkit";
function guid(): any {
  const now = new Date();

  // 获取具体的日期和时间部分
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份从0开始，+1
  const day = now.getDate();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const seconds = now.getSeconds();
  return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;
}
// import * as base from "../base/base";
import { UserStorage, type StorageConstructorOptions } from "../base/base";
import { BottomInfoData } from "@/interface/IOnemap";

export class Globalvariable {
  /** 快捷工具栏名称数组 */
  public ToolQuickItem?: Array<String> = [];
  /** 地形加载最大级别 */
  public terrainMaxLevel?: number = 10;
  /** 屏幕误差 */
  public screenSpaceError?: number = 16;
  /** 地下模式 */
  public underground? = {
    distance: 8000,
    isDistance: true,
    transAlpha: 0.5,
  };
  /** 重力模式 */
  public isGravity? = false;
  /** 设置抗锯齿效果 */
  public isFxaa? = false;
  /** 设置HDR效果 */
  public isHDR? = false;
  /** 鼠标操作模式 */
  public mouseOperationMode?: number = 1;
  /** 显存 */
  public graphicsMemory?: number = 512;
  /** 本地缓存 */
  public indexDBCache?: Array<string> = [];
  /** 三维模型颜色方案 
   @example 
    enhancelight:暗部增亮
    saturation:饱和度
    brightness:亮度
    contrast:对比度 
  */
  public SceneColor?: any = {
    enhancelight: true,
    saturation: 1.263,
    brightness: 1.813,
    contrast: 1.03,
  };
  /**
   * 原始色彩 [enhancelight,saturation,brightness,contrast]
   */
  public originalSceneColor: Array<any> = [false, 1, 1, 1];
  /**
   * 默认色彩 [enhancelight,saturation,brightness,contrast]
   */
  public defaultSceneColor: Array<any> = [true, 1.263, 1.813, 1.03];
  /**
   * 增强色彩 [enhancelight,saturation,brightness,contrast]
   */
  public enhanceSceneColor: Array<any> = [true, 2.0, 1.9, 1.02];

  /** 快捷菜单按钮 */
  public shortcutMenu?: Array<string> = [];
}
/**
 * LayerStorage继承 UserStorage 类
 @example 
  UserStorage { 
    Storage,
    getStorage(key: string),
    //创建代理参数,
    //param 变量的初始化参数,key变量存储名称,
    //isLocalStorage 是存储为LocalStorage，还是sessionStorage
    ProxyParameter(param: Array<any> | Object, key: string,isLocalStorage: boolean = true): any
  }
 */
export class LayerStorage extends UserStorage {
  public MapType = false as any;
  public MapControlName = "mainMapControl";
  /** 添加变量，如果存在返回不做处理
   * @param {StorageConstructorOptions} option 构建变量的参数
   */
  public addStorage(option: StorageConstructorOptions): any {
    if (this.StorageKeyValue.has(option.storagekey)) {
      console.info("已存在变量" + option.storagekey);
    } else {
      this.StorageKeyValue.set(option.storagekey, option);
      this._initStorage(option);
    }
  }
  /** 添加变量，如果存在就覆盖已有变量
   * @param {StorageConstructorOptions} option 构建变量的参数
   */
  public setStorage(option: StorageConstructorOptions): any {
    this.StorageKeyValue.set(option.storagekey, option);
    this._initStorage(option);
  }

  //onemapkit的固定内部变量
  public CheckedLayerids: Array<string> = reactive([]);
  //onemapkit的固定内部变量
  public LayerAlpha: Array<{ layerid: string; alpha: number }> =
    this.ProxyParameter(reactive([]), "LayerAlpha");
  // public get CheckedLayerids(): any {
  //   return this.getStorage("CheckedLayerids") || reactive([]);
  // }
  /** 全局变量存储
   * @param {Array<base.StorageConstructorOptions>}options 
   * @example  
   const PropStore: any = new PropStorage([
      {
        variableType: 2, //变量类型
        storagekey: "CheckedLayerids", //变量名称
        isLocalStorage: true, //是LocationalStorage还是SessionLocalStorage
        initStoragevalue: [], //变量初值
      } 
    ]); 
   */
  constructor(options?: Array<StorageConstructorOptions>) {
    super();
    // let isCheckedLayerids = false;
    if (options) {
      options.forEach((_option: StorageConstructorOptions) => {
        this.StorageKeyValue.set(_option.storagekey, _option);
        this._initStorage(_option);
        // if (_option.storagekey == "CheckedLayerids") isCheckedLayerids = true;
      });
    }
    // if (isCheckedLayerids == false) {
    //   let tmp = {
    //     isLocalStorage: true,
    //     storagekey: "CheckedLayerids",
    //     variableType: 2,
    //     initStoragevalue: [],
    //   };
    //   this.StorageKeyValue.set(tmp.storagekey, tmp);
    //   this._initStorage(tmp);
    // }
  }
  private _initStorage(_option: StorageConstructorOptions) {
    switch (_option.variableType) {
      case 0:
        this.Storage.set(_option.storagekey, _option.initStoragevalue);
        break;
      case 1:
        this.Storage.set(_option.storagekey, ref(_option.initStoragevalue));
        break;
      case 2:
        this.Storage.set(
          _option.storagekey,
          reactive(_option.initStoragevalue)
        );
        break;
      case 3:
        this.Storage.set(
          _option.storagekey,
          reactive(
            this.ProxyParameter(
              _option.initStoragevalue,
              _option.storagekey,
              _option.isLocalStorage
            )
          )
        );
        break;
      case 4:
        this.Storage.set(
          _option.storagekey,
          ref(
            this.ProxyParameter(
              _option.initStoragevalue,
              _option.storagekey,
              _option.isLocalStorage
            )
          )
        );
        break;
    }
  }
  private isHave(layer: IMapLayer): any {
    return this.LayerAlpha.findIndex((itm) => itm.layerid == layer.layerid);
  }
  /** 更新CheckedLayerids变量
   * @param {Object} option
   * @param {string} option.layerid 图层ID
   * @param {boolean} option.isCheck 是否选中
   */
  public UpdateCheckedLayerids(option: {
    layerid: string;
    isCheck: boolean | Boolean;
  }) {
    const idx = this.CheckedLayerids.indexOf(option.layerid);
    if (idx > -1) {
      this.CheckedLayerids.splice(idx, 1);
    }
    if (option.isCheck) {
      this.CheckedLayerids.unshift(option.layerid);
    }
  }
  /** 更新Maplayer visible 属性
   * @param {Object} option
   * @param {string} option.layerid 图层ID
   * @param {boolean} option.isCheck 是否选中
   */
  public UpdateMaplayerVisible(option: {
    layerid: string;
    isCheck: boolean | Boolean;
  }) {
    const tmp = this.MapLayers.find((item) => item.layerid == option.layerid);
    if (tmp) {
      tmp.visible = option.isCheck as any;
    }
  }

  /** 初始化图层透明度，内部函数
   * @param {IMapLayer} itm
   */
  private UpdateAlpha(itm: IMapLayer) {
    const idx = this.isHave(itm);
    if (idx != -1) {
      itm["options"]["LayerOptions"] = {
        ...itm["options"]["LayerOptions"],
        alpha: this.LayerAlpha[idx],
      };
      if (itm?.subLayers) {
        itm?.subLayers.forEach((sublayer) => {
          sublayer["options"]["LayerOptions"] = {
            ...sublayer["options"]["LayerOptions"],
            alpha: this.LayerAlpha[idx],
          };
        });
      }
    }
  }
  public isAddLayerState = ref(false);
  public AddLayer(option: any) {
    let idx = this.Layers.findIndex((itm: IMapLayer) => itm.name == "临时图层");
    let tmplyr: IMapLayer;
    if (idx == -1) {
      tmplyr = {
        layerid: guid(),
        name: "临时图层",
        showInCatalog: true,
        children: [],
      };
      this.Layers.push(tmplyr);
    } else {
      tmplyr = this.Layers[idx];
      if (
        (tmplyr as any)?.children.findIndex(
          (chdItem: IMapLayer) => chdItem?.name == option.name
        ) > -1
      ) {
        console.info("yusy>已存在该图层");
        return;
      }
    }

    let layerobj = {};
    const _layerid = guid();
    if (option.isUrl) {
      const tmp: IMapLayer = {
        layerid: _layerid,
        name: option?.name ? option.name : `临时图层${_layerid}`,
        showInCatalog: true,
        isBaseLayer: false,
        visible: true,
        serviceType: option?.serviceType,
      };
      if (option?.useMapModel) {
        tmp["useMapModel"] = option?.useMapModel;
      }
      if (option?.layerType) {
        tmp["layerType"] = option?.layerType;
      }
      layerobj = {
        ...tmp,
        subLayers: [{ ...tmp, url: option?.url }],
      };
    } else {
      layerobj = option.JSONOBJ;
    }
    tmplyr.children?.push(layerobj);
    this.MapLayers.push(layerobj);
    this.isAddLayerState.value = true;
  }
  private _Layers: Array<IMapLayer> = [];
  public get Layers() {
    return this._Layers;
  }
  public set Layers(value: Array<IMapLayer>) {
    const fun = (layer: IMapLayer) => {
      if (layer?.children) {
        layer.children.forEach((cld) => {
          fun(cld);
        });
      } else {
        this.UpdateAlpha(layer);
      }
    };
    value.forEach((itm) => {
      fun(itm);
    });
    this._Layers = value;
  }

  /** 变量3.存放 MapLayer 数组 */
  private _MapLayers: Array<IMapLayer> = [];
  public get MapLayers() {
    return this._MapLayers;
  }
  public set MapLayers(value) {
    this._MapLayers = value;
  }
  /** 修改图层的透明度
   * @param {IMapLayer} _layer
   * @param {number} value  透明度
   */
  public EditMapAlpha(_layer: IMapLayer, value: number) {
    const lyridx = this.MapLayers.findIndex(
      (item: IMapLayer) => item.layerid == _layer.layerid
    );
    if (lyridx > -1) {
      //#region  1.存入LayerAlpha
      const alphaIdx = this.LayerAlpha.findIndex(
        (itm) => itm.layerid == _layer.layerid
      );
      if (value == 0) {
        if (alphaIdx > -1) {
          this.LayerAlpha.splice(alphaIdx, 1);
        }
      } else {
        if (alphaIdx > -1) {
          this.LayerAlpha[alphaIdx].alpha = value;
        } else {
          this.LayerAlpha.push({
            layerid: _layer.layerid as any,
            alpha: value,
          });
        }
      }
      //#endregion

      //#region  2.修改IMapLayer
      this.MapLayers[lyridx]["options"] = {
        ...this.MapLayers[lyridx]?.options,
        LayerOptions: {
          ...this.MapLayers[lyridx]?.options?.LayerOptions,
          alpha: value,
        },
      };
      if (this.MapLayers[lyridx]?.subLayers) {
        this.MapLayers[lyridx]?.subLayers?.forEach((sublyr) => {
          sublyr["options"] = {
            ...sublyr?.options,
            LayerOptions: {
              ...sublyr?.options?.LayerOptions,
              alpha: value,
            },
          };
        });
      }
      //#endregion
    }
  }
  /** 修改MapLayer参数，暂未启用
   * @param {IMapLayer} _layer 要修改的IMapLayer对象
   * @param {Object} attr 新值对象
   * @param {mapType} maptype 如果省略，就表示二三维都实用，否者就是指定模式的IMapLayer
   * @returns
   */
  public UpdateMapLayer(_layer: IMapLayer, attr: any, maptype?: mapType) {
    const lyridx = this.MapLayers.findIndex((item: IMapLayer) =>
      item.layerid == _layer.layerid && maptype
        ? Utils.isUseMapModel(item, maptype)
        : true
    );

    if (lyridx == -1) return;
    const update = (lyridx: any, key: string) => {
      if (this.MapLayers[lyridx]?.subLayers) {
        this.MapLayers[lyridx].subLayers?.forEach((_lyr: IMapLayer) => {
          if (maptype) {
            if (Utils.isUseMapModel(_lyr, maptype)) {
              //@ts-ignore
              _lyr[key] = attr[key];
            }
          } else {
            //@ts-ignore
            _lyr[key] = attr[key];
          }
        });
      } else {
        if (maptype) {
          if (Utils.isUseMapModel(this.MapLayers[lyridx], maptype)) {
            //@ts-ignore
            this.MapLayers[lyridx][key] = attr[key];
          }
        } else {
          //@ts-ignore
          this.MapLayers[lyridx][key] = attr[key];
        }
      }
    };
    for (const key in attr) {
      switch (key) {
        case "options": {
          this.MapLayers[lyridx]["options"] = attr[key];
          if (this.MapLayers[lyridx]?.subLayers) {
            update(lyridx, key);
          }
          break;
        }
        case "ProviderOptions": {
          update(lyridx, key);
          break;
        }
        case "LayerOptions": {
          update(lyridx, key);
          break;
        }
        case "arcOption": {
          update(lyridx, key);
          break;
        }
        default: {
          update(lyridx, key);
        }
      }
    }
    /*
    if (maptype == mapType.arcgis || maptype == mapType.cesium) {
      this.MapLayers[lyridx].subLayers?.forEach((_lyr: IMapLayer) => {
        if (Utils.isUseMapModel(_lyr, maptype)) {
          _lyr = Utils.MergeObjects(_lyr, attr);
        }
      });
    } else {
      this.MapLayers[lyridx] = Utils.MergeObjects(this.MapLayers[lyridx], attr);
      this.MapLayers[lyridx].subLayers?.forEach((_lyr: IMapLayer) => {
        _lyr = Utils.MergeObjects(_lyr, attr);
      });
    }*/
  }

  /** F1.利用initTreeData方法标准化后的数据源，
   * 初始化 Layers 和 MapLayers
   * @param tree Array<IMapLayer>
   */
  public initTreeDataExecute(tree: Array<IMapLayer>): any {
    Utils.initTreeData(tree, this.CheckedLayerids).forEach((obj: IMapLayer) => {
      this.Layers.push(obj);
    });
    //构建 MapLayers
    Utils.createMaplayerData(this.Layers, (_para: any) => {
      return true;
    }).forEach((itm) => {
      //#region 初始化透明参数
      const alphaIdx = this.LayerAlpha.findIndex(
        (alphaItm) => alphaItm.layerid == itm.layerid
      );
      if (alphaIdx > -1) {
        itm["options"] = {
          ...itm?.options,
          LayerOptions: {
            ...itm?.options?.LayerOptions,
            alpha: this.LayerAlpha[alphaIdx].alpha,
          },
        };
        if (itm?.subLayers) {
          itm?.subLayers?.forEach((sublyr) => {
            sublyr["options"] = {
              ...sublyr?.options,
              LayerOptions: {
                ...sublyr?.options?.LayerOptions,
                alpha: this.LayerAlpha[alphaIdx].alpha,
              },
            };
          });
        }
      }
      //#endregion
      this.MapLayers.push(itm);
    });
    console.info("yusy> Layers", this.Layers);
    console.info("yusy> MapLayers", this.MapLayers);
  }

	public LedendData: Ref<Array<any>> = ref([] as Array<any>);
  /** 获取底图参数
   * @param {mapType} _mapType
   * @returns {mapType}
   */
  public GetBaseMapLayer(_mapType?: mapType) {
    if (_mapType) this.MapType = _mapType;
    //从新配置底图
    const baseLayers: Array<IMapLayer> = Utils.getBaseMapLayerByVisible(
      this.MapType,
      true,
      this.MapLayers
    );

    if (baseLayers?.length > 0) {
      return baseLayers[0];
    } else {
      return {
        layerid: "czm_singlemap",
        isBaseLayer: true,
        useMapModel: [mapType.cesium],
        visible: true,
        name: "单张图片",
        showInCatalog: false,
        isDynamicMapLayer: false,
        url: "/assets/earth.jpg",
        serviceType: serviceType.SingleImage,
      };
    }
    // if (baseLayers?.length > 0) {
    //   //优先使用非SingleImage地图
    //   const noSinglemap = baseLayers.filter(
    //     (item: any) => item.serviceType != serviceType.SingleImage
    //   );
    //   if (noSinglemap?.length > 0) {
    //     return noSinglemap[0];
    //   } else {
    //     return baseLayers[0];
    //   }
    // } else {
    //   throw new Error("底图参数无效的IMapLayer配置");
    // }
  }
}

/**
 * PropStorage继承 UserStorage 类
 @example 
  UserStorage { 
    Storage,
    getStorage(key: string),
    //创建代理参数,
    //param 变量的初始化参数,key变量存储名称,
    //isLocalStorage 是存储为LocalStorage，还是sessionStorage
    ProxyParameter(param: Array<any> | Object, key: string,isLocalStorage: boolean = true): any
  }
 */
export class PropStorage extends UserStorage {
  public foldLayerResPanel = ref(true);
  /** 添加变量，如果存在返回不做处理
   * @param {StorageConstructorOptions} option 构建变量的参数
   */
  public addStorage(option: StorageConstructorOptions): any {
    if (this.StorageKeyValue.has(option.storagekey)) {
      console.info("已存在变量" + option.storagekey);
    } else {
      this.StorageKeyValue.set(option.storagekey, option);
      this._initStorage(option);
    }
  }
  /** 添加变量，如果存在就覆盖已有变量
   * @param {StorageConstructorOptions} option 构建变量的参数
   */
  public setStorage(option: StorageConstructorOptions): any {
    this.StorageKeyValue.set(option.storagekey, option);
    this._initStorage(option);
  }

  /** 获取固定的全局变量 */
  public get Globalvariable(): Globalvariable {
    return (
      this.getStorage("Globalvariable") ||
      reactive(this.ProxyParameter(new Globalvariable(), "Globalvariable"))
    );
  }
  /** 获取固定的全局变量：ToolQuickItem */
  public get ToolQuickItem(): Array<IToolItemType> {
    return (
      this.getStorage("ToolQuickItem") ||
      reactive(this.ProxyParameter([], "ToolQuickItem"))
    );
  }
  /** 获取固定的全局变量：MousePosition */
  public get MousePosition(): IPosition {
    return (
      this.getStorage("MousePosition") ||
      ref(this.ProxyParameter([], "MousePosition"))
    );
  }

	// 底栏信息
	public bottomInfoData: Ref<BottomInfoData | undefined> = ref<BottomInfoData | undefined>();
	
  private _initStorage(_option: StorageConstructorOptions) {
    switch (_option.variableType) {
      case 0:
        this.Storage.set(_option.storagekey, _option.initStoragevalue);
        break;
      case 1:
        this.Storage.set(_option.storagekey, ref(_option.initStoragevalue));
        break;
      case 2:
        this.Storage.set(
          _option.storagekey,
          reactive(_option.initStoragevalue)
        );
        break;
      case 3:
        this.Storage.set(
          _option.storagekey,
          reactive(
            this.ProxyParameter(
              _option.initStoragevalue,
              _option.storagekey,
              _option.isLocalStorage
            )
          )
        );
        break;
      case 4:
        this.Storage.set(
          _option.storagekey,
          ref(
            this.ProxyParameter(
              _option.initStoragevalue,
              _option.storagekey,
              _option.isLocalStorage
            )
          )
        );
        break;
    }
  }
  /**
   * 全局变量存储
   * @param {Array<base.StorageConstructorOptions>}options
   * @example 
   //下面是两个默认变量例子
   const PropStore: any = new PropStorage([
      {
        isLocalStorage: true,
        storagekey: "Toolset", 
        variableType: 3,
        initStoragevalue: new Globalvariable(),
      },
      {
        isLocalStorage: false,
        storagekey: "ToolQuickItem", 
        variableType: 3,
        initStoragevalue: [],
      },
      {
        isLocalStorage: false,
        storagekey: "MousePosition", 
        variableType: 3,
        initStoragevalue: [],
      },
      
    ]);

    variableType参数取值： 
      0-表示普通变量，内部实现方式：var variable;
      1-表示 普通 ref 变量，内部实现方式：var ref(variable);
      2-表示 普通 reactive 变量，内部实现方式：var reactive(variable);
      3-表示 Proxy-reactive 变量，内部实现方式： var reactive(ProxyParameter(variable))，
      4-表示 Proxy-ref 变量，内部实现方式： var ref(ProxyParameter(variable))
   */
  constructor(options?: Array<StorageConstructorOptions>) {
    super();
    let isGlobalvariable = false;
    let isToolQuickItem = false;
    let isMousePosition = false;
		this.bottomInfoData.value = new BottomInfoData();
    if (options) {
      options.forEach((_option: StorageConstructorOptions) => {
        this.StorageKeyValue.set(_option.storagekey, _option);
        this._initStorage(_option);
        if (_option.storagekey == "Globalvariable") isGlobalvariable = true;
        if (_option.storagekey == "ToolQuickItem") isToolQuickItem = true;
        if (_option.storagekey == "isMousePosition") isMousePosition = true;
      });
    }
    if (isGlobalvariable == false) {
      let tmp = {
        isLocalStorage: true,
        storagekey: "Globalvariable",
        variableType: 3,
        initStoragevalue: new Globalvariable(),
      };
      this.StorageKeyValue.set(tmp.storagekey, tmp);
      this._initStorage(tmp);
    }
    if (isToolQuickItem == false) {
      let tmp = {
        isLocalStorage: true,
        storagekey: "ToolQuickItem",
        variableType: 3,
        initStoragevalue: [],
      };
      this.StorageKeyValue.set(tmp.storagekey, tmp);
      this._initStorage(tmp);
    }
    if (isMousePosition == false) {
      let tmp = {
        isLocalStorage: true,
        storagekey: "MousePosition",
        variableType: 3,
        initStoragevalue: {
          longitude: 0,
          latitude: 0,
          altitude: 0,
          Heading: 0,
          Pitch: 0,
          Roll: 0,
          mark: `<div>
          天地图-tianditu.gov.cn-审图号:GS(2022)3124号 | 桂S(2022)01-43号|
          <span style="color: red">提示:地图数据属于内部数据，请勿截图、拍照外传!</span>
        </div>`,
        },
      };
      this.StorageKeyValue.set(tmp.storagekey, tmp);
      this._initStorage(tmp);
    }
  }
  //
  //
  //==============================下面的一些变量请清理掉
  //
  //
  //地图点选属性，开放出来，方便其他组件调用，也可以通过Store来设置数据
  public propertyData = ref([]) as any;
  //控制属性面板显示隐藏，业务系统有单独触发显示隐藏的需求，所以开放出来
  public propertyVisible = ref(false);
  public propertyLoading = ref(false);
  public UserToolCollection: Array<any> = [];
  public AddUserToolItems(value: []) {
    value.forEach((item) => {
      this.UserToolCollection.push(item);
    });
  }
  public mapexten = ref({
    xmin: 105.9738504337227,
    ymin: 21.624205573999646,
    xmax: 110.55005559052246,
    ymax: 34.67104274606849,
    heading: 0,
    pitch: -90,
    roll: 0,
  });

	public centerArea = ref('南宁市');//当前中心的城市名

	public mapIdentifyButtons = ref([]);	// 点选属性按钮

  public ClearScreenTimes = ref('');//点击清屏的时间，用于清屏

  // 全幅定位配置
  public fullExtent = {
    xmin: 105.71685125,
    ymin: 21.802522646484373,
    xmax: 110.99028875,
    ymax: 24.354097353515623,
    heading: 0,
    pitch: -90,
    roll: 0,
    wkid: 4490,
    toolbar: "定位到南宁",
  };
  public baseMapType = <BaseMapType>BaseMapType.satellite;

  public setMapMousePosition(data: IPosition) {
    Object.keys(data).forEach((key) => {
      (this.MousePosition as any)[key] = (data as any)[key];
    });
  }
  public setPropertyData(data: any) {
    this.propertyData.value = data;
  }
  public pushPropertyData(data: any) {
    this.propertyData.value.push(data);
  }
  public setPropertyVisible(data: boolean) {
    this.propertyVisible.value = data;
  }
  public setPropertyLoading(data: boolean) {
    this.propertyLoading.value = data;
  }
  public setFullExtent(data: any) {
    for (let key of Object.getOwnPropertyNames(data)) {
      if (data.hasOwnProperty(key)) {
        //@ts-ignore
        this.fullExtent[key] = data[key];
      }
    }
  }
  public setClearScreenTimes(data: string) {
    this.ClearScreenTimes.value = data;
  }
}
