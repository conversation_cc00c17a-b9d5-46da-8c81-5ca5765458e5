<template>
  <div
    ref="tooltip"
    v-show="isDrawing"
    class="drawing-tooltip"
    :style="{
      left: `${tooltipPosition.x}px`,
      top: `${tooltipPosition.y}px`,
    }"
  >
    {{ tipText }}
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, defineProps } from "vue";
import { getOnemap, mapType } from "../onemapkit";
import * as Cesium from "@onemapkit/cesium";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});

const tooltipPosition = ref<{ x: number; y: number }>({ x: 0, y: 0 });
const tipText = ref<string>("");
const isDrawing = ref(false);
// 更新提示文字
const updateTipText = () => {
  if (isDrawing.value) {
    let drawType = localStorage.getItem("geoType");
    switch (drawType) {
      case "point":
        tipText.value = "单击左键绘制";
        break;
      case "polyline":
        tipText.value = "单击左键绘制，双击结束";
        break;
      case "polygon":
        tipText.value = "单击左键绘制，双击结束";
        break;
      case "circle":
        tipText.value = "按住左键拖拽绘制，抬起结束";
        break;
      case "rectangle":
        tipText.value = "按住左键拖拽绘制，抬起结束";
        break;
      default:
        tipText.value = "点击左键开始绘制";
        break;
    }
  }
};

// 状态同步逻辑
watch(
  () => props.PropStore,
  () => {
    // 更新 isDrawing 状态
    const isDrawingStorage = props.PropStore?.getStorage("isDrawing");
    let drawType = localStorage.getItem("geoType");
    if (isDrawingStorage && drawType !== null) {
      isDrawing.value = isDrawingStorage.value;
      updateTipText();
    } else {
      isDrawing.value = false;
      // document.removeEventListener('mousemove', handleMouseMove);
    }
  },
  { deep: true }
);

// 监听 isDrawing 状态变化
watch(
  () => isDrawing.value,
  (newVal: boolean) => {
    if (newVal) {
      handelTipPosition()
    } else {
      tipText.value = "";
      // document.removeEventListener("mousemove", handleMouseMove);
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const _Onemap = getOnemap(props.MapControlName);
let handler: Cesium.ScreenSpaceEventHandler | null = null;
const handelTipPosition = () =>{
  const dragElement = document.getElementById('mapviewerContainer');
  if (dragElement) {
      if (_Onemap.MapType.value === mapType.arcgis) {
        //console.log("   二维--  ",_Onemap)
        if (handler) {
          handler.destroy();
          handler = null;
        }
        document.addEventListener("mousemove", startPan2D);
      } else {
        //console.log("   san维--  ",_Onemap)
        document.removeEventListener("mousemove", startPan2D);
        handler = new Cesium.ScreenSpaceEventHandler(
          _Onemap.MapViewer.scene.canvas
        );
        if (handler) {
          handler.setInputAction((event: any) => {
            startPan(event);
          }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

        }
      }

    }
}

const startPan2D = (event: any) => {
    const dragElement = document.getElementById('mapviewerContainer');
    if(dragElement) {
      let resc = dragElement.getBoundingClientRect();
        tooltipPosition.value = {
        x: event.clientX - resc.left + 20,
        y: event.clientY - resc.top - 50,
      };
    }
}

const startPan = (event: any) => {
    // 屏幕坐标转世界坐标
    const viewer = _Onemap.MapViewer;
    let ray = viewer.camera.getPickRay(event.endPosition);
    let cartesian = viewer.scene.globe.pick(ray, viewer.scene);
    let resc = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
      _Onemap.MapViewer.scene,
      cartesian
    );
    if (resc) {
      tooltipPosition.value = {
        x: resc.x + 20 ,
        y: resc.y - 50,
      };
    } else {
      tooltipPosition.value = {
        x: 0,
        y: 0,
      };
    }
};


// 鼠标移出事件
const handleMouseLeave = () => {
  document.removeEventListener("mousemove", startPan2D);
};

// 初始化
onMounted(() => {
  updateTipText();
  // handelTipPosition()
});

// 销毁
onUnmounted(() => {
  Cesium.destroyObject(_Onemap);
  handleMouseLeave();
});
</script>

<style scoped>
.drawing-tooltip {
  position: absolute;
  padding: 8px 12px;
  background-color: #333;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
   /* 添加过渡效果减少突兀感 */
   transition: opacity 0.2s;
  /* 防止工具提示干扰鼠标事件 */
  pointer-events: none;
  /* 确保层级最高 */
  z-index: 99999;
  max-width: 200px;
  text-align: center;
}
</style>
