<template>
  <div class="show-terrain-panel__content">
    <el-form>
      <el-form-item label="透明度" v-show="openBasemap">
        <el-slider
          v-model="Opacity"
          size="small"
          @change="onChangeOpacity()"
        ></el-slider>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, onMounted, onUnmounted } from "vue";
import { getOnemap, mapType } from "../../onemapkit";
import * as Cesium from "@onemapkit/cesium";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  panelVisible: {
    type: Boolean,
    default: false,
  },
});

const openBasemap = ref(true);
const Opacity = ref(0);
const _inOnemap = getOnemap(props.MapControlName);

// const viewer = _inOnemap.MapViewer;
const onChangeOpacity = () => {
  console.log("設置底圖透明度");
  if (_inOnemap.MapType.value == mapType.arcgis) {
    let length = _inOnemap.MapViewer.layerIds.length;
    for (let i = 0; i < length; i++) {
      let baseMapL = _inOnemap.MapViewer.getLayer(
        _inOnemap.MapViewer.layerIds[i]
      );
      console.log("二维", baseMapL, i);
      if (baseMapL && !baseMapL.layers && !props.LayerStore.CheckedLayerids.includes(baseMapL.id) ) {
        let opacity = 1 - Opacity.value / 100;
        baseMapL.setOpacity(opacity);
      }
      if (baseMapL.layers) {
        let lengthC = baseMapL.layers.length;
        for (let i = 0; i < lengthC; i++) {
          let baseMapC = baseMapL.layers[i];
          if(!props.LayerStore.CheckedLayerids.includes(baseMapC.id) && baseMapC) {
            let opacity = 1 - Opacity.value / 100;
            baseMapC.setOpacity(opacity);
          }
        }
      }
    }
  } else {
    let _length = _inOnemap.MapViewer.imageryLayers.length;
    for (let i = 0; i < _length; ++i) {
      let baseImageryLayer = _inOnemap.MapViewer.imageryLayers.get(i);
      console.log(baseImageryLayer, "三维");
      if (baseImageryLayer && !baseImageryLayer._layers) {
        let opacity = 1 - Opacity.value / 100;
        baseImageryLayer.alpha = opacity;
      }
      if (baseImageryLayer._layers) {
        let baseImageryLayerLength = baseImageryLayer._layers.length;
        for (let i = 0; i < baseImageryLayerLength; i++) {
          let baseMapC = baseImageryLayer._layers[i];
          if (baseMapC) {
            // let opacity = 1 - Opacity.value / 100;
            // baseMapC.alpha(opacity);
          }
        }
      }
    }
  }
};

onMounted(() => {
  Opacity.value = 50;
  onChangeOpacity();
});

onUnmounted(() => {
  Opacity.value = 0;
  onChangeOpacity();
});
</script>

<style scoped>
.show-terrain-panel__content {
  margin: 10px 20px;
}
</style>
