<template>
  <el-form label-width="70px" class="popdivtoolpanel">
    <div class="formitemcontainer">
      <el-form-item class="form-item formitemwidth" label="光源强度">
        <el-slider
          :min="0"
          :max="500"
          :step="0.1"
          show-input
          v-model="GroupTable.intensity"
          @input="onChangeLightIntensity()"
        />
      </el-form-item>
      <el-form-item class="form-item formitemwidth" label="发光半径">
        <el-slider
          class="form-item-slider"
          :min="0.1"
          :max="200"
          :step="0.1"
          show-input
          v-model="GroupTable.radius"
          @input="onChangeLightRadius()"
        />
      </el-form-item>
      <el-form-item class="form-item formitemwidth" label="聚灯外角">
        <el-slider
          class="form-item-slider"
          :min="0"
          :max="90"
          :step="0.1"
          show-input
          v-model="GroupTable.outerConeDegrees"
          @input="onChangeLightOuterConeDegrees()"
        />
      </el-form-item>
      <el-form-item class="form-item formitemwidth" label="聚灯内角">
        <el-slider
          class="form-item-slider"
          :min="0"
          :max="90"
          :step="0.1"
          show-input
          v-model="GroupTable.innerConeDegrees"
          @input="onChangeLightInnerConeDegrees()"
        />
      </el-form-item>

      <!-- 时间范围 style="width: 280px"-->

      <el-form-item class="form-item formitemwidth" label="时间范围">
        <div
          style="
            display: flex;
            width: 100%;
            align-items: center;
            white-space: nowrap;
          "
        >
          <el-input-number
            class="timerangetext"
            :min="0"
            :max="24"
            v-model="startHours"
            @change="onChangeTimeRanges()"
            :controls="false"
          />
          <span class="timerangetextlabel"> 时</span>
          <el-input-number
            class="timerangetext"
            :min="0"
            :max="59"
            v-model="startMinutes"
            @change="onChangeTimeRanges()"
            :controls="false"
          />
          <span class="timerangetextlabel">分 至</span>
          <el-input-number
            class="timerangetext"
            :min="0"
            :max="24"
            v-model="endHours"
            @change="onChangeTimeRanges()"
            :controls="false"
          />
          <span class="timerangetextlabel"> 时</span>
          <el-input-number
            class="timerangetext"
            :min="0"
            :max="59"
            v-model="endMinutes"
            @change="onChangeTimeRanges()"
            :controls="false"
          />
          <span style="margin-left: 0px">分</span>
        </div>
      </el-form-item>

      <!-- 光源颜色参数 -->

      <el-form-item
        label-width="0px"
        class="form-item formitemwidth"
        style="border: 1px solid rgb(255, 255, 255)"
      >
        <div>
          <span>光源颜色</span>
          <el-color-picker
            v-model="GroupTable.color"
            @change="onChangeLightColor()"
          />
        </div>

        <el-button
          style="margin: 1px 1px; padding: 3px"
          type="primary"
          link
          @click="addLightAnimation()"
        >
          添加动画
        </el-button>
      </el-form-item>
      <el-form-item
        v-if="GroupTable.lightAnimation.length > 0"
        style="width: 100%"
        label-width="0px"
        label=""
      >
        <div
          style="
            display: flex;
            width: 100%;
            flex-wrap: wrap;
            align-items: center;
            margin: 2px 0px 2px 0px;
            border: 1px solid rgb(200, 200, 200);
            /* border: 1px solid rgb(255, 255, 255); */
          "
        >
          <el-form-item
            label-width="0px"
            v-for="idx in GroupTable.lightAnimation"
            :key="idx"
            style="
              margin: 2px 2px 2px 2px;
              border: 1px solid rgb(255, 255, 255);
            "
          >
            <div>
              <span>光源颜色</span>
              <el-color-picker v-model="idx.color" />
            </div>
            <div>
              <span style="margin: 0px 5px 0px 5px">播放间隔</span>
              <el-input-number
                style="width: 120px"
                v-model="idx.timeInterval"
              />
              <el-button type="primary" link @click="removeLightAnimation(idx)">
                删除
              </el-button>
            </div>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label-width="0px" style="width: 100%" class="form-item">
        <div style="width: 100%; display: flex; justify-content: flex-end">
          <el-button
            style="margin: 1px 30px"
            type="primary"
            plain
            @click="_onCreatePointLight"
          >
            添加光源
          </el-button>
        </div>
      </el-form-item>
    </div>
  </el-form>
</template>
<script lang="ts" setup>
import { Color } from "@onemapkit/cesium";
import { ref } from "vue";
// import { getOnemap } from "../../onemapkit";
import {
  changeLightColor,
  changeLightInnerConeDegrees,
  changeLightIntensity,
  changeLightOuterConeDegrees,
  changeLightRadius,
} from "./tools";
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
  },
  globalSettings: {
    type: Object,
    default: undefined,
    require: true,
  },
  GroupTable: {
    type: Object,
    default: undefined,
    require: true,
  },
});
// const _inOnemap = getOnemap(props.MapControlName);
const GroupTable = ref(props.GroupTable as any);
// 计算起始时间的时分
const startSecondTime = GroupTable.value.timeRanges[0] * 3600;
const startHours = ref(Math.floor(GroupTable.value.timeRanges[0]));
const startMinutes = ref(
  Math.floor((startSecondTime - startHours.value * 3600) / 60)
);
// 计算最大时间的时分
const endSecondTime = GroupTable.value.timeRanges[1] * 3600;
const endHours = ref(Math.floor(GroupTable.value.timeRanges[1]));
const endMinutes = ref(
  Math.floor((endSecondTime - endHours.value * 3600) / 60)
);

const emits = defineEmits(["onCreateLight"]);
const _onCreatePointLight = () => {
  emits("onCreateLight", props.GroupTable);
};

// 修改光源组强度
function onChangeLightIntensity() {
  GroupTable.value.children.forEach((light: any) => {
    changeLightIntensity(light.id, GroupTable.value.intensity);
  });
}

// 修改光源组半径
function onChangeLightRadius() {
  GroupTable.value.children.forEach((light: any) => {
    changeLightRadius(light.id, GroupTable.value.radius);
  });
}

// 修改光源外径
function onChangeLightOuterConeDegrees() {
  GroupTable.value.children.forEach((light: any) => {
    changeLightOuterConeDegrees(light.id, GroupTable.value.outerConeDegrees);
  });
}

// 修改光源内径
function onChangeLightInnerConeDegrees() {
  GroupTable.value.children.forEach((light: any) => {
    changeLightInnerConeDegrees(light.id, GroupTable.value.innerConeDegrees);
  });
}

// 修改光源组颜色
function onChangeLightColor() {
  const color = Color.fromCssColorString(GroupTable.value.color);
  GroupTable.value.children.forEach((light: any) => {
    changeLightColor(light.id, color);
  });
}

// 修改时间范围
function onChangeTimeRanges() {
  GroupTable.value.timeRanges[0] = startHours.value + startMinutes.value / 60;
  GroupTable.value.timeRanges[1] = endHours.value + endMinutes.value / 60;
}

function addLightAnimation() {
  GroupTable.value.lightAnimation.push({
    color: "#ffffff",
    timeInterval: 10,
  });
}
function removeLightAnimation(event: any) {
  if (GroupTable.value.lightAnimation.length > 0) {
    GroupTable.value.lightAnimation.splice(event, 1);
    for (let x = event; x < GroupTable.value.lightAnimation.length; x++) {
      GroupTable.value.lightAnimation[x] =
        (GroupTable.value.lightAnimation[x] as any) - 1;
    }
  }
}
</script>

<style scoped>
.createlightbutton {
  display: flex;
  width: 100%;
  background-color: rgb(253, 246, 236);
  align-items: center;
  white-space: nowrap;
}

.popdivtoolpanel {
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(245, 245, 245);
  margin: 2px 3px 5px 3px;
  border-radius: 3px;
}
.timerangetext {
  width: 110px;
}
.timerangetextlabel {
  margin: 0px 5px;
}
:deep .el-input .el-input-group .el-input-group--append {
  border-right: none; /* Remove the left border */
  border-top-right-radius: 0; /* 去掉右上角圆角 */
  border-bottom-right-radius: 0; /* 去掉右下角圆角 */
}
:deep .el-input-group {
  border-right: none; /* Remove the left border */
  border-top-right-radius: 0; /* 去掉右上角圆角 */
  border-bottom-right-radius: 0; /* 去掉右下角圆角 */
}
:deep .el-input-group--append {
  border-right: none; /* Remove the left border */
  border-top-right-radius: 0; /* 去掉右上角圆角 */
  border-bottom-right-radius: 0; /* 去掉右下角圆角 */
}
:deep .el-input-group__append {
  margin: 0px;
  padding-left: 2px;
  padding-right: 2px;
  background-color: #fff;
  border: none; /* Remove the left border */
}
:deep .el-input__wrapper {
  border-left: none; /* Remove the left border */
}
/*
:deep .el-form-item {
  margin: 5px;
}*/
.formitemcontainer {
  padding: 0px;
  margin: 3px 3px;
  display: flex;
  flex-wrap: wrap; /* 允许自动换行 */
}
.subformitemcontainer {
  width: 340px;
}
.formitemwidth {
  width: 340px;
  height: 40px;
  margin: 3px 2px;
  border: 1px solid rgb(255, 255, 255);
}
.formitemwidth2 {
  width: 180px;
  height: 30px;
}
/* 使用 :deep 选择器来穿透 Scoped 样式 */
:deep .el-slider__runway {
  margin-right: 12px; /* 调整 slider 和 input 之间的间距 */
  margin-left: 1px; /* 调整 slider 和 input 之间的间距 */
}
:deep .el-slider__input {
  width: 130px;
}
:deep .el-input-number .el-input__wrapper {
  padding-left: 2px;
  padding-right: 2px;
}
:deep .el-input-number__decrease .el-input-number__increase {
  width: 25px;
}
</style>
