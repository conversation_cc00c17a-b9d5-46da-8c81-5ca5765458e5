.popdivtoolpanel {
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(245, 245, 245);
  margin: 2px 3px 5px 3px;
  border-radius: 3px;
}
.formitemcontainer {
  padding: 0px;
  margin: 3px 3px;
  display: flex;
  flex-wrap: wrap; /* 允许自动换行 */
}
.formitemwidth {
  width: 340px;
  height: 40px;
  margin: 3px 2px;
  border: 1px solid rgb(255, 255, 255);
}

.timerangetext {
  width: 110px;
}
.timerangetextlabel {
  margin: 0px 5px;
}
/* 使用 :deep 选择器来穿透 Scoped 样式 */
:deep .el-slider__runway {
  margin-right: 12px; /* 调整 slider 和 input 之间的间距 */
  margin-left: 1px; /* 调整 slider 和 input 之间的间距 */
}
:deep .el-slider__input {
  width: 130px;
}
:deep .el-input-number .el-input__wrapper {
  padding-left: 2px;
  padding-right: 2px;
}
:deep .el-input-number__decrease .el-input-number__increase {
  width: 25px;
}
