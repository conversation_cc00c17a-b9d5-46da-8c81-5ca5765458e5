<template>
  <el-button-group style="margin: 0px 0px 0px 1px; display: flex">
    <el-button
      v-for="(item, idx) in props.ToolItems.filter(
        (itm: any) => itm.isVisible
      )"
      :key="'btn' + idx"
      class="draw-btn"
      plain
      :style="{
        ...defaultStyle,
        ...props.ToolStyle,
      }"
      :title="(item as IToolItemType).label"
      @click="_ToolItemClickEvent(item)"
      :icon="props.ToolItemModel == ToolItemModel.icontext ? item.icon : undefined"
    >
      {{ props.ToolItemModel == ToolItemModel.icon ? undefined : item.label }}
      <component
        v-if="
          props.ToolItemModel == ToolItemModel.icon ||
          props.ToolItemModel == ToolItemModel.icontextright
        "
        class="draw-icon"
        :style="{ transform: `scale(${props.ToolItemModel == ToolItemModel.icon ? 1 : 0.6})` }"
        :is="(item as IToolItemType).icon"
      />
    </el-button>
  </el-button-group>
</template>
<script lang="ts" setup>
import { type PropType, type CSSProperties, computed } from "vue";
import { geometryType, type IToolItemType, Icons, DockType } from "../../onemapkit";
import { cloneDeep } from "lodash";
const enum ToolItemModel {
  icon,
  text,
  icontext,
  icontextright,
}
interface ToolEventType {
  sender: IToolItemType;
  avg?: any;
}
const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
  },
  ToolItemModel: {
    type: (Object as PropType<ToolItemModel>) || Number || String || Boolean,
    default: ToolItemModel.icontext,
  },
  ToolStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  ToolItems: {
    type: Array<IToolItemType>,
    default: [
      {
        label: "绘制点",
        name: "drawpoint",
        icon: Icons.PointIcon,
        Dock: DockType.center,
        geoType: geometryType.point,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
      {
        label: "绘制线",
        name: "drawpolyline",
        Dock: DockType.center,
        icon: Icons.polylineIcon,
        geoType: geometryType.polyline,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
      {
        label: "绘制多边形",
        name: "drawpolygon",
        icon: Icons.Polygon2DIcon,
        Dock: DockType.center,
        geoType: geometryType.polygon,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
      {
        label: "绘制圆",
        name: "drawcircle",
        icon: Icons.CircleIcon,
        Dock: DockType.center,
        geoType: geometryType.circle,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
      {
        label: "绘制矩形",
        name: "drawrectangle",
        icon: Icons.RectangleIcon,
        Dock: DockType.center,
        geoType: geometryType.rectangle,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
      {
        label: "清除图形",
        name: "cleargraphics",
        icon: Icons.deleteion,
        Dock: DockType.center,
        geoType: geometryType.undefined,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
      {
        label: "导入坐标",
        name: "importcoord",
        icon: Icons.dataExchange,
        Dock: DockType.center,
        geoType: geometryType.undefined,
        useMapModel: undefined,
        isVisible: true,
        ToolButtonClickEvent: undefined,
        ToolButtonEvent: undefined,
        isDefault: true,
      },
    ],
  },
  onToolItemClick: {
    type: Object as PropType<(option: ToolEventType) => any>,
    default: undefined,
  },
});
const defaultStyle: any = computed(() => {
  switch (props.ToolItemModel) {
    case ToolItemModel.icon:
      return { width: "50px" };
    case ToolItemModel.text:
      return { width: undefined, paddingLeft: "6px", paddingRight: "6px" };
    case ToolItemModel.icontext:
      return { width: undefined, paddingLeft: "6px", paddingRight: "6px" };
    case ToolItemModel.icontextright:
      return { width: undefined, paddingLeft: "6px", paddingRight: "6px" };
  }
});

const emits = defineEmits(["ToolItemClickEvent", "ToolItemCallbackEvent"]);

const _ToolItemClickEvent = (btnObject: IToolItemType) => {
  const tmp: any = cloneDeep(btnObject);
  if (props.onToolItemClick) {
    props.onToolItemClick({ sender: tmp });
  }
  emits("ToolItemClickEvent", { sender: tmp });
};
</script>

<style lang="scss" scoped>
.draw-btn {
  height: 30px;
  margin-left: 0px;

  .draw-icon {
    width: 22px;
    height: 22px;
    transform: scale(1);
  }
}
</style>
