<template>
  <el-table
    :data="props.GraphicData"
    style="width: 100%; text-align: center"
    :max-height="360"
    :row-style="{ 'line-height': '30px' }"
    :show-header="false"
    size="small"
    border
  >
    <!--2.1.1 序号 -->
    <el-table-column type="index" width="30" align="center" />
    <!-- 2.1.2 primitive 名称 _graphicDataValue.get() _graphicDataValue.get(RowData.row) -->
    <el-table-column width="160">
      <template #default="scope">
        <div class="geometry-name">
          <component class="geometry-icon" :is="markRaw(scope.row.icon)" />
          <el-input size="small" v-model="scope.row.label" @input="nameChange(scope.row)" />
        </div>
      </template>
    </el-table-column>
    <!--2.1.3 操作的按钮-->
    <el-table-column>
      <template #default="scope">
        <el-button
          v-for="item in props.ButtonItems"
          size="small"
          text
          :type="(item as any)?.isWarn ? 'warning' : 'primary'"
          style="margin: 2px 6px; padding: 0px"
          @click="_onCellButtonClick(scope, item)"
          >{{ (item as any)?.label }}</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup lang="ts">
import { type PropType, markRaw } from "vue";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  GraphicData: {
    type: Array<{ geoid: String; geoname: String; geotype?: any }>,
    default: [
      { geoid: "geoid111", geoname: "geoname111" },
      { geoid: "geoid222", geoname: "geoname222" },
    ],
  },
  ButtonItems: {
    type: Array<any>,
    default: [
      /** 1.定位 */
      {
        label: "定位",
        name: "location",
        isDefault: true,
        isWarn: false,
        isShow: true,
      },
      /** 2.收藏 */
      {
        label: "收藏",
        name: "collect",
        //@ts-ignore
        funClickEvent: (sender: any, options: any) => {
          // console.log("=====下载坐标", key, value, options);
        },
        isDefault: true,
        isWarn: false,
        isShow: true,
      },
      /** 3.下载坐标 */
      {
        label: "下载坐标",
        name: "downCoord",
        isWarn: false,
        isDefault: true,
        isShow: true,
      },
      /** 4.空间分析 */
      {
        label: "空间分析",
        name: "spatialanalysis",
        isWarn: false,
        isDefault: false,
        isShow: false,
      },
      /** 5.编辑 */
      {
        label: "编辑",
        name: "edit",
        isWarn: true,
        isDefault: true,
        isShow: false,
      },
      /** 6.确定 */
      {
        label: "确定",
        name: "endedit",
        isWarn: true,
        isDefault: true,
        isShow: false,
      },
      /** 7.删除 */
      {
        label: "删除",
        name: "delete",
        isWarn: true,
        isDefault: true,
        isShow: true,
      },
    ],
  },
  onCellClick: {
    type: Function as PropType<(option: any) => any>,
    default: undefined,
  },
  onChingeName: {
    type: Function as PropType<(option: any) => any>,
    default: undefined,
  },
});

const emits = defineEmits(["CellClickEvent", "ChingeNameEvent"]);

const _onCellButtonClick = (RowData: any, toolitem: any) => {
  if (props?.onCellClick) {
    props?.onCellClick({
      sender: toolitem,
      row: RowData,
    });
  }

  emits("CellClickEvent", {
    sender: toolitem,
    row: RowData,
  });
};
const nameChange = (row: any) => {
  if (props?.onChingeName) {
    props?.onChingeName({
      row: row,
    });
  }
  emits("CellClickEvent", {
    row: row,
  });
};
</script>
