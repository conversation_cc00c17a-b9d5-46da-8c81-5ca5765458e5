import * as Cesium from "@onemapkit/cesium";

export enum DrawGeometryType {
    POINT = 'point',
    POLYLINE = 'polyline',
    POLYGON = 'polygon',
    CIRCLE = 'circle',
    RECTANGLE = 'rectangle'
}

export const toDegree = (cord: any) => {
    let _cord = Cesium.Cartographic.fromCartesian(cord);
    return {
        x: Cesium.Math.toDegrees(_cord.longitude),
        y: Cesium.Math.toDegrees(_cord.latitude),
        z: _cord.height
    };
};

/**
 * 确保坐标数组闭合（二维数组格式）
 * @param coordinates 坐标数组 [[lng, lat], [lng, lat], ...]
 * @returns 闭合后的坐标数组
 */
export const ensureClosed = (coordinates: number[][]): number[][] => {
    if (coordinates.length < 2) return coordinates;

    const firstPoint = coordinates[0];
    const lastPoint = coordinates[coordinates.length - 1];

    // 检查第一个点和最后一个点是否相同
    if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
        // 如果不相同，添加第一个点到末尾
        coordinates.push([...firstPoint]);
    }

    return coordinates;
}

/**
 * 将笛卡尔坐标数组转换为经纬度坐标数组
 * @param coord Cartesian3坐标点数组
 * @returns 经纬度坐标数组 [[longitude, latitude, height], ...]
 */
export const toDegrees = (coord: Cesium.Cartesian3[]): [number, number, number][] => {
    // return coord?.map((cartesianPoint: Cesium.Cartesian3) => {
    //     // 将笛卡尔坐标转换为弧度制的经纬度坐标
    //     const cartographic = Cesium.Cartographic.fromCartesian(cartesianPoint);
    //     return [
    //         // 将经度从弧度转换为度
    //         Cesium.Math.toDegrees(cartographic.longitude),
    //         // 将纬度从弧度转换为度
    //         Cesium.Math.toDegrees(cartographic.latitude),
    //         // 高度已经是米为单位，无需转换
    //         cartographic.height
    //     ];
    // }) || [];
    let sunCoord: any = [];
    coord.forEach((subitm: any /* subitm =false 为经纬度 */) => {
        let _cord = Cesium.Cartographic.fromCartesian(subitm);
        sunCoord.push([
            Cesium.Math.toDegrees(_cord.longitude),
            Cesium.Math.toDegrees(_cord.latitude),
            _cord.height,
        ]);
    });
    return sunCoord;
};

export const getPolylineCenter = (positions: Cesium.Cartesian3[], isDegree = false): Cesium.Cartesian3 | any => {
    // 计算平均坐标
    const centerCartesian = positions.reduce((sum, pos) => {
        return Cesium.Cartesian3.add(sum, pos, new Cesium.Cartesian3());
    }, new Cesium.Cartesian3());
    Cesium.Cartesian3.divideByScalar(centerCartesian, positions.length, centerCartesian);
    return isDegree ? toDegree(centerCartesian) : centerCartesian ;
}

export const formatUnitPolygon = (value: string, unitType: string): string => {
    if (!value) return "";
    let res: string;
    switch (unitType) {
        case "m2":
            res = Number(value).toFixed(2) + "平方米";
            break;
        case "km2":
            res = Number(Number(value) / 1000000).toFixed(2) + "平方公里";
            break;
        case "mu":
            res = (Number(value) * 0.0015).toFixed(2) + "亩";
            break;
        case "gq":
            res = (Number(value) * 0.0001).toFixed(2) + "公顷";
            break;

        case "m":
            res = Number(value).toFixed(2) + "米";
            break;
        case "km":
            res = Number(Number(value) * 0.001).toFixed(2) + "千米";
            break;
        default:
            res = "NaN";
            break;
    }
    return res;
};
