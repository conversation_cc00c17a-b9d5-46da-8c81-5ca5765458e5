<template>
  <div style="height: 100%;overflow: hidden;">
    <el-form :model="exportFormModel" :rules="exportCheckRules">
      <el-form-item label="导出格式" label-width="80px" prop="format" :required="true" style="margin-bottom: 5px;">
        <el-select size="small" v-model="exportFormModel.format" placeholder="请选择格式">
          <el-option v-for="item in exportFormat" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="坐标类型" label-width="80px" prop="coord" :required="true">
        <el-select size="small" v-model="exportFormModel.coord" placeholder="请选择坐标系">
          <el-option v-for="item in coords" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align: center;margin-top: 10px;">
      <el-space wrap :size="10">
        <el-button type="primary" plain v-loading="exportLoading" @click="exportCroodData()" size="small">导出</el-button>
        <el-button type="primary" plain style="border-color: #E7E9EC; background-color: #E7E9EC;color: #181C21;" @click="onClose()"
          size="small">取 消</el-button>
      </el-space>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, ref } from "vue";
import {
  mapType,
} from "../../onemapkit";
import * as Cesium from "@onemapkit/cesium";

const emits = defineEmits(["close"]);
const props = defineProps({
  graphics: {
    type: Object as any,
    default: null,
  },
  inOnemap: {
    type: Object as any,
    default: null,
  },
});
const exportFormModel = ref({ format: "txtMultiple", coord: 4490 });
const exportFormat = ref([
  {
    value: "txt",
    label: "txt单图形坐标文件",
    show: "one",
  },
  {
    value: "txtMultiple",
    label: "信息集团坐标文本（*.txt）支持多个地块",
    show: "all",
  },
  {
    label: "geojson",
    value: "geojson",
  },
  {
    label: "arcgisJson",
    value: "arcgis_json",
  },
]);

const exportCheckRules = {
  format: [{ required: true, message: "请输入导出格式", trigger: "blur" }],
  coord: [{ required: true, message: "请选择坐标系", trigger: "blur" }],
};
const coords = ref([
  {
    label: "2000地理坐标系（4490）",
    value: 4490,
  },
  {
    label: "2000平面坐标系_有带号（4524）",
    value: 4524,
  },
  // {
  //   label: "2000平面坐标系_有带号（4525）",
  //   value: 4525,
  // },
  {
    label: "2000平面坐标系_无带号（4545）",
    value: 4545,
  },
  {
    label: "WGS84地理坐标系（4326）",
    value: 4326,
  },
  {
    label: "墨卡托投影（3857）",
    value: 3857,
  },
  {
    label: "西安80坐标系（2360）",
    value: 2360,
  },
  {
    label: "北京54坐标系（2412）",
    value: 2412,
  },
]);

const exportLoading = ref(false);

const onClose = () => {
  emits("close");
};

const onMounted = () => {
  console.log('graphics', props.graphics);

};

//笛卡尔坐标转化
const toDegrees = (coord: Array<any>) => {
  let sunCoord: any = [];
  coord.forEach((subitm: any /* subitm =false 为经纬度 */) => {
    let _cord = Cesium.Cartographic.fromCartesian(subitm);
    sunCoord.push([
      Cesium.Math.toDegrees(_cord.longitude),
      Cesium.Math.toDegrees(_cord.latitude),
      _cord.height,
    ]);
  });
  return sunCoord;
};
//笛卡尔坐标转化
const changeCoord = (formCoord: number, toCoord: number, coords: any) => {
  let sunCoord = props.inOnemap.projectPoints(formCoord, toCoord, coords)
  return sunCoord;
};

/**
 * 导出props.graphics数据，props.graphics数组中的每个元素都是一个图形，遍历数组，每个图形导出一个文件，文件名为图形的名称
 */
const exportCroodData = () => {
  console.log("=====66666", props.graphics)
  exportLoading.value = true;
  const { format } = exportFormModel.value;
  //下载全部
  if (props.graphics.options.Row.hasOwnProperty('isAll') && props.graphics.options.Row.isAll) {
    for (const item of props.graphics.options.Row.data) {
      if (format === "txt") {
        downloadTxt(item);
      } else if (format === "txtMultiple") {
        downloadTxt(item);
      } else if (format === "geojson") {
        downloadGeojson(item);
      } else if (format === "arcgis_json") {
        downloadArcgisjson(item);
      }
    }
  } else {
    //单个下载
    if (format === "txt") {
      downloadTxt(props.graphics.options.Row);
    } else if (format === "txtMultiple") {
      downloadTxt(props.graphics.options.Row);
    } else if (format === "geojson") {
      downloadGeojson(props.graphics.options.Row);
    } else if (format === "arcgis_json") {
      downloadArcgisjson(props.graphics.options.Row);
    }
  }
  exportLoading.value = false;
  onClose();
};

/**
 * 导出text坐标数据
 */
const downloadTxt = async (row: any) => {
  let geometry = row.geometry
  if (!row.hasOwnProperty('isCollect')) {
    geometry.type = row.type
  }
  let coordName: any = coords.value.find((item: any) => {
    return exportFormModel.value.coord == item.value
  })
  let name
  if (row.label && coordName) {
    name = row.label + "(" + coordName.label + ")"
  }
  if (
    geometry.type === "polygon" ||
    geometry.type === "circle" ||
    geometry.type === "rectangle"
  ) {

    let txt
    const { rings } = geometry;
    const ring = rings[0];
    txt = ring.map((item: any, i: number) => {
      let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [{
        x: item[0], y: item[1], spatialReference:
          geometry.spatialReference
      }])
      if (i == 0) {
        return `,${list[0][0]},${list[0][1]},0\n`;
      } else {
        return `${list[0][0]},${list[0][1]},0\n`;
      }
    });
    const blob = new Blob([txt], {
      type: "text/plain;charset=utf-8",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  } else if (geometry.type === "polyline") {
    const { paths } = geometry;
    const path = paths[0];
    let txt = path.map((item: any, i: number) => {
      let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [{
        x: item[0], y: item[1], spatialReference:
          geometry.spatialReference
      }])
      return `,${list[0][0]},${list[0][1]},0 \n`;

    });
    const blob = new Blob([txt.join("\n")], {
      type: "text/plain;charset=utf-8",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  } else if (geometry.type === "point") {
    let point = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [geometry])
    const txt = `,${point[0][0]},${point[0][1]},0`;
    const blob = new Blob([txt], { type: "text/plain;charset=utf-8" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  } else if (geometry.type === "extent") {
    const { xmin, ymin, xmax, ymax } = geometry;
    const txt = `${xmin} ${ymin}\n${xmax} ${ymax}`;
    const blob = new Blob([txt], { type: "text/plain;charset=utf-8" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  }

};

/**
 * 导出Geojson坐标数据
 */
const downloadGeojson = async (row: any) => {
  const geojson = {
    type: "FeatureCollection",
    features: [] as any[],
  };
  console.log('row', row);

  let geometry: any = row.geometry
  if (props.inOnemap.MapType.value !== mapType.arcgis) {
    geometry.type = row.type
  }
  let coordName: any = coords.value.find((item: any) => {
    return exportFormModel.value.coord == item.value
  })
  let name
  if (row.label && coordName) {
    name = row.label + "(" + coordName.label + ")"
  }
  if (
    geometry.type === "polygon" ||
    geometry.type === "circle" ||
    geometry.type === "rectangle"
  ) {

    const { rings } = geometry;
    const ring = rings[0];
    const coordinates = ring.map((item: any) => {
      let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [{
        x: item[0], y: item[1], spatialReference:
          geometry.spatialReference
      }])
      return list[0];
    });
    const feature = {
      type: "Feature",
      properties: {
        name,
      },
      geometry: {
        type: "Polygon",
        coordinates: [coordinates],
      },
    };
    geojson.features.push(feature);
  } else if (geometry.type === "polyline") {
    const { paths } = geometry;
    const path = paths[0];
    const coordinates = path.map((item: any) => {
      let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [{
        x: item[0], y: item[1], spatialReference:
          geometry.spatialReference
      }])
      return list[0];
    });
    const feature = {
      type: "Feature",
      properties: {
        name,
      },
      geometry: {
        type: "LineString",
        coordinates: coordinates,
      },
    };
    geojson.features.push(feature);
  } else if (geometry.type === "point") {
    // const { x, y } = geometry;
    let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [geometry])

    const feature = {
      type: "Feature",
      properties: {
        name,
      },
      geometry: {
        type: "Point",
        coordinates: list[0],
      },
    };
    geojson.features.push(feature);
  }
  const blob = new Blob([JSON.stringify(geojson)], {
    type: "text/plain;charset=utf-8",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${name}.geojson`;
  a.click();
  URL.revokeObjectURL(url);
};

/**
 * 导出ArcgisJson坐标数据
 */
const downloadArcgisjson = async (row: any) => {
  const arcgisjson = {
    displayFieldName: "",
    fieldAliases: {},
    geometryType: "",
    spatialReference: {
      wkid: 4490,
      latestWkid: exportFormModel.value.coord,
    },
    fields: [],
    features: [] as any[],
  };
  let geometry: any = row.geometry
  if (!row.hasOwnProperty('isCollect')) {
    geometry.type = row.type
  }
  let coordName: any = coords.value.find((item: any) => {
    return exportFormModel.value.coord == item.value
  })
  let name
  if (row.label && coordName) {
    name = row.label + "(" + coordName.label + ")"
  }
  if (
    geometry.type === "polygon" ||
    geometry.type === "circle" ||
    geometry.type === "rectangle"
  ) {
    const { rings } = geometry;
    const ring = rings[0];
    const coordinates = ring.map((item: any) => {
      let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [{
        x: item[0], y: item[1], spatialReference:
          geometry.spatialReference
      }])
      return list[0];
    });
    const feature = {
      attributes: {
        name,
      },
      geometry: {
        rings: [coordinates],
        spatialReference: {
          wkid: 4490,
          latestWkid: exportFormModel.value.coord,
        },
      },
    };
    arcgisjson.features.push(feature);
  } else if (geometry.type === "polyline") {
    const { paths } = geometry;
    const path = paths[0];
    const coordinates = path.map((item: any) => {
      let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [{
        x: item[0], y: item[1], spatialReference:
          geometry.spatialReference
      }])
      return list[0];
    });
    const feature = {
      attributes: {
        name,
      },
      geometry: {
        paths: [coordinates],
        spatialReference: {
          wkid: 4490,
          latestWkid: exportFormModel.value.coord,
        },
      },
    };
    arcgisjson.features.push(feature);
  } else if (geometry.type === "point") {
    // const { x, y } = geometry;
    let list = changeCoord(geometry.spatialReference.wkid, exportFormModel.value.coord, [geometry])
    const feature = {
      attributes: {
        name,
      },
      geometry: {
        x: list[0][0],
        y: list[0][1],
        spatialReference: {
          wkid: 4490,
          latestWkid: exportFormModel.value.coord,
        },
      },
    };
    arcgisjson.features.push(feature);
  } else if (geometry.type === "extent") {
    const { xmin, ymin, xmax, ymax } = geometry;
    const coordinates = [
      [xmin, ymin],
      [xmax, ymax],
    ];
    const feature = {
      attributes: {
        name,
      },
      geometry: {
        rings: [coordinates],
        spatialReference: {
          wkid: 4490,
          latestWkid: 4490,
        },
      },
    };
    arcgisjson.features.push(feature);
  }

  const blob = new Blob([JSON.stringify(arcgisjson)], {
    type: "text/plain;charset=utf-8",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${name}.json`;
  a.click();
  URL.revokeObjectURL(url);
};
</script>
<style></style>
