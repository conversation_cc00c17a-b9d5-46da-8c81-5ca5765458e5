<template>
	<div>
		<el-form :model="collectFormModel" :rules="checkRules" ref="collectForm" style="margin-top: 12px;padding-right: 10px;">
			<el-form-item label="分组名称" label-width="80px" prop="group" style="margin-left: 10px">
				<el-autocomplete size="small" v-model="collectFormModel.group" :fetch-suggestions="querySearchAsync"
					:select-when-unmatched="true" clearable hide-loading  />
			</el-form-item>
			<el-form-item label="图形名称" label-width="80px" v-if="isShowName" style="margin-left: 10px" prop="name">
				<el-input v-model="collectFormModel.name" size="small" autocomplete="off"
					></el-input>
			</el-form-item>
			<!-- <el-form-item label="图形高度" label-width="80px" v-show="showHeight" style="margin-left: 10px">
				<el-input v-model="collectFormModel.height" size="small" autocomplete="off"></el-input>
			</el-form-item> -->
			<el-form-item :label="_inOnemap.MapType.value == mapType.cesium ? '收藏视角':''" label-width="80px" style="margin-left: 10px;" >
				<el-switch size="small" v-model="collectView" @change="collectSJ" v-if="_inOnemap.MapType.value == mapType.cesium"/>
        		<span class="more" @click="setMoreList">{{
						!setMore ? "更多设置" : "收起"
					}}</span>
			</el-form-item>
      			<!-- 符号化 -->
			<!-- <el-form :model="styleFormModel" :rules="styleFormRules"
					v-if="showSymbol && setMore && collectTitle == '编辑收藏图形'" ref="styleForm"> -->
			<el-form :model="styleFormModel" :rules="styleFormRules"
					v-if="false" ref="styleForm">
					<template v-if="styleFormModel.type == 'polyline'">
						<el-form-item label="填充样式" label-width="80px" style="margin-left: 10px"
							v-if="_inOnemap.MapType.value == mapType.arcgis"><el-select size="small" v-model="styleFormModel.lineStyle"
								style="margin-right: 15px; width: 160px">
								<el-option v-for="(item, index) in lineType" :key="index" :label="item.label"
									:value="item.value" :style="item.value != 'none'
											? 'height: 55px;line-height:25px;width:120px;'
											: 'width:120px;'
										">
									<span>{{ item.label || item.value }}</span><br />
									<img v-if="item.img" :src="item.img" />
								</el-option>
							</el-select>
							<el-form-item label="填充颜色" label-width="80px" prop="lineColor"><el-color-picker
									style="width: 100%" show-alpha size="small" v-model="styleFormModel.lineColor" />
							</el-form-item>
						</el-form-item>
						<el-form-item label="边框宽度" label-width="80px" style="margin-left: 10px">
							<el-input-number size="small" v-model="styleFormModel.lineWidth" :min="1" />
							<el-form-item label="颜色" label-width="80px" prop="lineColor"><el-color-picker
									style="width: 100%" show-alpha size="small" v-model="styleFormModel.lineColor" />
							</el-form-item>
						</el-form-item>
						<el-form-item label="标注颜色" label-width="80px" style="margin-left: 10px;display: flex;">
							<el-color-picker size="small" v-model="styleFormModel.lineColor" />
							<el-form-item label="标注字号" label-width="80px" style="margin-left: 10px">
								<el-input-number size="small" v-model="styleFormModel.tipFontSize" :min="8" :max="80" />
							</el-form-item>
						</el-form-item>
					</template>
					<template v-else-if="['polygon','rectangle','circle'].includes(styleFormModel.type)">
						<el-form-item label="填充样式" label-width="80px" style="margin-left: 10px"
							v-if="!styleFormModel.isPolygon3d"><el-select size="small" v-model="styleFormModel.polygonStyle"
								style="margin-right: 15px; width: 160px">
								<el-option v-for="(item, index) in fillType" :key="index" :label="item.label"
									:value="item.value" :style="item.value != 'none'
											? 'height: 55px;line-height:25px;width:120px;'
											: 'width:120px;'
										">
									<span>{{ item.label || item.value }}</span><br />
									<img v-if="item.img" :src="item.img" />
								</el-option>
							</el-select>
							<el-form-item label="填充颜色" label-width="80px" prop="polygonColor">
								<el-color-picker style="width: 100%" show-alpha size="small"
									v-model="styleFormModel.polygonColor" />
							</el-form-item>
						</el-form-item>
						<el-form-item label="边框宽度" label-width="80px" style="margin-left: 10px">
							<el-input-number size="small" v-model="styleFormModel.outlineWidth" :min="1" />
							<el-form-item label="边框颜色" label-width="80px" prop="outlineColor">
								<el-color-picker style="width: 100%" size="small" v-model="styleFormModel.outlineColor" />
							</el-form-item>
						</el-form-item>
						<el-form-item label="标注颜色" label-width="80px" style="margin-left: 10px">
							<el-color-picker size="small" v-model="styleFormModel.tipColor" />
							<el-form-item label="标注字号" label-width="80px" style="margin-left: 10px">
								<el-input-number size="small" v-model="styleFormModel.tipFontSize" :min="8" :max="80" />
							</el-form-item>
						</el-form-item>
					</template>
					<template v-else>
						<el-form-item label="图形样式" label-width="80px" style="margin-left: 10px"><el-select size="small"
								v-model="styleFormModel.pointStyle" style="margin-right: 15px; width: 160px">
								<el-option label="圆形" value="circle" />
								<el-option label="十字星" value="cross" />
								<el-option v-if="_inOnemap.MapType.value == mapType.arcgis" label="菱形" value="diamond" />
								<el-option label="正方形" value="square" />
								<el-option label="x" value="x" />
								<el-option v-if="_inOnemap.MapType.value == mapType.cesium" label="菱形" value="diamond" />
								<el-option label="三角形" value="triangle" />
							</el-select>
							<el-form-item label="填充颜色" label-width="80px" prop="pointColor">
								<el-color-picker style="width: 100%" size="small" show-alpha
									v-model="styleFormModel.pointColor" />
							</el-form-item>
						</el-form-item>

						<el-form-item label="图形大小" label-width="80px" style="margin-left: 10px">
							<el-input-number size="small" v-model="styleFormModel.pointSize" :min="1" />
						</el-form-item>
						<el-form-item label="边框宽度" label-width="80px" style="margin-left: 10px">
							<el-input-number size="small" v-model="styleFormModel.outlineWidth" :min="1" />
							<el-form-item label="边框颜色" label-width="80px">
								<el-color-picker style="width: 100%" size="small" v-model="styleFormModel.outlineColor" />
							</el-form-item>
						</el-form-item>
						<el-form-item label="标注颜色" label-width="80px" style="margin-left: 10px">
							<el-color-picker style="width: 100%" size="small" v-model="styleFormModel.tipColor" />
							<el-form-item label="标注字号" label-width="80px" style="margin-left: 10px">
								<el-input-number size="small" v-model="styleFormModel.tipFontSize" :min="8" :max="80" />
							</el-form-item>
						</el-form-item>
					</template>
			</el-form>
      <el-divider v-if="setMore" />
      <el-form-item label="上传附件" label-width="80px" prop="linkFile" style="margin-left: 10px" v-if="setMore">
				<el-upload ref="uploadRef" class="upload" style="width: 100%" :action="urlObj.drawFileUpload + '/' + collectFormModel.id"
					:auto-upload="false"
					accept=".xls,.xlsx,.doc,.docx,.pdf,.jpg,.png,.zip" :on-change="handleChange"
					:on-success="uploadSuccess" :on-remove="handleRemove" :multiple="false"
					:before-upload="beforeUpload"
					:headers="{
						Authorization: 'bearer ' + localToken,
					}" :file-list="fileList">
					<div class="up-btn">
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
							t="1663736127174" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="2949" width="25"
							height="25">
							<path
								d="M512 954.88c-243.712 0-442.88-199.168-442.88-442.88s199.168-442.88 442.88-442.88 442.88 199.168 442.88 442.88-199.168 442.88-442.88 442.88z m0-63.488c208.896 0 379.392-170.496 379.392-379.392s-170.496-379.392-379.392-379.392-379.392 170.496-379.392 379.392 170.496 379.392 379.392 379.392z m192-360.96h-363.52c-14.336 0-26.624-11.776-26.624-26.624V496.64c0-14.336 11.776-26.624 26.624-26.624h363.52c14.336 0 26.624 11.776 26.624 26.624v7.168c0 14.336-12.288 26.624-26.624 26.624z m-212.48 171.52l1.024-363.52c0-14.336 11.776-26.624 26.624-26.624h7.168c14.336 0 26.624 11.776 26.624 26.624l-1.024 363.52c0 14.336-11.776 26.624-26.624 26.624h-7.168c-14.848 0-26.624-11.776-26.624-26.624z"
								p-id="2950" fill="#8a8a8a" />
						</svg>
					</div>
					<template #tip>
						<span class="el-upload__tip">
							文件大小不得超过15M，上传文件附件类型包括：.xls、.xlsx、.doc、.docx、.pdf、.jpg、.png、
							.zip。
						</span>
					</template>
				</el-upload>
			</el-form-item>

			<el-form-item>
				<el-table
				:data="fileData"
				v-if="setMore"
				empty-text="暂无数据"
				style="width: 100%; max-height: 210px; overflow-y: auto"
			>
				<el-table-column
					align="center"
					prop="fileName"
					label="附件名"
					></el-table-column>
					<el-table-column
					align="center"
					prop="fileSuffix"
					label="附件格式"
					></el-table-column>
					<el-table-column align="center" label="操作" width="150">
					<template v-slot="{ row }">
						<span class="table-tools">
						<el-button @click="download(row)" type="success" size="small"
							>下载</el-button
						>
						<el-button @click="setDeleteIds(row)"  type="danger" size="small"
							>移除</el-button
						>
						</span>
					</template>
					</el-table-column>
				</el-table>
			</el-form-item>
			<el-form-item label-width="80px" style="margin-left: 10px;position: relative;">
				<div style="position: absolute;right: 0px">
					<el-button size="small" @click="closeCollectForm">取 消</el-button>
					<el-button size="small" type="primary" @click="addCollection">确 定</el-button>
				</div>
			</el-form-item>
		
		</el-form>
	</div>
</template>

<script  lang="ts" setup>
import { ref, onMounted, watch, toRaw, defineExpose, inject } from "vue";
import * as Cesium from "@onemapkit/cesium";
import { ElMessage, ElMessageBox } from "element-plus";
import { mapType } from "../../onemapkit";
import arcgisScale from "../../MapLayerTree/arcgisScale"
import { cloneDeep } from "lodash";
import request from "../../utils/axios";
// import { cloneDeep } from "lodash";
const props = defineProps({
  PropStore: {
    type: Object as any,
    default: null,
  },
  _inOnemap: {
    type: Object as any,
    default: null,
  },
  groupList: {
    type: Object as any,
    default: null,
  },
  collectTitle: {
    type: String,
    default: '',
  },
});
const localToken = localStorage.getItem('token')
const checkRules = {
	name: [{ required: true, message: "请输入名称", trigger: "blur" }],
	group: [{ required: true, message: "请选择分组", trigger: "change" }],
};
const fileData = ref([])

onMounted(()=>{
  console.log('props.collectTitle',props.collectTitle);
  if (props.collectTitle == "编辑收藏图形") {
		showSymbol.value = true;
		return;
	}

	showSymbol.value = false;
})


const styleFormModel:any = ref({
	lineColor: "",
	outlineColor: "",
	polygonColor: "",
	pointColor: "",
  type: ""
});

const getAssetsFile = (url: string) => {
  return new URL(`../../icon/images/${url}`, import.meta.url).href;
};

const urlObj: any = props.PropStore.getStorage('urlObj')?.value
//获取附件列表
const getTableData = async(id: string) => {
    let res = await request.get({url: urlObj.drawFileList,
      params: {bid:id},
      header: {
        Authorization:  `bearer ` + localToken,
      }
    })
	console.log('附件列表res',res);
	if(res && res.data){
		fileData.value = res.data
	}
	
};

const fileList:any = ref([])
//文件改变时
const handleChange = (uploadFile:any, uploadFiles:any) => {
			fileList.value = uploadFiles;
			let testmsg = uploadFile.name.substring(
				uploadFile.name.lastIndexOf(".") + 1
			);
			const extension = [
				"xls",
				"XLS",
				"xlsx",
				"XLSX",
				"docx",
				"doc",
				"DOC",
				"DOCX",
				"pdf",
				"PDF",
				"jpg",
				"JPG",
				"png",
				"PNG",
				"zip",
				"ZIP",
			];
			let type = extension.indexOf(testmsg);
			const isLt2M = uploadFile.size / 1024 / 1024 < 15;
			if (type < 0) {
				ElMessage.error("文件格式不支持");
				if (fileList.value.length == 1) {
					fileList.value = [];
				} else {
					fileList.value.forEach((item:any, index:number) => {
						if (item.name == uploadFile.name) {
							fileList.value.splice(index, 1);
						}
					});
				}
				return;
			}
			if (!isLt2M) {
				ElMessage.error("文件大小不得超过15M");
				if (fileList.value.length == 1) {
					fileList.value = [];
				} else {
					fileList.value.forEach((item:any, index:number) => {
						if (item.name == uploadFile.name) {
							fileList.value.splice(index, 1);
						}
					});
				}
			}
};

const uploadRef = ref()
// 上传成功时的回调
const uploadSuccess = (res:any,file:any) => {
	
	console.log('上传成功file',file);
	console.log('上传成功res',res);
};

// 上传之前的回调
const beforeUpload = (file:any) => {
	console.log('file',file);
};
const handleRemove = (file:any, files:any) => {
	fileList.value = files;
};

let camera:any
const collectView = ref(false)
const collectSJ = (row:any) => {
	console.log('row',row);
};

const getCamera = () => {
	//获取zoom
	let Cartographic = (props._inOnemap.MapViewer as Cesium.Viewer).camera.positionCartographic
	let getzoom: any = arcgisScale.getZoomFromHeight(props._inOnemap.MapViewer, Cartographic.height, Cesium.Math.toDegrees(Cartographic.latitude))

	console.log('getzoom', getzoom);
	// //相机坐标
	let cameraPosition = {
		x: Cesium.Math.toDegrees(Cartographic.longitude),
		y: Cesium.Math.toDegrees(Cartographic.latitude),
	}
	// 鼠标信息
	const mapPoint: any = props.PropStore.bottomInfoData.value.coordinates
	camera = {
		lat: cameraPosition.y.toFixed(5),
		lon: cameraPosition.x.toFixed(5),
		tilt: mapPoint && mapPoint.pitch ? (mapPoint.pitch + 90).toFixed(2) : 0.00,
		heading: mapPoint && mapPoint.heading ? mapPoint.heading.toFixed(2) : 0.00,
		zoom: getzoom.toFixed(5)
	}
	collectFormModel.value.geoData['camera'] = camera
}

const lineType = ref([
			{
				value: "dash",
				img: getAssetsFile("draw/symbols-sls-dash.png"),
			},
			{
				value: "dash-dot",
				img: getAssetsFile("draw/symbols-sls-dash-dot.png"),
			},
			{
				value: "dot",
				img: getAssetsFile("draw/symbols-sls-dot.png"),
			},
			{
				value: "long-dash",
				img: getAssetsFile("draw/symbols-sls-long-dash.png"),
			},
			{
				value: "long-dash-dot",
				img: getAssetsFile("draw/symbols-sls-long-dash-dot.png"),
			},
			{
				value: "long-dash-dot-dot",
				img: getAssetsFile("draw/symbols-sls-dash-dot-dot.png"),
			},
			{
				value: "none",
				label: "无设置",
			},
			{
				value: "short-dash",
				img: getAssetsFile("draw/symbols-sls-short-dash.png"),
			},
			{
				value: "short-dash-dot",
				img: getAssetsFile("draw/symbols-sls-short-dash-dot.png"),
			},
			{
				value: "short-dash-dot-dot",
				img: getAssetsFile("draw/symbols-sls-short-dash-dot-dot.png"),
			},
			{
				value: "short-dot",
				img: getAssetsFile("draw/symbols-sls-short-dot.png"),
			},
			{
				value: "solid",
				img: getAssetsFile("draw/symbols-sls-solid.png"),
			},
		]);

const fillType = ref([
	{
		value: "backward-diagonal",
		img: getAssetsFile("draw/symbols-sfs-backward-diagonal.png"),
	},
	{
		value: "cross",
		img: getAssetsFile("draw/symbols-sfs-cross.png"),
	},
	{
		value: "diagonal-cross",
		img: getAssetsFile("draw/symbols-sfs-diagonal-cross.png"),
	},
	{
		value: "forward-diagonal",
		img: getAssetsFile("draw/symbols-sfs-forward-diagonal.png"),
	},
	{
		value: "horizontal",
		img: getAssetsFile("draw/symbols-sfs-horizontal.png"),
	},
	{
		value: "none",
		label: "无设置",
	},
	{
		value: "vertical",
		img: getAssetsFile("draw/symbols-sfs-vertical.png"),
	},
	{
		value: "solid",
		img: getAssetsFile("draw/symbols-sfs-solid.png"),
	},
]);

const styleFormRules = {
	lineColor: [
		{ required: false, message: "请选择颜色", trigger: "change" },
	],
	outlineColor: [
		{ required: false, message: "请选择边框颜色", trigger: "change" },
	],
	polygonColor: [
		{ required: false, message: "请选择填充颜色", trigger: "change" },
	],
	pointColor: [
		{ required: false, message: "请选择填充颜色", trigger: "change" },
	],
};
const collectFormModel:any = ref({
	name: "",
	height: 10,
	linkFile: [],
	attribute: "",
	collectView: false,
	group: "",
	Metadata: '',
	geoData:{}
});

//当前编辑的graphic的dataId,在加载收藏图形时，赋了一个dataId的属性，根据这个ID找到图形，设置风格。
const querySearchAsync = (queryString:string, cb:any) => {
  console.log('groupList',props.groupList);
	cb(props.groupList);
};

const emits = defineEmits(['closeCollectForm','addCollection','clearAll','addCollectionAll'])
const closeCollectForm = () => {
	emits('closeCollectForm','')
};

let collectList:any = []
const collectForm = ref()
const addCollection = async () => {
	collectForm.value.validate(async (valid: any) => {
		if (valid) {
			
			let list:any = []
			if(deleteIds.length){
				for (const id of deleteIds) {
					let res = await deleteFile(id)
				}
			}
			if(collectList.length){
				emits('clearAll','')
				setTimeout(()=>{
					for (const item of collectList) {
						changeCollectionName(item)
						if(collectView.value){
							getCamera()
						}else{
							collectFormModel.value.geoData['camera'] = ''
						}
						let it = cloneDeep(collectFormModel.value)
						list.push(it)
					}	
					emits('addCollectionAll',list)
				},400)
			}else{
			  if(collectView.value){
				getCamera()
			  }else{
				collectFormModel.value.geoData['camera'] = ''
			  }
			  console.log('fileList.value',fileList.value);
			
			  if(fileList.value.length){		
				uploadRef.value.submit()		
			  }
			  emits('addCollection',collectFormModel.value)
			}

			
		}
	})
};

  //下载附件
  const download = async (row:any) => {
	let res = await request.download({url: urlObj.drawFileDownload + '/' + row.id,
      header: {
        Authorization:  `bearer ` + localToken,
      }
    })
	console.log('下载res',res);
	let blob = new Blob([res.data])
	let url = window.URL.createObjectURL(blob)
	const a = window.document.createElement("a");
	a.style.display = "none";
	a.href = url;
	a.download = row.fileName;
	document.body.appendChild(a);
	// 触发标签点击事件
	a.click();
	document.body.removeChild(a);
	// 释放URL
	window.URL.revokeObjectURL(a.href);
  };
  //删除文件
  let deleteIds:any = []
  
  const setDeleteIds = async (row:any) => {
	deleteIds.push(row.id)
	fileData.value = fileData.value.filter((item:any) => !deleteIds.includes(item.id))
    fileList.value = fileList.value.filter((x:any)=> row.id != x.id)
  };

  const deleteFile = async (row:any) => {
    console.log('filesList.value',fileList.value);
	let res = await request.delete({url: urlObj.drawFileDelete + `?id=${row}`,
      params: {id:row},
      header: {
        Authorization:  `bearer ` + localToken,
      }
    })
	return res
  };

const showSymbol = ref(false);
const setMore = ref(false)
const setMoreList = () => {
	setMore.value = !setMore.value;
};

const isShowName = ref(true)
const collectAll = (list:any) => {
	console.log('list',list);
	isShowName.value = false
	collectList = list
};

const changeCollectionName = (row:any) => {
	collectFormModel.value.name = row.label
	collectFormModel.value.id = row.id
	//统一收藏4490坐标
	if(!row.geometry.hasOwnProperty('type')){
		let geo = getGeometry(row.type,row.geometry)
		collectFormModel.value.geoData = {
			geometry: geo,
			camera
		}
	}else{
		collectFormModel.value.geoData = {
			geometry: row.geometry,
			camera
		}
	}
};
const editCollect = async (row:any,res:any) => {
	console.log('编辑row,res',row,res);
	if(res && res.geometry.type){
		styleFormModel.value.type = res.geometry.type
	}
	getTableData(row.id)
	collectFormModel.value = JSON.parse(JSON.stringify(row))
	collectFormModel.value.geoData = res
	if(res.camera){
		collectView.value = true
	}else{
		collectView.value = false
	}
	console.log('collectFormModel.value',collectFormModel.value);
	
};
defineExpose({
	changeCollectionName,
	editCollect,
	collectAll
})

//#region 4.2.Arcgis Map 里重绘
const toDegrees = (coord: Array<any>) => {
  let sunCoord: any = [];
  coord.forEach((subitm: any /* subitm =false 为经纬度 */) => {
    let _cord = Cesium.Cartographic.fromCartesian(subitm);
    sunCoord.push([
      Cesium.Math.toDegrees(_cord.longitude),
      Cesium.Math.toDegrees(_cord.latitude),
      _cord.height,
    ]);
  });
  return sunCoord;
};

const getGeometry = (type: string,geo: any)=>{
  let _geo:any
  switch (type) {
    case 'point':
    let xyz: any = toDegrees(geo)[0];
            console.log('xyz',xyz);
              _geo = {
                wkid: 4490,
                spatialReference: {
                  wkid: 4490
                },
                x: xyz[0],
                y: xyz[1],
                z: xyz[2],
                type
              };
      break;
    case 'polyline':
      let list: any = toDegrees(geo);
              _geo = {
                wkid: 4490,
                spatialReference: {
                  wkid: 4490
                },
                paths: [list],
                type
              };
      break;
    case 'rectangle':
      let tmp:any = toDegrees(geo);
      let xy1 = [tmp[0][0], tmp[0][1], tmp[0][2]];
      let xy2 = [tmp[1][0], tmp[0][1], tmp[0][2]];
      let xy3 = [tmp[1][0], tmp[1][1], tmp[1][2]];
      let xy4 = [tmp[0][0], tmp[1][1], tmp[1][2]];
      let xy5 = [tmp[0][0], tmp[0][1], tmp[0][2]];
      let rectangleRings = [[xy1, xy2, xy3, xy4, xy5]];
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490
        },
        rings: rectangleRings,
        type
      };
      break;
    case 'polygon':
      let polygonRings:any = toDegrees(geo);
      console.log('polygonRings',polygonRings);
      //多边形要闭合
      polygonRings.push(polygonRings[0])
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490
        },
        rings: [polygonRings],
        type
      };
      break;
    case 'circle':
      let circlelist:any = toDegrees(geo);
      //2.圆心
      let center:any = Cesium.Cartesian3.fromDegrees(circlelist[0][0],circlelist[0][1],0)
      //半径
      let radius = getDistance(geo)
      
      let circleRings = changeToPosition(center,radius)
      console.log('circleRings',circleRings);
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490
        },
		center: toDegrees([center]),
		radius,
        rings: [circleRings],
        type
      };
      break;
    default:
      break;
  }
  return _geo
}

//计算半径
const getDistance = (itm:any)=>{
      //计算两点之间的水平距离
      let cartesian3x = new Cesium.Cartesian3(itm[0].x, itm[0].y, itm[0].z)
      let cartesian3y = new Cesium.Cartesian3(itm[1].x, itm[1].y, itm[1].z)
      let distance1 = Cesium.Cartesian3.distance(cartesian3x,cartesian3y);
      // 计算两个笛卡尔坐标的高度差
      let cartographic1 = Cesium.Cartographic.fromCartesian(cartesian3x);
      let alt1 = cartographic1.height; // 高度
      let cartographic2 = Cesium.Cartographic.fromCartesian(cartesian3y);
      let alt2 = cartographic2.height; // 高度
      let height = Math.abs(alt1 - alt2);
      let horizontalDistance = Math.sqrt(distance1 ** 2 - height ** 2);
      return horizontalDistance
}

//三维圆转化为图形
const changeToPosition = (center:any,radius:number) => {
    const ellipseGeometry = new Cesium.EllipseOutlineGeometry({
      center: center,
      semiMajorAxis: radius,
      semiMinorAxis: radius,
      height: 0,
    }) as any;
      
    const options = {
      center: ellipseGeometry._center,
      semiMajorAxis: ellipseGeometry._semiMajorAxis,
      semiMinorAxis: ellipseGeometry._semiMinorAxis,
      ellipsoid: ellipseGeometry._ellipsoid,
      rotation: ellipseGeometry._rotation,
      height: ellipseGeometry._height,
      granularity: ellipseGeometry._granularity,
      numberOfVerticalLines: ellipseGeometry._numberOfVerticalLines,
    };

    let cep = (Cesium as any).EllipseGeometryLibrary.computeEllipsePositions(
      options,
      false,
      true
    ).outerPositions;

    cep = (Cesium as any).EllipseGeometryLibrary.raisePositionsToHeight(
      cep,
      options,
      false
    );
    const outLinePositions = [];
    for (let i = 0; i < cep.length; i += 3) {
      let cartographic = Cesium.Cartographic.fromCartesian(new Cesium.Cartesian3(cep[i], cep[i + 1], cep[i + 2]))
      let x = 180 / Math.PI * cartographic.longitude
      let y = 180 / Math.PI * cartographic.latitude
      outLinePositions.push([x,y])
    }
    return outLinePositions
};

</script>
<style scoped lang="scss">
// .el-form-item {
//   margin-bottom: 10px;
// }
.el-dialog__body {
	padding: 0 15px;
}

.dialog-content {
	position: absolute;
	width: 400px;
	top: 40px;
	right: 5px;
	z-index: 9999;
	background-color: #fff;
	box-shadow: 0px 4px 4px 4px rgba(0, 0, 0, 0.2);

	.util-panel__header {
		height: 40px;
		line-height: 40px;
		padding: 0 10px;
		border-bottom: 1px solid #e6ebf5;
		background: #e1e4e6;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.icon-guanbi {
			cursor: pointer;
		}
	}
}

.footer {
	width: 100%;
	padding: 15px;
	text-align: right;
}

.el-input-number--small {
	margin-right: 15px;
	width: 160px;
}

// .el-input--small {
// 	width: 200px;
// }

.el-divider {
	width: 100%;
	margin: 15px 10px;
}

.more {
	position: absolute;
	right: 0px;
	cursor: pointer;
	color: #1890ff;
}

.up-btn {
	height: 25px;
	width: 25px;
	margin-top: 5px;
	margin-right: 10px;
}

.el-upload {
	margin-top: 5px;
	margin-right: 5px;
}

.el-table__body-wrapper {
	max-height: 200px;
	overflow-y: auto;
}

.upload {
	display: flex;
	flex-direction: column;
}

.el-upload__tip {
	line-height: 20px;
	overflow-wrap: anywhere;
}
</style>
