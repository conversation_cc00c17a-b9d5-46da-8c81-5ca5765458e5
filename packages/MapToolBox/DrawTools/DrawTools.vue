<template>
  <!-- 1. 绘制工具栏按钮 _Toolitems-->
  <div class="draw-panel">
    <el-button-group style="margin: 0 0 0 6px">
      <el-button v-for="(item, idx) in [..._inOptions.MapDrawTools, ...inToolItems].filter((itm: any) => itm.isVisible)"
        :key="'btn' + idx" class="draw-btn" plain
        :disabled="(item as IToolItemType).label == '清除图形' && _graphicDataKeysprop.length == 0"
        :title="(item as IToolItemType).label" @click="_toolItemDefaultEvent(item)">
        <component class="draw-icon" :is="(item as IToolItemType).icon" />
      </el-button>
      <el-button class="draw-icon draw-btn" title="批量下载" v-show="currentTab == 2" @click="downGraphicAll()">
        <el-icon>
          <Download />
        </el-icon>
      </el-button>
    </el-button-group>
    <div v-show="currentTab == 1" style="display: inline-block">
      <!-- <div v-show="false" style="display: inline-block"> -->
      <span style="padding-right: 2px; padding-left: 10px">面积</span>
      <el-select size="small" v-model="drawArea" @change="areaChange" style="width: 85px">
        <el-option v-for="item in rectUnitsShare" :key="item.code" :value="item.code" :label="item.name" />
      </el-select>
      <span style="padding-right: 2px; padding-left: 10px">距离</span>
      <el-select size="small" v-model="drawDistance" @change="distanceChange" style="width: 60px">
        <el-option v-for="item in lengthUnitsShare" :key="item.code" :value="item.code" :label="item.name" />
      </el-select>
    </div>
  </div>
  <el-tabs v-model="currentTab" style="height: calc(100% - 35px)">
    <el-tab-pane :key="1" label="绘制" :name="1" style="height: 100%">
      <el-table :data="_graphicDataKeysprop" style="width: 100%; text-align: center; height: calc(100% - 30px)"
        :row-style="{ 'line-height': '30px' }" :show-header="false" size="small" border
        @selection-change="handleSelectionChange">
        <template #empty>
          <el-empty description="暂无绘制数据" :image-size="100" />
        </template>
        <!--2.1.1 序号 -->
        <el-table-column type="selection" width="30" align="center" />
        <el-table-column type="index" width="30" align="center" />
        <!-- 2.1.2 primitive 名称 _graphicDataValue.get() _graphicDataValue.get(RowData.row) -->
        <el-table-column width="160">
          <template #default="scope">
            <div class="geometry-name">
              <component class="geometry-icon" :is="getIconComponent(scope.row)" />
              <!-- <component class="geometry-icon" :is="getIcons(scope.row)" />  -->
              <el-input size="small" v-model="scope.row['label']" @input="nameChange(scope.row)" />
            </div>
          </template>
        </el-table-column>
        <!--2.1.3 操作的按钮-->
        <el-table-column>
          <template #default="scope">
            <span v-for="(item, index) in ButtonItems" :key="index">
              <el-button size="small" text v-if="
                item.label !== '确定' ||
                (currentId == scope.row.id && item.label == '确定')
              " :type="(item as any)?.isWarn ? 'warning' : 'primary'" style="margin: 2px 6px; padding: 0"
                @click="DrawToolListItemEvent(scope, item)">{{ (item as any)?.label }}</el-button>
            </span>
            <span v-for="(item, index) in Options?.singleOptions" :key="index">
              <el-button size="small" text :type="item.type" style="margin: 2px 6px; padding: 0"
                @click="DrawToolListCustomItemEvent(scope, item)">{{ (item as any)?.label }}</el-button>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex;align-items: center;min-height: 32px;">
        <span style="margin-left: 15px">名称：</span>
        <el-switch v-model="drawName" size="small" :disabled="!_graphicDataKeysprop.length" @change="setLableDisplay">
        </el-switch>

        <span style="margin-left: 15px">量算值：</span>
        <el-switch v-model="drawMeasure" size="small" @change="setLableDisplay"
          :disabled="!_graphicDataKeysprop.length"></el-switch>
        <span>
          <el-button text type="primary" @click="downAll" v-show="_graphicDataKeysprop.length > 1"
            :disabled="!(_graphicDataKeysprop.length > 1)">下载全部图形坐标</el-button>
          <el-button v-for="item in Options?.customHandleButtons" text type="primary" @click="handleChecked(item)">
            item.label</el-button>
        </span>
      </div>
    </el-tab-pane>
    <!-- 动态渲染自定义tab页 -->
    <el-tab-pane v-for="tab in Options?.customTabs" :key="tab.name" :label="tab.label" :name="tab.name">
      <component :is="tab.component" :PropStore="props.PropStore" />
    </el-tab-pane>
  </el-tabs>

  <PopPanel v-if="showExportGeometry" title="坐标导出" :width="330" :height="160" :top="20" :right="75" :index="1001"
    @close="close">
    <div>
      <exportGeometry :graphics="exportCoord" @close="close" :inOnemap="_inOnemap"></exportGeometry>
    </div>
  </PopPanel>
  <PopPanel v-if="showUploadPanel" title="坐标导入" :width="370" :max-height="300" :top="20" :right="75" :index="1001"
    @close="closeUploadPanel">
    <UploadPanel :isDialog="true" @result="result" :isClear="false"></UploadPanel>
  </PopPanel>
</template>
<script lang="ts" setup>
import { computed, markRaw, nextTick, onBeforeUnmount, onMounted, onUnmounted, ref, watch, } from "vue";
import {
  defaultParameter,
  DockType,
  geometryType,
  getOnemap,
  getOption,
  Icons,
  type IToolItemType,
  mapType,
  PopPanel,
  UploadPanel,
  OnemapClass,
  Utils,
} from "onemapkit";
import { ElMessage, ElMessageBox } from "element-plus";
import "element-plus/es/components/message/style/css";
import "element-plus/es/components/message-box/style/css";
import "element-plus/dist/index.css";
import { Download } from "@element-plus/icons-vue";
import exportGeometry from "./exportGeometry.vue";
import { cloneDeep } from "lodash";
import * as Cesium from "@onemapkit/cesium";
import * as turf from "@turf/turf";
import {
  DrawGeometryType,
  getPolylineCenter,
  toDegree,
  toDegrees,
  formatUnitPolygon,
  ensureClosed
} from "@/MapToolBox/DrawTools/tools";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  Onemap: {
    type: OnemapClass,
    default: null,
  },
  Options: {
    type: Object as any,
    default: null,
  },
  ToolData: {
    type: Object as any,
    default: null,
  },
});
console.log("props.PropStore", props.PropStore);
const currentTab = ref(1);
const drawGraphicId = "drawToolsLayer";
//const toolList = ref([] as any);
//const toolList = [] as any;
// const _inOnemap: OnemapClass = getOnemap(props.MapControlName);
const oneMapObj = getOnemap(props.MapControlName);
const _inOnemap = oneMapObj ? oneMapObj : props.Onemap as OnemapClass;
const _inOptions = getOption(props.MapControlName);
const isDrawing = props.PropStore.getStorage("isDrawing");//控制绘制控件的绘制状态

//toolList = defaultParameter.defaultEditToolItems;
_inOptions.MapDrawTools = defaultParameter.defaultEditToolItems;

//绘制工具弹窗上的菜单点击事件，感觉这个事件没有什么意义，计划弃用
// const emits = defineEmits(["ToolItemClick"]);
// const _ToolItemClick: any = (options: any) => {
//   emits("ToolItemClick", options);
// };

//#region 1.参数定义

const getIconComponent = (row: any) => {
  return row?.icon || 'div'
}

onBeforeUnmount(() => {
  //退出编辑状态
  if (_inOnemap.MapType.value == mapType.arcgis) {
    _inOnemap.removeDrawStatus();
  } else {
    _inOnemap.DrawBrush.reEdit("");
  }
  clearAllGeo(true);
});
const shareMeasure = ref(true);
const rectUnitShare = ref("m2");
const rectUnitsShare = ref([
  { code: "m2", name: "平方米", },
  { code: "km2", name: "平方公里", },
  { code: "gq", name: "公顷", },
  { code: "mu", name: "亩", },
]);
let drawgeometryIds = [] as any[];

const drawArea = ref("m2");
const drawDistance = ref("m");

const areaChange = () => {
  for (const one of _graphicDataKeysprop.value) {
    let geo: any;
    if (one.geometry.type) {
      geo = one.geometry;
    } else {
      geo = getGeometry(one.type, one.geometry);
    }
    let area = getLengthOrRect(geo);
    let text: any = "";
    if (drawName.value) {
      text = one.label;
    }
    if (drawMeasure.value) {
      text += "\n面积：" + useFormatUnitPolygon(Number(area), drawArea.value);
    }
    if (
      _inOnemap.MapType.value == mapType.arcgis &&
      ["polygon", "circle", "rectangle"].includes(geo.type) &&
      drawMeasure.value
    ) {
      _inOnemap.setGraphicName(
        "",
        "poi_" + one.id + "_unit",
        "面积：" + useFormatUnitPolygon(Number(area), drawArea.value)
      );
    }
    if (
      _inOnemap.MapType.value == mapType.cesium &&
      ["polygon", "circle", "rectangle"].includes(geo.type)
    ) {
      _inOnemap.removeAllGraphicsByLayerId(one.id + "_label"); //删除标注
      //重新绘制
      drawLabel(one.centerPoint, one.id, text);
    }
  }
};
const distanceChange = () => {
  console.log("_graphicDataKeysprop.value", _graphicDataKeysprop.value);
  for (const one of _graphicDataKeysprop.value) {
    let geo: any;
    if (one.geometry.type) {
      geo = one.geometry;
    } else {
      geo = getGeometry(one.type, one.geometry);
    }
    if (_inOnemap.MapType.value == mapType.arcgis && one.type == "polyline") {
      // if (geo.paths && drawMeasure.value) {
      //   geo.paths[0].forEach((_: any, i: number) => {
      //     if (i > 0) {
      //       let line = turf.lineString(geo.paths[0].slice(0, i + 1));
      //       let length = turf.length(line, { units: "meters" });
      //       _inOnemap.setGraphicName(
      //           "",
      //           "poi_" + one.id + i,
      //           useFormatUnitPolygon(length, drawDistance.value)
      //       );
      //     }
      //   });
      // }
      if (one?.entityLabels && drawMeasure.value) {
        one.entityLabels?.forEach((el: any, i: number) => {
          _inOnemap.setGraphicName(
            "",
            "poi_" + one.id + i,
            useFormatUnitPolygon(el.areaMeasurement?.value, drawDistance.value)
          );
        })
      }
      if (drawName.value) {
        _inOnemap.setGraphicName("", "poi_" + one.id, one.label);
      } else {
        _inOnemap.setGraphicName("", "poi_" + one.id, "");
      }
    }
    if (_inOnemap.MapType.value == mapType.cesium && one.type == "polyline") {
      console.log("one", one);

      _inOnemap.removeAllGraphicsByLayerId(one.id + "_label"); //删除标注

      if (geo.paths && drawMeasure.value) {
        // geo.paths[0].forEach((it: any, i: number) => {
        //   if (i > 0) {
        //     let line = turf.lineString(geo.paths[0].slice(0, i + 1));
        //     let length = turf.length(line, { units: "meters" });
        //     _inOnemap.removeAllGraphicsByLayerId(one.id + i + "_label"); //删除标注
        //     drawLabel(
        //         {
        //           x: it[0],
        //           y: it[1],
        //           z: it[2],
        //         },
        //         one.id,
        //         useFormatUnitPolygon(length, drawDistance.value)
        //     );
        //   }
        // });
        if (one?.entityLabels && drawMeasure.value) {
          one.entityLabels?.forEach((el: any, i: number) => {
            _inOnemap.removeAllGraphicsByLayerId(one.id + i + "_label"); //删除标注
            drawLabel(
              el.position,
              one.id,
              useFormatUnitPolygon(el.areaMeasurement?.value, drawDistance.value)
            );
          })
        }
      }
      //重新绘制
      if (drawName.value) {
        drawLabel(one.centerPoint, one.id, one.label);
      }
    }
  }
};

const showLabelRewrite = (data: any, unit?: any) => {
  let text: string;
  let nameText = "";
  let measureText = "";
  if (drawName.value) {
    nameText = data.label;
  }
  const value = Number(data?.areaMeasurement?.value)?.toFixed(2);
  if (drawMeasure.value) {
    if (["polygon", "circle", "rectangle"].includes(data.type)) {
      if (unit) {
        measureText += `面积：` + formatUnitPolygon(value, unit);
      } else {
        measureText += `面积：` + value + "平方米";
      }
    }
  }
  text = `${nameText}\n${measureText}`;
  return text;
}

const showLabel = (row: any, unit?: any) => {
  let text = "";
  let nameText = "";
  let measureText = "";
  if (drawName.value) {
    nameText = row.label;
  }

  console.log("row.geometry", row.geometry);
  let geo: any;
  if (!row.geometry.hasOwnProperty("type")) {
    geo = cloneDeep(row.geometry);
    geo.type = row.type;
  } else {
    geo = cloneDeep(row.geometry);
  }
  if (drawMeasure.value) {
    if (["polygon", "circle", "rectangle"].includes(row.type)) {
      let area: any = getLengthOrRect(geo);
      if (unit) {
        measureText += `\n面积：` + useFormatUnitPolygon(area, unit);
      } else {
        measureText += `\n面积：` + area + "平方米";
      }
    }
  }
  text = nameText + measureText;
  return text;
};

const drawMeasure = ref(true);
const drawName = ref(true);
//切换单位和显示的标注,参数分别是切换的列表，量算值，名称，单位
const changeLabel = (
  list: any[],
  mearsure: boolean,
  name: boolean,
  unit: string
) => {
  if (_inOnemap.MapType.value == mapType.arcgis) {
    for (const item of list) {
      if (item.type == "polyline") {
        // let polylines = item.geometry.paths[0];
        let unitValue =
          unit == "draw" ? drawDistance.value : shareDistance.value;
        item?.entityLabels?.forEach((el: any, i: number) => {
          if (mearsure) {
            _inOnemap.setGraphicName(
              "",
              "poi_" + item.id + i,
              useFormatUnitPolygon(el.areaMeasurement?.value, unitValue)
            );
          } else {
            _inOnemap.setGraphicName("", "poi_" + item.id + i, "");
          }
        })
      }
      let showtext: any = showLabelRewrite(
        item,
        unit == "draw" ? drawArea.value : shareMeasure.value
      );
      console.log("showtext", showtext);

      if (["polygon", "circle", "rectangle"].includes(item.type)) {
        _inOnemap.setGraphicName(
          "",
          "poi_" + item.id + "_unit",
          showtext.split("\n")[1]
        );
      }
      _inOnemap.setGraphicName("", "poi_" + item.id, showtext.split("\n")[0]);
    }
  } else {
    for (const item of list) {
      console.log("item", item);

      let geo: any;
      if (item.geometry.spatialReference && item.geometry.type) {
        geo = {
          type: item.type,
          geometry: item.geometry,
          label: item.label,
        };
      } else {
        geo = {
          type: item.type,
          geometry: getGeometry(item.type, item.geometry),
          label: item.label,
        };
      }
      // @ts-ignore
      const coll = _inOnemap._impl.primitiveCollections.find((x: any) =>
        x.layerId.includes(item.id)
      );
      if (coll) {
        coll.primitives.removeAll();
      }
      let label: string = "";
      if (item.type == "polyline") {
        if (geo.geometry.paths && drawMeasure.value) {
          item.entityLabels?.forEach((el: any) => {
            // _inOnemap.removeAllGraphicsByLayerId(item.id + "_label")
            drawLabel(el.position, item.id, useFormatUnitPolygon(el.areaMeasurement?.value, drawDistance.value));
          });
        }
        if (name) {
          drawLabel(item.centerPoint, item.id, item.label);
        }
      } else {
        console.log("geo", geo);
        if (name || mearsure) {
          if (name) {
            label = item.label;
          } else {
            label = "";
          }
          if (mearsure) {
            let unitValue = unit == "draw" ? drawArea.value : rectUnitShare.value;
            let area: any = getLengthOrRect(geo.geometry);
            if (area) {
              label += "\n面积：" + useFormatUnitPolygon(area, unitValue);
            }
          } else {
            label += "\n" + " ";
          }
          drawLabel(item.centerPoint, item.id, label);
        }
      }
    }
  }
};

// @ts-ignore
const downAll = async (row: boolean) => {
  console.log("_graphicDataKeysprop", _graphicDataKeysprop.value);
  let list = cloneDeep(_graphicDataKeysprop.value);
  for (const item of list) {
    if (item.geometry.type) {
      item.geometry = item.geometry;
    } else {
      item.geometry = getGeometry(item.type, item.geometry);
    }
  }
  console.log("list", list);

  _exportCoord.value = {
    options: {
      Row: {
        data: list,
        isAll: true,
      },
    },
  };
  showExportGeometry.value = true;
};
const getAllGraphicList = () => {
  let list: any = [];
  _graphicDataKeys.value.forEach((item: any) => {
    list.push(_graphicDataValue.get(item));
  });
  return list;
}
// 更新标注显示的统一处理函数
const setLableDisplay = () => {
  const list = getAllGraphicList();
  changeLabel(list, drawMeasure.value, drawName.value, "draw");
};

const useFormatUnitPolygon = (value: number, unitType: string): string => {
  console.log("value,unitType", value, unitType);

  if (!value) return "";
  let res: string;
  switch (unitType) {
    case "m2":
      res = Number(value).toFixed(2) + "平方米";
      break;
    case "km2":
      res = Number(value / 1000000).toFixed(2) + "平方公里";
      break;
    case "mu":
      res = (value * 0.0015).toFixed(2) + "亩";
      break;
    case "gq":
      res = (value * 0.0001).toFixed(2) + "公顷";
      break;

    case "m":
      res = Number(value).toFixed(2) + "米";
      break;
    case "km":
      res = Number(value * 0.001).toFixed(2) + "千米";
      break;
    default:
      res = "NaN";
      break;
  }
  return res;
};
const shareDistance = ref("m");
const lengthUnitsShare = ref([
  {
    code: "m",
    name: "米",
  },
  {
    code: "km",
    name: "千米",
  },
]);
const result = (data: any) => {
  nextTick(() => {
    console.log("导入的data", data);

    if (_inOnemap.MapType.value == mapType.cesium) {
      console.log("_graphicDataKeys", _graphicDataKeys);
      let tem: any;
      switch (data.type.toLowerCase()) {
        case "point":
          tem = Icons.PointIcon;
          break;
        case "polyline":
          tem = Icons.polylineIcon;
          break;
        case "polygon":
          tem = Icons.RectangleIcon;
          break;
        default:
          break;
      }
      _graphicDataValue.set(data.id, {
        geometry: data.geometry,
        type: data.type,
        id: data.id,
        label: data.label || data.id,
        icon: tem,
        isUpload: true,
      });
      _graphicDataKeys.value.push(data.id);
    } else {
      drawgeometryIds.push(data.id);

      let ly = _inOnemap.GetLayerByID(data.id);
      if (ly && ly.graphics) {
        _inOnemap.fullExtent(ly.graphics[0]._extent);
      }
      let tem: any;
      switch (data.type.toLowerCase()) {
        case "point":
          tem = Icons.PointIcon;
          break;
        case "polyline":
          tem = Icons.polylineIcon;
          break;
        case "polygon":
          tem = Icons.RectangleIcon;
          break;
        default:
          break;
      }
      _graphicDataValue.set(data.id, {
        geometry: data.geometry,
        type: data.type,
        id: data.id,
        label: data.label || data.id,
        icon: tem,
        isUpload: true,
      });
      _graphicDataKeys.value.push(data.id);
      showUploadPanel.value = false;
    }
  });
};

const clearAllGeo = async (_clearTree: boolean) => {
  if (_inOnemap.MapType.value == mapType.cesium) {
    _inOnemap.DrawBrush.quitEdit();
    _graphicDataKeys.value.forEach((_key: any) => {
      // _inOnemap.DrawBrush.RemoveGeometryById(_key);
      _inOnemap.DrawBrush.RemovePrimitiveById(_key);
      _inOnemap.removeAllGraphicsByLayerId(_key + "_label"); //删除标注
      _inOnemap.clearDrawGraphicById(_key);
      const item: any = _graphicDataValue.get(_key);
      if (item?.type == "polyline" && item?.entityLabels) {
        item.entityLabels?.forEach((item: any) => {
          _inOnemap.removeAllGraphicsByLayerId(item.id + "_label");
        });
      }
    });
  } else {
    //删除二维绘制线的图形的标注
    _graphicDataKeys.value.forEach((_key: any) => {
      _inOnemap.removeAllGraphicsByLayerId(_key);
      _inOnemap.DrawBrush.removeGraphics(undefined, ["poi_" + _key + "_unit"]);
      let item: any = _graphicDataValue.get(_key);
      if (item?.type == "polyline" && item?.geometry.paths) {
        item.geometry.paths[0].forEach((_: any, i: number) => {
          if (i > 0) {
            //删除在二维绘制的图形和标注
            _inOnemap.DrawBrush.removeGraphics(undefined, [_key + i]);
          }
        });
      }
    });
    //删除在二维绘制的图形和标注
    _inOnemap.DrawBrush.removeGraphics(undefined, _graphicDataKeys.value);
  }
  _graphicDataKeys.value.length = 0;
  saveLabels = [];
  if (isDrawing) {
    isDrawing.value = false;
    localStorage.removeItem("geoType");
  }

  _graphicDataValue.clear();

  //移除收藏里勾选绘制的图形
  // if (treeRef.value && clearTree) {
  //   let nodes = treeRef.value.getCheckedNodes();
  //   console.log("nodes", nodes);
  //   if (nodes && nodes.length) {
  //     for (const node of nodes) {
  //       if (node.fileType == "2") {
  //         let res = await getSingleData(node.fileId);
  //         console.log("res", res);

  //         if (res && res.geometry && res.geometry.paths) {
  //           res.geometry.paths[0].forEach((it: any, i: number) => {
  //             if (i > 0) {
  //               //删除在二维绘制的图形和标注
  //               if (_inOnemap.MapType.value == mapType.cesium) {
  //                 _inOnemap.removeAllGraphicsByLayerId(node.id + i + "_label"); //删除标注
  //               } else {
  //                 _inOnemap.DrawBrush.removeGraphics(undefined, [node.id + i]);
  //               }
  //             }
  //           });
  //         }
  //       }
  //       _inOnemap.removeAllGraphicsByLayerId(node.id + "_label"); //删除三维标注
  //       _inOnemap.removeAllGraphicsByLayerId(node.id);
  //       //删除标注
  //       if (_inOnemap.MapType.value == mapType.arcgis) {
  //         _inOnemap.DrawBrush.removeGraphics(undefined, ["poi_" + node.id]);
  //         _inOnemap.DrawBrush.removeGraphics(undefined, [
  //           "poi_" + node.id + "_unit",
  //         ]);
  //       } else {
  //         _inOnemap.DrawBrush.RemovePrimitiveById(node.id); //删除绘制的圆
  //       }
  //     }
  //   }
  // }
};

//1.2 内部固定工具按钮
const inToolItems = [
  {
    label: "导入坐标",
    name: "importcoord",
    icon: Icons.importIcon,
    Dock: DockType.center,
    geoType: geometryType.undefined,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: (
      //@ts-ignore
      sender: any,
      _graphicDataKeys: any,
      _graphicDataValue: any
    ) => {
      showUploadPanel.value = true;
      // RemoveGeometryById
    },
    ToolButtonExecuteEvent: () => {
    },
  },
  {
    label: "清除图形",
    name: "cleargraphics",
    icon: Icons.deleteion,
    Dock: DockType.center,
    geoType: geometryType.undefined,
    useMapModel: undefined,
    isVisible: true,
    ToolButtonClickEvent: (
      //@ts-ignore
      sender: any,
      _graphicDataKeys: any,
      _graphicDataValue: any
    ) => {
      ElMessageBox.confirm("是否清除所有绘制图形？", "提示", {
        customClass: "ElMessageBoxStyle",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          clearAllGeo(false);
        })
        .catch(() => {
        });
    },
    ToolButtonExecuteEvent: () => {
    },
  },
];
/** 1.3 与界面ResultTable绑定，因为绑定_graphicDataValue非常吃资源 */
const _graphicDataKeys = ref<Array<string>>([]);
//1.4 画图结果列表
const _graphicDataKeysprop = computed(() => {
  let data: any = [];
  _graphicDataKeys.value.forEach((itm: string) => {
    // console.log(
    //   "=====computed itm:",
    //   _graphicDataValue,
    //   _graphicDataValue.get(itm)
    // );
    data.push(_graphicDataValue.get(itm));
  });
  return data;
});
// 1.5 存放图形列表 Map<string, Object>
const _graphicDataValue = new Map<string, Object>();

//#endregion

//#region  2.其他功能定义
const showExportGeometry = ref(false);
const showUploadPanel = ref(false);
const close = () => {
  showExportGeometry.value = false;
};
const closeUploadPanel = () => {
  showUploadPanel.value = false;
};

//#endregion

//绘制标注
const drawLabel = (point: any, id: string, label: string) => {
  if (_inOnemap.MapType.value == mapType.cesium) {
    _inOnemap.drawGraphic(
      { id: id + "_label", name: "" },
      {
        x: point.x,
        y: point.y,
        z: point.z ?? 0,
        type: "point",
        spatialReference: { wkid: 4490 },
      },
      {
        type: "text-symbol",
        color: [255, 0, 0],
        haloColor: [255, 255, 255],
        haloSize: 3,
        text: label,
        font: {
          size: 20,
          family: "Helvetica",
        },
      },
      true,
      id + "_label"
    );
  } else {
    _inOnemap.drawGraphic(
      { id: id, name: label },
      {
        x: point.x,
        y: point.y,
        type: "point",
        spatialReference: { wkid: 4490 },
      },
      {
        type: "simple-fill",
        color: [255, 99, 71, 1],
        haloColor: [255, 255, 255, 1],
        text: label,
        font: {
          size: 18,
        },
      },
      true,
      ""
    );
  }
};

const drawLineLabelPro = (entityLabels: any, id: string) => {
  if (!drawMeasure.value) return;
  if (_inOnemap.MapType.value == mapType.cesium) {
    entityLabels.forEach((label: any) => {
      const { position, areaMeasurement } = label;
      const { unit, value } = areaMeasurement;
      const text = `${value}${unit}`;
      drawLabel(position, id, text);
    });
  } else {
    entityLabels.forEach((label: any, index: number) => {
      const { position, areaMeasurement } = label;
      const { unit, value } = areaMeasurement;
      const text = `${value}${unit}`;
      drawLabel(position, id + index, text);
    });
  }
}

//绘制线沿途的点的标注
const drawLineLabel = (list: any, id: string, unit?: any) => {
  if (_inOnemap.MapType.value == mapType.cesium) {
    if (!drawMeasure.value) return;
    list.forEach((item: any, i: number) => {
      if (i > 0) {
        let line = turf.lineString(list.slice(0, i + 1));
        let length: any = turf.length(line, { units: "meters" });
        console.log("计算出来的线长度length", length);
        drawLabel(
          {
            x: item[0],
            y: item[1],
            z: item[2],
          },
          id,
          useFormatUnitPolygon(length, unit)
        );
      }
    });
  } else {
    list.forEach((item: any, i: number) => {
      if (i > 0) {
        let line = turf.lineString(list.slice(0, i + 1));
        let length = turf.length(line, { units: "meters" });
        let line2Dtext = drawMeasure.value ? useFormatUnitPolygon(length, unit) : "";
        drawLabel(
          {
            x: item[0],
            y: item[1],
            z: item[2],
          },
          id + i,
          line2Dtext
        );
      }
    });
  }
};

//三维编辑完成绘制标注
const drawEditGraphic = (data: any, tmp: any, isFinish: boolean) => {
  if (isFinish) {
    _inOnemap.DrawBrush.quitEdit();
  }
  //绘制标注
  let pt = toDegrees(data.data);
  console.log("pt", pt);
  let symbol = {
    type: "text-symbol",
    color: [255, 99, 71, 1],
    haloColor: [255, 255, 255, 1],
    text: data.id,
    font: {
      size: 24,
    },
  };
  let centerPoint: any;
  let text: any;
  switch (data.type) {
    case "point":
      let _point = {
        spatialReference: {
          wkid: 4490,
        },
        x: pt[0][0],
        y: pt[0][1],
        z: pt[0][2],
        type: "point",
      };
      text = showLabel({
        type: data.type,
        geometry: data,
        label: data.label,
      });
      drawLabel(_point, data.id, text);
      saveLabels.push({
        geo: _point,
        id: data.id,
        symbol,
        name: data.label,
      });
      centerPoint = _point;
      break;
    case "polyline":
      let _polyline = {
        paths: [pt],
        spatialReference: {
          wkid: 4490,
        },
        type: "polyline",
      };
      text = showLabel(
        {
          type: data.type,
          geometry: _polyline,
          label: data.label,
        },
        drawDistance.value
      );
      let polylineCenter: any = getGraphicCenter(_polyline);
      //绘制线名
      drawLabel(polylineCenter, data.id, text);
      centerPoint = polylineCenter;
      //绘制线沿途的点的长度
      drawLineLabel(pt, data.id, drawDistance.value);
      saveLabels.push({
        geo: {
          spatialReference: {
            wkid: 4490,
          },
          x: polylineCenter.x,
          y: polylineCenter.y,
          z: polylineCenter.z,
          type: "point",
        },
        id: data.id,
        symbol,
        name: data.label,
      });
      break;
    case "polygon":
      //返回的data没有闭合，绘制图形的rings需要第一个和最后一个position需要一样
      let list = pt.concat([pt[0]]);
      let _polygon = {
        rings: [list],
        spatialReference: {
          wkid: 4490,
        },
        type: "polygon",
      };
      let polygonCenter: any = getGraphicCenter(_polygon);
      // let polygonText = data.id + '\n面积：' + getLengthOrRect(_polygon) + '平方米'
      text = showLabel(
        {
          type: data.type,
          geometry: _polygon,
          label: data.label,
        },
        drawArea.value
      );
      drawLabel(polygonCenter, data.id, text);
      centerPoint = polygonCenter;
      saveLabels.push({
        geo: {
          spatialReference: {
            wkid: 4490,
          },
          x: polygonCenter.x,
          y: polygonCenter.y,
          z: polygonCenter.z,
          type: "point",
        },
        id: data.id,
        symbol,
        name: data.id,
      });
      break;
    case "circle":
      //2.圆心
      let circleCenter = pt[0];
      //半径
      let radius = getDistance(data.data);
      console.log("半径radius", radius);
      //面积和标注
      let circleLabel;
      if (drawName.value) {
        circleLabel = data.label;
      } else {
        circleLabel = "";
      }
      if (drawMeasure.value) {
        circleLabel +=
          "\n面积：" +
          useFormatUnitPolygon(Math.PI * Math.pow(radius, 2), drawArea.value);
      }
      drawLabel(
        {
          x: circleCenter[0],
          y: circleCenter[1],
          z: circleCenter[2],
        },
        data.id,
        circleLabel
      );
      centerPoint = {
        x: circleCenter[0],
        y: circleCenter[1],
        z: circleCenter[2],
      };
      saveLabels.push({
        geo: {
          spatialReference: {
            wkid: 4490,
          },
          x: circleCenter[0],
          y: circleCenter[1],
          z: circleCenter[2],
          type: "point",
        },
        id: data.id,
        symbol,
        name: data.label,
      });
      break;
    case "rectangle":
      let _rectangle = {
        rings: [pt],
        spatialReference: {
          wkid: 4490,
        },
        type: "polygon",
      };
      //转化为图形
      // let rectangleGeo = getGeometry("rectangle", data.data);
      let rectangleCenter: any = getGraphicCenter(_rectangle);
      let rectangleLabel = data.label + '\n面积：' + getLengthOrRect(data) + '平方米'
      // text = showLabel(
      //   {
      //     type: data.type,
      //     geometry: rectangleGeo,
      //     label: data.label,
      //   },
      //   drawArea.value
      // );
      drawLabel(rectangleCenter, data.id, rectangleLabel);
      centerPoint = rectangleCenter;
      saveLabels.push({
        geo: {
          spatialReference: {
            wkid: 4490,
          },
          x: rectangleCenter.x,
          y: rectangleCenter.y,
          z: rectangleCenter.z,
          type: "point",
        },
        id: data.id,
        symbol,
        name: data.label,
      });
      break;
    default:
      break;
  }
  _graphicDataValue.set(data.id, {
    geometry: data.data || data.geometry,
    type: data.type,
    id: data.id,
    label: data.label || data.id,
    centerPoint: centerPoint,
    icon: markRaw(tmp.icon),
  });
  console.log("_graphicDataValue", _graphicDataValue);

  console.log("centerPoint11", centerPoint);
};

let saveLabels: any = [];
// let isDrawing = false;
let currentBtn: any = "";
//当前图形的序号，此序号在当前工具关闭前不重置，持续加1
let currentGraphicIndex = 0;
// 3.绘制工具栏上的按钮点击事件，
const _toolItemDefaultEvent = (btnObject: IToolItemType) => {
  // 如果正在绘制且当前按钮类型与绘制的图形类型相同，且按钮类型不为"undefined"，则直接返回
  if (
    isDrawing.value &&
    currentBtn == btnObject.geoType &&
    btnObject.geoType !== "undefined"
  ) {
    return;
  }
  if (isDrawing && btnObject.geoType !== "undefined") {
    isDrawing.value = true;
  }

  currentBtn = btnObject.geoType;
  props.PropStore.setStorage({
    storagekey: "PositionSwitch",
    isLocalStorage: true,
    initStoragevalue: false,
    variableType: 1,
  });

  localStorage.setItem("tipswitch", `false`);
  const _key = "geo" + new Date().getTime().toString();
  const tmp: any = cloneDeep(btnObject);
  console.log("tmp", tmp);
  currentTab.value = 1;
  //如果自定义了 ToolButtonClickEvent ，就执行 否则就用内部实现
  if (tmp.ToolButtonClickEvent) {
    tmp.ToolButtonClickEvent(tmp, _graphicDataKeys.value, _graphicDataValue, {
      id: _key,
    });
  } else {
    //cesium
    if (_inOnemap.MapType.value == mapType.cesium) {
      if (tmp.label == "清除图形") {
        _inOnemap.clearAllDrawGraphic();
      }
      switch (tmp.geoType) {
        case "point":
          _inOnemap.drawHandler(DrawGeometryType.POINT, "meter", (data: any) => {
            handleCallback(data, tmp);
          });
          // _inOnemap.DrawGeometryEvent(
          //     //p1.callback
          //     (data: any) => {
          //       handleDrawCallback(data, tmp);
          //     },
          //     //p2.option
          //     { id: _key, geoType: tmp.geoType }
          // );
          break;
        case "polyline":
          _inOnemap.drawHandler("linesegment", drawDistance.value, (data: any) => {
            console.log("返回的东西是什么呢", data);
            const { labels } = data.multiDistance;
            labels.forEach((entity: Cesium.Entity) => {
              const label = entity.label;
              if (label) {
                label.show = new Cesium.ConstantProperty(false);
              }
            });
            handleCallback(data, tmp);
            // handleDrawCallback(data.result, tmp);
          })
          break;
        case "polygon":
          _inOnemap.drawHandler("polygon", drawArea.value, (data: any) => {
            console.log("返回的东西是什么呢", data);
            // 隐藏绘制中的图形标注
            const geometry = data?.geometry;
            if (geometry && geometry.label) {
              geometry.label.show = false;
            }
            handleCallback(data, tmp);
          })
          break;
        case "circle":
          _inOnemap.drawHandler("circle", drawArea.value, (data: any) => {
            console.log("返回的东西是什么呢", data);
            // 隐藏绘制中的图形标注
            const geometry = data?.geometry;
            if (geometry && geometry) {
              geometry.label.show = false;
            }
            handleCallback(data, tmp);
          })
          break;
        case "rectangle":
          _inOnemap.drawHandler("rectangle", drawArea.value, (data: any) => {
            console.log("返回的东西是什么呢", data);
            console.log("看看地图对象", _inOnemap.MapViewer.entities.values);
            // 隐藏绘制中的图形标注
            const geometry = data?.geometry;
            if (geometry && geometry.label) {
              geometry.label.show = false;
            }
            handleCallback(data, tmp);
          })
          break;
      }
    } else {
      //arcgis
      if (tmp.label == "清除图形") {
        _inOnemap.clearAllDrawGraphic();
      }
      switch (tmp.geoType) {
        case "point":
          _inOnemap.drawHandler("point", "meter", (data: any) => {
            handleDrawCallback(data, tmp)
          },
            { id: _key, geoType: tmp.geoType }
          );
          break
        case "polyline":
          _inOnemap.drawHandler("polyline", drawDistance.value, (data: any) => {
            handleDrawCallback(data, tmp)
          },
            { id: _key, geoType: tmp.geoType }
          );
          break;
        case "polygon":
          _inOnemap.drawHandler("polygon", drawArea.value, (data: any) => {
            handleDrawCallback(data, tmp)
          },
            { id: _key, geoType: tmp.geoType }
          );
          break;
        case "circle":
          _inOnemap.drawHandler(
            "circle",
            drawArea.value,
            (data: any) => {
              console.log("返回的东西是什么呢", data);
              handleDrawCallback(data, tmp)
            },
            { id: _key, geoType: tmp.geoType }
          )
          break;
        case "rectangle":
          _inOnemap.drawHandler(
            "rectangle",
            drawArea.value,
            (data: any) => {
              handleDrawCallback(data, tmp)
            },
            { id: _key, geoType: tmp.geoType }
          )
          break;
      }
    }
  }
};

/**
 * 三维绘制回调
 * @param data
 * @param tmp
 * @param isFinish
 */
const handleCallback = (data: any, tmp: any, isFinish: boolean = true) => {
  if (isFinish) {
    _inOnemap.DrawBrush.quitEdit();
  }

  const { result } = data;
  // const area = data.multiDistance.result
  result.label = "图形" + ++currentGraphicIndex;
  if (!_graphicDataKeys.value.includes(result.id)) {
    _graphicDataKeys.value.push(result.id);
  }

  const symbol = {
    type: "text-symbol",
    color: [255, 99, 71, 1],
    haloColor: [255, 255, 255, 1],
    text: data.id,
    font: {
      size: 24,
    },
  }
  let centerPoint: any = null;
  let layerLabel: string = '';

  const { areaMeasurement, label, id } = result;
  switch (result.type) {
    case DrawGeometryType.POINT:
      const pointCartesian = result.data[0];
      centerPoint = toDegree(pointCartesian);
      layerLabel = result.label;
      break
    case DrawGeometryType.POLYLINE:
      centerPoint = getPolylineCenter(result.data, true);
      for (const label of result.entityLabels) {
        const { areaMeasurement } = label;
        label.position = toDegree(label.position);
        // drawLabel(label.position, label.id, `${areaMeasurement.value} ${areaMeasurement.unit}`);
        drawLabel(label.position, id, `${areaMeasurement.value} ${areaMeasurement.unit}`);
      }
      layerLabel = result.label;
      break
    case DrawGeometryType.POLYGON:
      centerPoint = getPolylineCenter(result.data, true);
      layerLabel = `${label}\n面积: ${areaMeasurement?.value}${areaMeasurement?.unit}`;
      break
    case DrawGeometryType.CIRCLE:
      centerPoint = toDegree(result.data[0]);
      layerLabel = `${label}\n面积: ${areaMeasurement?.value}${areaMeasurement?.unit}`;
      break
    case DrawGeometryType.RECTANGLE:
      centerPoint = getPolylineCenter(result.data, true);
      layerLabel = `${label}\n面积: ${areaMeasurement?.value}${areaMeasurement?.unit}`;
      break
  }

  drawLabel(centerPoint, result.id, layerLabel);
  saveGraphicAndLabelData({ ...result, layerLabel, positionType: "cesium" }, centerPoint, symbol, tmp);
}

const handleDrawCallback = (data: any, tmp: any) => {
  if (data?.type == "point") {
    if (data?.data) data["data"] = [data["data"]];
    // if (data?.geometry) data["geometry"] = [data["geometry"]];
  }
  console.log("结果返回data", data);

  data.label = "图形" + ++currentGraphicIndex;
  if (!_graphicDataKeys.value.includes(data.id)) {
    _graphicDataKeys.value.push(data.id);
  }
  let centerPoint: any;
  let centerP: any = getGraphicCenter(data.geometry);
  // let showText = showLabel(data);
  let showText = showLabelRewrite({ ...data?.result, label: data?.label });
  centerPoint = centerP;
  if (data.type == "polyline") {
    // drawLineLabel(data.geometry.paths[0], data.id, drawDistance.value);
    drawLineLabelPro(data?.result?.entityLabels, data.id);
  }
  const symbol = {
    type: "simple-fill",
    color: [255, 99, 71, 1],
    haloColor: [255, 255, 255, 1],
    font: {
      size: 18,
    },
  }
  //绘制名称
  _inOnemap.drawGraphic(
    { id: data.id, name: showText.split("\n")[0] },
    {
      x: centerP.x,
      y: centerP.y,
      type: "point",
      spatialReference: { wkid: 4490 },
    },
    symbol,
    true,
    ""
  );
  //绘制单位
  _inOnemap.drawGraphic(
    { id: data.id + "_unit", name: showText.split("\n")[1] },
    {
      x: centerP.x,
      y: centerP.y,
      type: "point",
      spatialReference: { wkid: 4490 },
    },
    {
      type: "text",
      color: [255, 99, 71, 1],
      haloColor: [255, 255, 255, 1],
      offset: true,
      yoffset: -24,
      xoffset: 0,
      font: {
        size: 18,
      },
    },
    true,
    ""
  );

  saveGraphicAndLabelData({ ...data.result, layerLabel: showText, label: data.label, positionType: "arcgis" }, centerPoint, symbol, tmp);
}

const saveGraphicData = (data: any, tmp: any, centerPoint: any) => {
  _graphicDataValue.set(data.id, {
    geometry: data.data || data.geometry,

    type: data.type,
    id: data.id,
    label: data.label || data.id,
    centerPoint: centerPoint,
    layerLabel: data.layerLabel,
    entityLabels: data.entityLabels || [],
    areaMeasurement: data.areaMeasurement || {},
    positionType: data.positionType,
    icon: markRaw(tmp.icon),
  });
  console.log("_graphicDataValue", _graphicDataValue);
}

/**
 * 保存图形信息
 * @param data 图形信息
 * @param centerPoint 中心点
 * @param symbol 符号
 * @param tmp 临时变量
 */
const saveGraphicAndLabelData = (data: any, centerPoint: any, symbol: any, tmp: any) => {
  saveGraphicData(data, tmp, centerPoint);

  saveLabels.push({
    geo: {
      spatialReference: {
        wkid: 4490,
      },
      x: centerPoint.x,
      y: centerPoint.y,
      z: centerPoint.z,
      type: "point",
    },
    id: data.id,
    symbol,
    name: data.label,
  });

  setTimeout(() => {
    props.PropStore.setStorage({
      storagekey: "PositionSwitch",
      isLocalStorage: true,
      initStoragevalue: true,
      variableType: 1,
    });
    localStorage.setItem("tipswitch", `true`);
  }, 500);
  if (isDrawing) {
    isDrawing.value = false;
    localStorage.removeItem("geoType");
  }
}

//#region 4. 地图切换后要重绘列表的图元对象
watch(
  () => _inOnemap.isMapReady.value,
  (newval) => {
    if (newval) {
      if (_inOnemap.MapType.value == mapType.cesium) {
        czmRedrawGeometry();
      } else {
        arcRedrawGeometry();
      }
    }
  }
);

//清屏
// watch(
// 	() => props.PropStore.ClearScreenTimes.value,
// 	(val) => {
//     console.log('点击清屏val',val);
//     clearAllGeo(true)
// 	},
// 	{ deep: true }
// )

//#region 4.1.Cesium Map 里重绘

const toCartographic = (
  itm: any,
  key: any,
  coord: any,
  callback: any,
  isRemoveEnd: boolean = false
) => {
  //1.将经纬度转为地理坐标
  const lnth = isRemoveEnd ? coord.length - 1 : coord.length;
  let idx = 0;
  let _Cartographics: any = [];

  coord.forEach((subitm: any /* subitm 为经纬度 */) => {
    if (idx < lnth) {
      _Cartographics.push(
        Cesium.Cartographic.fromDegrees(subitm[0], subitm[1])
      );
      idx = idx + 1;
    }
  });
  console.log("coord", coord);
  console.log("_Cartographics", _Cartographics);
  console.log(_inOnemap.MapViewer.terrainProvider);
  //2.获取高程
  // Cesium.sampleTerrainMostDetailed(
  //     _inOnemap.MapViewer.terrainProvider,
  //     _Cartographics
  // ).then((data) => {
  //   //转为Cartesian3
  //   let Cartesian3s: any = [];
  //   data.forEach((itm) => {
  //     Cartesian3s.push(itm.longitude);
  //     Cartesian3s.push(itm.latitude);
  //     Cartesian3s.push(itm.height);
  //   });
  //   itm.geometry.data = Cesium.Cartesian3.fromRadiansArrayHeights(Cartesian3s);
  //   console.log("返回 的itm.geometry", itm.geometry);
  //
  //   // callback(itm.geometry.data);
  //   //3.绘制
  //   czmDrawByPositions(itm.type, itm.geometry.data, { id: key }, callback);
  // });

  let Cartesian3s: any = [];
  _Cartographics.forEach((cartographic: any) => {
    Cartesian3s.push(cartographic.longitude);
    Cartesian3s.push(cartographic.latitude);
    Cartesian3s.push(0);
  });

  itm.geometry.data = Cesium.Cartesian3.fromRadiansArrayHeights(Cartesian3s);
  // callback(itm.geometry.data);
  // czmDrawByPositions(itm.type, itm.geometry.data, { id: key });
  // if (['circle', 'rectangle'].includes(itm.type)) {
  //   czmDrawByPositions(itm.type, itm.geometry.data, { id: key }, callback);
  // } else {
  //   czmDrawByPositions(itm.type, coord, { id: key }, callback);
  // }
  czmDrawByPositions(itm.type, itm.geometry.data, coord, { id: key }, callback);
};

const czmDrawByPositions = (type: string, positions: any, coord: any, attributes: any, callback?: Function) => {
  if (!_inOnemap?.DrawWidget) {
    return;
  }
  const { _drawHandler } = _inOnemap?.DrawWidget;
  switch (type) {
    case DrawGeometryType.POINT:
      _drawHandler?.useCreatePoint(attributes?.id, positions[0]);
      if (callback) {
        callback(positions[0]);
      }
      break
    case DrawGeometryType.POLYLINE:
      // _drawHandler?.useCreateLine(attributes?.id, {
      //   value: positions
      // });

      // positions.push(positions[0]);
      // let _geoLine = {
      //   paths: [positions],
      //   spatialReference: { wkid: 4490 },
      //   type: "polyline",
      // };
      // _inOnemap.drawGraphic(
      //     { id: attributes?.id, name: "" },
      //     _geoLine,
      //     {
      //       type: "simple-line",
      //       color: [125, 125, 125, 0.2],
      //       costomColor: [66, 147, 244, 1],
      //       width: 4,
      //     },
      //     false,
      //     drawGraphicId
      // );
      _inOnemap.DrawBrush.drawByPositions(
        type,
        positions,
        { id: attributes?.id },
      );
      if (callback) {
        callback(positions);
      }
      break
    case DrawGeometryType.POLYGON:
      // _drawHandler?.useCreatePolygon(attributes?.id, {
      //   value: positions
      // });
      const temp = ensureClosed(coord); //图形要闭合

      let _geo = {
        paths: [temp],
        spatialReference: { wkid: 4490 },
        type: "polygon",
      };
      _inOnemap.drawGraphic(
        { id: attributes?.id, name: "" },
        _geo,
        {
          type: "simple-line",
          color: [125, 125, 125, 0.2],
          costomColor: [66, 147, 244, 1],
          width: 4,
        },
        true,
        drawGraphicId
      );
      // _inOnemap.DrawBrush.drawByPositions(
      //     type,
      //     positions,
      //     { id: attributes?.id },
      // );
      if (callback) {
        callback(_geo);
      }
      break
    case DrawGeometryType.CIRCLE:
      const centerCartesian = positions[0];
      const radiusPoint = positions[1];
      const radius = _drawHandler?.useMeasureHorizontalDistance(centerCartesian, radiusPoint);
      _drawHandler?.useCreateCircle(attributes?.id, centerCartesian, radius);

      if (callback) {
        callback(positions);
      }
      break
    case DrawGeometryType.RECTANGLE:
      const startPoint = positions[0];
      const endPoint = positions[1];
      _drawHandler?.useCreateRectangle(attributes?.id, startPoint, endPoint);
      if (callback) {
        callback(positions);
      }
      break
  }
}
const czmRedrawGeometry = () => {
  _graphicDataValue.forEach((itm: any, key) => {
    console.log("切换三维==itm,key", itm, key);
    let geometry: any;
    //二维切三维
    if (itm.geometry.paths || itm.geometry.x || itm.geometry.rings) {
      geometry = itm.geometry;
    } else {
      //三维切三维
      let pt = toDegrees(itm.geometry);
      if (itm.type == "point") {
        geometry = {
          spatialReference: {
            wkid: 4490,
          },
          x: pt[0][0],
          y: pt[0][1],
          z: pt[0][2],
          type: "point",
        };
      } else if (itm.type == "polyline") {
        geometry = {
          spatialReference: {
            wkid: 4490,
          },
          paths: [pt],
          type: "polyline",
        };
      } else {
        geometry = getGeometry(itm.type, itm.geometry);
      }
      // itm.geometry = geometry
    }
    console.log("geometry", geometry);
    //判断单位是绘制的单位还是收藏的单位
    // let unit: any;
    // let lengthUnit: any;
    // unit = drawArea.value;
    // lengthUnit = drawDistance.value;

    // if (treeRef.value) {
    //   let checkKeys = treeRef.value.getCheckedKeys();
    //   if (checkKeys.length && checkKeys.includes(itm.id)) {
    //     unit = rectUnitShare.value;
    //     lengthUnit = shareDistance.value;
    //   } else {
    //     unit = drawArea.value;
    //     lengthUnit = drawDistance.value;
    //   }
    // }

    // let text = showLabel(
    //     {
    //       type: itm.type,
    //       geometry: geometry,
    //       label: itm.label,
    //     },
    //     unit
    // );
    let text = itm.layerLabel;
    switch (itm.type) {
      case "point": {
        toCartographic(
          itm,
          key,
          [[geometry.x, geometry.y, geometry?.z]],
          (data: any) => {
            _graphicDataValue.set(key, {
              geometry: data,
              id: itm.id,
              label: itm.label,
              type: itm.type,
              icon: markRaw(itm.icon),
              layerLabel: itm.layerLabel,
              centerPoint: geometry,
            });
          }
        );
        if (drawName.value) {
          drawLabel(itm.centerPoint, itm.id, itm.label);
        } else {
          drawLabel(itm.centerPoint, itm.id, "");
        }
      }
        break;
      case "polyline": {
        toCartographic(itm, key, geometry.paths[0], (data: any) => {
          _graphicDataValue.set(key, {
            geometry: data,
            id: itm.id,
            label: itm.label,
            type: itm.type,
            icon: markRaw(itm.icon),
            layerLabel: itm.layerLabel,
            entityLabels: itm.entityLabels || [],
            centerPoint: getGraphicCenter(geometry),
          });
        });
        if (drawMeasure.value) {
          drawLineLabelPro(itm.entityLabels, itm.id);
        }
        drawLabel(itm.centerPoint, itm.id, text);
        break;
      }
      case "polygon": {
        toCartographic(
          itm,
          key,
          geometry.rings[0],
          (data: any) => {
            _graphicDataValue.set(key, {
              geometry: data,
              id: itm.id,
              label: itm.label,
              type: itm.type,
              icon: markRaw(itm.icon),
              layerLabel: itm.layerLabel,
              areaMeasurement: itm.areaMeasurement || {},
              centerPoint: getGraphicCenter(geometry),
            });
          },
          true
        );
        drawLabel(itm.centerPoint, itm.id, text);
        break;
      }
      case "rectangle": {
        toCartographic(
          itm,
          key,
          [geometry.rings[0][0], geometry.rings[0][2]],
          (data: any) => {
            _graphicDataValue.set(key, {
              geometry: data,
              id: itm.id,
              label: itm.label,
              type: itm.type,
              icon: markRaw(itm.icon),
              layerLabel: itm.layerLabel,
              areaMeasurement: itm.areaMeasurement || {},
              centerPoint: getGraphicCenter(geometry),
            });
          }
        );
        drawLabel(itm.centerPoint, itm.id, text);
        break;
      }
      case "circle": {
        toCartographic(
          itm,
          key,
          [[geometry.center[0], geometry.center[1]], geometry.rings[0][0]],
          (data: any) => {
            _graphicDataValue.set(key, {
              geometry: data,
              id: itm.id,
              label: itm.label,
              type: itm.type,
              icon: markRaw(itm.icon),
              layerLabel: itm.layerLabel,
              areaMeasurement: itm.areaMeasurement || {},
              centerPoint: getGraphicCenter(geometry),
            });

            // saveGraphicData(null, null, null);
          }
        );
        drawLabel(itm.centerPoint, itm.id, text);
        break;
      }
    }
  });
};
//#endregion

//#region 4.2.Arcgis Map 里重绘

const arcRedrawGeometry = () => {
  _graphicDataValue.forEach((itm: any, key) => {
    let text: any;
    let areaunit: any;
    // let lengthunit: any;
    // areaunit = drawArea.value;
    // lengthunit = drawDistance.value;
    //判断单位是绘制的单位还是收藏的单位
    // if (treeRef.value) {
    //   let checkKeys = treeRef.value.getCheckedKeys();
    //   if (checkKeys.length && checkKeys.includes(itm.id)) {
    //     areaunit = rectUnitShare.value;
    //     lengthunit = shareDistance.value;
    //   } else {
    //     areaunit = drawArea.value;
    //     lengthunit = drawDistance.value;
    //   }
    // }
    switch (itm.type) {
      case "point": {
        let xyz: any = toDegree(itm.geometry);
        itm.geometry.x = xyz.x;
        itm.geometry.y = xyz.y;
        let pt = new _inOnemap.EsriObj.Point(
          itm.geometry.x,
          itm.geometry.y,
          new _inOnemap.EsriObj.SpatialReference({
            wkid: 4490,
          })
        );
        let _geo = {
          wkid: pt.spatialReference.wkid,
          spatialReference: pt.spatialReference,
          x: pt.x,
          y: pt.y,
          type: "point",
        };
        let text = itm.layerLabel;
        _graphicDataValue.set(key, {
          geometry: _geo,
          id: itm.id,
          centerPoint: itm.centerPoint || _geo,
          label: itm.label,
          type: itm.type,
          layerLabel: itm.layerLabel,
          areaMeasurement: itm.areaMeasurement,
          icon: markRaw(itm.icon),
        });
        _inOnemap.drawGraphic(
          { id: itm.id, name: text },
          _geo,
          Icons.geoSymbol.point,
          true,
          key
        );
      }
        break;
      case "polyline": {
        // itm.geometry["paths"] = [toDegrees(itm.geometry)];
        itm.geometry["paths"] = [toDegrees(itm.geometry)];

        let pl = new _inOnemap.EsriObj.Polyline({
          paths: itm.geometry["paths"],
          spatialReference: new _inOnemap.EsriObj.SpatialReference({
            wkid: 4490,
          }),
        });
        let _geo = {
          paths: pl.paths,
          spatialReference: pl.spatialReference,
          type: itm.type,
        };
        // text = showLabel({
        //   type: itm.type,
        //   geometry: _geo,
        //   label: itm.label,
        // });
        let text = itm.layerLabel;
        _graphicDataValue.set(itm.id, {
          geometry: _geo,
          id: itm.id,
          label: itm.label,
          centerPoint: pl.getExtent().getCenter(),
          layerLabel: itm.layerLabel,
          entityLabels: itm.entityLabels || [],
          type: itm.type,
          icon: markRaw(itm.icon),
        });

        _inOnemap.drawGraphic(
          { id: itm.id, name: text },
          _geo,
          Icons.geoSymbol.polyline,
          true,
          itm.id
        );
        //绘制线沿途的标注
        let { entityLabels } = itm;

        drawLineLabelPro(entityLabels, itm.id);
      }
        break;
      case "polygon": {
        // const tmp = toDegrees(itm.geometry);
        let positionRings: any;
        if (itm.positionType == "cesium") {
          positionRings = [ensureClosed(toDegrees(itm.geometry))];
        } else {
          positionRings = itm.geometry["paths"];
        }

        let pg = new _inOnemap.EsriObj.Polygon({
          rings: positionRings,
          spatialReference: new _inOnemap.EsriObj.SpatialReference({
            wkid: 4490,
          }),
        });
        let _geo = {
          rings: pg.rings,
          spatialReference: pg.spatialReference,
          type: itm.type,
        };
        console.log("_geo", _geo);

        text = showLabelRewrite(itm, areaunit);
        const graphicDataValue = {
          geometry: _geo,
          id: itm.id,
          label: itm.label,
          centerPoint: itm.centerPoint || pg.getExtent().getCenter(),
          type: itm.type,
          layerLabel: itm.layerLabel,
          areaMeasurement: itm.areaMeasurement,
          icon: markRaw(itm.icon)
        }
        _graphicDataValue.set(key, graphicDataValue);
        // xxxxxx
        //绘制名称
        _inOnemap.drawGraphic(
          {
            id: key,
            name: text.split("\n")[0],
          },
          _geo,
          Icons.geoSymbol.polygon,
          true,
          key
        );
        //绘制面积
        _inOnemap.drawGraphic(
          { id: key + "_unit", name: text.split("\n")[1] },
          _geo,
          {
            type: "text",
            costomColor: [255, 0, 0, 1],
            color: [255, 0, 0, 1],
            offset: true,
            yoffset: -24,
            xoffset: 0,
            width: 2,
          },
          true,
          key
        );
      }
        break;
      case "rectangle": {
        let tmp = toDegrees(itm.geometry);
        let xy1 = [tmp[0][0], tmp[0][1], tmp[0][2]];
        let xy2 = [tmp[1][0], tmp[0][1], tmp[0][2]];
        let xy3 = [tmp[1][0], tmp[1][1], tmp[1][2]];
        let xy4 = [tmp[0][0], tmp[1][1], tmp[1][2]];
        let xy5 = [tmp[0][0], tmp[0][1], tmp[0][2]];
        itm.geometry["rings"] = [[xy1, xy2, xy3, xy4, xy5]];

        let rc = new _inOnemap.EsriObj.Polygon({
          rings: itm.geometry["rings"],
          spatialReference: new _inOnemap.EsriObj.SpatialReference({
            wkid: 4490,
          }),
        });
        let _geo = {
          rings: rc.rings,
          spatialReference: rc.spatialReference,
          type: itm.type,
        };
        // text = showLabel(
        //     {
        //       type: itm.type,
        //       geometry: _geo,
        //       label: itm.label,
        //     },
        //     areaunit
        // );
        text = showLabelRewrite(itm, areaunit);
        _graphicDataValue.set(key, {
          geometry: _geo,
          id: itm.id,
          label: itm.label,
          centerPoint: itm.centerPoint || rc.getExtent().getCenter(),
          type: itm.type,
          layerLabel: itm.layerLabel,
          areaMeasurement: itm.areaMeasurement,
          icon: markRaw(itm.icon),
        });
        _inOnemap.drawGraphic(
          { id: key, name: text.split("\n")[0] },
          _geo,
          Icons.geoSymbol.polygon,
          true,
          key
        );
        _inOnemap.drawGraphic(
          { id: key + "_unit", name: text.split("\n")[1] },
          _geo,
          {
            type: "text",
            costomColor: [255, 0, 0, 1],
            color: [255, 0, 0, 1],
            offset: true,
            yoffset: -24,
            xoffset: 0,
            width: 2,
          },
          true,
          key
        );
      }
        break;
      case "circle": {
        //1.计算半径
        itm.geometry["radius"] = Cesium.Cartesian3.distance(
          new Cesium.Cartesian3(itm.geometry[0].x, itm.geometry[0].y, 0),
          new Cesium.Cartesian3(itm.geometry[1].x, itm.geometry[1].y, 0)
        );
        //2.圆心
        itm.geometry["center"] = toDegrees(itm.geometry)[0];
        //3.创建圆
        var circle = new _inOnemap.EsriObj.Circle({
          center: [itm.geometry["center"][0], itm.geometry["center"][1]],
          radius: itm.geometry["radius"],
        });
        var graphic = new _inOnemap.EsriObj.Graphic(
          circle,
          Icons.geoSymbol.polygon
        );
        itm.geometry["rings"] = graphic.geometry.rings;
        let _geo = {
          center: itm.geometry["center"],
          rings: graphic.geometry.rings,
          spatialReference: new _inOnemap.EsriObj.SpatialReference({
            wkid: 4490,
          }),
          type: itm.type,
        };
        console.log("要存的_geo", _geo);

        // text = showLabel(
        //     {
        //       type: itm.type,
        //       geometry: _geo,
        //       label: itm.label,
        //     },
        //     areaunit
        // );
        text = showLabelRewrite(itm, areaunit);
        _graphicDataValue.set(key, {
          geometry: _geo,
          id: itm.id,
          label: itm.label,
          centerPoint: itm.centerPoint || {
            x: itm.geometry["center"][0],
            y: itm.geometry["center"][1],
          },
          type: itm.type,
          layerLabel: itm.layerLabel,
          areaMeasurement: itm.areaMeasurement,
          icon: markRaw(itm.icon),
        });
        _inOnemap.drawGraphic(
          { id: key, name: text.split("\n")[0] },
          _geo,
          Icons.geoSymbol.polygon,
          true,
          key
        );
        _inOnemap.drawGraphic(
          { id: key + "_unit", name: text.split("\n")[1] },
          _geo,
          {
            type: "text",
            costomColor: [255, 0, 0, 1],
            color: [255, 0, 0, 1],
            offset: true,
            yoffset: -24,
            xoffset: 0,
            width: 2,
          },
          true,
          key
        );
      }
        break;
    }
  });
};

//#endregion
//#endregion

//#region 5 画图结果列表行的操作实现，_ButtonItems  @click="DrawToolListItemEvent(scope, item)"

/** 5.1 列表行操作事件 */
const DrawToolListItemEvent = (RowData: any, toolitem: any) => {
  if (toolitem.funClickEvent) {
    toolitem.funClickEvent(toolitem, {
      Row: RowData.row,
      graphicDataKeys: _graphicDataKeys.value,
      graphicDataValue: _graphicDataValue,
    });
  } else {
    // _inOnemap.DrawToolListItemEvent(_graphicDataKeys.value, _graphicDataValue, {
    //   row: RowData.row.id,
    //   toolLabel: toolitem.label,
    //   toolname: toolitem.name,
    // });
  }
};
// 5.2  导出坐标
let _exportCoord: any = ref();
const exportCoord = computed(() => {
  return _exportCoord.value;
});
/** 5.3 自定义行操作事件 */
const DrawToolListCustomItemEvent = (RowData: any, toolitem: any) => {
  if (toolitem.funClickEvent) {
    toolitem.funClickEvent(toolitem, {
      Row: RowData.row,
      graphicDataKeys: _graphicDataKeys.value,
      graphicDataValue: _graphicDataValue,
    });
  } else {
    ElMessage.warning("该功能未实现");
  }
};
//计算半径
const getDistance = (itm: any) => {
  //计算两点之间的水平距离
  let cartesian3x = new Cesium.Cartesian3(itm[0].x, itm[0].y, itm[0].z);
  let cartesian3y = new Cesium.Cartesian3(itm[1].x, itm[1].y, itm[1].z);
  let distance1 = Cesium.Cartesian3.distance(cartesian3x, cartesian3y);
  // 计算两个笛卡尔坐标的高度差
  let cartographic1 = Cesium.Cartographic.fromCartesian(cartesian3x);
  let alt1 = cartographic1.height; // 高度
  let cartographic2 = Cesium.Cartographic.fromCartesian(cartesian3y);
  let alt2 = cartographic2.height; // 高度
  let height = Math.abs(alt1 - alt2);
  let horizontalDistance = Math.sqrt(distance1 ** 2 - height ** 2);
  return horizontalDistance;
};

//三维圆转化为图形
const changeToPosition = (center: any, radius: number) => {
  const ellipseGeometry = new Cesium.EllipseOutlineGeometry({
    center: center,
    semiMajorAxis: radius,
    semiMinorAxis: radius,
    height: 0,
  }) as any;

  const options = {
    center: ellipseGeometry._center,
    semiMajorAxis: ellipseGeometry._semiMajorAxis,
    semiMinorAxis: ellipseGeometry._semiMinorAxis,
    ellipsoid: ellipseGeometry._ellipsoid,
    rotation: ellipseGeometry._rotation,
    height: ellipseGeometry._height,
    granularity: ellipseGeometry._granularity,
    numberOfVerticalLines: ellipseGeometry._numberOfVerticalLines,
  };

  let cep = (Cesium as any).EllipseGeometryLibrary.computeEllipsePositions(
    options,
    false,
    true
  ).outerPositions;

  cep = (Cesium as any).EllipseGeometryLibrary.raisePositionsToHeight(
    cep,
    options,
    false
  );
  const outLinePositions = [];
  for (let i = 0; i < cep.length; i += 3) {
    let cartographic = Cesium.Cartographic.fromCartesian(
      new Cesium.Cartesian3(cep[i], cep[i + 1], cep[i + 2])
    );
    let x = (180 / Math.PI) * cartographic.longitude;
    let y = (180 / Math.PI) * cartographic.latitude;
    outLinePositions.push([x, y]);
  }
  return outLinePositions;
};

const getGeometry = (type: string, geo: any) => {
  let _geo: any;
  switch (type) {
    case "point":
      let xyz: any = toDegrees(geo)[0];
      console.log("xyz", xyz);
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490,
        },
        x: xyz[0],
        y: xyz[1],
        z: xyz[2],
        type: "point",
      };
      break;
    case "polyline":
      let list: any = toDegrees(geo);
      console.log("list", list);
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490,
        },
        paths: [list],
        type: "polyline",
      };
      break;
    case "rectangle":
      let tmp: any = toDegrees(geo);
      let xy1 = [tmp[0][0], tmp[0][1], tmp[0][2]];
      let xy2 = [tmp[1][0], tmp[0][1], tmp[0][2]];
      let xy3 = [tmp[1][0], tmp[1][1], tmp[1][2]];
      let xy4 = [tmp[0][0], tmp[1][1], tmp[1][2]];
      let xy5 = [tmp[0][0], tmp[0][1], tmp[0][2]];
      let rectangleRings = [[xy1, xy2, xy3, xy4, xy5]];
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490,
        },
        rings: rectangleRings,
        type: "rectangle",
      };
      break;
    case "polygon":
      let polygonRings: any = toDegrees(geo);
      console.log("polygonRings", polygonRings);
      //多边形要闭合
      polygonRings.push(polygonRings[0]);
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490,
        },
        rings: [polygonRings],
        type: "polygon",
      };
      break;
    case "circle":
      let circlelist: any = toDegrees(geo);
      //2.圆心
      let center = Cesium.Cartesian3.fromDegrees(
        circlelist[0][0],
        circlelist[0][1],
        0
      );
      //半径
      let radius = getDistance(geo);
      console.log("计算半径geo", geo);

      let circleRings = changeToPosition(center, radius);
      console.log("circleRings", circleRings);
      //闭合rings
      circleRings.push(circleRings[0]);
      _geo = {
        wkid: 4490,
        spatialReference: {
          wkid: 4490,
        },
        rings: [circleRings],
        type: "circle",
        center: [circlelist[0][0], circlelist[0][1]],
      };
      break;
    default:
      break;
  }
  return _geo;
};
const currentId = ref();

/** 5.3  图元列表的操作按钮:定位 编辑 确定 删除 */
const _ButtonItems = ref([
  /** 1.定位 */
  {
    label: "定位",
    name: "location",
    //@ts-ignore
    funClickEvent: (sender: any, options: any) => {
      const data = options.graphicDataValue.get(options.Row.id);
      let cartesianPoints: any = [];
      if (_inOnemap.MapType.value == "cesium") {
        if (["polygon", "polyline"].includes(data.type)) {
          cartesianPoints = data.geometry;
        } else {
          cartesianPoints = data.geometry.data || data.geometry;
        }
        let transGeo = Utils.transCesiumGeoToDegrees(cartesianPoints, data.type) as any;
        console.log("transGeo", transGeo);
        if (transGeo.type == "circle" || transGeo.type == "rectangle") {
          transGeo.type = "polygon";
          _inOnemap.zoomByGeometry([transGeo]);
        } else {
          _inOnemap.zoomByGeometry([transGeo]);
        }
      } else {
        if (data.geometry && data.geometry.type) {
          let geo: any;
          if (["circle", "rectangle"].includes(data.geometry.type)) {
            geo = {
              rings: data.geometry.rings,
              type: "polygon",
              spatialReference: data.geometry.spatialReference,
            };
          } else {
            geo = data.geometry;
          }
          _inOnemap.zoomByGeometry([geo]);
        } else if (data.geometry && !data.geometry.type) {
          let geo: any;
          if (data.geometry.x) {
            geo = {
              type: "point",
              spatialReference: {
                wkid: 4490,
              },
              x: data.geometry.x,
              y: data.geometry.y,
            };
          } else if (data.geometry.paths) {
            geo = {
              type: "polyline",
              spatialReference: {
                wkid: 4490,
              },
              paths: data.geometry.paths,
            };
          } else if (data.geometry.rings) {
            geo = {
              type: "polygon",
              spatialReference: {
                wkid: 4490,
              },
              rings: data.geometry.rings,
            };
          }
          if (geo) {
            _inOnemap.zoomByGeometry([geo], 1.8);
          }
        }
      }
    },
    isDefault: true,
    isWarn: false,
    isShow: true,
  },
  /** 3.下载坐标 */
  {
    label: "下载坐标",
    name: "downCoord",
    isWarn: false,
    isDefault: true,
    isShow: true,
    funClickEvent: (sender: any, options: any) => {
      //判断如果是在cesium下绘制的图形
      if (!options.Row.geometry.hasOwnProperty("type")) {
        options.Row.geometry = getGeometry(
          options.Row.type,
          options.Row.geometry
        );
      }
      _exportCoord.value = {
        sender: sender,
        options: options,
      };
      showExportGeometry.value = true;
      // console.log("=====下载坐标", key, value, options);
    },
  },
  /** 4.空间分析 */
  {
    label: "空间分析",
    name: "spatialanalysis",
    isWarn: false,
    isDefault: false,
    isShow: false,
  },
  /** 5.编辑 */
  {
    label: "编辑",
    name: "edit",
    //@ts-ignore
    funClickEvent: (sender: any, options: any) => {
      console.log("_graphicDataKeysprop.value", _graphicDataKeysprop.value);
      console.log("options", options);
      currentId.value = options.Row.id;
      if (_inOnemap.MapType.value == "cesium") {
        _inOnemap.DrawBrush.reEdit({
          id: options.Row.id,
          callback: (data: any) => {
            console.log("编辑完成的data", data);

            if (data?.data) data["data"] = cloneDeep(data).data.positions;
            //删除标注
            const coll = _inOnemap.getGraphicsLayerByLayerId(`${data.id}_label`);
            if (coll) {
              coll.primitives.removeAll();
            }
            // let icon = cloneDeep(options.Row.icon)
            drawEditGraphic(data, options.Row, false);
            _graphicDataKeysprop.value.forEach((item: any) => {
              if (item.id == data.id) {
                item.geometry = data.data;
              }
            });
          },
        });
        nextTick(() => {
          for (const item of _ButtonItems.value) {
            if (item.name == "endedit") {
              item.isDefault = true;
            }
            if (item.name == "delete") {
              item.isDefault = false;
            }
          }
        });
      } else {
      }
    },
    isWarn: true,
    isDefault: true,
    isShow: false,
  },
  /** 6.确定 */
  {
    label: "确定",
    name: "endedit",
    //@ts-ignore
    funClickEvent: (sender: any, options: any) => {
      if (_inOnemap.MapType.value == "cesium") {
        _inOnemap.DrawBrush.quitEdit();
      } else {
      }
      currentId.value = "";
      for (const item of _ButtonItems.value) {
        if (item.name == "endedit") {
          item.isDefault = false;
        }
        if (item.name == "delete") {
          item.isDefault = true;
        }
      }
    },
    isWarn: true,
    isDefault: false,
    isShow: false,
  },
  /** 7.删除 */
  {
    label: "删除",
    name: "delete",
    //@ts-ignore
    funClickEvent: (sender: any, options: any) => {
      ElMessageBox.confirm("是否删除该图形？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        console.log("options", options);

        if (_inOnemap.MapType.value == "cesium") {
          _inOnemap.DrawBrush.quitEdit();
          setTimeout(() => {
            if (options.Row.isUpload) {
              // 三维模式下清除通过上传坐标组件绘制的图形
              const primitives = _inOnemap.MapViewer.scene.primitives;
              const primitivesLength = primitives.length;
              for (let i = primitivesLength - 1; i >= 0; i--) {
                const primitive = primitives.get(i);
                if (
                  primitive.id === "GlobePrimitiveCollection" ||
                  primitive.id === "GlobePointPrimitiveCollection"
                ) {
                  const curPrimitivesLength = primitive.length;
                  for (let j = curPrimitivesLength - 1; j >= 0; j--) {
                    const curPrimitive = primitive.get(j);
                    if (curPrimitive.id === options.Row.id) {
                      primitive.remove(curPrimitive);
                      break;
                    }
                  }
                }
              }
              return;
            }
          }, 200);
          _inOnemap.DrawBrush.RemovePrimitiveById(options.Row.id); //删除绘制的图形
          _inOnemap.removeAllGraphicsByLayerId(options.Row.id + "_label"); //删除标注
          _inOnemap.clearDrawGraphicById(options.Row.id); //删除标注

          const item: any = options.graphicDataValue.get(options.Row.id);
          if (options.Row.type == "polyline" && item.geometry) {
            item.geometry.forEach((_: any, i: number) => {
              if (i > 0) {
                _inOnemap.removeAllGraphicsByLayerId(options.Row.id + "_label" + i + "_label");
              }
            });
          }
        } else {
          _inOnemap.removeGraphics("", [options.Row.id]);
          //删除通过drawGraphic方法绘制的图形
          _inOnemap.removeAllGraphicsByLayerId(options.Row.id);
          _inOnemap.DrawBrush.removeGraphics(undefined, [
            "poi_" + options.Row.id,
          ]); //标注
          _inOnemap.DrawBrush.removeGraphics(undefined, [
            "poi_" + options.Row.id + "_unit",
          ]); //标注
          if (options.Row.type == "polyline") {
            options.Row.geometry.paths[0].forEach((_: any, i: number) => {
              if (i > 0) {
                _inOnemap.DrawBrush.removeGraphics(undefined, [
                  "poi_" + options.Row.id + i,
                ]); //线标注
              }
            });
          }
        }

        options.graphicDataKeys.splice(
          options.graphicDataKeys.indexOf(options.Row.id),
          1
        );
        saveLabels.splice(saveLabels.indexOf(options.Row.id), 1);
        options.graphicDataValue.delete(options.Row.id);
      }).catch(() => {
        console.log("删除异常");
      });
    },
    isWarn: true,
    isDefault: true,
    isShow: true,
  },
]);
const ButtonItems = computed(() => {
  if (_inOnemap.MapType.value == mapType.arcgis) {
    return _ButtonItems.value.filter((itm: any) => itm.isShow && itm.isDefault);
  } else {
    return _ButtonItems.value.filter((itm: any) => itm.isShow && itm.isDefault);
  }
});

// 5.4 更新图形名称
const nameChange = (row: any) => {
  _inOnemap.removeAllGraphicsByLayerId(row.id + "_label"); //删除标注
  (_graphicDataValue.get(row.id) as any).label = row.label;
  _graphicDataValue.forEach((item: any) => {
    console.log("item", item);
    if (row.id == item.id) {
      let geo: any;
      if (item.geometry.type) {
        geo = item.geometry;
      } else {
        geo = getGeometry(item.type, item.geometry);
      }
      let label = showLabel(
        {
          type: row.type,
          geometry: geo,
          label: row.label,
        },
        drawArea.value
      );
      if (_inOnemap.MapType.value == mapType.arcgis) {
        _inOnemap.setGraphicName("", "poi_" + item.id, label);
      } else {
        if (item.type == "polyline") {
          drawLabel(item.centerPoint, item.id, row.label);
          drawLineLabel(geo.paths[0], item.id, drawDistance.value);
        } else {
          drawLabel(item.centerPoint, item.id, label);
        }
      }
    }
  });
};

const downGraphicAll = async () => {
  // let checkKeys = treeRef.value.getCheckedKeys();
  // if (!checkKeys.length) {
  //   ElMessage.error("请勾选批量下载的图形!");
  //   return;
  // }
  // let list: any = [];
  // for (const node of treeRef.value.getCheckedNodes()) {
  //   if (node.fileId) {
  //     let result = await getSingleData(node.fileId);
  //     list.push({
  //       geometry: result.geometry,
  //       label: node.name,
  //       id: node.id,
  //       isCollect: true,
  //       type: result.geometry.type,
  //     });
  //   }
  // }
  // _exportCoord.value = {
  //   options: {
  //     Row: {
  //       data: list,
  //       isAll: true,
  //     },
  //   },
  // };
  // showExportGeometry.value = true;
};
//计算面积和长度
const getLengthOrRect = (data: any) => {
  console.log("计算面积和长度data", data);
  if (data.type == "polyline") {
    let line = turf.lineString(data["paths"][0]);
    let length = turf.length(line, { units: "meters" });
    console.log("计算出来的线长度length", length);
    return length.toFixed(2);
  } else if (["polygon", "circle", "rectangle"].includes(data.type)) {
    if (data.type == "circle") {
      data["rings"][0].push(data["rings"][0][0]);
    }
    let tfgeometry = turf.polygon(data["rings"]);
    let newArea = turf.area(tfgeometry);
    console.log("计算出来的面积newArea", newArea);
    return newArea.toFixed(2);
  }
};

//三维计算包围球的中心点(用于绘制标注的位置)
const getGraphicCenter = (obj: any) => {
  if (obj.type == "point") {
    return obj;
  } else {
    let list = obj.paths || obj.rings;
    let pointlist: any = [];

    if (list) {
      for (const item of list[0]) {
        pointlist.push(Cesium.Cartesian3.fromDegrees(item[0], item[1]));
      }
      console.log("pointlist", pointlist);
      let boundingSphere = Cesium.BoundingSphere.fromPoints(pointlist);
      let center = boundingSphere.center;
      console.log("计算中心点绘制标注center", {
        x: toDegrees([center])[0][0],
        y: toDegrees([center])[0][1],
        z: toDegrees([center])[0].length == 3 ? toDegrees([center])[0][2] : 0,
      });
      return {
        x: toDegrees([center])[0][0],
        y: toDegrees([center])[0][1],
        z: toDegrees([center])[0].length == 3 ? toDegrees([center])[0][2] : 0,
      };
      // return toDegrees([center])
    }
  }
};
onMounted(async () => {
  //初始化收藏的图像
  // getCollectList()
});
onUnmounted(() => {
  // 删除绘制的几何对象
  _graphicDataValue.clear();
  _graphicDataKeys.value.length = 0;

  if (_inOnemap.MapType.value == "cesium") {
    // 清除带标签的primitive集合
    const primitives = _inOnemap.MapViewer.scene.primitives;
    const primitivesLength = primitives.length;
    for (let i = primitivesLength - 1; i >= 0; i--) {
      const primitive = primitives.get(i);
      if (
        primitive.id === "GlobePrimitiveCollection" ||
        primitive.id === "GlobePointPrimitiveCollection"
      ) {
        primitives.remove(primitive);
      }
    }
  }
});

// @ts-ignore - Will be used in future
const getGraphicList = () => {
  let list: any = [];
  _graphicDataKeys.value.forEach((item: any) => {
    list.push(_graphicDataValue.get(item));
  });
  return list;
}
// 自定义按钮点击事件，取所有选中的图形，进行处理
// 新增选中行数据存储
const selectedRows = ref<any[]>([]);
// 表格多选变化处理函数
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};
// 获取选中图形列表的方法
const getSelectedGraphics = () => {
  return selectedRows.value.map(row => {
    // 获取图形的完整信息
    const graphicInfo = {
      id: row.id,
      type: row.type,
      label: row.label,
      geometry: row.geometry
    };
    return graphicInfo;
  });
};
const handleChecked = (item: any) => {
  //判断是否有选中项
  if (selectedRows.value.length == 0) {
    ElMessage.error("请先勾选图形!");
    return;
  }
  if (item.callback) {
    item.callback(getSelectedGraphics());
  }
};
</script>

<style lang="scss" scoped>
.draw-panel {
  margin: 5px 0px 5px 0px;

  .draw-btn {
    width: 34px;
    height: 28px;
    margin-left: 0px;

    .draw-icon {
      width: 24px;
      height: 24px;
      transform: scale(1);
    }
  }
}

.tabs {
  margin: 10px 0 0 10px;
}

.footer {
  text-align: right;
}

.geometry-name {
  display: flex;
  align-items: center;

  .geometry-icon {
    margin-right: 4px;
    width: 20px;
    height: 20px;
  }
}

.custom-tree-node__ldiv {
  height: 26px;
  display: flex;
  align-items: center;

  .draw-icon {
    width: 18px;
    height: 18px;
    transform: scale(1);
    color: #f0d394;
  }
}

:deep(.el-tree-node__content) {
  position: relative;
  width: 284px;
}

:deep(.el-message-box__headerbtn) {
  top: 0;
  right: 0;
  width: 0;
  height: 0;
}

.editIcon {
  margin-right: 8px;
}
</style>

<style lang="scss" scoped>
.ElMessageBoxStyle {
  padding: 0;

  .el-message-box__headerbtn {
    top: 10px;
    right: 15px;
    width: 10px;
    height: 10px;
  }
}
</style>
