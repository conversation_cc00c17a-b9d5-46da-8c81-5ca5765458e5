import {
  Geometry,
  Cartesian3,
  Cartesian2,
  GeometryAttribute,
  ComponentDatatype,
  PrimitiveType,
  BoundingSphere,
} from "@onemapkit/cesium";

export default class CylinderGeometry extends Geometry {
  constructor(
    radiusTop = 1, // 上面的半径
    radiusBottom = 1, // 下面的半径
    height = 1, // 圆台的高度
    radialSegments = 32, // 径向分段数
    heightSegments = 1, // 高度分段数
    openEnded = false, // 上下面是否敞开
    thetaStart = 0, // 起始的角度
    thetaLength = Math.PI * 2 // 终止的角度
  ) {
    radialSegments = Math.floor(radialSegments);
    heightSegments = Math.floor(heightSegments);

    const indices: number[] = [];
    const vertices: number[] = [];
    const normals: number[] = [];
    const uvs: number[] = [];

    let index = 0;
    const indexArray: number[][] = [];
    const halfHeight = height / 2;

    generateTorso();

    if (openEnded === false) {
      if (radiusTop > 0) generateCap(true);
      if (radiusBottom > 0) generateCap(false);
    }

    function generateTorso() {
      const normal = new Cartesian3();
      const vertex = new Cartesian3();

      const slope = (radiusBottom - radiusTop) / height;

      for (let y = 0; y <= heightSegments; y++) {
        const indexRow:any = [];

        const v = y / heightSegments;

        const radius = v * (radiusBottom - radiusTop) + radiusTop;

        for (let x = 0; x <= radialSegments; x++) {
          const u = x / radialSegments;

          const theta = u * thetaLength + thetaStart;

          const sinTheta = Math.sin(theta);
          const cosTheta = Math.cos(theta);

          vertex.x = radius * sinTheta;
          vertex.y = -v * height + halfHeight;
          vertex.z = radius * cosTheta;
          vertices.push(vertex.x, vertex.y, vertex.z);

          normal.x = sinTheta;
          normal.y = slope;
          normal.z = cosTheta;
          Cartesian3.normalize(normal, normal);
          normals.push(normal.x, normal.y, normal.z);

          uvs.push(u, 1 - v);

          indexRow.push(index++);
        }

        indexArray.push(indexRow);
      }

      for (let x = 0; x < radialSegments; x++) {
        for (let y = 0; y < heightSegments; y++) {
          const a = indexArray[y][x];
          const b = indexArray[y + 1][x];
          const c = indexArray[y + 1][x + 1];
          const d = indexArray[y][x + 1];

          indices.push(a, b, d);
          indices.push(b, c, d);
        }
      }
    }

    function generateCap(top: boolean) {
      const centerIndexStart = index;

      const uv = new Cartesian2();
      const vertex = new Cartesian3();

      const radius = top === true ? radiusTop : radiusBottom;
      const sign = top === true ? 1 : -1;

      for (let x = 1; x <= radialSegments; x++) {
        vertices.push(0, halfHeight * sign, 0);
        normals.push(0, sign, 0);
        uvs.push(0.5, 0.5);
        index++;
      }

      const centerIndexEnd = index;

      for (let x = 0; x <= radialSegments; x++) {
        const u = x / radialSegments;
        const theta = u * thetaLength + thetaStart;

        const cosTheta = Math.cos(theta);
        const sinTheta = Math.sin(theta);

        vertex.x = radius * sinTheta;
        vertex.y = halfHeight * sign;
        vertex.z = radius * cosTheta;
        vertices.push(vertex.x, vertex.y, vertex.z);

        normals.push(0, sign, 0);

        uv.x = cosTheta * 0.5 + 0.5;
        uv.y = sinTheta * 0.5 * sign + 0.5;
        uvs.push(uv.x, uv.y);
        index++;
      }

      for (let x = 0; x < radialSegments; x++) {
        const c = centerIndexStart + x;
        const i = centerIndexEnd + x;
        if (top === true) {
          indices.push(i, i + 1, c);
        } else {
          indices.push(i + 1, i, c);
        }
      }
    }

    super({
      attributes: {
        position: new GeometryAttribute({
          componentDatatype: ComponentDatatype.DOUBLE,
          componentsPerAttribute: 3,
          values: new Float64Array(vertices),
        }),
        st: new GeometryAttribute({
          componentDatatype: ComponentDatatype.FLOAT,
          componentsPerAttribute: 2,
          values: new Float32Array(uvs),
        }),
        normal: new GeometryAttribute({
          componentDatatype: ComponentDatatype.FLOAT,
          componentsPerAttribute: 3,
          values: new Float32Array(normals),
        }),
      },
      indices: new Uint16Array(indices),
      primitiveType: PrimitiveType.LINE_STRIP,
      boundingSphere: BoundingSphere.fromVertices(vertices),
    } as any);
  }
}
