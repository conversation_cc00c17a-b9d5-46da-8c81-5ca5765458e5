<template>
  <el-form
    :model="globalSettings"
    class="multi-light-global-settings"
    label-width="96px"
  >
    <el-form-item class="form-item" label="灯带发光强度">
      <el-slider
        class="form-item-slider"
        v-model="globalSettings.bloomStrength"
        :min="0"
        :max="10"
        :step="0.1"
        show-input
        @input="bloomStrengthChange()"
      />
    </el-form-item>
    <el-form-item class="form-item" label="灯带发光半径">
      <el-slider
        class="form-item-slider"
        v-model="globalSettings.bloomRadius"
        :min="0"
        :max="1"
        :step="0.01"
        show-input
        @input="bloomRadiusChange()"
      />
    </el-form-item>
    <el-form-item class="form-item" label="场景时间">
      <el-slider
        class="form-item-slider"
        v-model="globalSettings.time"
        :min="0"
        :max="24"
        :step="0.01"
        show-input
        @input="timeChange()"
      />
    </el-form-item>
    <el-form-item class="form-item" label="时间比例">
      <el-slider
        class="form-item-slider"
        v-model="globalSettings.multiplier"
        :min="0"
        :max="8000"
        :step="1"
        show-input
        @input="multiplierChange()"
      />
    </el-form-item>
    <el-form-item class="form-item" label="时间流动">
      <el-switch
        v-model="globalSettings.shouldAnimate"
        @change="shouldAnimateChange()"
      />
    </el-form-item>
    <el-form-item class="form-item" label="大气美化">
      <el-switch
        v-model="globalSettings.atmosphericBeautify"
        @change="atmosphericBeautifyChange()"
      />
    </el-form-item>

    <el-form-item class="form-item" label="视点">
      <el-button type="primary" @click="addViewPoint()">添加视点</el-button>
    </el-form-item>
  </el-form>

  <el-table :data="globalSettings.viewPoints" height="200" width="100%">
    <el-table-column label="视点名称">
      <template #default="viewPoint">
        <el-input size="small" v-model="viewPoint.row.name" />
      </template>
    </el-table-column>

    <el-table-column fixed="right" label="操作">
      <template #default="viewPoint">
        <el-button
          link
          size="small"
          type="primary"
          @click="zoomToViewPoint(viewPoint.row)"
          >定位</el-button
        >
        <el-button
          link
          size="small"
          type="danger"
          @click="deleteViewPoint(viewPoint)"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts">
import { defineComponent, ref, onUnmounted } from "vue";
import { JulianDate, Camera, Cartesian3 } from "@onemapkit/cesium";
import { getOnemap } from "../../onemapkit";
import { changeBloomRadius, changeBloomStrength } from "./tools";

export default defineComponent({
  name: "MultiLightGlobalSettings",
  // 入参
  props: {
    MapControlName: {
      type: String,
      default: "mainMapControl",
      require: true,
    },
    globalSettings: {
      type: Object,
      require: true,
    },
  },

  setup(props, {expose}) {
    const _inOnemap = getOnemap(props.MapControlName);

    // 子组件的值修改，同步修改父组件的内容
    const globalSettings = ref(props.globalSettings as any);

    const curDate = new Date();

    const onTickEventRemove = _inOnemap.MapViewer.clock.onTick.addEventListener(
      () => {
        const date = JulianDate.toDate(_inOnemap.MapViewer.clock.currentTime);
        globalSettings.value.time =
          date.getHours() + date.getMinutes() / 60 + date.getSeconds() / 3600;
      }
    );

    // 根据初始值设置默认状态
    timeChange();
    multiplierChange();
    shouldAnimateChange();
    atmosphericBeautifyChange();

    // 时间调整
    function timeChange() {
      const time = globalSettings.value.time;
      const secondTime = time * 3600;
      const hours = Math.floor(time);
      const minutes = Math.floor((secondTime - hours * 3600) / 60);
      const seconds = Math.floor(secondTime - hours * 3600 - minutes * 60);

      curDate.setHours(hours);
      curDate.setMinutes(minutes);
      curDate.setSeconds(seconds);
      if (_inOnemap.MapViewer) {
        _inOnemap.MapViewer.clock.currentTime = JulianDate.fromDate(curDate);
      }
    }

    // 时间流逝倍率修改
    function multiplierChange() {
      if (_inOnemap.MapViewer) {
        _inOnemap.MapViewer.clock.multiplier = globalSettings.value.multiplier;
      }
    }

    // 时间是否流动修改
    function shouldAnimateChange() {
      if (_inOnemap.MapViewer) {
        _inOnemap.MapViewer.clock.shouldAnimate =
          globalSettings.value.shouldAnimate;
      }
    }

    function atmosphericBeautifyChange() {
      if (_inOnemap.MapViewer) {
        if (globalSettings.value.atmosphericBeautify) {
          // 开启地形光照计算
          _inOnemap.MapViewer.scene.globe.enableLighting = true;
          // 大气光在地面的投射强度
          _inOnemap.MapViewer.scene.globe.atmosphereLightIntensity = 30;
          // 调整大气的强度
          _inOnemap.MapViewer.scene.skyAtmosphere.atmosphereLightIntensity = 30;
        } else {
          // 还原默认值
          _inOnemap.MapViewer.scene.globe.enableLighting = false;
          _inOnemap.MapViewer.scene.globe.atmosphereLightIntensity = 10;
          _inOnemap.MapViewer.scene.skyAtmosphere.atmosphereLightIntensity = 50;
        }
      }
    }

    function bloomRadiusChange() {
      changeBloomRadius(globalSettings.value.bloomRadius);
    }

    function bloomStrengthChange() {
      changeBloomStrength(globalSettings.value.bloomStrength);
    }

    function addViewPoint() {
      if (_inOnemap.MapViewer) {
        const camera = _inOnemap.MapViewer.camera as Camera;

        const viewPoint = {
          directionX: camera.directionWC.x,
          directionY: camera.directionWC.y,
          directionZ: camera.directionWC.z,
          upX: camera.upWC.x,
          upY: camera.upWC.y,
          upZ: camera.upWC.z,
          x: camera.positionWC.x,
          y: camera.positionWC.y,
          z: camera.positionWC.z,
          name: "未命名视点",
        };

        globalSettings.value.viewPoints.push(viewPoint);
      }
    }

    function zoomToViewPoint(data: any) {
      if (_inOnemap.MapViewer) {
        _inOnemap.MapViewer.camera.setView({
          destination: new Cartesian3(data.x, data.y, data.z),
          orientation: {
            direction: new Cartesian3(
              data.directionX,
              data.directionY,
              data.directionZ
            ),
            up: new Cartesian3(data.upX, data.upY, data.upZ),
          },
        });
      }
    }

    function deleteViewPoint(data: any) {
      globalSettings.value.viewPoints.splice(data.$index, 1);
    }

    expose({
      timeChange,
      multiplierChange,
      shouldAnimateChange,
      atmosphericBeautifyChange,
      bloomRadiusChange,
      bloomStrengthChange,
    });

    onUnmounted(() => {
      onTickEventRemove();
    });

    return {
      globalSettings,
      timeChange,
      multiplierChange,
      shouldAnimateChange,
      atmosphericBeautifyChange,
      bloomRadiusChange,
      bloomStrengthChange,
      addViewPoint,
      zoomToViewPoint,
      deleteViewPoint,
    };
  },
});
</script>

<style lang="less" scoped>
.multi-light-global-settings {
  padding: 16px 20px 0px 34px;
}
</style>
