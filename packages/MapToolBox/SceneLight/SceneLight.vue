<template>
  <el-tabs class="eltabs" v-model="activeName">
    <el-tab-pane
      class="eltabpane"
      label="全局设置"
      name="globalSettings"
      key="globalSettings"
    >
      <div class="tabcontent">
        <el-scrollbar>
          <MultiLightGlobalSettings
            ref="MultiLightGlobalSettingsRef"
            :globalSettings="globalSettings"
            :MapControlName="props.MapControlName"
          />
        </el-scrollbar>
      </div>
    </el-tab-pane>
    <el-tab-pane
      class="eltabpane"
      label="光源列表"
      name="lightList"
      key="lightList"
    >
      <div class="tabcontent">
        <el-scrollbar>
          <MultiLightLightList
            ref="MultiLightLightListRef"
            :globalSettings="globalSettings"
            :lightData="lightData"
            :MapControlName="props.MapControlName"
            :loadJsonEvent="loadJsonEvent"
          />
        </el-scrollbar>
      </div>
    </el-tab-pane>
    <el-tab-pane
      class="eltabpane"
      label="光源列表AA"
      name="lightform"
      key="lightform"
    >
      <div class="tabcontent">
        <el-scrollbar>
          <lightform />
        </el-scrollbar>
      </div
    ></el-tab-pane>
  </el-tabs>
</template>

<script lang="ts">
import { ref, defineComponent, onUnmounted, onMounted } from "vue";
import { JulianDate, ClockRange, Color, Event } from "@onemapkit/cesium";
import { getOnemap } from "../../onemapkit";
import MultiLightGlobalSettings from "./globalSettings.vue";
import MultiLightLightList from "./lightlist.vue";
import lightform from "./lightform.vue";
import {
  LightListType,
  initSceneBeautification,
  destroySceneBeautification,
  getLightById,
  changeLightBarColor,
} from "./tools";

export default defineComponent({
  name: "SceneLight",
  props: {
    MapControlName: {
      type: String,
      default: "mainMapControl",
      require: true,
    },
  },
  components: {
    MultiLightGlobalSettings,
    MultiLightLightList,
  },
  setup(props, { expose }) {
    const _inOnemap = getOnemap(props.MapControlName);

    // MultiLightLightList中加载json后的事件
    const loadJsonEvent = new Event();

    const MultiLightGlobalSettingsRef = ref();
    const MultiLightLightListRef = ref();

    // 初始化场景美化插件
    initSceneBeautification(_inOnemap.MapViewer);

    const oldRequestRenderMode = _inOnemap.MapViewer.scene.requestRenderMode;
    const oldDepthTestAgainstTerrain =
      _inOnemap.MapViewer.scene.globe.depthTestAgainstTerrain;

    _inOnemap.MapViewer.scene.requestRenderMode = false;
    _inOnemap.MapViewer.scene.globe.depthTestAgainstTerrain = true;

    const clock = _inOnemap.MapViewer.clock;
    const oldShouldAnimate = clock.shouldAnimate;
    const oldMultiplier = clock.multiplier;
    const curDate = JulianDate.toDate(clock.currentTime);

    const globalSettings = ref({
      bloomStrength: 2,
      bloomRadius: 0.589,
      // 设置当前时间为全局设置的时间
      time:
        curDate.getHours() +
        curDate.getMinutes() / 60 +
        curDate.getSeconds() / 3600,
      shouldAnimate: oldShouldAnimate,
      multiplier: oldMultiplier,
      atmosphericBeautify: true,
      viewPoints: [],
    });

    const lightData = ref([] as any[]);

    // 设置起始时间为当日零点
    curDate.setHours(0);
    curDate.setMinutes(0);
    curDate.setSeconds(0);
    clock.startTime = JulianDate.fromDate(curDate);
    curDate.setHours(24);
    clock.stopTime = JulianDate.fromDate(curDate);
    clock.clockRange = ClockRange.LOOP_STOP;

    const outTimeRangeColoe = new Color(1, 1, 1, 0);

    let oldTime = clock.currentTime.secondsOfDay;

    // 获取颜色
    function getColor(lightOrGroud: any) {
      const lightAnimationLength = lightOrGroud.lightAnimation.length;
      const date = JulianDate.toDate(clock.currentTime);
      const dateTime =
        date.getHours() + date.getMinutes() / 60 + date.getSeconds() / 3600;

      let curColor: any;

      if (dateTime < lightOrGroud.timeRanges[0]) {
        // 如果小于时间范围，则灯光颜色为全透明
        curColor = outTimeRangeColoe;
      } else if (dateTime > lightOrGroud.timeRanges[1]) {
        // 如果大于时间范围，则灯光颜色为全透明
        curColor = outTimeRangeColoe;
      } else {
        // 所有动画的总耗时(秒)
        let totalSeconds = 0;
        lightOrGroud.lightAnimation.forEach((lightAnimation: any) => {
          totalSeconds += lightAnimation.timeInterval;
        });

        // 当前时间在时间范围中行进的量（小时）
        let curTimeProgression = dateTime - lightOrGroud.timeRanges[0];
        // 转换成秒
        curTimeProgression *= 3600;

        // 计算前进量
        curTimeProgression = curTimeProgression / totalSeconds;
        // 已经前进了多少段完整的时间序列
        const curTimeSlot = Math.floor(curTimeProgression);
        // 当前时间序列内前进的秒数
        curTimeProgression = (curTimeProgression - curTimeSlot) * totalSeconds;

        // 查询当前时间落在哪一个颜色的时间范围里面
        let time = 0;
        let index = 0;
        for (let i = 0; i < lightAnimationLength; i++) {
          time += lightOrGroud.lightAnimation[i].timeInterval;
          if (curTimeProgression < time) {
            index = i;
            break;
          }
        }

        curColor = Color.fromCssColorString(
          lightOrGroud.lightAnimation[index].color
        );
      }
      return curColor;
    }

    // 改变光源颜色的统一方法
    function changeLightColor(id: string, color: Color, intensity: number) {
      const lightObject = getLightById(id);
      if (lightObject) {
        if (color.alpha === 0) {
          // 如果是全透明颜色，则直接设置强度为0，达到关闭灯光的目的
          lightObject.value.intensity = 0;
        } else {
          lightObject.value.intensity = intensity;
        }

        // 更改颜色
        lightObject.value.color = color;
      }
    }

    // 利用Cesium自身的时钟系统，控制整个场景的灯光动画
    const onTickEventRemove = clock.onTick.addEventListener(() => {
      if (clock.currentTime.secondsOfDay !== oldTime) {
        oldTime = clock.currentTime.secondsOfDay;

        lightData.value.forEach((scene) => {
          scene.children.forEach((lightOrGroud: any) => {
            switch (lightOrGroud.type) {
              case LightListType.PointLightGroud:
              case LightListType.SpotLightGroud:
                {
                  // 仅当有动画序列才计算动画
                  const lightAnimationLength =
                    lightOrGroud.lightAnimation.length;
                  const lightChildrenLength = lightOrGroud.children.length;
                  if (lightAnimationLength > 0 && lightChildrenLength > 0) {
                    const curColor = getColor(lightOrGroud);
                    if (curColor) {
                      // 设置组里面的所有
                      lightOrGroud.children.forEach((light: any) => {
                        changeLightColor(
                          light.id,
                          curColor,
                          lightOrGroud.intensity
                        );
                      });
                    }
                  }
                }
                break;
              case LightListType.VerticalLightBarGroud:
              case LightListType.HorizontalLightBarGroud:
                {
                  // 仅当有动画序列才计算动画
                  const lightAnimationLength =
                    lightOrGroud.lightAnimation.length;
                  const lightChildrenLength = lightOrGroud.children.length;
                  if (lightAnimationLength > 0 && lightChildrenLength > 0) {
                    const curColor = getColor(lightOrGroud);
                    if (curColor) {
                      // 设置组里面的所有
                      lightOrGroud.children.forEach((light: any) => {
                        changeLightBarColor(light.id, curColor);
                      });
                    }
                  }
                }
                break;
            }
          });
        });
      }
    });

    loadJsonEvent.addEventListener((json) => {
      const jsonGlobalSettings = json.globalSettings;
      const jsonLightData = json.lightData;
      const multiLightGlobalSettings = MultiLightGlobalSettingsRef.value;
      const multiLightLightList = MultiLightLightListRef.value;

      // 设置全局状态
      if (multiLightGlobalSettings && jsonGlobalSettings) {
        if (
          jsonGlobalSettings.atmosphericBeautify !==
          globalSettings.value.atmosphericBeautify
        ) {
          // 如果大气美化变化了，则更新全局变量，并刷新效果
          globalSettings.value.atmosphericBeautify =
            jsonGlobalSettings.atmosphericBeautify;

          multiLightGlobalSettings.atmosphericBeautifyChange();
        }

        if (jsonGlobalSettings.time !== globalSettings.value.time) {
          // 如果时间变化了，则更新全局变量，并刷新效果
          globalSettings.value.time = jsonGlobalSettings.time;
          multiLightGlobalSettings.timeChange();
        }

        if (jsonGlobalSettings.multiplier !== globalSettings.value.multiplier) {
          // 如果时间乘数变化了，则更新全局变量，并刷新效果
          globalSettings.value.multiplier = jsonGlobalSettings.multiplier;
          multiLightGlobalSettings.multiplierChange();
        }

        if (
          jsonGlobalSettings.shouldAnimate !==
          globalSettings.value.shouldAnimate
        ) {
          // 如果动画状态变化了，则更新全局变量，并刷新效果
          globalSettings.value.shouldAnimate = jsonGlobalSettings.shouldAnimate;
          multiLightGlobalSettings.shouldAnimateChange();
        }

        if (
          jsonGlobalSettings.bloomStrength !==
          globalSettings.value.bloomStrength
        ) {
          // 如果泛光强度变化了，则更新全局变量，并刷新效果
          globalSettings.value.bloomStrength = jsonGlobalSettings.bloomStrength;
          multiLightGlobalSettings.bloomStrengthChange();
        }

        if (
          jsonGlobalSettings.bloomRadius !== globalSettings.value.bloomRadius
        ) {
          // 如果泛光半径变化了，则更新全局变量，并刷新效果
          globalSettings.value.bloomRadius = jsonGlobalSettings.bloomRadius;
          multiLightGlobalSettings.bloomRadiusChange();
        }

        globalSettings.value.viewPoints = jsonGlobalSettings.viewPoints;
      }

      // 移除场景灯光，并添加新的灯光
      if (multiLightLightList && jsonLightData) {
        // 移除场景所有的灯光
        multiLightLightList.removeAllLight();

        lightData.value = jsonLightData;
        multiLightLightList.createFromLightData(jsonLightData);
      }
    });

    const activeName = ref("globalSettings");

    expose({});

    onUnmounted(() => {
      // 移除时间变化监听
      onTickEventRemove();

      // 还原一些场景默认设置
      _inOnemap.MapViewer.scene.requestRenderMode = oldRequestRenderMode;
      _inOnemap.MapViewer.clock.shouldAnimate = oldShouldAnimate;
      _inOnemap.MapViewer.clock.multiplier = oldMultiplier;
      _inOnemap.MapViewer.scene.globe.depthTestAgainstTerrain =
        oldDepthTestAgainstTerrain;

      _inOnemap.MapViewer.scene.globe.enableLighting = false;
      _inOnemap.MapViewer.scene.globe.atmosphereLightIntensity = 10;
      _inOnemap.MapViewer.scene.skyAtmosphere.atmosphereLightIntensity = 50;

      destroySceneBeautification();
    });
    onMounted(() => {});
    return {
      MultiLightGlobalSettingsRef,
      MultiLightLightListRef,
      loadJsonEvent,
      globalSettings,
      lightData,
      activeName,
      props,
    };
  },
});
</script>

<style lang="less" scoped>
.eltabs {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.eltabpane {
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
}
.el-tabs__content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.tabcontent {
  height: 100%;
  width: 100%;
  overflow: auto;
}
.scrollable-content {
  background-color: aqua;
  height: 300px; /* Set the height you need */
  overflow: hidden; /* Hide default scrollbars */
}
.position-class {
  width: 100px;
  margin-right: 16px;
}
.collapse-item {
  padding: 0px 10px 0px 34px;
}
.space-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
}
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
