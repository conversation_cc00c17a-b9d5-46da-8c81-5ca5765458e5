<template>
  <div class="from-item-dom">
    <div style="margin: 2px; width: 97px; white-space: nowrap">场景名称:</div>
    <el-input
      style="margin: 0px"
      v-model="newSceneName"
      placeholder="输入场景名称"
    >
    </el-input>
    <el-button style="margin: 1px" type="success" plain @click="createScene()">
      创建场景
    </el-button>
    <el-button style="margin: 1px" type="success" plain @click="load()">
      导入场景
    </el-button>
    <!-- <el-button style="margin: 1px" type="warning" plain @click="save()">
        批量导出场景
      </el-button> -->
  </div>
  <!--1级表  光源列表 -->
  <el-table
    :data="lightData"
    stripe
    border
    size="small" 
    :show-header="false"
    :highlight-current-row="true"
    :header-cell-style="{ 'text-align': 'center' }"
    row-key="id"
  >
    <el-table-column type="expand">
      <template #default="dtProps">
        <!--2级表 groupname  -->
        <el-table 
          :data="dtProps.row.children"
          row-key="id"
          stripe
          :show-header="false"
          border
          size="small"
          :highlight-current-row="true"
          :header-cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column type="expand">
            <template #default="dtPropsSub">
              <div
                class="inline-flex"
                style="
                  margin: 5px 20px;
                  padding: 3px;
                  box-shadow: var(--el-box-shadow-lighter);
                "
              >
                <el-form-item label="颜色">
                  <el-color-picker
                    :teleported="false"
                    v-model="dtPropsSub.row.color"
                    @change="lightColorChange(dtPropsSub.row)"
                  />
                </el-form-item>

                <!-- 光源强度 光源范围(发光半径)-->
                <SliderInput
                  v-if="
                    dtPropsSub.row.type === LightListType.PointLightGroud ||
                    dtPropsSub.row.type === LightListType.SpotLightGroud
                  "
                  label="光源强度"
                  style="margin: 1px 0px"
                  :input-width="320"
                  :width="'320px'"
                  :min="0"
                  :max="500"
                  :step="0.1"
                  v-model="dtPropsSub.row.intensity"
                  @change="lightIntensityChange(dtPropsSub.row)"
                ></SliderInput>
                <SliderInput
                  v-if="
                    dtPropsSub.row.type === LightListType.PointLightGroud ||
                    dtPropsSub.row.type === LightListType.SpotLightGroud
                  "
                  label="光源范围"
                  style="margin: 1px 0px"
                  :input-width="320"
                  :width="'320px'"
                  :min="0.1"
                  :max="200"
                  :step="0.1"
                  v-model="dtPropsSub.row.radius"
                  @change="lightRadiusChange2(dtPropsSub.row)"
                ></SliderInput>

                <!-- 聚灯外角 聚灯内角-->
                <SliderInput
                  v-if="dtPropsSub.row.type === LightListType.SpotLightGroud"
                  style="margin: 1px 0px"
                  :input-width="320"
                  :width="'320px'"
                  label="聚灯外角"
                  :min="0"
                  :max="90"
                  :step="0.1"
                  v-model="dtPropsSub.row.outerConeDegrees"
                  @change="lightOuterConeDegreesChange2(dtPropsSub.row)"
                ></SliderInput>
                <SliderInput
                  v-if="dtPropsSub.row.type === LightListType.SpotLightGroud"
                  style="margin: 1px 0px"
                  :input-width="320"
                  :width="'320px'"
                  label="聚灯内角"
                  :min="0"
                  :max="90"
                  :step="0.1"
                  v-model="dtPropsSub.row.innerConeDegrees"
                  @change="lightInnerConeDegreesChange2(dtPropsSub.row)"
                ></SliderInput>

                <el-form-item
                  label="首尾闭合"
                  style="margin-top: 18px"
                  v-if="
                    dtPropsSub.row.type ===
                    LightListType.HorizontalLightBarGroud
                  "
                >
                  <el-switch
                    v-model="dtPropsSub.row.lightBarClose"
                    @change="
                      updateHorizontalLightBarLightBarClose(dtPropsSub.row)
                    "
                  />
                </el-form-item>

                <SliderInput
                  v-if="
                    dtPropsSub.row.type === LightListType.VerticalLightBarGroud
                  "
                  :input-width="260"
                  :width="'260px'"
                  label="顶部高程"
                  :min="0"
                  :max="1000"
                  :step="0.01"
                  v-model="dtPropsSub.row.topHeight"
                  @change="updateLightBarTopHeight(dtPropsSub.row)"
                ></SliderInput>

                <SliderInput
                  v-if="
                    dtPropsSub.row.type ===
                      LightListType.HorizontalLightBarGroud ||
                    dtPropsSub.row.type === LightListType.VerticalLightBarGroud
                  "
                  :input-width="260"
                  :width="'260px'"
                  label="灯带半径"
                  :min="0.01"
                  :max="1"
                  :step="0.01"
                  v-model="dtPropsSub.row.lightBarRadius"
                  @change="updateLightBarRadius(dtPropsSub.row)"
                ></SliderInput>

                <!-- 时间范围 -->
                <el-form-item label="时间范围">
                  <el-slider
                    style="width: 280px"
                    v-model="dtPropsSub.row.timeRanges"
                    range
                    :marks="timeRangeMarks"
                    :max="24"
                    :min="0"
                    :step="0.01"
                  />
                </el-form-item>

                <!-- 动画序列 -->
                <el-form-item label="添加动画" style="margin-top: 32px">
                  <el-button @click="addLightAnimation(dtPropsSub.row)"
                    >添加</el-button
                  >
                </el-form-item>
                <el-form-item
                  v-for="(lightAnimation, index) in dtPropsSub.row
                    .lightAnimation"
                  :key="lightAnimation.color + index"
                >
                  <div class="multi-light-block">
                    <span>颜色</span>
                    <el-color-picker
                      :teleported="false"
                      v-model="lightAnimation.color"
                    />
                  </div>
                  <div class="multi-light-block">
                    <span>时间间隔</span>
                    <el-input-number v-model="lightAnimation.timeInterval" />
                  </div>
                </el-form-item>

                <div style="width: 100%; align-items: end; text-align: right">
                  <el-button
                    style="margin: 1px 30px 1px 0px"
                    type="primary"
                    size="small"
                    plain
                    v-if="
                      dtPropsSub.row.type === LightListType.PointLightGroud ||
                      dtPropsSub.row.type === LightListType.SpotLightGroud
                    "
                    @click="addLight(dtPropsSub)"
                  >
                    绘制光源并添加
                  </el-button>

                  <el-button
                    size="small"
                    v-if="
                      dtPropsSub.row.type ===
                      LightListType.HorizontalLightBarGroud
                    "
                    @click="drawHorizontalLightBar2(dtPropsSub)"
                  >
                    绘制灯带并添加
                  </el-button>

                  <el-button
                    size="small"
                    v-if="
                      dtPropsSub.row.type ===
                      LightListType.VerticalLightBarGroud
                    "
                    @click="drawVerticalLightBar2(dtPropsSub)"
                  >
                    拾取灯带底部点
                  </el-button>
                </div>
              </div>

              <!--3级表 lightname-->
              <el-table 
                :header-cell-style="{ 'text-align': 'center' }"
                :data="dtPropsSub.row.children"
                row-key="id"
                :highlight-current-row="true"
                stripe
                :show-header="true"
                border
                size="small"
              >
                <el-table-column type="expand">
                  <template #default="light">
                    <div
                      class="inline-flex"
                      style="
                        margin: 5px 20px;
                        padding: 3px;
                        box-shadow: var(--el-box-shadow-lighter);
                      "
                    >
                      <el-form-item
                        label="灯带底部点"
                        v-if="light.row.type === LightListType.VerticalLightBar"
                      >
                        <el-input
                          v-model="light.row.bottomPoint"
                          @change="
                            onVerticalLightBarBottomPointChange(light.row)
                          "
                        />
                        <el-button
                          @click="pickVerticalLightBarBottomPoint(light.row)"
                        >
                          重新拾取
                        </el-button>
                      </el-form-item>

                      <el-form-item
                        label="灯带点集合"
                        v-if="
                          light.row.type === LightListType.HorizontalLightBar
                        "
                      >
                        <el-input
                          size="small"
                          v-model="light.row.positions"
                          @change="
                            onHorizontalLightBarPositionsChange(light.row)
                          "
                        />
                        <el-button
                          size="small"
                          @click="drawHorizontalLightBarPositions(light.row)"
                        >
                          重新绘制
                        </el-button>
                      </el-form-item>

                      <el-form-item
                        label="光源位置"
                        v-if="
                          light.row.type === LightListType.PointLight ||
                          light.row.type === LightListType.SpotLight
                        "
                      >
                        <el-input
                          v-model="light.row.position"
                          @change="lightPositionChange(light.row)"
                        />
                      </el-form-item>

                      <el-form-item
                        label="聚光灯看向位置"
                        v-if="light.row.type === LightListType.SpotLight"
                      >
                        <el-input
                          v-model="light.row.directionPoint"
                          @change="lightDirectionPointChange(light.row)"
                        />
                      </el-form-item>

                      <el-form-item
                        label="编辑轴"
                        v-if="
                          light.row.type === LightListType.PointLight ||
                          light.row.type === LightListType.SpotLight
                        "
                      >
                        <el-button @click="startLightEditLight(light.row)"
                          >开启</el-button
                        >
                        <el-button @click="stopLightEditLight()"
                          >关闭</el-button
                        >
                      </el-form-item>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="类型" align="center" :width="85">
                  <template #default="light">
                    <div>{{ light.row.type }}</div>
                  </template>
                </el-table-column>

                <el-table-column label="光源名称" prop="groupname">
                  <template #default="light">
                    <el-input v-model="light.row.name" />
                  </template>
                </el-table-column>

                <el-table-column
                  label="操作"
                  prop="operations"
                  :width="118"
                  align="center"
                >
                  <template #default="light">
                    <el-button
                      style="margin: 1px"
                      type="warning"
                      link
                      size="small"
                      @click="deleteLight(light)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>

          <el-table-column label="类型" align="center" :width="95">
            <template #default="dtPropsChildren">
              <div>{{ dtPropsChildren.row.type }}</div>
            </template>
          </el-table-column>

          <el-table-column label="光源组名称" prop="groupname">
            <template #default="dtPropsChildren">
              <el-input v-model="dtPropsChildren.row.name" />
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            prop="operations"
            :width="128"
            align="center"
          >
            <template #default="dtPropsChildren">
              <el-button
                style="margin: 1px"
                type="warning"
                link
                size="small"
                @click="deleteGroud(dtPropsChildren)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-table-column>

    <el-table-column label="类型" :width="50">
      <div>{{ LightListType.Scene }}</div>
    </el-table-column>
    <el-table-column>
      <template #header>
        <span> 场景名称 </span>
      </template>
      <template #default="scope">
        <el-input
          style="margin: 0px"
          v-model="scope.row.name"
          placeholder="输入场景名称"
        >
        </el-input>
      </template>
    </el-table-column>

    <!-- 操作 -->
    <el-table-column label="操作" prop="operations" :width="126" align="center">
      <template #default="scope">
        <el-dropdown >
          <el-button style="margin-top: 3.5px" type="success" link size="small">
            创建组
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="type of lightGrouds"
                :key="type"
                @click="addLightGroud(scope.row, type)"
                >{{ type }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          style="margin: 1px"
          type="warning"
          link
          size="small"
          @click="deleteScene(scope)"
        >
          删除
        </el-button>
        <el-button
          style="margin: 1px"
          type="warning"
          link
          size="small"
          @click="deleteScene(scope)"
        >
          导出
        </el-button> 
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang='ts'>
import { ref, defineComponent, onUnmounted } from "vue";
import * as Cesium from "@onemapkit/cesium";
import { ElMessage } from "element-plus";
import { getOnemap, SliderInput } from "../../onemapkit";
import {
  cartesian3ToString,
  LightListType,
  drawPointLight,
  drawSpotLight,
  stringToCartesian3,
  cartesian3ArrayToString,
  stringToCartesian3Array,
  saveJson,
  changeLightIntensity,
  changeLightRadius,
  changeLightColor,
  changeLightInnerConeDegrees,
  changeLightOuterConeDegrees,
  getLightById,
  startEditLight,
  stopEditLight,
  removeLight,
  addHorizontalExtents,
  addVerticalExtents,
  updateVerticalLightBar,
  updateHorizontalLightBar,
  updateLightBarPrimitive,
  updateColorTexture,
  changeLightBarColor,
  removeLightBar,
  loadJson,
  addPointLight,
  addSpotLight,
} from "./tools";
import { DrawMod, DrawTool } from "@onemapkit/cesium-tools"; 

const props = {
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  globalSettings: {
    type: Object,
    require: true,
  },
  lightData: {
    type: Object,
    require: true,
  },
  loadJsonEvent: {
    type: Object,
    require: true,
  },
};

export default defineComponent({
  name: "MultiLightLightList",
  // 入参
  props: props,

  components: {
    SliderInput,
  },
  setup(props, { expose }) {
    const lightData = ref(props.lightData as any[]);

    const addLightPopoverHideRef = ref<HTMLSpanElement>();

    const timeRangeMarks = {
      0: "0点",
      24: "24点",
    };

    const lightGrouds = [
      LightListType.PointLightGroud,
      LightListType.SpotLightGroud,
      LightListType.VerticalLightBarGroud,
      LightListType.HorizontalLightBarGroud,
    ];

    const newSceneName = ref("");

    const globalSettings = ref(props.globalSettings as any);

    // 读取json文件后的回调事件（读取文件后，交给SceneLight实现后续逻辑）
    const loadJsonEvent = props.loadJsonEvent as Cesium.Event;

    // ********************************** 测试追加的 **********************************
    const _inOnemap = getOnemap(props.MapControlName);
    // 创建绘制线工具来绘制聚光灯
    const drawLineTools = new DrawTool(_inOnemap.MapViewer, DrawMod.Line, {});
    // 绘制两点结束
    function drawPoint(callback: Function) {
      drawLineTools.start();
      const removeFunction = drawLineTools.drawEvent.addEventListener(
        (positions: Cesium.Cartesian3[]) => {
          if (positions && positions.length === 1) {
            drawLineTools.stop();
            callback(positions[0]);
            // 绘制结束后，取消当前的监听事件
            removeFunction();
          }
        }
      );
    }
    // 绘制多点
    function drawLine(callback: Function) {
      drawLineTools.start();
      const removeFunction = drawLineTools.drawEndEvent.addEventListener(
        (positions: Cesium.Cartesian3[]) => {
          callback(positions);
          // 绘制结束后，取消当前的监听事件
          removeFunction();
        }
      );
    }
    // ********************************** 测试追加的 **********************************

    // 快速查询，根据ID快速返回索引，避免重复遍历
    const quickQuery = ref({} as any);
    updateQuickQuery();
    // 构建快速查询字典
    function updateQuickQuery() {
      quickQuery.value = {};
      lightData.value.forEach((scene, sceneIndex) => {
        // 记录场景id的对应的场景index
        quickQuery.value[scene.id] = [sceneIndex];
        scene.children.forEach((groudOrLight: any, groudOrLightIndex: any) => {
          // 记录组id和对应的场景index、组index
          quickQuery.value[groudOrLight.id] = [sceneIndex, groudOrLightIndex];
          groudOrLight.children?.forEach((light: any, lightIndex: any) => {
            // 记录光源id和对应的场景index、组index、光源index
            quickQuery.value[light.id] = [
              sceneIndex,
              groudOrLightIndex,
              lightIndex,
            ];
          });
        });
      });
    }

    // 创建新场景
    function createScene() {
      if (newSceneName.value) {
        lightData.value.push({
          id: Cesium.createGuid(),
          type: LightListType.Scene,
          name: newSceneName.value,
          children: [],
        });
        newSceneName.value = "";
      } else {
        ElMessage.warning("未输入场景名");
      }

      // 更新一下快速查询字典
      updateQuickQuery();
    }

    // 删除灯
    function deleteLight(data: any) {
      const id = data.row.id;
      const indexs = quickQuery.value[id];
      const type = data.row.type;

      switch (type) {
        case LightListType.PointLight:
        case LightListType.SpotLight:
          removeLight(id);
          break;
        case LightListType.HorizontalLightBar:
        case LightListType.VerticalLightBar:
          removeLightBar(id);
          break;
      }

      lightData.value[indexs[0]].children[indexs[1]].children?.splice(
        indexs[2],
        1
      );

      // 重新更新快速查询字典
      updateQuickQuery();
    }

    function deleteGroud(data: any) {
      const id = data.row.id;
      const indexs = quickQuery.value[id];
      const type = data.row.type;
      data.row.children?.forEach((light: any) => {
        switch (type) {
          case LightListType.PointLightGroud:
          case LightListType.SpotLightGroud:
            removeLight(light.id);
            break;
          case LightListType.HorizontalLightBarGroud:
          case LightListType.VerticalLightBarGroud:
            removeLightBar(light.id);
            break;
        }
      });

      lightData.value[indexs[0]].children.splice(indexs[1], 1);

      // 重新更新快速查询字典
      updateQuickQuery();
    } 
    function deleteScene(data: any) {
      const id = data.row.id;
      const indexs = quickQuery.value[id];
      data.row.children?.forEach((groud: any) => {
        groud.children?.forEach((light: any) => {
          switch (light.type) {
            case LightListType.PointLight:
            case LightListType.SpotLight:
              removeLight(light.id);
              break;
            case LightListType.HorizontalLightBar:
            case LightListType.VerticalLightBar:
              removeLightBar(light.id);
              break;
          }
        });
      });
      lightData.value.splice(indexs[0], 1);
      // 重新更新快速查询字典
      updateQuickQuery();
    }

    // 添加组
    function addLightGroud(scene: any, type: LightListType) {
      const id = scene.id;
      const indexs = quickQuery.value[id];
      if (indexs) {
        const newGroud = {
          id: Cesium.createGuid(),
          type: type,
          name: "未命名" + type,
          color: "#ffffff",
          children: [],
          timeRanges: [0, 24], // 时间范围
          lightAnimation: [], // 灯光动画序列
        } as any;

        // 追加一些组的公共参数
        switch (type) {
          case LightListType.SpotLightGroud:
            // 聚光灯组公共参数为：强度、半径、外圆锥半径、内圆锥半径
            newGroud.intensity = 60;
            newGroud.radius = 100;
            newGroud.outerConeDegrees = 45;
            newGroud.innerConeDegrees = 10;
            break;
          case LightListType.PointLightGroud:
            // 点光源组公共参数为：强度、半径
            newGroud.intensity = 60;
            newGroud.radius = 100;
            break;
          case LightListType.HorizontalLightBarGroud:
            // 水平灯带公共参数为：是否闭合、灯带半径
            newGroud.lightBarClose = false;
            newGroud.lightBarRadius = 0.2;
            break;
          case LightListType.VerticalLightBarGroud:
            // 竖直灯带公共参数为：顶部高程、灯带半径
            newGroud.topHeight = 200;
            newGroud.lightBarRadius = 0.2;
            break;
        }

        // 只有场景能够添加光源组
        lightData.value[indexs[0]].children.push(newGroud);
        // 重新更新快速查询字典
        updateQuickQuery();
      }
    }

    function addLight(data: any) {
      const id = data.row.id;
      const indexs = quickQuery.value[id];

      let cueLight = {} as any;

      // 在灯光组下面添加时，初始化当前的类型
      if (data.row.type === LightListType.PointLightGroud) {
        cueLight.type = LightListType.PointLight;
        cueLight.name = "未命名光源";
        cueLight.intensity = data.row.intensity;
        cueLight.radius = data.row.radius;
        cueLight.color = data.row.color;
      } else if (data.row.type === LightListType.SpotLightGroud) {
        cueLight.type = LightListType.SpotLight;
        cueLight.name = "未命名光源";
        cueLight.intensity = data.row.intensity;
        cueLight.radius = data.row.radius;
        cueLight.color = data.row.color;
        cueLight.outerConeDegrees = data.row.outerConeDegrees;
        cueLight.innerConeDegrees = data.row.innerConeDegrees;
      }

      if (indexs) {
        // 当前选的类型是点光源，或者当前在点光源组里面
        if (cueLight.type === LightListType.PointLight) {
          const pointLightOption = {
            color: Cesium.Color.fromCssColorString(cueLight.color),
            intensity: cueLight.intensity,
            radius: cueLight.radius,
            id: Cesium.createGuid(),
            callback: (lightInfor: any) => {
              // 绘制完成的回调函数
              // 添加页面信息
              lightData.value[indexs[0]].children[indexs[1]].children?.push({
                id: lightInfor.id,
                type: cueLight.type,
                name: cueLight.name,
                position: cartesian3ToString(lightInfor.position),
              });

              // 重新更新快速查询字典
              updateQuickQuery();
            },
          };
          drawPointLight(pointLightOption);
        } else if (cueLight.type === LightListType.SpotLight) {
          const spotLightOption = {
            color: Cesium.Color.fromCssColorString(cueLight.color),
            intensity: cueLight.intensity,
            radius: cueLight.radius,
            id: Cesium.createGuid(),
            outerConeDegrees: cueLight.outerConeDegrees,
            innerConeDegrees: cueLight.innerConeDegrees,
            callback: (lightInfor: any) => {
              // 添加页面信息
              lightData.value[indexs[0]].children[indexs[1]].children?.push({
                id: spotLightOption.id,
                type: cueLight.type,
                name: cueLight.name,
                position: cartesian3ToString(lightInfor.position),
                directionPoint: cartesian3ToString(
                  lightInfor.directionPosition
                ),
              });
              // 重新更新快速查询字典
              updateQuickQuery();
            },
          };
          drawSpotLight(spotLightOption);
        }
      }
    }

    // 添加灯光动画序列
    function addLightAnimation(data: any) {
      data.lightAnimation.push({
        color: data.color,
        timeInterval: 10,
      });
    }

    // 编辑光照强度
    function lightIntensityChange(data: any) {
      data.children.forEach((light: any) => {
        changeLightIntensity(light.id, data.intensity);
      });
    }

    function lightRadiusChange2(data: any) {
      data.children.forEach((light: any) => {
        changeLightRadius(light.id, data.radius);
      });
    }

    function lightOuterConeDegreesChange2(data: any) {
      data.children.forEach((light: any) => {
        changeLightOuterConeDegrees(light.id, data.outerConeDegrees);
      });
    }

    function lightInnerConeDegreesChange2(data: any) {
      data.children.forEach((light: any) => {
        changeLightInnerConeDegrees(light.id, data.innerConeDegrees);
      });
    }

    // 灯光位置更改
    function lightPositionChange(data: any) {
      const light = getLightById(data.id);
      if (light) {
        light.value.position = stringToCartesian3(data.position);
      }
    }

    function lightDirectionPointChange(data: any) {
      const light = getLightById(data.id);
      if (light) {
        const directionPoint = stringToCartesian3(data.directionPoint);
        const direction = Cesium.Cartesian3.subtract(
          directionPoint,
          light.value.position,
          new Cesium.Cartesian3()
        );
        Cesium.Cartesian3.normalize(direction, direction);
        light.value.direction = direction;
      }
    }

    function startLightEditLight(data: any) {
      const indexs = quickQuery.value[data.id];
      const light =
        lightData.value[indexs[0]].children[indexs[1]].children[indexs[2]];
      startEditLight(
        data.id,
        (newPosition: Cesium.Cartesian3) => {
          light.position = cartesian3ToString(newPosition);
        },
        (newDirPosition: Cesium.Cartesian3) => {
          light.directionPoint = cartesian3ToString(newDirPosition);
        }
      );
    }

    function stopLightEditLight() {
      stopEditLight();
    }

    function drawHorizontalLightBar2(data: any) {
      const id = data.row.id;

      const indexs = quickQuery.value[id];

      const curLightBar = {
        type: LightListType.HorizontalLightBar,
        name: "未命名光源",
        id: Cesium.createGuid(),
        color: data.row.color,
        lightBarClose: data.row.lightBarClose,
        lightBarRadius: data.row.lightBarRadius,
      };

      drawLine((positions: Cesium.Cartesian3[]) => {
        addHorizontalExtents(
          curLightBar.lightBarClose ? [...positions, positions[0]] : positions,
          curLightBar.lightBarRadius,
          Cesium.Color.fromCssColorString(curLightBar.color),
          curLightBar.id
        );

        // 手动刷新ColorTexture
        updateColorTexture();
        // 手动刷新LightBarPrimitive
        updateLightBarPrimitive();

        const newLightBar = {
          id: curLightBar.id,
          type: curLightBar.type,
          name: curLightBar.name,
          positions: cartesian3ArrayToString(positions),
        } as any;

        lightData.value[indexs[0]].children[indexs[1]].children?.push(
          newLightBar
        );
        // 重新更新快速查询字典
        updateQuickQuery();
      });
    }

    function drawVerticalLightBar2(data: any) {
      const id = data.row.id;
      const indexs = quickQuery.value[id];

      const curLightBar = {
        type: LightListType.VerticalLightBar,
        name: "未命名光源",
        id: Cesium.createGuid(),
        color: data.row.color,
        lightBarRadius: data.row.lightBarRadius,
        topHeight: data.row.topHeight,
      };

      drawPoint((position: Cesium.Cartesian3) => {
        addVerticalExtents(
          position,
          curLightBar.topHeight,
          curLightBar.lightBarRadius,
          Cesium.Color.fromCssColorString(curLightBar.color),
          curLightBar.id
        );

        // 手动刷新ColorTexture
        updateColorTexture();
        // 手动刷新LightBarPrimitive
        updateLightBarPrimitive();

        const newLightBar = {
          id: curLightBar.id,
          type: curLightBar.type,
          name: curLightBar.name,
          bottomPoint: cartesian3ToString(position),
        } as any;

        lightData.value[indexs[0]].children[indexs[1]].children?.push(
          newLightBar
        );

        // 重新更新快速查询字典
        updateQuickQuery();
      });
    }

    // 水平灯带点位改变
    function onHorizontalLightBarPositionsChange(data: any) {
      updateHorizontalLightBar(data.id, {
        positions: stringToCartesian3Array(data.positions),
      });
      updateLightBarPrimitive();
    }

    // 重新绘制水平灯带点位
    function drawHorizontalLightBarPositions(data: any) {
      drawLine((positions: Cesium.Cartesian3[]) => {
        updateHorizontalLightBar(data.id, {
          positions: positions,
        });

        updateLightBarPrimitive();
        data.positions = cartesian3ArrayToString(positions);
      });
    }

    // 竖直灯带底部点位置改变
    function onVerticalLightBarBottomPointChange(data: any) {
      updateVerticalLightBar(data.id, {
        position: stringToCartesian3(data.bottomPoint),
      });
      updateLightBarPrimitive();
    }

    // 重新拾取竖直灯带底部点
    function pickVerticalLightBarBottomPoint(data: any) {
      drawPoint((point: Cesium.Cartesian3) => {
        updateVerticalLightBar(data.id, {
          position: point,
        });
        updateLightBarPrimitive();
        data.bottomPoint = cartesian3ToString(point);
      });
    }

    // 水平灯带组首尾闭合改变
    function updateHorizontalLightBarLightBarClose(data: any) {
      const children = data.children;
      const lightBarClose = data.lightBarClose;
      children.forEach((bar: any) => {
        const positions = stringToCartesian3Array(bar.positions);
        if (lightBarClose) {
          updateHorizontalLightBar(bar.id, {
            positions: [...positions, positions[0]],
          });
        } else {
          updateHorizontalLightBar(bar.id, {
            positions: positions,
          });
        }
      });

      // 调整完之后重新构建灯带primitive
      updateLightBarPrimitive();
    }

    function updateLightBarTopHeight(data: any) {
      const children = data.children;
      const topHeight = data.topHeight;
      children.forEach((bar: any) => {
        updateVerticalLightBar(bar.id, {
          topHeight: topHeight,
        });
      });

      // 调整完之后重新构建灯带primitive
      updateLightBarPrimitive();
    }

    // 灯带半径改变
    function updateLightBarRadius(data: any) {
      const children = data.children;
      const lightBarRadius = data.lightBarRadius;
      children.forEach((bar: any) => {
        if (bar.type === LightListType.VerticalLightBar) {
          updateVerticalLightBar(bar.id, {
            radius: lightBarRadius,
          });
        } else {
          updateHorizontalLightBar(bar.id, {
            radius: lightBarRadius,
          });
        }
      });

      // 调整完之后重新构建灯带primitive
      updateLightBarPrimitive();
    }

    function lightColorChange(data: any) {
      const type = data.type;
      const color = Cesium.Color.fromCssColorString(data.color);
      const children = data.children;
      children.forEach((light: any) => {
        switch (type) {
          case LightListType.PointLightGroud:
          case LightListType.SpotLightGroud:
            changeLightColor(light.id, color);
            break;
          case LightListType.HorizontalLightBarGroud:
          case LightListType.VerticalLightBarGroud:
            changeLightBarColor(light.id, color);
            break;
        }
      });
    }

    function save() {
      const data = {
        lightData: lightData.value,
        globalSettings: globalSettings.value,
      };

      saveJson(data, "light.json");
    }

    function load() {
      loadJson((json: any) => {
        loadJsonEvent.raiseEvent(json);
      });
    }

    // 给父组件调用的方法，移除当前场景所有的灯光
    function removeAllLight() {
      lightData.value.forEach((scene) => {
        scene.children?.forEach((groud: any) => {
          groud.children?.forEach((light: any) => {
            switch (light.type) {
              case LightListType.PointLight:
              case LightListType.SpotLight:
                removeLight(light.id);
                break;
              case LightListType.HorizontalLightBar:
              case LightListType.VerticalLightBar:
                removeLightBar(light.id);
                break;
            }
          });
        });
      });
      lightData.value = [];
      updateQuickQuery();
    }

    // 给父组件调用的方法，重新构建场景灯光
    function createFromLightData(newLightData: any) {
      lightData.value = newLightData;
      // 是否需要刷新lightBar，当场景没有LightBar的时候，不需要执行
      let needUpdateLightBar = false;

      lightData.value.forEach((scene) => {
        scene.children?.forEach((groud: any) => {
          groud.children?.forEach((light: any) => {
            switch (light.type) {
              case LightListType.PointLight:
                addPointLight({
                  position: stringToCartesian3(light.position),
                  color: Cesium.Color.fromCssColorString(groud.color),
                  intensity: groud.intensity,
                  radius: groud.radius,
                  id: light.id,
                });
                break;
              case LightListType.SpotLight:
                {
                  const position = stringToCartesian3(light.position);
                  const directionPoint = stringToCartesian3(
                    light.directionPoint
                  );
                  Cesium.Cartesian3.subtract(
                    directionPoint,
                    position,
                    directionPoint
                  );
                  Cesium.Cartesian3.normalize(directionPoint, directionPoint);
                  addSpotLight({
                    position: position,
                    direction: directionPoint,
                    color: Cesium.Color.fromCssColorString(groud.color),
                    intensity: groud.intensity,
                    radius: groud.radius,
                    id: light.id,
                    outerConeDegrees: groud.outerConeDegrees,
                    innerConeDegrees: groud.innerConeDegrees,
                  });
                }
                break;
              case LightListType.HorizontalLightBar:
                {
                  let positions = stringToCartesian3Array(light.positions);
                  if (groud.lightBarClose) {
                    positions = [...positions, positions[0]];
                  }
                  addHorizontalExtents(
                    positions,
                    groud.lightBarRadius,
                    Cesium.Color.fromCssColorString(groud.color),
                    light.id
                  );
                  needUpdateLightBar = true;
                }
                break;
              case LightListType.VerticalLightBar:
                addVerticalExtents(
                  stringToCartesian3(light.bottomPoint),
                  groud.topHeight,
                  groud.lightBarRadius,
                  Cesium.Color.fromCssColorString(groud.color),
                  light.id
                );
                needUpdateLightBar = true;
                break;
            }
          });
        });
      });

      if (needUpdateLightBar) {
        updateColorTexture();
        updateLightBarPrimitive();
      }

      updateQuickQuery();
    }

    expose({
      removeAllLight,
      createFromLightData,
    });

    onUnmounted(() => {});

    return {
      createScene,
      newSceneName,
      lightGrouds,
      lightData,
      props,
      addLightPopoverHideRef,
      addLightGroud,
      addLight,
      LightListType,
      timeRangeMarks,
      addLightAnimation,
      lightIntensityChange,
      lightRadiusChange2,
      lightOuterConeDegreesChange2,
      lightInnerConeDegreesChange2,
      lightPositionChange,
      lightDirectionPointChange,
      startLightEditLight,
      stopLightEditLight,
      quickQuery,
      drawHorizontalLightBar2,
      drawVerticalLightBar2,
      onHorizontalLightBarPositionsChange,
      drawHorizontalLightBarPositions,
      onVerticalLightBarBottomPointChange,
      pickVerticalLightBarBottomPoint,
      updateHorizontalLightBarLightBarClose,
      updateLightBarRadius,
      deleteLight,
      deleteGroud, 
      deleteScene, 
      lightColorChange,
      updateLightBarTopHeight,
      save,
      load,
    };
  },
});  
</script>

<style lang="less" scoped>
.el-table .el-table__placeholder {
  display: none !important;  
}
.multi-light-list {
  padding: 0px;
}
.light-list-button {
  padding: 5px 0px;
}
.multi-light-block {
  display: flex;
  align-items: center;
}
.from-item-dom {
  display: flex;
  margin: 2px 4px;
  align-items: center;
}
</style>
