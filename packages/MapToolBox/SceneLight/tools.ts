import * as Cesium from "@onemapkit/cesium";
import { SceneBeautification } from "@onemapkit/cesium-tools";
import LightBar from "./LightBar";

let sceneBeautification: SceneBeautification | undefined;
let sceneBeautificationPositionEditEvent: any;
let sceneBeautificationDirictionEditEvent: any;
let lightBar: LightBar | undefined;
let _viewer: Cesium.Viewer;
/**
 * 初始化灯光美化插件
 * @param {Cesium.Viewer} viewer 视图对象
 */
export function initSceneBeautification(viewer: Cesium.Viewer) {
  _viewer = viewer;

  if (!sceneBeautification) {
    sceneBeautification = new SceneBeautification(viewer);
  }

  if (!lightBar && sceneBeautification) {
    lightBar = new LightBar(viewer, sceneBeautification);
  }
}

/**
 * 销毁灯光美化插件
 */
export function destroySceneBeautification() {
  if (sceneBeautification) {
    sceneBeautification.destroy();
    sceneBeautification = undefined;
  }

  if (lightBar) {
    lightBar.destroy();
    lightBar = undefined;
  }
}

// 改变泛光半径
export function changeBloomRadius(bloomRadius: number) {
  if (sceneBeautification) {
    sceneBeautification.bloomRadius = bloomRadius;
  }
}

// 改变泛光强度
export function changeBloomStrength(bloomStrength: number) {
  if (sceneBeautification) {
    sceneBeautification.bloomStrength = bloomStrength;
  }
}

/**
 * 绘制点光源
 * @param {Object} options 包含以下参数的对象
 * @param {Cesium.Color} [options.color=Cesium.Color.WHITE] 点光源的颜色
 * @param {Number} [options.intensity=10] 点光源的强度
 * @param {Number} [options.radius=10] 点光源的光照范围
 * @param {String} [options.id] 点光源的ID
 * @param {Function} [options.callback] 绘制完成后执行的回调，回调方法包含光源的信息
 * @returns {String | undefined} 当前图形的id，如果id重复无法创建，则返回undefined
 */
export function drawPointLight(options: any) {
  if (sceneBeautification) {
    return sceneBeautification.drawPointLight(options);
  }
}

/**
 * @param {Object} options 包含以下参数的对象
 * @param {Cesium.Cartesian3} options.position 光源位置
 * @param {Cesium.Cartesian3} options.direction 光源照射方向
 * @param {Cesium.Color} [options.color=Cesium.Color.WHITE] 点光源颜色
 * @param {String} [options.id] 点光源的ID
 * @param {number} [options.intensity=10] 光源强度
 * @param {number} [options.radius=1000] 光源范围
 * @param {number} [options.outerConeDegrees=45.0] 聚光灯的外圆锥角度的一半，角度制。
 * @param {number} [options.innerConeDegrees=10.0] 聚光灯的内圆锥角度的一半，角度制。
 * @returns {String | undefined} 当前图形的id，如果id重复无法创建，则返回undefined
 */
export function addSpotLight(options: any) {
  if (sceneBeautification) {
    return sceneBeautification.addSpotLight(options);
  }
}

/**
 * 通过参数添加点光源
 * @param {Object} options 包含以下参数的对象
 * @param {Cesium.Cartesian3} options.position 点光源位置
 * @param {Cesium.Color} [options.color=Cesium.Color.WHITE] 点光源的颜色
 * @param {Number} [options.intensity=10] 点光源的强度
 * @param {Number} [options.radius=1000] 点光源的光照范围
 * @param {String} [options.id] 点光源的ID
 * @returns {String | undefined} 当前图形的id，如果id重复无法创建，则返回undefined
 */
export function addPointLight(options: any) {
  if (sceneBeautification) {
    return sceneBeautification.addPointLight(options);
  }
}

/**
 * 绘制聚光灯
 * @param {Object} options 包含以下参数的对象
 * @param {Cesium.Color} [options.color=Cesium.Color.WHITE] 点光源颜色
 * @param {String} [options.id] 点光源的ID
 * @param {number} [options.intensity=10] 光源强度
 * @param {number} [options.radius=100] 光源范围
 * @param {number} [options.outerConeDegrees=45.0] 聚光灯的外圆锥角度的一半，角度制。
 * @param {number} [options.innerConeDegrees=10.0] 聚光灯的内圆锥角度的一半，角度制。
 * @param {Function} [options.callback] 绘制完成后执行的回调，回调方法包含光源的信息
 * @returns {String | undefined} 当前图形的id，如果id重复无法创建，则返回undefined
 */
export function drawSpotLight(options: any) {
  if (sceneBeautification) {
    return sceneBeautification.drawSpotLight(options);
  }
}

/**
 * 设置光源的强度
 * @param {string} id id
 * @param {number} intensity 强度
 */
export function changeLightIntensity(id: string, intensity: number) {
  if (sceneBeautification) {
    sceneBeautification.changeLightIntensity(id, intensity);
  }
}

/**
 * 设置光源的半径
 * @param {string} id id
 * @param {number} intensity 强度
 */
export function changeLightRadius(id: string, radius: number) {
  if (sceneBeautification) {
    sceneBeautification.changeLightRadius(id, radius);
  }
}

/**
 * 设置光源的颜色
 * @param {string} id id
 * @param {Cesium.Color} color 颜色
 */
export function changeLightColor(id: string, color: Cesium.Color) {
  if (sceneBeautification) {
    sceneBeautification.changeLightColor(id, color);
  }
}

/**
 * 设置聚光灯内圆锥角度
 * @param {string} id id
 * @param {number} innerConeDegrees 内圆锥角度
 */
export function changeLightInnerConeDegrees(
  id: string,
  innerConeDegrees: number
) {
  if (sceneBeautification) {
    sceneBeautification.changeLightInnerConeDegrees(id, innerConeDegrees);
  }
}

/**
 * 设置聚光灯外圆锥角度
 * @param {string} id id
 * @param {number} outerConeDegrees 外圆锥角度
 */
export function changeLightOuterConeDegrees(
  id: string,
  outerConeDegrees: number
) {
  if (sceneBeautification) {
    sceneBeautification.changeLightOuterConeDegrees(id, outerConeDegrees);
  }
}

/**
 * 根据id获取光源
 * @param {String} id id
 */
export function getLightById(id: string) {
  if (sceneBeautification) {
    return (sceneBeautification as any)._lines[id];
  }
}

/**
 * 开始编辑灯光
 * @param {string} id id
 */
export function startEditLight(
  id: string,
  positionEditCallBack: Function,
  dirictionEditCallBack: Function
) {
  // 注销之前的监听事件
  if (sceneBeautificationPositionEditEvent) {
    sceneBeautificationPositionEditEvent();
    sceneBeautificationPositionEditEvent = undefined;
  }
  if (sceneBeautificationDirictionEditEvent) {
    sceneBeautificationDirictionEditEvent();
    sceneBeautificationDirictionEditEvent = undefined;
  }
  if (sceneBeautification) {
    sceneBeautification.startEditLight(id);

    // 光源位置发生编辑时，进行回调
    sceneBeautificationPositionEditEvent = (
      sceneBeautification as any
    )._positionEdit.editEvent.addEventListener((newPosition: any) => {
      positionEditCallBack(newPosition);
    });

    // 光源看向的位置发生编辑时，进行回调
    sceneBeautificationDirictionEditEvent = (
      sceneBeautification as any
    )._dirictionEdit.editEvent.addEventListener((newPosition: any) => {
      dirictionEditCallBack(newPosition);
    });
  }
}

/**
 * 停止编辑灯光
 */
export function stopEditLight() {
  // 注销之前的监听事件
  if (sceneBeautificationPositionEditEvent) {
    sceneBeautificationPositionEditEvent();
    sceneBeautificationPositionEditEvent = undefined;
  }
  if (sceneBeautificationDirictionEditEvent) {
    sceneBeautificationDirictionEditEvent();
    sceneBeautificationDirictionEditEvent = undefined;
  }

  if (sceneBeautification) {
    sceneBeautification.stopEditLight();
  }
}

/**
 * 移除灯光
 * @param {String} id id
 */
export function removeLight(id: string) {
  if (sceneBeautification) {
    sceneBeautification.removeLight(id);
  }
}

/**
 * 将字符串转成笛卡尔坐标
 * @param {string} positionStr 字符串
 * @returns {Cesium.Cartesian3}
 */
export function stringToCartesian3(positionStr: string) {
  const positionStrArr = positionStr.split(",");
  const positionNumArr = positionStrArr.map((p) => Number(p));
  return Cesium.Cartesian3.fromDegrees(
    positionNumArr[0],
    positionNumArr[1],
    positionNumArr[2]
  );
}

/**
 * 将笛卡尔坐标转成字符串
 * @param {Cesium.Cartesian3} position 笛卡尔坐标
 * @returns {string}
 */
export function cartesian3ToString(position: Cesium.Cartesian3) {
  const cartographic = Cesium.Cartographic.fromCartesian(position);
  return `${Cesium.Math.toDegrees(
    cartographic.longitude
  )},${Cesium.Math.toDegrees(cartographic.latitude)},${cartographic.height}`;
}

/**
 * 将笛卡尔数组转成字符串
 * @param {Cesium.Cartesian3[]} positions 笛卡尔坐标数组
 * @return {string}
 */
export function cartesian3ArrayToString(positions: Cesium.Cartesian3[]) {
  let str = "";
  positions.forEach((p, index) => {
    str += cartesian3ToString(p);
    if (index < positions.length - 1) {
      str += ",";
    }
  });
  return str;
}

/**
 * 将字符串转成笛卡尔坐标数组
 * @param {string} positionsStr 字符串
 * @returns {Cesium.Cartesian3[]}
 */
export function stringToCartesian3Array(positionsStr: string) {
  const positionStrArr = positionsStr.split(",");
  const positionNumArr = positionStrArr.map((p) => Number(p));

  const pointLength = positionStrArr.length / 3;

  const positions:any = [];

  for (let i = 0; i < pointLength; i++) {
    positions.push(
      Cesium.Cartesian3.fromDegrees(
        positionNumArr[i * 3],
        positionNumArr[i * 3 + 1],
        positionNumArr[i * 3 + 2]
      )
    );
  }

  return positions;
}

export function removeLightBar(id: string) {
  if (lightBar) {
    lightBar.removeLightBar(id);
  }
}
export function addVerticalExtents(
  position: Cesium.Cartesian3,
  topHeight: number,
  radius: number,
  color: Cesium.Color,
  id: string
) {
  if (lightBar) {
    lightBar.addVerticalExtents(position, topHeight, radius, color, id);
  }
}

export function updateHorizontalLightBar(id: string, options?: any) {
  if (lightBar) {
    lightBar.updateHorizontalLightBar(id, options);
  }
}

export function updateVerticalLightBar(id: string, options?: any) {
  if (lightBar) {
    lightBar.updateVerticalLightBar(id, options);
  }
}

export function changeLightBarColor(id: string, color: Cesium.Color) {
  if (lightBar) {
    const colorInfor = lightBar.lightBarIdColorMap[id];
    if (colorInfor && !Cesium.Color.equals(colorInfor.color, color)) {
      // 颜色不同才刷新纹理
      colorInfor.color = color;
      lightBar.updateColorTexture();
    }
  }
}

export function updateLightBarPrimitive() {
  if (lightBar) {
    lightBar.updatePrimitive();
  }
}

export function updateColorTexture() {
  if (lightBar) {
    lightBar.updateColorTexture();
  }
}

export function addHorizontalExtents(
  positions: Cesium.Cartesian3[],
  radius: number,
  color: Cesium.Color,
  id: string
) {
  if (lightBar) {
    lightBar.addHorizontalExtents(positions, radius, color, id);
  }
}

// 光源类型的枚举
export enum LightListType {
  Scene = "场景",
  PointLightGroud = "点光源组",
  PointLight = "点光源",
  SpotLightGroud = "聚光灯组",
  SpotLight = "聚光灯",
  VerticalLightBarGroud = "竖直灯带组",
  VerticalLightBar = "竖直灯带",
  HorizontalLightBarGroud = "水平灯带组",
  HorizontalLightBar = "水平灯带",
}

function click(node: any) {
  try {
    node.dispatchEvent(new MouseEvent("click"));
  } catch (e) {
    var evt = document.createEvent("MouseEvents");
    evt.initMouseEvent(
      "click",
      true,
      true,
      window,
      0,
      0,
      0,
      80,
      20,
      false,
      false,
      false,
      false,
      0,
      null
    );
    node.dispatchEvent(evt);
  }
}

export function saveJson(data: any, fileName: string) {
  const value = JSON.stringify(data, undefined, 4);
  const blob = new Blob([value], { type: "text/json" });
  const a = document.createElement("a");
  a.download = fileName;
  a.href = URL.createObjectURL(blob);
  a.dataset.downloadurl = ["text/json", a.download, a.href].join(":");
  click(a);
}

export function loadJson(callback: Function) {
  const fileInput = document.createElement("input");
  // document.appendChild(fileInput);
  _viewer.canvas.parentElement?.appendChild(fileInput);
  fileInput.accept = ".json";
  fileInput.type = "file";
  click(fileInput);

  // 点击取消/关闭上传页面，都移除dom元素
  fileInput.addEventListener("cancel", () => {
    _viewer.canvas.parentElement?.removeChild(fileInput);
  });

  fileInput.addEventListener("change", (event: any) => {
    const file = event.target.files[0];

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result;
      if (content) {
        try {
          const jsonData = JSON.parse(content as any);
          // 执行回调
          callback(jsonData);
        } catch {
          console.error("json解析失败");
        }
      }
      _viewer.canvas.parentElement?.removeChild(fileInput);
    };

    // 读取失败也移除dom元素
    reader.onerror = () => {
      _viewer.canvas.parentElement?.removeChild(fileInput);
    };

    reader.readAsText(file);
  });
}
