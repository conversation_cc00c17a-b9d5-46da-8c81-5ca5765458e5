import * as Cesium from "@onemapkit/cesium";
import { SceneBeautification } from "@onemapkit/cesium-tools";

// 灯带侧面形状
function computeCircle(radius: number) {
  const positions = [];
  for (let i = 0; i < 360; i += 30) {
    const radians = Cesium.Math.toRadians(i);
    positions.push(
      new Cesium.Cartesian2(
        radius * Math.cos(radians),
        radius * Math.sin(radians)
      )
    );
  }
  return positions;
}

function getNightProgrammePositions(
  positions: Cesium.Cartesian3[],
  radius: number
) {
  const newPositions = positions.map((p) => {
    const cartographic = Cesium.Cartographic.fromCartesian(p);
    return Cesium.Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      cartographic.height - radius
    );
  });

  if (newPositions[0].equals(newPositions[newPositions.length - 1])) {
    // 如果首尾相连，则返回首尾相连的格式
    const first = Cesium.Cartesian3.midpoint(
      newPositions[newPositions.length - 1],
      newPositions[newPositions.length - 2],
      new Cesium.Cartesian3()
    );

    const final = Cesium.Cartesian3.midpoint(
      newPositions[0],
      newPositions[1],
      new Cesium.Cartesian3()
    );

    return [first, ...newPositions, final];
  } else {
    // 如果首尾不相连，则直接返回
    return newPositions;
  }
}

// 计算纹理大小
function computeTextureResolution(pixelsNeeded: number) {
  const maxSize = (Cesium as any).ContextLimits.maximumTextureSize;
  const resultX = Math.min(pixelsNeeded, maxSize);
  const resultY = Math.ceil(pixelsNeeded / resultX);
  return {
    x: resultX,
    y: resultY,
  };
}

// 补全纹理大小（翻转Y轴需要补全）
function completionBuffer(bufferView: number[], resolution: any) {
  const length = resolution.x * resolution.y;
  const bufferViewLength = bufferView.length / 4;
  for (let i = bufferViewLength; i < length; i++) {
    bufferView.push(0);
    bufferView.push(0);
    bufferView.push(0);
    bufferView.push(0);
  }
}

export default class LightBar {
  private _viewer: Cesium.Viewer;
  private _geometryInstances: any[];

  private _lightBarIdColorTexture: any;
  private _lightBarColorTexture: any;

  private _primitiveCollection: Cesium.PrimitiveCollection;
  private _nextIdColor: Uint32Array;
  private _lightBarLength: number;
  private _sceneBeautification: SceneBeautification;

  public lightBarMaterial: Cesium.Material;
  public lightBarAppearance: Cesium.MaterialAppearance;
  public lightBarIdColorMap: any;

  constructor(viewer: Cesium.Viewer, sceneBeautification: SceneBeautification) {
    this._viewer = viewer;
    this._geometryInstances = [];
    this._nextIdColor = new Uint32Array(1);
    this.lightBarIdColorMap = {};
    this._lightBarLength = 0;
    this._sceneBeautification = sceneBeautification;

    // 创建私有的绘图空间
    this._primitiveCollection = new Cesium.PrimitiveCollection();
    viewer.scene.primitives.add(this._primitiveCollection);

    this.lightBarMaterial = new Cesium.Material({
      fabric: {
        type: "lightBarMaterial",
        source: `
          czm_material czm_getMaterial(czm_materialInput materialInput){
            czm_material material = czm_getDefaultMaterial(materialInput);
            return material;
          }`,
      },
    });

    (this.lightBarMaterial as any)._uniforms.u_lightBarColorTexture = () => {
      if (this._lightBarColorTexture) {
        return this._lightBarColorTexture;
      }
      return (viewer as any).scene.context.defaultTexture;
    };
    (this.lightBarMaterial as any)._uniforms.u_lightBarIdColorTexture = () => {
      if (this._lightBarIdColorTexture) {
        return this._lightBarIdColorTexture;
      }
      return (viewer as any).scene.context.defaultTexture;
    };
    (this.lightBarMaterial as any)._uniforms.u_lightBarLength = () => {
      return this._lightBarLength;
    };
    (this.lightBarMaterial as any)._uniforms.u_maximumTextureSize = () => {
      return (Cesium as any).ContextLimits.maximumTextureSize;
    };

    this.lightBarAppearance = new Cesium.MaterialAppearance({
      material: this.lightBarMaterial,
      fragmentShaderSource: `
              in vec3 v_positionEC;
              in vec2 v_st;
              in vec4 v_color;
          
              void main()
              {
                  vec3 positionToEyeEC = -v_positionEC;
          
                  czm_materialInput materialInput;
                  materialInput.positionToEyeEC = positionToEyeEC;
                  materialInput.st = v_st;
                  czm_material material = czm_getMaterial(materialInput);

                  out_FragColor = vec4(v_color.xyz, 1.0);
              }
            `,
      vertexShaderSource: `
              in vec3 position3DHigh;
              in vec3 position3DLow;
              in vec2 st;
              in vec4 color;
              in float batchId;
          
              out vec3 v_positionEC;
              out vec2 v_st;
              out vec4 v_color;

              uniform sampler2D u_lightBarColorTexture;
              uniform sampler2D u_lightBarIdColorTexture;
              uniform int u_lightBarLength;
              uniform int u_maximumTextureSize;
          
              void main()
              {
                  vec4 p = czm_computePosition();
          
                  v_positionEC = (czm_modelViewRelativeToEye * p).xyz;      // position in eye coordinates
                  v_st = st;
                  v_color = vec4(0.0, 1.0, 0.0, 1.0);
                  
                  for(int i = 0; i < 100000; i++){
                    if(i > u_lightBarLength){
                      break;  
                    }

                    int u = min(u_maximumTextureSize, i);
                    int v = int(ceil(float(i) / float(u_maximumTextureSize)));
                    if(v > 0){
                      // 从0开始
                      v -= 1;
                    }

                    vec4 idColor = texelFetch(u_lightBarIdColorTexture, ivec2(u, v), 0);
                    if(idColor.x == color.x && idColor.y == color.y && idColor.z == color.z && idColor.w == color.w){
                      v_color = texelFetch(u_lightBarColorTexture, ivec2(u, v), 0);
                      break;  
                    }
                  }
                  gl_Position = czm_modelViewProjectionRelativeToEye * p;
              }
            `,
      flat: true,
    });
  }

  /**
   * 刷新颜色纹理
   */
  updateColorTexture() {
    if (this._lightBarColorTexture) {
      this._lightBarColorTexture.destroy();
      this._lightBarColorTexture = undefined;
    }

    if (this._lightBarIdColorTexture) {
      this._lightBarIdColorTexture.destroy();
      this._lightBarIdColorTexture = undefined;
    }

    const lightBarColorValues = [];
    const lightBarIdColorValues = [];
    for (let key in this.lightBarIdColorMap) {
      const infor = this.lightBarIdColorMap[key];
      const { idColor, color } = infor;
      lightBarIdColorValues.push(
        idColor.red,
        idColor.green,
        idColor.blue,
        idColor.alpha
      );
      lightBarColorValues.push(color.red, color.green, color.blue, color.alpha);
    }

    // 计算纹理大小
    const requiredResolution = computeTextureResolution(
      lightBarColorValues.length / 4
    );
    // 补全纹理
    completionBuffer(lightBarColorValues, requiredResolution);
    completionBuffer(lightBarIdColorValues, requiredResolution);

    const context = (this._viewer as any).scene.context;

    this._lightBarColorTexture = new (Cesium as any).Texture({
      context: context,
      source: {
        arrayBufferView: new Float32Array(lightBarColorValues),
        width: requiredResolution.x,
        height: requiredResolution.y,
      },
      pixelDatatype: Cesium.PixelDatatype.FLOAT,
      pixelFormat: Cesium.PixelFormat.RGBA,
      flipY: false,
    });

    this._lightBarIdColorTexture = new (Cesium as any).Texture({
      context: context,
      source: {
        arrayBufferView: new Float32Array(lightBarIdColorValues),
        width: requiredResolution.x,
        height: requiredResolution.y,
      },
      pixelDatatype: Cesium.PixelDatatype.FLOAT,
      pixelFormat: Cesium.PixelFormat.RGBA,
      flipY: false,
    });
  }

  /**
   * 刷新Primitive
   */
  updatePrimitive() {
    this._primitiveCollection.removeAll();
    const geometryInstances = this._geometryInstances.map((gi) => {
      return new Cesium.GeometryInstance({
        id: gi.id,
        geometry: gi.geometry,
        modelMatrix: gi.modelMatrix,
        attributes: {
          color: gi.color,
        },
      });
    });

    const primitive = this._primitiveCollection.add(
      new Cesium.Primitive({
        geometryInstances: geometryInstances,
        appearance: this.lightBarAppearance,
        asynchronous: false,
        releaseGeometryInstances: false,
      })
    );

    this._sceneBeautification.bloomPrimitive(primitive);
    
    this._lightBarLength = this._geometryInstances.length;
  }

  removeLightBar(id: string) {
    if(this.lightBarIdColorMap){
      delete this.lightBarIdColorMap[id];

      const index = this._geometryInstances.findIndex(gi => gi.id === id);

      this._geometryInstances.splice(index, 1);

      this.updateColorTexture();
      this.updatePrimitive();
    }
  }

  /**
   * 追加水平灯带
   * @param {Cesium.Cartesian3[]} positions 位置
   * @param {number} radius 半径
   * @param {Cesium.Color} color 颜色
   * @param {string} id id
   */
  addHorizontalExtents(
    positions: Cesium.Cartesian3[],
    radius: number,
    color: Cesium.Color,
    id: string
  ) {
    ++this._nextIdColor[0];
    const idColor = Cesium.Color.fromRgba(this._nextIdColor[0]);

    const geometry = new Cesium.PolylineVolumeGeometry({
      polylinePositions: getNightProgrammePositions(positions, radius),
      shapePositions: computeCircle(radius),
    });

    // 存储对应的IDColoer和实际的Color
    this.lightBarIdColorMap[id] = {
      idColor,
      color,
    };

    // 存储创建geometryInstance所需的信息（不存储geometryInstance，因为移除时会被销毁）
    this._geometryInstances.push({
      id: id,
      geometry: Cesium.PolylineVolumeGeometry.createGeometry(geometry),
      radius,
      positions,
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(idColor),
    });

    // 不默认刷新，手动控制
    // this.updateColorTexture();
    // this.updatePrimitive();
  }

  addVerticalExtents(
    position: Cesium.Cartesian3,
    topHeight: number,
    radius: number,
    color: Cesium.Color,
    id: string
  ) {
    ++this._nextIdColor[0];
    const idColor = Cesium.Color.fromRgba(this._nextIdColor[0]);

    const positionOnEllipsoid = Cesium.Cartographic.fromCartesian(position);
    const length = topHeight - positionOnEllipsoid.height;

    const modelMatrix = Cesium.Matrix4.multiplyByTranslation(
      Cesium.Transforms.eastNorthUpToFixedFrame(position),
      new Cesium.Cartesian3(0.0, 0.0, length * 0.5),
      new Cesium.Matrix4()
    );

    const geometry = new Cesium.CylinderGeometry({
      length: length,
      topRadius: radius,
      bottomRadius: radius,
    });

    // 存储对应的IDColoer和实际的Color
    this.lightBarIdColorMap[id] = {
      idColor,
      color,
    };

    // 存储创建geometryInstance所需的信息（不存储geometryInstance，因为移除时会被销毁）
    this._geometryInstances.push({
      id: id,
      geometry: Cesium.CylinderGeometry.createGeometry(geometry),
      radius,
      topHeight,
      position,
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(idColor),
      modelMatrix: modelMatrix,
    });

    // 不默认刷新，手动控制
    // this.updateColorTexture();
    // this.updatePrimitive();
  }


  /**
   * 更新水平灯带
   * @param {string} id id
   * @param {Object} options 更新的信息
   * @returns 
   */
  updateHorizontalLightBar(id: string, options: any = {}) {
    for (let i = 0; i < this._lightBarLength; i++) {
      const geometryInstance = this._geometryInstances[i];
      if (id === geometryInstance.id) {
        const radius = Cesium.defaultValue(options.radius, geometryInstance.radius);
        const positions = Cesium.defaultValue(options.positions, geometryInstance.positions);
        const geometry = new Cesium.PolylineVolumeGeometry({
          polylinePositions: getNightProgrammePositions(positions, radius),
          shapePositions: computeCircle(radius),
        });
        geometryInstance.geometry = Cesium.PolylineVolumeGeometry.createGeometry(geometry);
        geometryInstance.radius = radius;
        geometryInstance.positions = positions;
        return;
      }
    }
  }

  /**
   * 更新竖直灯带
   * @param {string} id id
   * @param {Object} options 更新的信息
   * @returns 
   */
  updateVerticalLightBar(id: string, options: any = {}) {
    for (let i = 0; i < this._lightBarLength; i++) {
      const geometryInstance = this._geometryInstances[i];
      if (id === geometryInstance.id) {
        const radius = Cesium.defaultValue(options.radius, geometryInstance.radius);
        const position = Cesium.defaultValue(options.position, geometryInstance.position);
        const topHeight = Cesium.defaultValue(options.topHeight, geometryInstance.topHeight);
        const positionOnEllipsoid = Cesium.Cartographic.fromCartesian(position);
        const length = topHeight - positionOnEllipsoid.height;
        const modelMatrix = Cesium.Matrix4.multiplyByTranslation(
          Cesium.Transforms.eastNorthUpToFixedFrame(position),
          new Cesium.Cartesian3(0.0, 0.0, length * 0.5),
          new Cesium.Matrix4()
        );
        const geometry = new Cesium.CylinderGeometry({
          length: length,
          topRadius: radius,
          bottomRadius: radius,
        });
        geometryInstance.geometry = Cesium.CylinderGeometry.createGeometry(geometry);
        geometryInstance.modelMatrix = modelMatrix;
        geometryInstance.radius = radius;
        geometryInstance.position = position;
        geometryInstance.topHeight = topHeight;
        return;
      }
    }
  }

  destroy() {
    // 移除primitiveCollection
    this._viewer.scene.primitives.remove(this._primitiveCollection);
    // 销毁纹理释放内存
    if (this._lightBarColorTexture) {
      this._lightBarColorTexture.destroy();
      this._lightBarColorTexture = undefined;
    }
    if (this._lightBarIdColorTexture) {
      this._lightBarIdColorTexture.destroy();
      this._lightBarIdColorTexture = undefined;
    }

    Cesium.destroyObject(this);
  }
}
