<template>
  <el-table
    :data="roamerData.viewPointRoaming"
    stripe
    border
    size="small"
    :show-header="false"
    :highlight-current-row="true"
    :header-cell-style="{ 'text-align': 'center' }"
    row-key="id"
    :expand-row-keys="[expandRowKey]"
  >
    <el-table-column type="expand">
      <template #default="pathdata">
        <div
          style="
            margin: 0px 1px 4px 30px;
            padding: 0px 0px 0px 4px;
            border: 1px solid rgb(235, 238, 245);
          "
        >
          <el-form>
            <!-- 视点漫游 -->
            <el-form-item
              class="form-item"
              style="margin: 0px 4px 0px 4px"
              label="视点名称"
            >
              <div style="width: 100%" class="from-item-dom">
                <el-input
                  v-model="curName"
                  style="width: 100%; margin: 0px"
                  placeholder="输入视点名称"
                >
                </el-input>
                <span style="white-space: nowrap; margin: 0px 3px 0px 5px"
                  >间隔时间</span
                >
                <el-input-number
                  v-model="viewPointDuration"
                  :min="0"
                  :step="1"
                  :controls="false"
                  style="width: 70px; max-width: 70px; min-width: 70px"
                />
                <el-button
                  style="margin: 1px"
                  type="success"
                  plain
                  @click="addViewPoint(pathdata.row)"
                >
                  添加当前视点
                </el-button>
              </div>
            </el-form-item>
          </el-form>

          <div style="overflow-x: hidden; margin-left: 30px">
            <!--2级表 group:SceneTable.row.children 每行都会有一个对应的二级表 -->
            <el-table
              style="margin: 0px 0px 0px 0px"
              :data="pathdata.row.viewPoints"
              row-key="id"
              stripe
              :show-header="false"
              border
              size="small"
              :highlight-current-row="true"
              :header-cell-style="{ 'text-align': 'center' }"
              :indent="50"
            >
              <!--2级表 漫游线路点 RoamerPointClass -->
              <el-table-column label="节点名称" prop="name">
                <template #default="pointsTable">
                  <div style="cursor: pointer; width: 50%">
                    {{ pointsTable.row.name }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="持续时间"
                prop="operations"
                :width="160"
                align="center"
              >
                <template #default="pointsTable">
                  <el-input-number
                    style="cursor: pointer; width: 100%"
                    v-model="pointsTable.row.duration"
                  />
                </template>
              </el-table-column>

              <!--2级表 操作   ****************     2级表 操作  -->
              <el-table-column
                label="操作"
                prop="operations"
                :width="190"
                align="center"
              >
                <template #default="pointsTable">
                  <!-- 组参数配置 -->
                  <el-button
                    style="margin: 1px"
                    type="primary"
                    link
                    @click="zoomTo(pointsTable.row)"
                  >
                    定位视点
                  </el-button>
                  <el-button
                    style="margin: 1px"
                    type="primary"
                    link
                    @click="reasetViewPoint(pointsTable.row)"
                  >
                    重置视点
                  </el-button>
                  <el-button
                    style="margin: 1px"
                    type="warning"
                    link
                    @click="deleteViewPoint(pathdata.row, pointsTable.$index)"
                  >
                    删除视点
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>
    </el-table-column>

    <!-- 1级表 类型 -->
    <el-table-column :width="90">
      <template #header>
        <span> 类型 </span>
      </template>
      <template>
        <div>视点漫游</div>
      </template>
    </el-table-column>
    <!-- 1级表  线路名称 -->
    <el-table-column>
      <template #header>
        <span> 线路名称 </span>
      </template>
      <template #default="pathdata">
        <el-tooltip effect="light" placement="top">
          <div style="cursor: pointer; width: 100%">
            {{ pathdata.row.name }}
          </div>
        </el-tooltip>
      </template>
    </el-table-column>
    <!-- 1级表 操作   ****************     1级表 操作：保存修改  删除场景-->
    <el-table-column label="操作" prop="operations" :width="190" align="center">
      <template #default="pathdata">
        <!-- 编辑工具 -->
        <el-button
          style="margin: 1px"
          type="warning"
          link
          @click="changeExpandRowKey(pathdata.row)"
          >编辑
        </el-button>
        <el-button
          style="margin: 1px"
          type="warning"
          link
          @click="removeCurData(pathdata.row)"
        >
          删除
        </el-button>
        <el-button
          style="margin: 1px"
          type="primary"
          link
          @click="savePathdataJson(pathdata.row)"
        >
          导出
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup lang="ts">
//@ts-ignore
import { ref, type PropType, onMounted } from "vue";
import { getOnemap } from "../../onemapkit";
import { createGuid, Camera, Cartesian3 } from "@onemapkit/cesium";
import { defaultRoamerData } from "./ViewRoamerClass";
import type {RoamerData} from "./ViewRoamerClass";
import { saveJson } from "./tools";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  roamerData: {
    type: Object as PropType<RoamerData>,
    default: defaultRoamerData,
    require: true,
  },
});
const _Onemap = getOnemap(props.MapControlName);

const roamerData = ref<RoamerData>(props.roamerData);
const expandRowKey = ref("");
// 界面的临时变量
const curName = ref("未命名视点");
const viewPointDuration = ref(2000);

function addViewPoint(viewPointData: any) {
  if (curName.value !== "") {
    const camera = _Onemap.MapViewer.camera as Camera;
    viewPointData.viewPoints.push({
      name: curName.value,
      id: createGuid(),
      directionX: camera.directionWC.x,
      directionY: camera.directionWC.y,
      directionZ: camera.directionWC.z,
      upX: camera.upWC.x,
      upY: camera.upWC.y,
      upZ: camera.upWC.z,
      x: camera.positionWC.x,
      y: camera.positionWC.y,
      z: camera.positionWC.z,
      duration: viewPointDuration.value,
    });
  } else {
    console.error("未命名");
  }
}

// 缩放
function zoomTo(pointsTable: any) {
  const camera = _Onemap.MapViewer.camera as Camera;

  camera.setView({
    destination: new Cartesian3(pointsTable.x, pointsTable.y, pointsTable.z),
    orientation: {
      direction: new Cartesian3(
        pointsTable.directionX,
        pointsTable.directionY,
        pointsTable.directionZ
      ),
      up: new Cartesian3(pointsTable.upX, pointsTable.upY, pointsTable.upZ),
    },
  });
}

// 重置视点
function reasetViewPoint(pointsTable: any) {
  const camera = _Onemap.MapViewer.camera as Camera;
  pointsTable.directionX = camera.directionWC.x;
  pointsTable.directionY = camera.directionWC.y;
  pointsTable.directionZ = camera.directionWC.z;
  pointsTable.upX = camera.upWC.x;
  pointsTable.upY = camera.upWC.y;
  pointsTable.upZ = camera.upWC.z;
  pointsTable.x = camera.positionWC.x;
  pointsTable.y = camera.positionWC.y;
  pointsTable.z = camera.positionWC.z;
}

// 删除视点
function deleteViewPoint(viewPointData: any, index: number) {
  viewPointData.viewPoints.splice(index, 1);
}

function changeExpandRowKey(pathdata: any) {
  expandRowKey.value = pathdata.id;
}

function removeCurData(pathdata: any) {
  const index = roamerData.value.viewPointRoaming.findIndex(
    (v) => v.id === pathdata.id
  );
  roamerData.value.viewPointRoaming.splice(index, 1);
}

function savePathdataJson(pathdata: any) {
  saveJson(pathdata, "pathdata.json");
}

onMounted(() => {});
</script>
<style scoped>
.form-item {
  margin: 3px 0px;
  padding: 0px;
}
.formitemcontainer {
  display: flex;
  flex-wrap: wrap; /* 允许自动换行 */
}
.formitemwidth {
  width: 280px;
  margin: 0px;
  padding: 0px;
  border: 1px solid rgb(255, 255, 255);
}
.formitemborder {
  margin: 0px;
  padding: 0px 6px;
  border: 1px solid rgb(240, 240, 240);
}
/* 使用 :deep 选择器来穿透 Scoped 样式 */
:deep .el-radio__label {
  padding-left: 2px;
}
:deep .el-slider__runway {
  margin-right: 12px; /* 调整 slider 和 input 之间的间距 */
  margin-left: 1px; /* 调整 slider 和 input 之间的间距 */
}
:deep .el-slider__input {
  width: 130px;
}
:deep .el-input-number .el-input__wrapper {
  padding-left: 2px;
  padding-right: 2px;
}
:deep .el-input-number__decrease .el-input-number__increase {
  width: 25px;
}
:deep .el-form-item {
  margin: 5px;
}

/***********/
.inputbox {
  width: calc(50% - 200px);
}
.marginleft {
  margin-left: 2px;
}
.from-item-dom {
  display: flex;
  width: 100%;
  align-items: center;
}
.form-item-btn {
  margin-left: 3px;
}
</style>
