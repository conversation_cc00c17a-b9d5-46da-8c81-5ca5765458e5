<template>
  <el-form>
    <!-- 1.漫游模式选择    :disabled="props.isEdit"-->
    <el-form-item label="漫游方式" class="form-item">
      <el-radio-group v-model="RoamerType" class="formitemborder">
        <el-radio
          style="font-weight: bold; margin: 0px 2px; padding: 0px"
          :value="RoamType.FirstPersion"
          >游戏模式漫游</el-radio
        >
        <el-radio
          style="font-weight: bold; margin: 0px 30px; padding: 0px"
          :value="RoamType.PathRoam"
          >路径漫游</el-radio
        >
        <el-radio
          style="font-weight: bold; margin: 0px 2px; padding: 0px"
          :value="RoamType.ViewPointRoam"
          >视点漫游</el-radio
        >
      </el-radio-group>
    </el-form-item>
    <!-- 2.1 游戏模式漫游 第一人称漫游 form.roamType  FirstPersion-->
    <div class="RamParamcontainer" v-if="RoamerType == RoamType.FirstPersion">
      <!-- 2.1.1移动速度 -->
      <el-form-item
        class="form-item"
        tyle="margin: 0px;padding:0px;"
        label="移动速度"
      >
        <div style="display: flex">
          <el-slider
            style="width: 110px"
            :min="0"
            :max="100"
            :step="0.1"
            v-model="roamerData.gameingMode.roamSpeed"
            @input="roamSpeedChange()"
          />
          <el-input-number
            style="width: 100px"
            :min="0"
            :max="100"
            controls-position="right"
            v-model="roamerData.gameingMode.roamSpeed"
            @change="roamSpeedChange()"
          />
        </div>
      </el-form-item>
      <!-- 2.1.2视野宽度 -->
      <el-form-item
        class="form-item"
        tyle="margin: 0px;padding:0px;"
        label="视野宽度"
      >
        <div style="display: flex">
          <el-slider
            style="width: 110px"
            :min="10"
            :max="90"
            :step="0.1"
            v-model="roamerData.gameingMode.cameraFovAngle"
            @input="cameraFovAngleChange()"
          />
          <el-input-number
            style="width: 100px"
            :min="10"
            :max="90"
            controls-position="right"
            v-model="roamerData.gameingMode.cameraFovAngle"
            @change="cameraFovAngleChange()"
          />
        </div>
      </el-form-item>
      <!-- 2.1.3开启碰撞检测  开启重力模式 -->
      <el-form-item class="form-item" tyle="margin: 0px;padding:0px">
        <div style="display: flex; width: 100%">
          <div style="white-space: nowrap; margin: 0px 3px 0px 5px">
            开启碰撞检测
          </div>
          <el-switch
            v-model="roamerData.gameingMode.openCollisionDetection"
            class="ml-2"
            inline-prompt
            style="
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
            "
            active-text="开"
            inactive-text="关"
            @change="openCollisionDetectionChange()"
          />
          <div style="white-space: nowrap; margin: 0px 3px 0px 25px">
            开启重力模式
          </div>
          <el-switch
            v-model="roamerData.gameingMode.openGravity"
            class="ml-2"
            inline-prompt
            style="
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
            "
            active-text="开"
            inactive-text="关"
            @change="openGravityChange()"
          />
        </div>
      </el-form-item>
      <!-- 2.1.4开始漫游按钮 -->
      <el-form-item class="form-item">
        <div style="width: 100%; text-align: right; padding: 0px 0px 0px 80px">
          <el-button plain type="success" @click="useIndoorCameraController()">
            开启漫游
          </el-button>
          <el-button
            plain
            type="warning"
            style="margin-left: 10px"
            @click="closeIndoorCameraController()"
          >
            关闭漫游
          </el-button>
        </div>
      </el-form-item>
    </div>

    <!-- 2.2 漫游控制 -->
    <el-form-item class="form-item">
      <!-- 2.2.1 视点漫游控制 -->
      <div
        style="text-align: center; width: 100%"
        v-if="RoamerType === RoamType.ViewPointRoam"
      >
        <el-button
          plain
          type="success"
          style="margin-left: 10px"
          @click="startViewRoamer()"
          >开始</el-button
        >
        <el-button
          plain
          type="success"
          style="margin-left: 10px"
          @click="pauseViewRoamer()"
          >暂停</el-button
        >
        <el-button
          plain
          type="success"
          style="margin-left: 10px"
          @click="resumeViewRoamer()"
          >继续</el-button
        >
        <el-button
          plain
          type="warning"
          style="margin-left: 10px"
          @click="stopViewRoamer()"
          >停止</el-button
        >
      </div>

      <!-- 2.2.2 路径漫游控制 -->
      <div
        style="text-align: center; width: 100%"
        v-if="RoamerType === RoamType.PathRoam"
      >
        <el-form-item
          class="form-item formitemwidth"
          style="width: 100%"
          label="扮演角色"
        >
          <el-radio-group
            class="formitemborder"
            v-model="curPathRoamer.roamerType"
            @change="changeRoamerType(curPathRoamer.roamerType)"
          >
            <el-radio
              style="margin: 0px; margin-right: 5px; padding: 0px"
              :value="RoamType.FirstPersionAngle"
              >沉浸视角</el-radio
            >
            <el-radio
              style="margin: 0px 6px; padding: 0px"
              :value="RoamType.ThirdPersonAngle"
              >观察者视角</el-radio
            >
            <el-radio
              style="margin: 0px; padding: 0px"
              :value="RoamType.FreedomAngle"
              >自由视角</el-radio
            >
          </el-radio-group>
          <!-- 循环播放 -->
          <el-form-item
            label="循环播放"
            class="form-item"
            style="margin: 0px 0px 0px 10px; padding: 0px"
          >
            <el-switch
              inline-prompt
              style="
                margin: 0px;
                padding: 0px;
                --el-switch-on-color: #13ce66;
                --el-switch-off-color: #ff4949;
              "
              active-text="开"
              inactive-text="关"
              v-model="curPathRoamer.runLoop"
              @change="changeRunLoop(curPathRoamer.runLoop)"
            />
            <span style="margin: 0px 2px 0px 10px">持续时间</span>
            <el-input-number
              :controls="false"
              style="margin: 0px; padding: 0px; width: 60px"
              v-model="curPathRoamer.runTime"
              @change="changeRunTime(curPathRoamer)"
            />
          </el-form-item>
        </el-form-item>
        <!-- 2.2.3开始漫游按钮 -->
        <el-form-item class="form-item" tyle="margin: 0px;padding:0px; ">
          <div style="width: 100%; text-align: right">
            <el-button plain type="success" @click="startPathRoamer()"
              >开启漫游</el-button
            >
            <el-button
              plain
              type="warning"
              style="margin-right: 50px"
              @click="stopPathRoamer()"
              >关闭漫游</el-button
            >
          </div>
        </el-form-item>
      </div>
    </el-form-item>

    <!-- 2.3 漫游列表 -->
    <el-form-item>
      <!-- 2.3.1 视点漫游列表 -->
      <el-table
        v-if="RoamerType === RoamType.ViewPointRoam"
        size="small"
        :data="roamerData.viewPointRoaming"
        border
        :highlight-current-row="true"
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="viewPointName" label="视点漫游线路名">
          <template #default="scope">
            <el-input
              v-model="scope.row.name"
              style="width: 100%"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
              size="small"
              :type="
                curViewPointRoamerId === scope.row.id ? 'success' : 'warning'
              "
              @click="viewPointRoamerPathChange(scope.row)"
              >置为当前线路</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 2.3.2 路径漫游列表 -->
      <el-table
        v-if="RoamerType === RoamType.PathRoam"
        size="small"
        :data="roamerData.pathRoaming"
        border
        :highlight-current-row="true"
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="viewPointName" label="路径漫游线路名">
          <template #default="scope">
            <el-input
              v-model="scope.row.name"
              style="width: 100%"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
              size="small"
              :type="curPathRoamer.id === scope.row.id ? 'success' : 'warning'"
              @click="pathRoamerPathChange(scope.row)"
              >置为当前线路</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { ref } from "vue";
import type { PropType } from "vue";
import { getOnemap, RoamType } from "../../onemapkit";
import { defaultRoamerData } from "./ViewRoamerClass";
import type {
  RoamerData,
  ViewPointRoaming,
  PathRoaming,
  PathPoint,
} from "./ViewRoamerClass";
import {
  indoorCameraController,
  viewPointRoaming,
  pathRoaming,
  setPathRoamerFromPathRoaming,
} from "./tools";
import {
  Math as CesiumMath,
  createGuid,
  Clock,
  JulianDate,
  Cartesian3,
  HeadingPitchRoll,
} from "@onemapkit/cesium";
import { PathRoamingMode } from "@onemapkit/cesium-tools";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  roamerData: {
    type: Object as PropType<RoamerData>,
    default: defaultRoamerData,
    require: true,
  },
});

const RoamerType = ref(RoamType.FirstPersion);
const roamerData = ref(props.roamerData);
const _Onemap = getOnemap(props.MapControlName);
const curViewPointRoamerId = ref(""); // 当前的视点漫游id
const curPathRoamer = ref<PathRoaming>({
  name: "test",
  id: createGuid(),
  roamerType: RoamType.FirstPersionAngle,
  runLoop: false,
  runTime: 100,
  modelUrl: "",
  modelScale: 1,
  originalHPR: [0, 0, 0],
  originalPosition: [0, 0, 0],
  cameraPosition: [0, 0, 0],
  cameraHPR: [0, 0, 0],
  pathPoints: [],
} as any);
// 清空当前场景中的路径漫游
pathRoaming.setPositions([]);
// 清空场景中的视点漫游
viewPointRoaming.setRoaming([]);
// 移除路径漫游时的监听事件方法
let removeMoveEvent: any = undefined;
let removeFinishEvent: any = undefined;

// ------------------------------------------------- 游戏漫游模式 ---------------------------------------------------------------
// 更新移动速度
function roamSpeedChange() {
  indoorCameraController.speed = roamerData.value.gameingMode.roamSpeed;
}

// 更新相机视场角
function cameraFovAngleChange() {
  _Onemap.MapViewer.camera.frustum.fov = CesiumMath.toRadians(
    roamerData.value.gameingMode.cameraFovAngle
  );
}

// 开启碰撞检测状态开关
function openCollisionDetectionChange() {
  indoorCameraController.enableCollisionDetection =
    roamerData.value.gameingMode.openCollisionDetection;
}

// 开启碰撞检测状态开关
function openGravityChange() {
  indoorCameraController.enableGravity =
    roamerData.value.gameingMode.openGravity;
}

// 使用第一人称控制器
function useIndoorCameraController() {
  indoorCameraController.enterIndoorCameraController(true);
}

// 关闭第一人称控制器
function closeIndoorCameraController() {
  indoorCameraController.enterIndoorCameraController(false);
}
// ------------------------------------------------- 游戏漫游模式 ---------------------------------------------------------------

// ------------------------------------------------- 视点漫游模式 ---------------------------------------------------------------
// 改变视点漫游线路
function viewPointRoamerPathChange(value: ViewPointRoaming) {
  // 切换线路的时候，初始化路线信息
  curViewPointRoamerId.value = value.id;
  const viewPoints = value.viewPoints;
  if (viewPoints.length > 0) {
    viewPointRoaming.setRoaming(viewPoints);
  } else {
    viewPointRoaming.setRoaming([]);
    console.error("当前视点漫游路径未设置视点");
  }
}
// 开始视点漫游
function startViewRoamer() {
  viewPointRoaming.start();
}
// 暂停视点漫游
function pauseViewRoamer() {
  viewPointRoaming.pause();
}
// 继续视点漫游
function resumeViewRoamer() {
  viewPointRoaming.resume();
}
// 结束视点漫游
function stopViewRoamer() {
  viewPointRoaming.stop();
}

// ------------------------------------------------- 视点漫游模式 ---------------------------------------------------------------

// ------------------------------------------------- 路径漫游线路模式 ---------------------------------------------------------------
// 改变路径漫游模式
function changeRoamerType(type: RoamType) {
  // 设置路径漫游的视角
  switch (type) {
    case RoamType.FirstPersionAngle:
      pathRoaming.perspective = PathRoamingMode.FIRSTFOLLOW;
      break;
    case RoamType.ThirdPersonAngle:
      pathRoaming.perspective = PathRoamingMode.THREE;
      break;

    case RoamType.FreedomAngle:
      pathRoaming.perspective = PathRoamingMode.FREE;
      break;
  }
}

// 改变路径漫游是否无限循环
function changeRunLoop(runLoop: boolean) {
  pathRoaming.infinite = runLoop;
}

// 改变路径漫游的持续时间
function changeRunTime(value: PathRoaming) {
  // 改变持续时间，需要重新计算所有路线信息，因此需要根据当前信息全部更新路经漫游
  setPathRoamerFromPathRoaming(value);
}

// 改变路径漫游线路
function pathRoamerPathChange(value: PathRoaming) {
  curPathRoamer.value = value;
  setPathRoamerFromPathRoaming(value);
}

// 更新当前站点的相机状态
function updatePathRoamerCamera(pathPoint: PathPoint) {
  const cameraHPR = pathPoint.headingPitchRoll;
  const cameraPosition = pathPoint.offset;
  // 设置初始的相机偏移量
  pathRoaming.offsetDistance = new Cartesian3(
    cameraPosition[0],
    cameraPosition[1],
    cameraPosition[2]
  );
  // 设置初始的相机旋转量
  pathRoaming.cameraHPR = new HeadingPitchRoll(
    CesiumMath.toRadians(cameraHPR[0]),
    CesiumMath.toRadians(cameraHPR[1]),
    CesiumMath.toRadians(cameraHPR[2])
  );
}

function startPathRoamer() {
  if (removeMoveEvent) {
    removeMoveEvent();
    removeMoveEvent = undefined;
  }
  if (removeFinishEvent) {
    removeFinishEvent();
    removeFinishEvent = undefined;
  }
  if (curPathRoamer.value.pathPoints.length > 0) {
    updatePathRoamerCamera(curPathRoamer.value.pathPoints[0]);

    pathRoaming.start();
    const siteTimes = pathRoaming.siteTimes;
    let index = 0;
    let lastIndex = 0;

    removeMoveEvent = pathRoaming.moveEvent.addEventListener((value: Clock) => {
      const curJulianDate = value.currentTime;
      for (let i = 0; i < siteTimes.length; i++) {
        if (JulianDate.lessThanOrEquals(curJulianDate, siteTimes[i])) {
          index = i - 1;
          break;
        }
      }

      if (lastIndex !== index) {
        lastIndex = index;
        updatePathRoamerCamera(curPathRoamer.value.pathPoints[index]);
      }
    });

    removeFinishEvent = pathRoaming.finishEvent.addEventListener(() => {
      if (removeMoveEvent) {
        removeMoveEvent();
        removeMoveEvent = undefined;
      }
      if (removeFinishEvent) {
        removeFinishEvent();
        removeFinishEvent = undefined;
      }
    });
  }
}

function stopPathRoamer() {
  pathRoaming.stop();
}

// ------------------------------------------------- 路径漫游线路模式 ---------------------------------------------------------------

// ------------------------------------------------- 漫游控制 -----------------------------------------------------------------

// ------------------------------------------------- 漫游控制 -----------------------------------------------------------------
</script>
<style scoped>
.RamParamcontainer {
  display: flex;
  flex-wrap: wrap;
  border-radius: 3px;
  border: 1px solid rgb(240, 240, 240);
  width: 100%;
}
</style>
