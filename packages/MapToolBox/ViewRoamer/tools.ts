import {
  IndoorCameraController,
  Roaming,
  PathRoaming,
  PathRoamingMode,
  PlaneEditableLine,
} from "@onemapkit/cesium-tools";
import {
  Viewer,
  Cartesian3,
  Cartographic,
  Math as CesiumMath,
  PolylineGlowMaterialProperty,
  Color,
  HeadingPitchRoll,
} from "@onemapkit/cesium";
import type { PathRoaming as PathRoamingType } from "./ViewRoamerClass";
import { RoamType } from "../../onemapkit";

export let indoorCameraController: IndoorCameraController;
export let viewPointRoaming: Roaming;
export let pathRoaming: PathRoaming;
export let planeEditableLine: PlaneEditableLine;

// 初始化第一人称漫游
export function initIndoorCameraController(viewer: Viewer) {
  indoorCameraController = new IndoorCameraController(viewer);
}

// 初始化视点漫游
export function initViewPointRoaming(viewer: Viewer) {
  viewPointRoaming = new Roaming(viewer);
}

// 初始化视点漫游
export function initPathRoaming(viewer: Viewer) {
  pathRoaming = new PathRoaming(viewer, {
    pathOptions: {
      resolution: 1,
      material: new PolylineGlowMaterialProperty({
        glowPower: 0.1,
        color: Color.YELLOW,
      }),
      width: 10,
    },
    offsetDistance: new Cartesian3(-0.28, 0.38, 1),
  });
}

// 更新一下路径漫游的信息（路径）
export function updatePathRoamerPositions(positions: Cartesian3[]) {
  if (positions.length > 1) {
    pathRoaming.setPositions(positions);
  } else {
    pathRoaming.setPositions([]);
  }
}

// 更新一下路径漫游的信息（模型和路径）
export function updatePathRoamer(
  modelPath: string,
  modelScale: number,
  positions: Cartesian3[]
) {
  if (modelPath) {
    pathRoaming.modelOptions = {
      uri: modelPath,
      scale: modelScale,
    };
  } else {
    pathRoaming.modelOptions = undefined;
  }
  // 刷新一下模型
  updatePathRoamerPositions(positions);
}

export function setPathRoamerFromPathRoaming(value: PathRoamingType) {
  const { modelUrl, modelScale, pathPoints } = value;
  let { originalHPR, originalPosition, cameraPosition, cameraHPR } = value;

  const positions = pathPoints.map((pp) =>
    Cartesian3.fromDegrees(pp.position[0], pp.position[1], pp.position[2])
  );

  // 设置路程持续时间
  pathRoaming.duration = value.runTime;

  // 设置是否无限循环
  pathRoaming.infinite = value.runLoop;

  // 设置模型信息
  if (modelUrl) {
    // 有模型信息使用模型信息
    pathRoaming.modelOptions = {
      uri: modelUrl,
      scale: modelScale,
    };
    pathRoaming.pointOptions = undefined;
  } else {
    // 没有模型信息，用点代替模型
    (pathRoaming.pointOptions = {
      pixelSize: 10,
    }),
      (pathRoaming.modelOptions = undefined);
  }
  pathRoaming.setPositions(positions);

  // 如果存在漫游点，则设置初始状态下的相机倾斜角和相机偏移位置，为第一个漫游点的位置
  if (pathPoints.length > 0) {
    cameraHPR = pathPoints[0].headingPitchRoll;
    cameraPosition = pathPoints[0].offset;
  }

  // 设置路径漫游的视角
  switch (value.roamerType) {
    case RoamType.FirstPersionAngle:
      pathRoaming.perspective = PathRoamingMode.FIRSTFOLLOW;
      break;
    case RoamType.ThirdPersonAngle:
      pathRoaming.perspective = PathRoamingMode.THREE;
      break;

    case RoamType.FreedomAngle:
      pathRoaming.perspective = PathRoamingMode.FREE;
      break;
  }

  // 设置模型偏移位置
  pathRoaming.originalPosition = new Cartesian3(
    originalPosition[0],
    originalPosition[1],
    originalPosition[2]
  );
  // 设置模型旋转量
  pathRoaming.originalHPR = new HeadingPitchRoll(
    CesiumMath.toRadians(originalHPR[0]),
    CesiumMath.toRadians(originalHPR[1]),
    CesiumMath.toRadians(originalHPR[2])
  );
  // 设置初始的相机偏移量
  pathRoaming.offsetDistance = new Cartesian3(
    cameraPosition[0],
    cameraPosition[1],
    cameraPosition[2]
  );
  // 设置初始的相机旋转量
  pathRoaming.cameraHPR = new HeadingPitchRoll(
    CesiumMath.toRadians(cameraHPR[0]),
    CesiumMath.toRadians(cameraHPR[1]),
    CesiumMath.toRadians(cameraHPR[2])
  );
}

export function initEditLine(viewer: Viewer) {
  planeEditableLine = new PlaneEditableLine(viewer);
}

/**
 * 将笛卡尔坐标转成数组
 * @param {Cartesian3} position 笛卡尔坐标
 * @returns {number[]}
 */
export function cartesian3ToNumberArray(position: Cartesian3) {
  const cartographic = Cartographic.fromCartesian(position);
  return [
    CesiumMath.toDegrees(cartographic.longitude),
    CesiumMath.toDegrees(cartographic.latitude),
    cartographic.height,
  ];
}

/**
 * 将笛卡尔数组转成二维数组
 * @param {Cartesian3[]} positions 笛卡尔坐标数组
 * @return {number[][]}
 */
export function cartesian3ArrayToNumberArray(positions: Cartesian3[]) {
  const numberArray: number[][] = [];
  positions.forEach((p) => {
    const array = cartesian3ToNumberArray(p);
    numberArray.push(array);
  });
  return numberArray;
}

/**
 * 将二维数组转成笛卡尔坐标数组
 * @param {number[][]} positionsStr 字符串
 * @returns {Cartesian3[]}
 */
export function numberArrayToCartesian3Array(array: number[][]) {
  const positions: any = [];
  array.forEach((point) => {
    positions.push(Cartesian3.fromDegrees(point[0], point[1], point[2]));
  });

  return positions;
}

// 使用可编辑线工具绘制
export function drawEditLine(editEndCallBack: any) {
  planeEditableLine.clear();
  planeEditableLine.draw();
  const removeEditEndEvent =
    planeEditableLine.drawCompleteEvent.addEventListener((infor: any) => {
      removeEditEndEvent();
      planeEditableLine.clear();
      editEndCallBack(infor.positions);
    });
}

// 重新编辑当前工具
export function editLine(positions: Cartesian3[], editEndCallBack: any) {
  planeEditableLine.clear();
  //@ts-ignore
  planeEditableLine.drawByPositions(positions);
  const removeEditEndEvent =
    planeEditableLine.drawCompleteEvent.addEventListener((infor: any) => {
      removeEditEndEvent();
      planeEditableLine.clear();
      editEndCallBack(infor.positions);
    });
  planeEditableLine.reEdit();
}

function click(node: any) {
  try {
    node.dispatchEvent(new MouseEvent("click"));
  } catch (e) {
    var evt = document.createEvent("MouseEvents");
    evt.initMouseEvent(
      "click",
      true,
      true,
      window,
      0,
      0,
      0,
      80,
      20,
      false,
      false,
      false,
      false,
      0,
      null
    );
    node.dispatchEvent(evt);
  }
}

export function saveJson(data: any, fileName: string) {
  const value = JSON.stringify(data, undefined, 4);
  const blob = new Blob([value], { type: "text/json" });
  const a = document.createElement("a");
  a.download = fileName;
  a.href = URL.createObjectURL(blob);
  a.dataset.downloadurl = ["text/json", a.download, a.href].join(":");
  click(a);
}

export function loadJson(callback: Function) {
  const fileInput = document.createElement("input");
  const _viewer = (pathRoaming as any)._viewer;
  // document.appendChild(fileInput);
  _viewer.canvas.parentElement?.appendChild(fileInput);
  fileInput.accept = ".json";
  fileInput.type = "file";
  click(fileInput);

  // 点击取消/关闭上传页面，都移除dom元素
  fileInput.addEventListener("cancel", () => {
    _viewer.canvas.parentElement?.removeChild(fileInput);
  });

  fileInput.addEventListener("change", (event: any) => {
    const file = event.target.files[0];

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result;
      if (content) {
        try {
          const jsonData = JSON.parse(content as any);
          // 执行回调
          callback(jsonData);
        } catch {
          console.error("json解析失败");
        }
      }
      _viewer.canvas.parentElement?.removeChild(fileInput);
    };

    // 读取失败也移除dom元素
    reader.onerror = () => {
      _viewer.canvas.parentElement?.removeChild(fileInput);
    };

    reader.readAsText(file);
  });
}
