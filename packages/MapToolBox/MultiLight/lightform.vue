
<template>
  <el-form :model="form" class="collapse-item">
    <!-- 1.光源类型 点光源 聚光灯 灯带 灯光动画  :disabled="props.isEdit"-->
    <el-form-item class="form-item" label="光源类型">
      <el-radio-group v-model="form.lightType" @change="changelightType">
        <el-radio :label="ScenelightType.pointLight">点光源</el-radio>
        <el-radio :label="ScenelightType.spotLight">聚光灯</el-radio>
        <el-radio :label="ScenelightType.LightBar">灯带</el-radio>
        <el-radio :label="ScenelightType.LightImage">灯光动画</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 2.场景名称 -->
    <el-form-item class="form-item" label="场景名称">
      <div class="from-item-dom">
        <el-input
          v-model="form.scenename"
          placeholder="输入场景名称"
          class="input-with-select"
        >
          <template #append>
            <el-select
              v-model="form.scenename"
              placeholder="选择"
              style="width: 150px"
            >
              <el-option
                v-for="item in Array.from(tmpSceneGroupsData.keys())"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </template>
        </el-input>
        <el-button
          v-if="props.isEdit"
          class="form-item-btn"
          type="primary"
          @click.prevent="_DaleteSceneListItem(form.scenename)"
          >删除</el-button
        >
      </div>
    </el-form-item>

    <!-- 3.光源组名称 -->
    <el-form-item
      class="form-item"
      label="光源组名称"
      style="margin-left: -15px"
    >
      <div class="from-item-dom">
        <el-input
          v-model="form.groupname"
          placeholder="光源组名称"
          class="input-with-select"
        >
          <template #append>
            <el-select
              v-model="form.groupname"
              placeholder="选择"
              style="width: 150px"
            >
              <el-option
                v-for="item in (tmpSceneGroupsData.get(form.scenename))?Array.from((tmpSceneGroupsData.get(form.scenename) as any).keys()):[]"
                :key="item "
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </template>
        </el-input>
        <el-button
          v-if="props.isEdit"
          class="form-item-btn"
          type="primary"
          @click.prevent="_DaleteLightGrouItem(form.scenename, form.groupname)"
          >删除</el-button
        >
      </div>
    </el-form-item>

    <!-- 4.光源名称 -->
    <el-form-item class="form-item" label="光源名称">
      <el-input v-model="form.lightname" />
    </el-form-item>

    <!-- 5.1光源效果: 静态光源 动态光源 (光源颜色 间隔时间) !=LightImage -->
    <el-form-item
      class="form-item"
      label="光源效果"
      v-if="form.lightType != ScenelightType.LightImage"
    >
      <!-- 静态光源 动态光源 -->
      <el-space :size="10">
        <el-form-item label="选择光源模式">
          <el-select
            v-model="form.lightEffectName"
            placeholder="Select"
            style="width: 100px"
          >
            <el-option
              :key="ScenelightType.lightmodelstatic"
              label="静态"
              value="static"
            />
            <el-option
              :key="ScenelightType.lightmodeldynamics"
              label="动态"
              value="dynamics"
            />
          </el-select>
        </el-form-item>
        <el-button
          style="margin-left: 5px"
          @click="
            _AddColorEffectItem({
              index: _tempLightEffectTable.length,
              EffectName: form.lightEffectName,
              ColorCode: '#232158',
              SpaceTime: 0,
            })
          "
          >添加光源</el-button
        >
      </el-space>
    </el-form-item>

    <!-- 5.2光源效果列表(Table->_tempLightEffectTable):光源颜色 间隔时间-->
    <el-form-item
      class="form-item"
      v-if="form.lightType != ScenelightType.LightImage"
    >
      <el-table
        size="small"
        :data="_tempLightEffectTable"
        border
        :highlight-current-row="true"
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="index" label="序号" width="45">
          <template #default="scope">
            <el-input
              v-model="scope.row.index"
              style="width: 35px"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column prop="EffectName" label="模式" />
        <el-table-column prop="ColorCode" label="光源颜色">
          <template #default="scope">
            <el-space :size="0">
              <el-input v-model="scope.row.ColorCode" size="small" />
              <el-color-picker
                label="颜色"
                v-model="scope.row.ColorCode"
                color-format="hex"
                size="small"
              ></el-color-picker>
            </el-space>
          </template>
        </el-table-column>
        <el-table-column prop="SpaceTime" label="间隔时间">
          <template #default="scope">
            <el-input v-model="scope.row.SpaceTime" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="90">
          <template #default="scope">
            <div style="text-align: center">
              <!-- 弹出编辑Tabs标签页 -->
              <el-button
                v-if="false"
                link
                type="primary"
                size="small"
                @click.prevent="_EditColorEffectItem(scope)"
                >修改</el-button
              >
              <!-- 删除该条记录 -->
              <el-button
                link
                type="primary"
                size="small"
                @click.prevent="_DeleteColorEffectItem(scope.$index)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <!--6.点光源/聚光灯/灯带： 光源强度 光源范围(发光半径) 聚灯外角 聚灯内角 灯带半径-->
    <el-form-item
      class="form-item"
      v-if="
        form.lightType == ScenelightType.pointLight ||
        form.lightType == ScenelightType.spotLight ||
        form.lightType == ScenelightType.LightBar
      "
    >
      <!-- 6.1  光源强度 光源范围(发光半径)-->
      <el-space :size="4">
        <SliderInput
          label="光源强度"
          style="margin: 1px 0px"
          :input-width="260"
          :width="'260px'"
          :min="0"
          :max="10"
          :placeholder="'0'"
          v-model="form.intensity"
        ></SliderInput>
        <SliderInput
          label="发光半径"
          style="margin: 1px 0px"
          :input-width="260"
          :width="'260px'"
          :min="0"
          :max="10"
          :placeholder="'0'"
          v-model="form.radius"
        ></SliderInput>
      </el-space>
      <!-- 6.2  聚灯外角 聚灯内角-->
      <el-space :size="4">
        <SliderInput
          v-if="form.lightType === ScenelightType.spotLight"
          style="margin: 1px 0px"
          :input-width="260"
          :width="'260px'"
          label="聚灯外角"
          :placeholder="'0'"
          :min="0"
          :max="90"
          v-model="form.outerConeDegrees"
        ></SliderInput>
        <SliderInput
          v-if="form.lightType === ScenelightType.spotLight"
          style="margin: 1px 0px"
          :input-width="260"
          :width="'260px'"
          label="聚灯内角"
          :placeholder="'0'"
          :min="0"
          :max="90"
          v-model="form.innerConeDegrees"
        ></SliderInput>
      </el-space>

      <!-- 6.3  灯带半径 开启灯带发光-->
      <el-space :size="4" v-if="form.lightType === ScenelightType.LightBar">
        <SliderInput
          style="margin: 1px 0px"
          :input-width="260"
          :width="'260px'"
          label="灯带半径"
          :placeholder="'0'"
          :min="0"
          :max="10"
          v-model="form.lightBarRadius"
        ></SliderInput>
        <el-form-item label="开启灯带发光">
          <el-switch
            v-model="form.lightBarOpenRay"
            class="ml-2"
            inline-prompt
            style="
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
            "
            active-text="开"
            inactive-text="关"
          />
        </el-form-item>
      </el-space>
    </el-form-item>

    <!-- 7.1绘制光源 :default-first-option="true"-->
    <el-form-item class="form-item" label="绘制光源">
      <span v-if="form.lightType == ScenelightType.LightBar">选择灯带类型</span>
      <el-select
        @change="changeLightBarType"
        v-model="form.lightBarType"
        placeholder="选择灯带类型"
        style="width: 100px; margin: 0px 2px"
        v-if="form.lightType == ScenelightType.LightBar"
      >
        <el-option
          :key="ScenelightType.LightBarHorizontal"
          label="水平灯带"
          value="LightBarHorizontal"
        />
        <el-option
          :key="ScenelightType.LightBarVertical"
          label="竖直灯带"
          value="LightBarVertical"
        />
      </el-select>
      <el-button @click="pickLightPosition()">{{
        form.lightType == ScenelightType.LightBar
          ? form.lightBarType == ScenelightType.LightBarVertical
            ? "拾取灯带底部点"
            : "绘制灯带"
          : form.lightType == ScenelightType.LightImage
          ? "点击选择模型"
          : "点击拾取光源位置"
      }}</el-button>
      <!-- 测试按钮 -->
      <el-button
        @click="
          testAddLightPosition({
            position: 'arg.position' as any,
            positionProps: 'arg.positionProps' as any,
          })
        "
        >AddLight</el-button
      >
    </el-form-item>

    <!-- 7.2光源列表(Table : _tempLightPositionTable)-->
    <el-form-item class="form-item">
      <el-table
        size="small"
        :data="_tempLightPositionTable"
        border
        :highlight-current-row="true"
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="index" label="序号" align="center" width="45">
          <template #default="scope">
            <el-input
              v-model="scope.row.index"
              style="width: 35px"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="position"
          :label="
            form.lightType == ScenelightType.LightImage ? '模型ID' : '光源位置'
          "
        >
          <template #default="scope">
            <el-input v-model="scope.row.position" size="small" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="form.lightType == ScenelightType.LightBar"
          prop="positionProps"
          :label="
            form.lightBarType == ScenelightType.LightBarHorizontal
              ? '首位闭合'
              : '顶部高程'
          "
          width="70"
        >
          <template #default="scope">
            <el-switch
              v-if="form.lightBarType == ScenelightType.LightBarHorizontal"
              v-model="scope.row.positionProps"
              class="ml-2"
              inline-prompt
              style="
                --el-switch-on-color: #13ce66;
                --el-switch-off-color: #ff4949;
              "
              active-text="闭合"
              inactive-text="不闭合"
            />
            <el-input v-else v-model="scope.row.positionProps" size="small" />
          </template>
        </el-table-column>
        <el-table-column
          v-if="form.lightType == ScenelightType.LightImage"
          prop="positionProps"
          label="资源路径"
        >
          <template #default="scope">
            <el-input v-model="scope.row.positionProps" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90" align="center">
          <template #default="scope">
            <!-- 主要实现位置修改 -->
            <el-button
              link
              type="primary"
              size="small"
              @click.prevent="_EditLightPositionItem(scope.$index)"
              >修改</el-button
            >
            <!-- 删除该条记录 -->
            <el-button
              link
              type="primary"
              size="small"
              @click.prevent="_DeleteLightPositionItem(scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <!-- 底部按钮 -->
    <el-form-item class="form-item">
      <div style="width: 100%; text-align: center">
        <el-button
          style="margin-right: 90px"
          type="danger"
          @click="AddSceneDataItem()"
          >确定
        </el-button>
        <el-button type="primary">取消 </el-button>
      </div>
    </el-form-item>
  </el-form>
  <!-- 没启用 -->
  <PopPanel v-if="isShowPosition" @close="isShowPosition = false">
    <!--点光源/聚光灯: 光源位置 -->
    <div
      style="width: 100%; padding: 2px 5px"
      :style="{
        boxShadow: `var(${getCssVarName('lighter')})`,
      }"
    >
      <el-space direction="vertical" alignment="start" :size="2">
        <SliderInput
          :input-width="355"
          v-model="form.position.x"
          label="经度"
          :placeholder="'输入经度'"
          :step="0.0000000000000011"
          :precision="15"
          :min="0"
          :max="360"
        ></SliderInput>
        <SliderInput
          :input-width="355"
          v-model="form.position.y"
          label="纬度"
          :placeholder="'输入纬度'"
          :step="0.0000000000000011"
          :precision="15"
          :min="0"
          :max="90"
        >
        </SliderInput>
        <SliderInput
          :input-width="355"
          v-model="form.position.z"
          label="高度"
          :placeholder="'输入高度'"
          :step="0.000001"
          :precision="6"
          :min="0"
          :max="500"
        ></SliderInput>
      </el-space>
    </div>
    <span v-if="form.lightType == ScenelightType.spotLight">聚灯照向</span>
    <div
      v-if="form.lightType == ScenelightType.spotLight"
      style="width: 100%; padding: 2px 5px"
      :style="{
        boxShadow: `var(${getCssVarName('lighter')})`,
      }"
    >
      <el-space direction="vertical" alignment="start" :size="2">
        <SliderInput
          :input-width="355"
          v-model="form.directionPoint.x"
          label="经度"
          :placeholder="'输入经度'"
          :step="0.0000000000000011"
          :precision="15"
          :min="0"
          :max="360"
        ></SliderInput>
        <SliderInput
          :input-width="355"
          v-model="form.directionPoint.y"
          label="纬度"
          :placeholder="'输入纬度'"
          :step="0.0000000000000011"
          :precision="15"
          :min="0"
          :max="90"
        >
        </SliderInput>
        <SliderInput
          :input-width="355"
          v-model="form.directionPoint.z"
          label="高度"
          :placeholder="'输入高度'"
          :step="0.0000000000000011"
          :precision="6"
          :min="0"
          :max="500"
        ></SliderInput>
      </el-space>
    </div>
  </PopPanel>
</template>

<script setup lang="ts">
import { ref, reactive, type PropType } from "vue";
import {
  type ILightType,
  ScenelightType,
  SliderInput,
  PopPanel,OnemapClass
} from "../../onemapkit";

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true,
  },  
  Onemap: {
    type: OnemapClass,
    default: () => {
      let tmp = new OnemapClass();
      tmp.isMapReady = ref(false);
      return tmp;
    },
    require: true,
  },
  Store: {
    type: Object as PropType<{
      layers: any;
      Props: any;
    }>,
    default: { layers: {}, Props: {} },
    require: true,
  },

});
/** element-plus CSS PopPanel */
const getCssVarName = (type: string) => {
  return `--el-box-shadow${type ? "-" : ""}${type}`;
};

///////////////////////// 一、数据操作

/** 1. 光源场景集合--------------------->场景------->-光源组-->光源列表           */
const tmpSceneGroupsData = reactive<Map<string, Map<string, [ILightType]>>>(
  new Map<string, Map<string, [ILightType]>>()
);

/** 2.光源效果临时表 */
const _tempLightEffectTable: any = reactive<
  Array<{
    index: number;
    EffectName: string;
    ColorCode: string;
    SpaceTime: string;
  }>
>([]);
/** 2.1 向“光源效果临时表”添加记录
 * @param arg
 */
const _AddColorEffectItem = (arg: any) => {
  _tempLightEffectTable.push({
    index: _tempLightEffectTable.length,
    EffectName: arg.EffectName,
    ColorCode: arg.ColorCode,
    SpaceTime: arg.SpaceTime,
  });
};

/** 2.2 向“光源效果临时表”删除记录
 * @param index
 */
const _DeleteColorEffectItem = (index: number) => {
  _tempLightEffectTable.splice(index, 1);
};

/** 2.3 编辑 光源效果临时表 记录*/
const _EditColorEffectItem = (index: any) => {
  console.log("=== ", index);
};

/** 3.光源位置临时表 */
const _tempLightPositionTable: any = ref<
  Array<{
    index: number;
    /** 光源类型如为PBR的情况表示的是模型对象 */
    position: Object;
    /** 1.lightBar时表示的是“首尾是否闭合”或“顶部高程”
     *  2.lightImage时表示图片资源路径
     *  3.其他光源类型该字段为空 */
    positionProps: string | boolean;
  }>
>([]);
/** 3.1 向“光源位置属性临时表”添加记录
 * @param index 行号
 */
const _EditLightPositionItem = (index: number) => {
  // 主要实现在 绘制完光源位置/模型ID 后，要把响应的坐标等信息更新在 位置临时表中
  console.log(_tempLightPositionTable.value[index]);
};
/** 3.2 向“光源位置属性临时表”删除记录
 * @param index 行号
 */
const _DeleteLightPositionItem = (index: number) => {
  _tempLightPositionTable.value.splice(index, 1);
};
/** 4.1 删除场景 */
const _DaleteSceneListItem = (_scenename: string) => {
  if (tmpSceneGroupsData.has(_scenename)) {
    tmpSceneGroupsData.delete(_scenename);
  }
};

/** 5.1 删除光源组 */
const _DaleteLightGrouItem = (_scenename: string, _lightgroupname: string) => {
  if (tmpSceneGroupsData.has(_scenename)) {
    if (tmpSceneGroupsData.get(_scenename)?.has(_lightgroupname)) {
      tmpSceneGroupsData.get(_scenename)?.delete(_lightgroupname);
    }
  }
};


///////////////////////// 二、界面绑定的数据表单

/** 光源数据结构*/
const form: ILightType = reactive({
  index: tmpSceneGroupsData.size,
  lightType: ScenelightType.pointLight,
  scenename: "SN" + new Date().getTime(),
  groupname: "GN" + new Date().getTime(),
  lightname: "LN" + new Date().getTime(),
  lightEffectName: ScenelightType.lightmodelstatic,
  lightEffects: _tempLightEffectTable,
  lightobjecttag: "lightobjecttag",
  intensity: 50,
  radius: 100,
  outerConeDegrees: 45,
  innerConeDegrees: 10,
  position: {
    x: 0,
    y: 0,
    z: 0,
  },
  directionPoint: {
    x: 0,
    y: 0,
    z: 0,
  },
  lightBarType: ScenelightType.LightBarHorizontal,
  lightBarRadius: 0.5,
  lightBarOpenRay: true,
  lightBarRingClose: false,
  lightBarTopHight: 145,
  color: "#BD0505",
});
/** 6.1光源类型选择事讲响应 */
const changelightType = () => {
  //“光源效果临时表”记录归零
  _tempLightEffectTable.splice(0, _tempLightEffectTable.length);
  //光源位置属性临时表
  _tempLightPositionTable.value.splice(0, _tempLightPositionTable.value.length);
};
/** 6.2 灯带类型选择事讲响应 */
const changeLightBarType = () => {
  //光源位置属性临时表
  _tempLightPositionTable.value.splice(0, _tempLightPositionTable.value.length);
};

/** 确定添加或修改光源 */
const AddSceneDataItem = () => {};
/** 现实位置调整 弹窗 */
const isShowPosition = ref(false);
/** 添加光源位置 函数 */
const pickLightPosition = () => {
  // isShowPosition.value = !isShowPosition.value;
};

///////////////////////// 三、测试函数

const testAddLightPosition = (arg: any) => {
  _tempLightPositionTable.value.push({
    index: _tempLightPositionTable.value.length,
    position: arg.position,
    positionProps: arg.positionProps,
  });
};
</script>
<style scoped>
.form-item {
  margin: 6px 0px;
}
.from-item-dom {
  display: flex;
  width: 100%;
  align-items: center;
}
.form-item-btn {
  margin-left: 3px;
}
</style>
