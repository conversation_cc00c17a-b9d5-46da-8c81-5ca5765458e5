import {
  Math as CesiumMath,
  Cartesian2,
  Cartesian3,
  Cartographic,
  PostProcessStage,
  PostProcessStageSampleMode,
  PostProcessStageComposite,
  Viewer,
  Primitive,
  GeometryInstance,
  PolylineVolumeGeometry,
  Matrix4,
  Transforms,
  CylinderGeometry,
  ColorGeometryInstanceAttribute,
  PerInstanceColorAppearance,
  defined,
  ComponentDatatype,
  //@ts-ignore
  BatchTable,
  defaultValue,
  Color,
  Cartesian4,
} from "@onemapkit/cesium";

const singleStageFrag = `
  uniform sampler2D colorTexture;
  in vec2 v_textureCoordinates;
  
  void main() {
      // 如果没选中 
      if(!czm_selected()) {
          discard;
      }
      vec4 texel = texture(colorTexture, v_textureCoordinates);        
      out_FragColor = texel;
  }
  `;

const shadersSeparableBlur = `
  uniform sampler2D colorTexture;
  uniform vec2 direction;
  in vec2 v_textureCoordinates;
  
  float gaussianPdf(in float x, in float sigma) {
      return 0.39894 * exp( -0.5 * x * x / ( sigma * sigma)) / sigma;
  }
  
  void main() {
      vec2 invSize = 1.0 / czm_viewport.zw;
      float fSigma = float(SIGMA);
      float weightSum = gaussianPdf(0.0, fSigma);
      vec3 diffuseSum = texture( colorTexture, v_textureCoordinates).rgb * weightSum;
      for( int i = 1; i < KERNEL_RADIUS; i ++ ) {
          float x = float(i);
          float w = gaussianPdf(x, fSigma);
          vec2 uvOffset = direction * invSize * x;
          vec3 sample1 = texture( colorTexture, v_textureCoordinates + uvOffset).rgb;
          vec3 sample2 = texture( colorTexture, v_textureCoordinates - uvOffset).rgb;
          diffuseSum += (sample1 + sample2) * w;
          weightSum += 2.0 * w;
      }
      out_FragColor = vec4(diffuseSum / weightSum, 1.0);
  }
  `;

const finshStageFrag = `
  #extension GL_OES_standard_derivatives : enable
  uniform sampler2D blurTexture1;
  uniform sampler2D blurTexture2;
  uniform sampler2D blurTexture3;
  uniform sampler2D blurTexture4;
  uniform sampler2D colorTexture;
  uniform float bloomStrength;
  uniform float bloomRadius;
  
  
  in vec2 v_textureCoordinates;
  
  float bloomFactors1 = 1.0;
  float bloomFactors2 = 0.6;
  float bloomFactors3 = 0.2;
  float bloomFactors4 = 0.1;
  
  float lerpBloomFactor(const in float factor, float bloomRadius) {
      float mirrorFactor = 1.2 - factor;
      return mix(factor, mirrorFactor, bloomRadius);
  }
  
  void main() {
      vec4 color = texture(colorTexture, v_textureCoordinates);
      color += bloomStrength * ( lerpBloomFactor(bloomFactors1, bloomRadius) * texture(blurTexture1, v_textureCoordinates) +
          lerpBloomFactor(bloomFactors2, bloomRadius) * texture(blurTexture2, v_textureCoordinates) +
          lerpBloomFactor(bloomFactors3, bloomRadius) * texture(blurTexture3, v_textureCoordinates) +
          lerpBloomFactor(bloomFactors4, bloomRadius) * texture(blurTexture4, v_textureCoordinates)
      );
  
      if(color.r > 1.0) color.r = 1.0;
      if(color.g > 1.0) color.g = 1.0;
      if(color.b > 1.0) color.b = 1.0;
  
      out_FragColor = color;
  }
  `;

function computeCircle(radius: number) {
  const positions:any = [];
  for (let i = 0; i < 360; i++) {
    const radians = CesiumMath.toRadians(i);
    positions.push(
      new Cartesian2(radius * Math.cos(radians), radius * Math.sin(radians))
    );
  }
  return positions;
}

function getNightProgrammePositions(positions: Cartesian3[], radius: number) {
  const newPositions = positions.map((p) => {
    const cartographic = Cartographic.fromCartesian(p);
    return Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      cartographic.height - radius
    );
  });

  if (newPositions[0].equals(newPositions[newPositions.length - 1])) {
    // 如果首尾相连，则返回首尾相连的格式
    const first = Cartesian3.midpoint(
      newPositions[newPositions.length - 1],
      newPositions[newPositions.length - 2],
      new Cartesian3()
    );

    const final = Cartesian3.midpoint(
      newPositions[0],
      newPositions[1],
      new Cartesian3()
    );

    return [first, ...newPositions, final];
  } else {
    // 如果首尾不相连，则直接返回
    return newPositions;
  }
}

function parseDefines(shader: any) {
  let defines: any = [];
  for (const key in shader.defines) {
    if (shader.defines.hasOwnProperty(key)) {
      const val = shader.defines[key];
      defines.push("#define " + key + " " + val);
    }
  }
  defines = defines.join("\n") + "\n";
  if (shader.fragmentShader) {
    shader.fragmentShader = defines + shader.fragmentShader;
  }
  if (shader.vertexShader) {
    shader.vertexShader = defines + shader.vertexShader;
  }
  return shader;
}

// 获取卷积处理的后处理(name,中心半径,纹理缩放尺寸)
function createBlurStage(
  name: string,
  kernelRadius: number,
  textureScale: number
) {
  const blurDirectionX = new Cartesian2(1.0, 0.0);
  const blurDirectionY = new Cartesian2(0.0, 1.0);

  const separableBlurShader = {
    defines: {
      KERNEL_RADIUS: kernelRadius,
      SIGMA: kernelRadius,
    },
    fragmentShader: shadersSeparableBlur,
  };
  parseDefines(separableBlurShader);

  // X方向上的卷积
  const blurX = new PostProcessStage({
    name: name + "_x_direction",
    fragmentShader: separableBlurShader.fragmentShader,
    textureScale: textureScale,
    uniforms: {
      direction: blurDirectionX,
    },
    sampleMode: PostProcessStageSampleMode.LINEAR,
  });

  // Y方向上的卷积
  const blurY = new PostProcessStage({
    name: name + "_y_direction",
    fragmentShader: separableBlurShader.fragmentShader,
    textureScale: textureScale,

    uniforms: {
      direction: blurDirectionY,
    },
    sampleMode: PostProcessStageSampleMode.LINEAR,
  });

  return new PostProcessStageComposite({
    name: name,
    stages: [blurX, blurY],
    inputPreviousStageTexture: true,
  });
}

function getCommonPerInstanceAttributeNames(instances: any[]) {
  const length = instances.length;

  const attributesInAllInstances:any = [];
  const attributes0 = instances[0].attributes;
  let name;

  for (name in attributes0) {
    if (attributes0.hasOwnProperty(name) && defined(attributes0[name])) {
      const attribute = attributes0[name];
      let inAllInstances = true;

      // Does this same attribute exist in all instances?
      for (let i = 1; i < length; ++i) {
        const otherAttribute = instances[i].attributes[name];

        if (
          !defined(otherAttribute) ||
          attribute.componentDatatype !== otherAttribute.componentDatatype ||
          attribute.componentsPerAttribute !==
            otherAttribute.componentsPerAttribute ||
          attribute.normalize !== otherAttribute.normalize
        ) {
          inAllInstances = false;
          break;
        }
      }

      if (inAllInstances) {
        attributesInAllInstances.push(name);
      }
    }
  }

  return attributesInAllInstances;
}

const scratchGetAttributeCartesian2 = new Cartesian2();
const scratchGetAttributeCartesian3 = new Cartesian3();
const scratchGetAttributeCartesian4 = new Cartesian4();

function getAttributeValue(value: any) {
  const componentsPerAttribute = value.length;
  if (componentsPerAttribute === 1) {
    return value[0];
  } else if (componentsPerAttribute === 2) {
    return Cartesian2.unpack(value, 0, scratchGetAttributeCartesian2);
  } else if (componentsPerAttribute === 3) {
    return Cartesian3.unpack(value, 0, scratchGetAttributeCartesian3);
  } else if (componentsPerAttribute === 4) {
    return Cartesian4.unpack(value, 0, scratchGetAttributeCartesian4);
  }
}

function createBatchTable(primitive: any, context: any) {
  const geometryInstances = primitive.geometryInstances;
  const instances = Array.isArray(geometryInstances)
    ? geometryInstances
    : [geometryInstances];
  const numberOfInstances = instances.length;
  if (numberOfInstances === 0) {
    return;
  }

  const names = getCommonPerInstanceAttributeNames(instances);
  const length = names.length;

  const attributes:any = [];
  const attributeIndices: any = {};
  const boundingSphereAttributeIndices: any = {};
  let offset2DIndex;

  const firstInstance = instances[0];
  let instanceAttributes = firstInstance.attributes;

  let i;
  let name;
  let attribute;

  for (i = 0; i < length; ++i) {
    name = names[i];
    attribute = instanceAttributes[name];

    attributeIndices[name] = i;
    attributes.push({
      functionName: `czm_batchTable_${name}`,
      componentDatatype: attribute.componentDatatype,
      componentsPerAttribute: attribute.componentsPerAttribute,
      normalize: attribute.normalize,
    });
  }

  if (names.indexOf("distanceDisplayCondition") !== -1) {
    attributes.push(
      {
        functionName: "czm_batchTable_boundingSphereCenter3DHigh",
        componentDatatype: ComponentDatatype.FLOAT,
        componentsPerAttribute: 3,
      },
      {
        functionName: "czm_batchTable_boundingSphereCenter3DLow",
        componentDatatype: ComponentDatatype.FLOAT,
        componentsPerAttribute: 3,
      },
      {
        functionName: "czm_batchTable_boundingSphereCenter2DHigh",
        componentDatatype: ComponentDatatype.FLOAT,
        componentsPerAttribute: 3,
      },
      {
        functionName: "czm_batchTable_boundingSphereCenter2DLow",
        componentDatatype: ComponentDatatype.FLOAT,
        componentsPerAttribute: 3,
      },
      {
        functionName: "czm_batchTable_boundingSphereRadius",
        componentDatatype: ComponentDatatype.FLOAT,
        componentsPerAttribute: 1,
      }
    );
    boundingSphereAttributeIndices.center3DHigh = attributes.length - 5;
    boundingSphereAttributeIndices.center3DLow = attributes.length - 4;
    boundingSphereAttributeIndices.center2DHigh = attributes.length - 3;
    boundingSphereAttributeIndices.center2DLow = attributes.length - 2;
    boundingSphereAttributeIndices.radius = attributes.length - 1;
  }

  if (names.indexOf("offset") !== -1) {
    attributes.push({
      functionName: "czm_batchTable_offset2D",
      componentDatatype: ComponentDatatype.FLOAT,
      componentsPerAttribute: 3,
    });
    offset2DIndex = attributes.length - 1;
  }

  attributes.push({
    functionName: "czm_batchTable_pickColor",
    componentDatatype: ComponentDatatype.UNSIGNED_BYTE,
    componentsPerAttribute: 4,
    normalize: true,
  });

  const attributesLength = attributes.length;
  const batchTable = new BatchTable(context, attributes, numberOfInstances);

  for (i = 0; i < numberOfInstances; ++i) {
    const instance = instances[i];
    instanceAttributes = instance.attributes;

    for (let j = 0; j < length; ++j) {
      name = names[j];
      attribute = instanceAttributes[name];
      const value = getAttributeValue(attribute.value);
      const attributeIndex = attributeIndices[name];
      batchTable.setBatchedAttribute(i, attributeIndex, value);
    }

    const pickObject = {
      primitive: defaultValue(instance.pickPrimitive, primitive),
    } as any;

    if (defined(instance.id)) {
      pickObject.id = instance.id;
    }

    const pickId = context.createPickId(pickObject);
    primitive._pickIds.push(pickId);

    const pickColor = pickId.color;
    const color = scratchGetAttributeCartesian4;
    color.x = Color.floatToByte(pickColor.red);
    color.y = Color.floatToByte(pickColor.green);
    color.z = Color.floatToByte(pickColor.blue);
    color.w = Color.floatToByte(pickColor.alpha);

    batchTable.setBatchedAttribute(i, attributesLength - 1, color);
  }

  primitive._batchTable = batchTable;
  primitive._batchTableAttributeIndices = attributeIndices;
  primitive._batchTableBoundingSphereAttributeIndices =
    boundingSphereAttributeIndices;
  primitive._batchTableOffsetAttribute2DIndex = offset2DIndex;
}

export default class NightProgramme {
  private _viewer: Viewer;
  private _primitive: Primitive | undefined;
  private _bloomStrength: number;
  private _bloomRadius: number;
  private _selected: any[];
  private _singleStage: any;

  public appearance: PerInstanceColorAppearance;
  public horizontalExtents: any[];
  public verticalExtents: any[];
  public postProcessStageComposite: any;


  constructor(viewer: Viewer) {
    this._viewer = viewer;
    this._bloomStrength = 1.479;
    this._bloomRadius = 0.589;
    this.appearance = new PerInstanceColorAppearance({
      flat: true,
      translucent: true,
    });

    this.horizontalExtents = [];
    this.verticalExtents = [];
    this._selected = [];


    this.createPostProcessStage();
  }

  update() {
    if (this._primitive) {
      this._viewer.scene.primitives.remove(this._primitive);
    }
    const geometryInstances: GeometryInstance[] = [];

    this.verticalExtents.forEach((ve: any) => {
      const { bottomPoint, topHeight, radius, color } = ve;
      const positionOnEllipsoid = Cartographic.fromCartesian(bottomPoint);
      const length = topHeight - positionOnEllipsoid.height;

      const modelMatrix = Matrix4.multiplyByTranslation(
        Transforms.eastNorthUpToFixedFrame(bottomPoint),
        new Cartesian3(0.0, 0.0, length * 0.5),
        new Matrix4()
      );

      const greenCylinder = new GeometryInstance({
        geometry: new CylinderGeometry({
          length: length,
          topRadius: radius,
          bottomRadius: radius,
        }),
        attributes: {
          color: ColorGeometryInstanceAttribute.fromColor(color),
        },
        modelMatrix: modelMatrix,
      });

      geometryInstances.push(greenCylinder);
    });

    this.horizontalExtents.forEach((he: any) => {
      const { positions, radius, color } = he;
      const tubeGeometry = new GeometryInstance({
        geometry: new PolylineVolumeGeometry({
          polylinePositions: getNightProgrammePositions(positions, radius),
          shapePositions: computeCircle(radius),
        }),
        attributes: {
          color: ColorGeometryInstanceAttribute.fromColor(color),
        },
      });

      geometryInstances.push(tubeGeometry);
    });

    this._primitive = new Primitive({
      geometryInstances: geometryInstances,
      appearance: this.appearance,
      asynchronous: false,
    });

    this._viewer.scene.primitives.add(this._primitive);
    createBatchTable(this._primitive, (this._viewer as any).scene.context);
    this._singleStage.selected = [];
    (this._primitive as any)._pickIds.forEach((pickId: any) => {
    this._singleStage.selected.push({
        pickId: pickId,
      });
    });
  }

  /**
   * 泛光的强度
   *
   * @type {Number}
   */
  get bloomStrength() {
    return this._bloomStrength;
  }
  set bloomStrength(value) {
    this._bloomStrength = value;
    this.postProcessStageComposite.bloomStrength = value;
  }

  /**
   * 泛光的范围
   *
   * @type {Number}
   */
  get bloomRadius() {
    return this._bloomRadius;
  }
  set bloomRadius(value) {
    this._bloomRadius = value;
    this.postProcessStageComposite.bloomRadius = value;
  }

  remocePostProcessStage() {
    if (this.postProcessStageComposite) {
      this._viewer.postProcessStages.remove(this.postProcessStageComposite);
      this.postProcessStageComposite = undefined;
    }
  }

  createPostProcessStage() {
    this.remocePostProcessStage();

    // 用于获取指定物体的颜色
    this._singleStage = new PostProcessStage({
      name: "step1",
      fragmentShader: singleStageFrag,
      sampleMode: PostProcessStageSampleMode.LINEAR,
    });

    this._singleStage.selected = this._selected;

    // 创建4个不同程度的卷积模糊
    const blurStage1 = createBlurStage("blur1", 3, 1);
    const blurStage2 = createBlurStage("blur2", 5, 0.5);
    const blurStage3 = createBlurStage("blur3", 7, 0.25);
    const blurStage4 = createBlurStage("blur4", 18, 0.25);

    // 对指定物体颜色进行第一重卷积
    const sdf = new PostProcessStageComposite({
      name: "sdf",
      stages: [this._singleStage, blurStage1],
      inputPreviousStageTexture: true,
    });

    // 第二重卷积
    const sdf2 = new PostProcessStageComposite({
      name: "sdf2",
      stages: [sdf, blurStage2],
      inputPreviousStageTexture: true,
    });

    // 第三重卷积
    const sdf3 = new PostProcessStageComposite({
      name: "sdf3",
      stages: [sdf2, blurStage3],
      inputPreviousStageTexture: true,
    });

    // 第四重卷积
    const sdf4 = new PostProcessStageComposite({
      name: "sdf4",
      stages: [sdf3, blurStage4],
      inputPreviousStageTexture: true,
    });

    const fish = new PostProcessStage({
      name: "fishshadersds",
      uniforms: {
        blurTexture1: blurStage1.name,
        blurTexture2: blurStage2.name,
        blurTexture3: blurStage3.name,
        blurTexture4: blurStage4.name,
        bloomStrength: () => {
          return this._bloomStrength;
        },
        bloomRadius: () => {
          return this._bloomRadius;
        },
      },
      fragmentShader: finshStageFrag,
    });

    // 原始颜色叠加四重卷积的指定颜色
    this.postProcessStageComposite = new PostProcessStageComposite({
      name: "sdfs",
      stages: [sdf, sdf2, sdf3, sdf4, fish],
      inputPreviousStageTexture: false,
    });
    this._viewer.postProcessStages.add(this.postProcessStageComposite);
  }

  destroy() {
    this.remocePostProcessStage();
    if (this._primitive) {
      this._viewer.scene.primitives.remove(this._primitive);
    }
  }
}
