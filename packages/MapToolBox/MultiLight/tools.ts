import { MultipleLight, PointLight, SpotLight } from "@onemapkit/cesium-tools";
import * as Cesium from "@onemapkit/cesium";
import NightProgramme from "./NightProgramme";

// 多光源后处理集合
let multipleLights: MultipleLight[] = [];
let lights: (PointLight | SpotLight)[] = [];

let nightProgramme: NightProgramme;

export function initNightProgramme(viewer: Cesium.Viewer) {
  nightProgramme = new NightProgramme(viewer);
}

// 改变泛光半径
export function changeBloomRadius(bloomRadius: number) {
  nightProgramme.bloomRadius = bloomRadius
}

// 改变泛光强度
export function changeBloomStrength(bloomStrength: number) {
  nightProgramme.bloomStrength = bloomStrength
}

export function destroyightProgramme() {
  nightProgramme.destroy();
}

// 获取水平灯带对象
export function getHorizontalLightBarById(id: string) {
  const index = nightProgramme.horizontalExtents.findIndex((h) => h.id === id);
  if (index > -1) {
    return nightProgramme.horizontalExtents[index];
  }
}

// 获取竖直灯带对象
export function getVerticalLightBarById(id: string) {
  const index = nightProgramme.verticalExtents.findIndex((h) => h.id === id);
  if (index > -1) {
    return nightProgramme.verticalExtents[index];
  }
}

export function updateNightProgramme() {
  nightProgramme.update();
}

// 追加水平灯带
export function addHorizontalLightBar(
  positions: Cesium.Cartesian3[],
  radius: number,
  color: Cesium.Color,
  id: string
) {
  nightProgramme.horizontalExtents.push({
    id,
    positions,
    radius,
    color
  });
  nightProgramme.update();
}

// 追加竖直灯带
export function addVerticalLightBar(
  bottomPoint: Cesium.Cartesian3,
  topHeight: number,
  radius: number,
  color: Cesium.Color,
  id: string
) {
  nightProgramme.verticalExtents.push({
    id,
    bottomPoint,
    topHeight,
    radius,
    color,
  });
  nightProgramme.update();
}

// 移除水平灯带
export function removeHorizontalLightBar(id: string) {
  const index = nightProgramme.horizontalExtents.findIndex((h) => h.id === id);
  if (index > -1) {
    nightProgramme.horizontalExtents.splice(index, 1);
    nightProgramme.update();
  }
}

// 移除竖直灯带
export function removeVerticalLightBar(id: string) {
  const index = nightProgramme.verticalExtents.findIndex((h) => h.id === id);
  if (index > -1) {
    nightProgramme.verticalExtents.splice(index, 1);
    nightProgramme.update();
  }
}

/**
 * 在有阴影的模式下添加多光源的逻辑，因为WebGL对单个着色器使用的纹理数有限制，因此需要控制纹理数
 * @param {Cesium.Viewer} viewer viewer对象
 * @param {PointLight | SpotLight} light 多光源对象
 */
export function addLightInShadowMod(
  viewer: Cesium.Viewer,
  light: PointLight | SpotLight
) {
  lights.push(light);

  // 如果没有添加过多光源，则新创建一个容器
  if (multipleLights.length === 0) {
    multipleLights.push(new MultipleLight(viewer, true));
  }

  // 最大能够使用的纹理数为WebGL最大的纹理数-2，原因是后处理本身需要使用两张纹理（颜色纹理和深度图）。
  const maxTextures =
    (Cesium as any).ContextLimits.maximumTextureImageUnits - 2;

  let isPush = false;
  for (let i = 0; i < multipleLights.length; i++) {
    const curLength = (multipleLights[i] as any)._lights.length;
    if (curLength + 1 < maxTextures) {
      // 如果当前的MultipleLight还足够放入灯光，则放入
      isPush = true;
      multipleLights[i].addLight(light);
    }
  }

  // 如果没有合适的位置，则新建一个MultipleLight存放
  if (!isPush) {
    const multipleLight = new MultipleLight(viewer, true);
    multipleLight.addLight(light);
    multipleLights.push(multipleLight);
  }
}

/**
 * 在没有阴影的模式下添加多光源，这时候不需要考虑WebGL对于纹理数的限制
 * @param {Cesium.Viewer} viewer viewer对象
 * @param {PointLight | SpotLight} light 多光源对象
 */
export function addLightInNotShadowMod(
  viewer: Cesium.Viewer,
  light: PointLight | SpotLight
) {
  lights.push(light);
  // 如果没有添加过多光源，则新创建一个容器
  if (multipleLights.length === 0) {
    multipleLights.push(new MultipleLight(viewer, false));
  }
  multipleLights[0].addLight(light);
}

/**
 * 更改多光源阴影模式
 * @param viewer
 * @param shadow
 */
export function changeShadowMod(viewer: Cesium.Viewer, shadow: boolean) {
  // 根据当前场景的灯光，重新完整复制一份，因为clear时会销毁之前的灯光
  const newLights = lights.map((l) => {
    let light;
    if (l instanceof SpotLight) {
      light = new SpotLight({
        scene: viewer.scene,
        position: l.position,
        direction: l.direction,
        color: l.color,
        intensity: l.intensity,
        radius: l.radius,
        outerConeDegrees: l.outerConeDegrees,
        innerConeDegrees: l.innerConeDegrees,
      });
    } else {
      light = new PointLight({
        scene: viewer.scene,
        position: l.position,
        color: l.color,
        intensity: l.intensity,
        radius: l.radius,
      });
    }
    (light as any)._id = (l as any)._id;
    return light;
  });
  clearMultipleLights();
  if (shadow) {
    newLights.forEach((l) => {
      addLightInShadowMod(viewer, l);
    });
  } else {
    newLights.forEach((l) => {
      addLightInNotShadowMod(viewer, l);
    });
  }
}

/**
 * 移除光源
 * @param {PointLight | SpotLight} light 光源
 */
export function removeLight(light: PointLight | SpotLight) {
  // 从光源列表中移除
  const index = lights.findIndex((l) => (light as any)._id === (l as any)._id);
  if (index > -1) {
    lights.splice(index, 1);
  }
  // 从场景中移除
  multipleLights.forEach((ml) => {
    ml.removeLight(light);
  });
}

/**
 * 根据ID移除灯光
 * @param {string} id id
 */
export function removeLightById(id: string) {
  // 从光源列表中移除
  const index = lights.findIndex((l) => id === (l as any)._id);
  if (index > -1) {
    // 从场景中移除
    multipleLights.forEach((ml) => {
      ml.removeLight(lights[index]);
    });
    lights.splice(index, 1);
  }
}

/**
 * 根据ID获取光源
 * @param id
 * @returns
 */
export function getLightById(id: string) {
  const index = lights.findIndex((l) => id === (l as any)._id);
  if (index > -1) {
    return lights[index];
  }
}

/**
 * 清除所有多光源
 */
export function clearMultipleLights() {
  multipleLights.forEach((multipleLight) => {
    multipleLight.destroy();
  });
  multipleLights = [];
  lights = [];
}

/**
 * 将字符串转成笛卡尔坐标
 * @param {string} positionStr 字符串
 * @returns {Cesium.Cartesian3}
 */
export function stringToCartesian3(positionStr: string) {
  const positionStrArr = positionStr.split(",");
  const positionNumArr = positionStrArr.map((p) => Number(p));
  return Cesium.Cartesian3.fromDegrees(
    positionNumArr[0],
    positionNumArr[1],
    positionNumArr[2]
  );
}

/**
 * 将笛卡尔坐标转成字符串
 * @param {Cesium.Cartesian3} position 笛卡尔坐标
 * @returns {string}
 */
export function cartesian3ToString(position: Cesium.Cartesian3) {
  const cartographic = Cesium.Cartographic.fromCartesian(position);
  return `${Cesium.Math.toDegrees(
    cartographic.longitude
  )},${Cesium.Math.toDegrees(cartographic.latitude)},${cartographic.height}`;
}

/**
 * 将笛卡尔数组转成字符串
 * @param {Cesium.Cartesian3[]} positions 笛卡尔坐标数组
 * @return {string}
 */
export function cartesian3ArrayToString(positions: Cesium.Cartesian3[]){
  let str = '';
  positions.forEach((p,index) => {
    str += cartesian3ToString(p);
    if(index < positions.length - 1){
       str += ',';
    }
  });
  return str;
}

/**
 * 将字符串转成笛卡尔坐标数组
 * @param {string} positionsStr 字符串
 * @returns {Cesium.Cartesian3[]}
 */
export function stringToCartesian3Array(positionsStr: string) {
  const positionStrArr = positionsStr.split(",");
  const positionNumArr = positionStrArr.map((p) => Number(p));

  const pointLength = positionStrArr.length / 3;

  const positions:any = [];

  for(let i = 0; i < pointLength; i++){
    positions.push(Cesium.Cartesian3.fromDegrees(
      positionNumArr[i * 3],
      positionNumArr[i * 3 + 1],
      positionNumArr[i * 3 + 2],
    ))
  }

  return positions;
}

// 光源类型的枚举
export enum LightListType {
  Scene = "场景",
  PointLightGroud = "点光源组",
  PointLight = "点光源",
  SpotLightGroud = "聚光灯组",
  SpotLight = "聚光灯",
  VerticalLightBarGroud = "竖直灯带组",
  VerticalLightBar = "竖直灯带",
  HorizontalLightBarGroud = "水平灯带组",
  HorizontalLightBar = "水平灯带",
}


function click (node: any) {
  try {
    node.dispatchEvent(new MouseEvent('click'))
  } catch (e) {
    var evt = document.createEvent('MouseEvents')
    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80,
                          20, false, false, false, false, 0, null)
    node.dispatchEvent(evt)
  }
}

export function  saveJson(data: any, fileName: string) {
  const value = JSON.stringify(data, undefined,4);
  const blob = new Blob([value], {type: 'text/json'});
  const a = document.createElement('a');
  a.download = fileName;
  a.href = URL.createObjectURL(blob);
  a.dataset.downloadurl = ['text/json', a.download, a.href].join(':');
  click(a);
}

