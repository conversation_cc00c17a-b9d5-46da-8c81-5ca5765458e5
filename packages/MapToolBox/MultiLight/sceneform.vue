<template>
  <div
    class="inline-flex"
    style="margin: 0px 10px 10px 10px; padding: 5px 3px 5px 3px"
    :style="{
      boxShadow: `var(--el-box-shadow-lighter)`,
    }"
  >
    <div class="from-item-dom"  style="margin-bottom: 5px">
      <div style="margin: 2px"><nobr>场景名称</nobr></div>
      <el-input
        style="margin: 0px"
        v-model="form.scenename"
        placeholder="输入场景名称"
      >
      </el-input>
    </div>

    <div class="from-item-dom">
      <div style="margin: 2px"><nobr>选择类型</nobr></div>
      <el-select style="width: 100%" placeholder="选择">
        <el-option
          v-for="item in (tmpSceneGroupsData.get(form.scenename))?Array.from((tmpSceneGroupsData.get(form.scenename) as any).keys()):[]"
          :key="item"
          :label="item"
          :value="item"
        >
        </el-option>
      </el-select>
      <el-button style="margin: 1px" type="success" plain>创建场景</el-button
      ><el-button style="margin: 1px" type="success" plain>导入场景</el-button
      ><el-button style="margin: 1px" type="warning" plain
        >批量导出场景</el-button
      >
    </div>
  </div>
  <el-table
    :data="lightData"
    stripe
    border
    size="small"
    style="width: 100%"
    show-header="false"
    :highlight-current-row="true"
    :header-cell-style="{ 'text-align': 'center' }"
  >
    <el-table-column type="expand">
      <template #default="dtProps">
        <div
          class="inline-flex"
          style="
            margin: 5px 20px;
            padding: 3px;
            box-shadow: var(--el-box-shadow-lighter);
          "
        >
          <el-space wrap>
            <el-button
              v-for="item in scenceViews"
              style="margin: 1px"
              type="warning"
              link
              >{{ item }}</el-button
            >
            <div class="from-item-dom ">
              <el-input
                style="margin: 0px;width: 80px;"
                v-model="scenceViewName"
                placeholder="输入视角名称"
              >
              </el-input>
              <el-button
                style="margin: 1px"
                plain
                type="success"
                @click="AddScenceView"
                >添加场景视角</el-button
              >
            </div>
          </el-space>
        </div>
        <!--2级表 groupname  -->
        <el-table
          style="margin: 0px 4px"
          :data="dtProps.row.children"
          stripe
          :show-header="false"
          border
          size="small"
          :highlight-current-row="true"
          :header-cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column type="expand">
            <template #default="dtPropsSub">
              <div
                class="inline-flex"
                style="
                  margin: 5px 20px;
                  padding: 3px;
                  box-shadow: var(--el-box-shadow-lighter);
                "
              >
                <!-- 6.1  光源强度 光源范围(发光半径)-->
                <el-space :size="4">
                  <SliderInput
                    label="光源强度"
                    style="margin: 1px 0px"
                    :input-width="260"
                    :width="'260px'"
                    :min="0"
                    :max="10"
                    :placeholder="'0'"
                    v-model="form.intensity"
                  ></SliderInput>
                  <SliderInput
                    label="发光半径"
                    style="margin: 1px 0px"
                    :input-width="260"
                    :width="'260px'"
                    :min="0"
                    :max="10"
                    :placeholder="'0'"
                    v-model="form.radius"
                  ></SliderInput>
                </el-space>
                <!-- 6.2  聚灯外角 聚灯内角-->
                <el-space :size="4">
                  <SliderInput
                    v-if="form.lightType === ScenelightType.spotLight"
                    style="margin: 1px 0px"
                    :input-width="260"
                    :width="'260px'"
                    label="聚灯外角"
                    :placeholder="'0'"
                    :min="0"
                    :max="90"
                    v-model="form.outerConeDegrees"
                  ></SliderInput>
                  <SliderInput
                    v-if="form.lightType === ScenelightType.spotLight"
                    style="margin: 1px 0px"
                    :input-width="260"
                    :width="'260px'"
                    label="聚灯内角"
                    :placeholder="'0'"
                    :min="0"
                    :max="90"
                    v-model="form.innerConeDegrees"
                  ></SliderInput>
                </el-space>
                <div style="width: 100%; align-items: end; text-align: right">
                  <el-button
                    style="margin: 1px 30px 1px 0px"
                    type="primary"
                    size="small"
                    plain
                    >添加光源</el-button
                  >
                </div>
              </div>
              <!--3级表 lightname-->
              <el-table
                style="margin: 0px 8px"
                :header-cell-style="{ 'text-align': 'center' }"
                :data="dtPropsSub.row.children"
                :highlight-current-row="true"
                stripe
                :show-header="true"
                border
                size="small"
              >
                <el-table-column label="类型" align="center" :width="85">
                  <div>{{ "水平灯带" }}</div>
                </el-table-column>
                <el-table-column label="光源名称" prop="lightname" />
                <el-table-column
                  label="操作"
                  prop="operations"
                  :width="118"
                  align="center"
                >
                  <el-button
                    style="margin: 1px"
                    type="success"
                    link
                    size="small"
                    >浏览</el-button
                  ><el-button
                    style="margin: 1px"
                    type="success"
                    link
                    size="small"
                    >编辑</el-button
                  ><el-button
                    style="margin: 1px"
                    type="warning"
                    link
                    size="small"
                    >删除</el-button
                  ></el-table-column
                >
              </el-table>
            </template>
          </el-table-column>

          <el-table-column label="类型" align="center" :width="45">
            <div>组</div>
          </el-table-column>
          <el-table-column label="光源组名称" prop="groupname">
            <el-input v-model="form.groupname" placeholder="类别组名">
            </el-input>
          </el-table-column>

          <el-table-column
            label="操作"
            prop="operations"
            :width="128"
            align="center"
          >
            <el-button style="margin: 1px" type="success" link size="small"
              >更名</el-button
            >
            <el-button style="margin: 1px" type="warning" link size="small"
              >删除</el-button
            >
          </el-table-column>
        </el-table>
      </template>
    </el-table-column>
    <el-table-column label="类型" :width="50">
      <div>场景</div>
    </el-table-column>
    <el-table-column label="场景名称" prop="scenename">
      <el-input
        style="margin: 0px"
        v-model="form.scenename"
        placeholder="输入场景名称"
      >
      </el-input>
    </el-table-column>
    <el-table-column label="操作" prop="operations" :width="126" align="center">
      <el-button style="margin: 1px" type="success" link size="small"
        >创建组</el-button
      >
      <el-button style="margin: 1px" type="success" link size="small"
        >更名</el-button
      >
      <el-button style="margin: 1px" type="warning" link size="small"
        >删除</el-button
      >
    </el-table-column>
  </el-table>

  <el-form :model="form">
    <!-- 2.场景名称 -->
    <!-- <el-form-item class="form-item" label="场景名称">
      <div class="from-item-dom">
        <el-input v-model="form.scenename" placeholder="输入场景名称">
          <template #append>
            <el-select
              v-model="form.scenename"
              placeholder="选择"
              class="inputselect"
            >
              <el-option
                v-for="item in Array.from(tmpSceneGroupsData.keys())"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
              <el-option key="itemkey" label="创建场景" value=""> </el-option>
            </el-select>
          </template>
        </el-input>
        <el-button
          v-if="props.isEdit"
          class="form-item-btn"
          plain
          type="success"
          size="small"
          @click.prevent="_DaleteSceneListItem(form.scenename)"
          >创建</el-button
        >
        <el-button
          v-if="props.isEdit"
          class="form-item-btn"
          plain
          type="success"
          size="small"
          @click.prevent="_DaleteSceneListItem(form.scenename)"
          >编辑</el-button
        >
        <el-button
          v-if="props.isEdit"
          class="form-item-btn"
          plain
          type="warning"
          size="small"
          @click.prevent="_DaleteSceneListItem(form.scenename)"
          >删除</el-button
        >
      </div>
    </el-form-item> -->

    <!-- 3.光源组名称 -->
    <!-- <el-form-item class="form-item" label="光源分组">
      <div
        style="
          width: 100%;
          padding: 3px;
          border: 1px solid #9b9a9a3e;
          border-radius: 5px;
        "
      >
        <div class="from-item-dom">
          <div style="margin-right: 2px; width: 35px"><nobr>名称</nobr></div>
          <el-input v-model="form.groupname" placeholder="类别组名">
            <template #append>
              <el-select
                v-model="form.groupname"
                placeholder="选择"
                class="inputselect"
              >
                <el-option
                  v-for="item in (tmpSceneGroupsData.get(form.scenename))?Array.from((tmpSceneGroupsData.get(form.scenename) as any).keys()):[]"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </template>
          </el-input>
          <el-button
            v-if="props.isEdit"
            class="form-item-btn"
            plain
            type="success"
            size="small"
            @click.prevent="
              _DaleteLightGrouItem(form.scenename, form.groupname)
            "
            >删除</el-button
          >
          <el-button
            v-if="props.isEdit"
            class="form-item-btn"
            plain
            type="success"
            size="small"
            @click.prevent="_DaleteSceneListItem(form.scenename)"
            >编辑</el-button
          >
          <el-button
            v-if="props.isEdit"
            class="form-item-btn"
            plain
            type="warning"
            size="small"
            @click.prevent="_DaleteSceneListItem(form.scenename)"
            >删除</el-button
          >
        </div>
        <div class="from-item-dom">
          <div style="margin-right: 2px; width: 35px"><nobr>类型</nobr></div>
          <el-select
            style="width: 100%; margin-right: 176px"
            placeholder="Select"
          >
            <el-option key="item.value" label="item.label" value="item.value" />
          </el-select>
        </div>
      </div>
    </el-form-item> -->
  </el-form>
</template>
<script setup lang="ts">
import { ref, reactive,  } from "vue";
import {
  type ILightType,
  ScenelightType,
  SliderInput,
  // OnemapClass,
} from "../../onemapkit";

const scenceViews = ref(["视角1", "视角2", "视角3"]);
const scenceViewName=ref("视角")
function AddScenceView() { 
  scenceViews.value.push(scenceViewName.value );
}
//测试数据结构
const lightData = [
  {
    index: "2",
    scenename: "场景1---",
    operations: "",
    children: [
      {
        index: "21",
        groupname: "光源组1",
        operations: "",
        children: [
          {
            index: "211",
            lightname: "光源211",
            operations: "",
          },
          {
            index: "212",
            lightname: "光源212",
            operations: "",
          },
        ],
      },
      {
        index: "22",
        groupname: "光源组2",
        operations: "",
        children: [
          {
            index: "221",
            lightname: "光源221",
            operations: "",
          },
          {
            index: "222",
            lightname: "光源222",
            operations: "",
          },
        ],
      },
    ],
  },
  {
    index: "2",
    scenename: "场景1---",
    operations: "",
    children: [
      {
        index: "21",
        groupname: "光源组1",
        operations: "",
        children: [
          {
            index: "211",
            lightname: "光源211",
            operations: "",
          },
          {
            index: "212",
            lightname: "光源212",
            operations: "",
          },
        ],
      },
      {
        index: "22",
        groupname: "光源组2",
        operations: "",
        children: [
          {
            index: "221",
            lightname: "光源221",
            operations: "",
          },
          {
            index: "222",
            lightname: "光源222",
            operations: "",
          },
        ],
      },
    ],
  },
];

//@ts-ignore
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true,
  },
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
});
///////////////////////// 一、数据操作

/** 1. 光源场景集合--------------------->场景------->-光源组-->光源列表           */
const tmpSceneGroupsData = reactive<Map<string, Map<string, [ILightType]>>>(
  new Map<string, Map<string, [ILightType]>>()
);

/** 2.光源效果临时表 */
const _tempLightEffectTable: any = reactive<
  Array<{
    index: number;
    EffectName: string;
    ColorCode: string;
    SpaceTime: string;
  }>
>([]);

/** 光源数据结构*/
const form: ILightType = reactive({
  index: tmpSceneGroupsData.size,
  lightType: ScenelightType.pointLight,
  scenename: "SN" + new Date().getTime(),
  groupname: "GN" + new Date().getTime(),
  lightname: "LN" + new Date().getTime(),
  lightEffectName: ScenelightType.lightmodelstatic,
  lightEffects: _tempLightEffectTable,
  lightobjecttag: "lightobjecttag",
  intensity: 50,
  radius: 100,
  outerConeDegrees: 45,
  innerConeDegrees: 10,
  position: {
    x: 0,
    y: 0,
    z: 0,
  },
  directionPoint: {
    x: 0,
    y: 0,
    z: 0,
  },
  lightBarType: ScenelightType.LightBarHorizontal,
  lightBarRadius: 0.5,
  lightBarOpenRay: true,
  lightBarRingClose: false,
  lightBarTopHight: 145,
  color: "#BD0505",
});
</script>
<style scoped>
.inputselect {
  width: 25px;
  margin: 0px;
  padding: 0px;
}
/*
:deep(.el-input__wrapper) {
  width: 0px;
  padding: 0px 1px;
}
 :deep(.el-card__body) {
  padding: 0px 0px;
  margin: 3px 3px;
}

:deep(.el-input-group__append) {
  width: 23px;
  padding: 0px;
}
:deep(.el-input__suffix) {
  width: 25px;
  padding: 0px 5px 0px 0px;
} */

.form-item {
  margin: 8px 0px;
}
.from-item-dom {
  display: flex;
  width: 100%;
  align-items: center;
}
.form-item-btn {
  margin-left: 3px;
}
</style>
