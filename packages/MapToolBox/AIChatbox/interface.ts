import { type VNodeChild, type Component } from 'vue';

// 临时常量ID
export const AI_TEMP_ID = {
    DRAW_ID: 'ai_temp_draw_id'
}

export const AI_GATHER_TYPE = {
    GATHER_START: 'gather_start',
    GATHER_END: 'gather_end'
}

export const AI_NEXT_TYPE = {
    GATHER: 'gather',
    DRAW_TOOLS: 'drawTools',
    MAPTOOL_QUERY: 'maptoolquery'
}

export interface PromptItem {
    key: string | number;
    label?: string;
    description?: string;
    icon?:  Component | string;
    disabled?: boolean;
    drawToolsVisible?: boolean;
    type?: 'drawTools';
    children?: PromptItem[];
    options?: Record<string, any>;
    model?: 'yusy-model' | 'dify-model' | 'mock-model';
}

export interface PromptsProps {
    prefixCls?: string;
    title?: string | VNodeChild;
    class?: string;
    items?: PromptItem[];
    onItemClick?: (info: PromptItem, options?: Record<string, any>) => void;
    vertical?: boolean;
    wrap?: boolean;
    rootClassName?: string;
    styles?: {
        title?: Record<string, any>;
        list?: Record<string, any>;
        item?: Record<string, any>;
        itemContent?: Record<string, any>;
        subList?: Record<string, any>;
        subItem?: Record<string, any>;
    };
    classNames?: {
        title?: string;
        list?: string;
        item?: string;
        itemContent?: string;
        subList?: string;
        subItem?: string;
    };
    style?: Record<string, any>;
    MapControlName?: string;
}

export interface AIBubbleItemButtonProps {
    label: string;
    type?: 'primary' | 'success' | 'warning' | 'danger';
    method: 'route' | 'location' | 'draw';
    params: any;
    onClick?: Function;
    callback?: () => void;
}