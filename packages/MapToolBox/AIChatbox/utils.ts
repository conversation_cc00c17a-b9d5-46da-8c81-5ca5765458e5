import coordtransform from 'coordtransform';
import * as Cesium from '@onemapkit/cesium';
import * as turf from '@turf/turf';
import type { DrivingRouteResponse } from "./types";
import { mapType, OnemapClass } from '@/onemapkit';

export const fromCGCS2000Array = (coordinates: number[][]): Cesium.Cartesian3[] => {
    return coordinates.map((coord) => {
        const longitude = coord[0];
        const latitude = coord[1];
        const height = coord.length > 2 ? coord[2] : 0;

        return Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
    });
};

/**
 * 将 GCJ02 坐标系转换为 WGS84 坐标系
 * @param response
 * @returns
 */
export const fromWgs84Array = (response: DrivingRouteResponse): number[][] => {
    const coordinates: number[][] = [];

    // 获取第一条路径的所有步骤
    const steps = response.route.paths[0].steps;

    // 遍历每个步骤
    steps.forEach((step) => {
        // 将 polyline 字符串按分号分割成坐标对
        const points = step.polyline.split(';');

        // 处理每个坐标对
        points.forEach((point) => {
            // 将坐标字符串分割成经度和纬度
            const [longitude, latitude] = point.split(',').map(Number);
            coordinates.push([longitude, latitude]);
        });
    });

    // 移除重复的坐标点
    const uniqueCoordinates = coordinates.filter(
        (coord, index, self) => index === self.findIndex((c) => c[0] === coord[0] && c[1] === coord[1])
    );

    // 将坐标转换为 WGS84 坐标系
    return uniqueCoordinates.map((coord) => {
        return coordtransform.gcj02towgs84(coord[0], coord[1]);
    });
};

/**
 * 将 WGS84 坐标系转换为 GCJ02 坐标系
 * @param coordinates
 * @returns
 */
export const toGCJ02Array = (coordinates: number[]): number[] => {
    return coordtransform.wgs84togcj02(coordinates[0], coordinates[1]);
};

/**
 * 将颜色转换为十六进制字符串
 * @param color 颜色值
 * @returns 十六进制字符串
 */
export const colorToString = (color: number[]) => {
    let temp = JSON.parse(JSON.stringify(color));
    let opacity = '';

    // 检查是否有透明度值
    if (color.length > 3 && color[3] !== undefined) {
        opacity = (parseInt((String(255 * color[3]))).toString(16)).padStart(2, '0');
        temp.pop(); // 只有在有透明度值时才移除
    } else {
        opacity = 'ff'; // 默认不透明
    }
    return `#${temp.map((c: number) => c.toString(16).padStart(2, '0')).join('')}${opacity}`;
}

/**
 * 将坐标转换为 GeoJSON 格式
 * @param rings 坐标数组
 * @returns GeoJSON 格式
 */
export const toGeoJsonFormat = (rings: number[][]) => {
    return {
        type: 'FeatureCollection',
        features: rings.map((coords) => ({
            type: 'Feature',
            geometry: {
                type: 'Polygon',
                coordinates: coords,
            },
        })),
    };
}

/**
 * 格式化并合并多边形并计算中心点
 * @param rings 坐标数组
 * @returns 合并后的多边形和中心点
 */
export const formatAndMergeGeoGetCenterPoint = (rings: number[][]) => {
    const geoJSON = toGeoJsonFormat(rings);
    const { merged, center } = computeCenterAndMergePolygons(geoJSON);
    return { merged, center };
};

const computeCenterAndMergePolygons = (geoJSON: any) => {
    // 批量合并（优化性能版）
    const merged = geoJSON.features.reduce((prev: any, current: any) => {
        try {
            return turf.union(prev, current) || prev;
        } catch (e) {
            console.warn('合并失败，保留之前结果', e);
            return prev;
        }
    });
    // 中心点计算
    const center = turf.centroid(merged);

    // 精确坐标处理
    center.geometry.coordinates = center.geometry.coordinates.map(
        (v: any) => Number(v.toFixed(6)) // 保留6位小数精度
    );

    return { merged, center: [center.geometry.coordinates[0], center.geometry.coordinates[1]] };
}

export const toDegree = (cord: Cesium.Cartesian3) => {
    let _cord = Cesium.Cartographic.fromCartesian(cord);
    return {
        x: Cesium.Math.toDegrees(_cord.longitude),
        y: Cesium.Math.toDegrees(_cord.latitude),
        z: _cord.height,
    };
};

export const getPolylineCenter = (positions: Cesium.Cartesian3[], isDegree = false): Cesium.Cartesian3 | any => {
    // 计算平均坐标
    const centerCartesian = positions.reduce((sum, pos) => {
        return Cesium.Cartesian3.add(sum, pos, new Cesium.Cartesian3());
    }, new Cesium.Cartesian3());
    Cesium.Cartesian3.divideByScalar(centerCartesian, positions.length, centerCartesian);
    return isDegree ? toDegree(centerCartesian) : centerCartesian;
};

/**
 * 防抖函数
 * @param fn 需要防抖的函数
 * @param delay 延迟时间,默认300ms
 * @returns 返回防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number = 300) => {
    let timer: any = null;
    
    return function(this: any, ...args: Parameters<T>) {
        if(timer) {
            clearTimeout(timer);
        }
        
        timer = setTimeout(() => {
            fn.apply(this, args);
            timer = null;
        }, delay);
    }
}

/**
 * 移除代码块或提取特定代码块内容
 * @param str 字符串
 * @param language 指定要提取的代码块语言，如果提供，则返回该语言的代码块内容
 * @returns 处理后的字符串
 */
export const removeCodeBlock = (str: string, language?: string) => {
    // 如果指定了语言，提取该语言的代码块内容
    if (language) {
        const regex = new RegExp(`\`\`\`${language}\\s*\\n([\\s\\S]*?)\\n\`\`\``, 'g');
        const match = regex.exec(str);
        
        if (match && match[1]) {
            // 直接返回捕获组中的内容
            return JSON.parse(match[1].trim());
        }
        return '';
    }

    // 移除所有代码块
    const stripped = str.replace(/```[\s\S]*?\n|```/g, '');

    const unescaped = stripped
        .replace(/\\(["'\\/bfnrt])/g, (_, char) => {
            const escapeMap = {
                '"': '"',
                "'": "'",
                '\\': '\\',
                '/': '/',
                b: '\b',
                f: '\f',
                n: '\n',
                r: '\r',
                t: '\t',
            };
            return escapeMap[char as keyof typeof escapeMap] || _;
        })
        .replace(/\\u([\dA-Fa-f]{4})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)));
    return JSON.parse(unescaped.trim());
}

export const setDynamicKey = <T extends string, V>(
    obj: Record<T, V>, // 约束键的类型为 T
    key: T,
    value: V
): Record<T, V> => {
    obj[key] = value;
    return obj;
}

/**
 * 根据ID移除图形
 * @param id 图形ID
 * @param _inOnemap Onemap实例
 */
export const removeGraphicById = (id: string, _inOnemap: OnemapClass) => {
    if (!_inOnemap) {
        return
    }
    if (_inOnemap.MapType.value === mapType.arcgis) {
        _inOnemap.removeGraphics("", [id]);
        //标注
        _inOnemap.DrawBrush.removeGraphics(undefined, [
            id,
        ]);
        return
    }
    const entity = _inOnemap.MapViewer.entities.getById(id)
    if (!entity) {
        return
    }
    _inOnemap.MapViewer.entities.remove(entity)
}