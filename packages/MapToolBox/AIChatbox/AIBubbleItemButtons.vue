<template>
    <div class="ai-bubble-item-buttons">
        <div v-for="(button, index) in buttons">
            <el-button size="small" plain :key="index" :type="button.type || 'primary'" @click="handleClick(button)">
                {{ button.label }}
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { AIBubbleItemButtonProps } from './interface';

defineProps<{
    buttons: AIBubbleItemButtonProps[];
}>();

const handleClick = (button: AIBubbleItemButtonProps) => {
    if (button.onClick) {
        return button.onClick(button.params);
    }
    if (button.callback) {
        button.callback();
    }
}
</script>

<style scoped lang="scss">
.ai-bubble-item-buttons {
    display: flex;
    // flex-direction: column;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}
</style>
