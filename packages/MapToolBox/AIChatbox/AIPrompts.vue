<script setup lang="ts">
import { computed, ref, type VNode } from 'vue';
import { ElCard } from 'element-plus';
import AIDrawTools, { type AIDrawToolsProps } from './components/AIDrawTools.vue';
import type { PromptsProps, PromptItem } from './interface';

defineOptions({ name: 'AIPrompts' });

const emits = defineEmits(['itemClick', 'toolClick', 'drawCallback']);

const props = withDefaults(defineProps<PromptsProps>(), {
  prefixCls: 'ai-prompts',
  items: () => [],
  vertical: false,
  wrap: false,
  styles: () => ({}),
  classNames: () => ({}),
  MapControlName: '',
});

const slots = defineSlots<{
  title?(): VNode | string;
}>();

const hoveredKeys = ref(new Set());
const activeKeys = ref(new Set());

// 计算样式类名
const mergedCls = computed(() => {
  return {
    [props.prefixCls || '']: true,
    [`${props.prefixCls}-rtl`]: false,
    [props.class || '']: !!props.class,
    [props.rootClassName || '']: !!props.rootClassName,
  }
});

const mergedListCls = computed(() => {
  return {
    [`${props.prefixCls}-list`]: true,
    [`${props.prefixCls}-list-wrap`]: props.wrap,
    [`${props.prefixCls}-list-vertical`]: props.vertical,
    [props.classNames?.list || '']: !!props.classNames?.list,
  }
});

// 标题节点
const titleNode = computed(() => {
  if (slots.title) {
    return slots.title();
  }
  return props.title;
});

const handleMouseEnter = (key: string | number) => {
  hoveredKeys.value.add(key)
}

const handleMouseLeave = (key: string | number) => {
  hoveredKeys.value.delete(key)
}

const handleMouseDown = (key: string | number) => {
  activeKeys.value.add(key)
}

const handleMouseUp = (key: string | number) => {
  activeKeys.value.delete(key)
}

const isHovered = (key: string | number) => {
  return hoveredKeys.value.has(key)
}

// 处理提示项点击
const handleItemClick = (item: PromptItem) => {
  console.log(item);
  if (item.disabled) {
    return
  }
  if (item.children && item.children.length > 0) {
    return
  }
  if (item.type === 'drawTools') {
    item.drawToolsVisible = !item.drawToolsVisible;
    return;
  }
  emits('itemClick', item)
};

const handleDrawCallback = (item: PromptItem, data: any) => {
  emits('drawCallback', item, data)
}

const handleToolClick = (tool: AIDrawToolsProps) => {
  emits('toolClick', tool)
}

</script>

<template>
  <div :class="mergedCls" :style="style">
    <!-- 标题 -->
    <div :class="[`${prefixCls}-title`, classNames?.title]" :style="styles?.title">
      {{ titleNode }}
    </div>

    <!-- 提示列表 -->
    <div :class="mergedListCls" :style="styles?.list">
      <ElCard v-for="(item, index) in items" shadow="never" :key="item.key || `key_${index}`" :class="[
        `${prefixCls}-item`,
        { [`${prefixCls}-item-disabled`]: item.disabled },
        { [`${prefixCls}-item-has-nest`]: item.children && item.children.length > 0 },
        { [`${prefixCls}-hover`]: isHovered(`key_${index}`) && (!item.children || item.children?.length === 0) },
        classNames?.item
      ]" :style="styles?.item" :body-style="{ padding: '0', width: '100%' }" @click.stop="handleItemClick(item)"
        @mouseenter.stop="handleMouseEnter(`key_${index}`)" @mouseleave.stop="handleMouseLeave(`key_${index}`)"
        @mousedown.stop="handleMouseDown(`key_${index}`)" @mouseup.stop="handleMouseUp(`key_${index}`)">
        <AIDrawTools v-model:visible="item.drawToolsVisible" :MapControlName="props.MapControlName" @tool-click="handleToolClick" @drawCallback="(data) => handleDrawCallback(item, data)">
          <template #reference>
            <div :class="`${prefixCls}-item-inner`">
              <!-- 图标 -->
              <div v-if="item.icon" :class="`${prefixCls}-icon`">
                <component :is="item.icon" />
              </div>

              <!-- 内容 -->
              <div :class="[
                `${prefixCls}-content`,
                classNames?.itemContent
              ]" :style="styles?.itemContent">
                <!-- 标签 -->
                <h6 v-if="item.label" :class="`${prefixCls}-label`">{{ item.label }}</h6>

                <!-- 描述 -->
                <p v-if="item.description" :class="`${prefixCls}-desc`">{{ item.description }}</p>

                <!-- 子项 -->
                <template v-if="item.children && item.children.length > 0">
                  <!-- <ElDivider /> -->
                  <div :class="[
                    `${prefixCls}-nested`,
                    classNames?.subList
                  ]" :style="styles?.subList">
                    <div v-for="(subItem, subIndex) in item.children" :key="subItem.key || `subkey_${subIndex}`" :class="[
                      `${prefixCls}-subitem`,
                      { [`${prefixCls}-subitem-disabled`]: subItem.disabled },
                      classNames?.subItem
                    ]" :style="styles?.subItem" @click.stop="!subItem.disabled && onItemClick && onItemClick(subItem, item?.options)"
                         :type="subItem.disabled ? 'info' : 'primary'" :effect="subItem.disabled ? 'plain' : 'dark'"
                         size="large">
                      <div class="ai-prompts-tag-content">
                        <!-- 子项图标 -->
                        <span v-if="subItem.icon" :class="`${prefixCls}-subicon`">
                          <component :is="subItem.icon" />
                        </span>
                        <span v-if="subItem.label" class="ai-prompts-tag-label">{{ subItem.label }}</span>
                        <span v-if="subItem.description" class="ai-prompts-subdesc">{{ subItem.description }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </AIDrawTools>
      </ElCard>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ai-prompts {
  width: 100%;

  &-title {
    margin-block-start: 0;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 0.5em;
    font-size: 16px;
    line-height: 1.5;
    box-sizing: border-box;
    word-break: break-word;
    font-family: BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
      sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }

  &-list {
    // display: flex;
    // flex-wrap: wrap;
    // gap: 12px;

    display: flex;
    gap: 12px;
    // overflow-x: scroll;
    list-style: none;
    padding-inline-start: 0;
    margin-block: 0;
    align-items: stretch;

    &-vertical {
      flex-direction: column;
    }

    &-wrap {
      flex-wrap: wrap;
    }
  }

  &-item {
    // transition: all 0.3s;
    // border-radius: 8px;
    // flex: 1;

    flex: none;
    display: flex;
    gap: 8px;
    height: auto;
    padding-block: 12px;
    padding-inline: 16px;
    align-items: flex-start;
    justify-content: flex-start;
    background: #ffffff;
    border-radius: 8px;
    transition: border 0.3s, background 0.3s;
    border: 1px solid #f0f0f0;

    &-disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    &-inner {
      width: 100%;
      flex: none;
      display: flex;
      gap: 8px;
    }

    &-has-nest {
      flex: 1;
    }
  }

  &-hover {
    cursor: pointer;
    background: rgba(0, 0, 0, 0.08);
  }


  &-icon {
    display: flex;
    align-items: center;
    // margin-right: 8px;
    margin: 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.5714285714285714;
    text-align: start;
    white-space: normal;
  }

  &-content {
    flex: auto;
    min-width: 0;
    display: flex;
    gap: 4px;
    flex-direction: column;
    align-items: flex-start;
  }

  &-label {
    margin: 0 0 4px 0;
    font-size: 16px;
    line-height: 1.5;
    font-weight: 500;
    text-align: start;
    white-space: normal;
    color: var(--el-text-color-primary);
  }

  &-desc {
    margin: 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.5714285714285714;
    text-align: start;
    white-space: normal;
    color: var(--el-text-color-primary);
  }

  // 当描述前面有标签时，描述使用次要颜色
  &-label+&-desc {
    color: var(--el-text-color-secondary);
  }

  &-nested {
    // margin-top: 8px;
    // display: flex;
    // flex-direction: column;
    // gap: 8px;

    margin-top: 8px;
    align-self: stretch;
    flex-direction: column;
    display: flex;
    gap: 12px;
    // overflow-x: scroll;
    list-style: none;
    padding-inline-start: 0;
    margin-block: 0;
  }

  &-subicon {
    margin-right: 4px;
  }

  &-tag-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  &-tag-label {
    font-weight: 500;
    font-size: 14px;
  }

  &-subdesc {
    // font-size: 12px;
    // opacity: 0.8;
    // margin-top: 4px;
    // display: block;

    margin: 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.5714285714285714;
    text-align: start;
    white-space: normal;
  }

  &-subitem {
    // cursor: pointer;
    // width: 100%;
    // display: flex;
    // padding: 6px 10px;
    // margin-bottom: 4px;

    flex: none;
    display: flex;
    gap: 8px;
    height: auto;
    padding-block: 12px;
    padding-inline: 16px;
    align-items: flex-start;
    justify-content: flex-start;

    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    transition: border 0.3s, background 0.3s;
    border: 0;

    &:hover {
      cursor: pointer;
      background: rgba(0, 0, 0, 0.08);
    }

    &-disabled {
      cursor: not-allowed;
    }

    :deep(.el-tag__content) {
      display: flex;
      width: 100%;
    }
  }
}
</style>
