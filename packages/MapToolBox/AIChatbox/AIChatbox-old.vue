<script setup lang="ts">
import { ref, h, onUnmounted, provide, reactive } from 'vue';
import { B<PERSON><PERSON>List, MentionSender, Welcome, Thinking } from 'vue-element-plus-x';
// @ts-ignore
import type { BubbleListProps } from 'vue-element-plus-x/types/BubbleList';
// import type { TypewriterInstance } from 'vue-element-plus-x/types/Typewriter'
import type { ListType, TipItem, ProcessResponse } from './types';
import { ElementPlus } from '@element-plus/icons-vue';
// import { ElIcon } from 'element-plus';
import AIPrompts from './AIPrompts.vue';
import AIBubbleItem from './AIBubbleItem.vue';
import { type PromptsProps, AI_GATHER_TYPE, AI_NEXT_TYPE } from './interface';
import { useSend, XRequest, type BaseFetchOptions } from './useSend';
import { Icons } from 'onemapkit';
import { mockDataList } from './mock';
// import { VideoPause } from '@element-plus/icons-vue';
// import lanmei from './lanmei.json';

const props = defineProps({
  MapControlName: {
    type: String,
    default: 'mainMapControl',
    require: true,
  },
  ToolData: {
    type: Object,
    default: {},
  },
  PropStore: {
    type: Object,
    default: {},
  },
  Options: {
    type: Object,
    default: {
      baseURL: 'http://localhost:3800',
      height: '100%',
      drivingRouteApi: () => { },
      searchBlueberryData: () => { },
      searchPigData: () => { },
      searchIndustryData: () => { }
    },
  },
});
// const _inOnemap = getOnemap(props.MapControlName);

const placeholderPromptsItems: PromptsProps['items'] = [
  // {
  //     label: '不动产问答助手',
  //     key: 'real-estate-assistant',
  //     description: '你好，有什么可以帮助你的吗?',
  //     children: [
  //         {
  //             key: '1-1',
  //             description: `一手房商品房和新建车位车库过户`,
  //         },
  //         {
  //             key: '1-2',
  //             description: `一手限价商品房过户`,
  //         },
  //         {
  //             key: '1-3',
  //             description: `为什么刷脸会失败`,
  //         },
  //     ],
  // },
  {
    label: 'AI选址助手',
    key: 'ai-map-assistant',
    description: '猜您可能问的问题',
    children: [
      {
        key: '1-1',
        description: `推荐几块面积10亩左右适合蓝莓种植的场地`,
      },
      {
        key: '1-2',
        description: `推荐几块面积10亩左右适合生猪养殖的场地`,
      },
      {
        key: '1-3',
        description: `推荐几块面积5亩左右适合罗氏虾养殖的场地`,
      },
      {
        key: '1-4',
        description: `查找哪些区域适合发展蓝莓种植,具体要求：面积大于2000平方米、坡度小于12度，挑选得分最高的前5个地块列表对比，按得分从大到小排列，当总分相同时，按面积从大到小排列`,
      },
    ],
  },
];
const senderPromptsItems: PromptsProps['items'] = [
  {
    key: 'new-dialog',
    description: '开启新对话',
    icon: h(Icons.newDialog, { style: { color: '#FF4D4F', width: '18px', height: '18px' } }),
  },
  // {
  //     key: 'ai-map-assistant',
  //     description: 'AI选址助手',
  //     icon: h(ElIcon, h(ElementPlus, { style: { color: '#1890FF' } })),
  // }
];

const messageList = ref<any[]>([]);
const inputContent = ref('');
const isDeepseekMode = ref(false);
const currentModel = ref<'deepseek-r1' | 'deepseek-v3'>('deepseek-v3');
const sessionid = ref<string>('');

const handlePromptsItemClick: PromptsProps['onItemClick'] = (info) => {
  if (info.key === 'new-dialog') {
    abort();
    sessionid.value = window.crypto.randomUUID();
    messageList.value = [];
    cacheState.clear();
  } else {
    handleSubmit(info.description as string);
  }
};

const handleSubmit = (nextContent: string, data?: { answer?: string, tipItems?: TipItem[], data?: any, toolParams?: any, isGather?: boolean, gatherContext?: any, isApiResult?: boolean }, callback?: () => void) => {
  if (!nextContent) {
    send(nextContent, data?.toolParams);
    return;
  }

  // 处理收集上下文场景
  if (data?.gatherContext) {
    askForNextParameter({
      lable: nextContent,
    }, data.gatherContext);
    return;
  }

  // 添加用户消息
  addUserMessage(nextContent);

  // 检查是否需要处理参数收集
  if (messageList.value.length > 0) {
    const lastAiMessage = getLastAiMessage();
    if (lastAiMessage?.gatherContext && lastAiMessage.gatherType === AI_GATHER_TYPE.GATHER_START) {
      handleUserAnswerForGather(nextContent, lastAiMessage.gatherContext);
      return;
    }
  }

  // 处理模拟回答场景
  if (data?.answer) {
    pushMockAiAnswer({
      answer: data.answer,
      data: data.data,
    }, callback);
    return;
  }

  // 发送正常请求
  send(nextContent, data?.toolParams);
};

// 通用消息推送方法
const addMessage = (messageConfig: Partial<ListType>) => {
  messageList.value.push(messageConfig as ListType);
};

// 预定义的消息配置
const createUserMessage = (content: string): Partial<ListType> => ({
  content,
  role: 'user',
  placement: 'end',
});

const createAiMessage = (content: string, extraConfig: Partial<ListType> = {}): Partial<ListType> => ({
  content,
  role: 'ai',
  placement: 'start',
  typing: false,
  ...extraConfig,
});

const addUserMessage = (content: string) => {
  addMessage(createUserMessage(content));
  inputContent.value = '';
};

const getLastAiMessage = () => {
  // 从右到左遍历, 找到第一条AI消息
  return messageList.value.reduceRight((found, msg) =>
      found || (msg?.role === 'ai' ? msg : null), null
  );
};

const handleUserAnswerForGather = (userAnswer: string, gatherContext: any) => {
  const { nextinfo, currentArgs } = gatherContext;
  const gatherItem = findGatherItem(nextinfo);

  if (!gatherItem) {
    console.error('未找到收集项配置');
    return;
  }

  const { gatherList } = gatherItem;

  // 更新当前缺失的参数
  updateMissingParameter(gatherList, currentArgs, userAnswer);

  // 检查是否还有缺失参数
  const missingParam = findMissingParameter(gatherList, currentArgs);

  if (missingParam) {
    // 继续询问下一个参数
    askForNextParameter(missingParam, { nextinfo, currentArgs });
  } else {
    // 所有参数收集完毕
    completeParameterGathering(gatherItem, nextinfo, currentArgs);
  }
};

const findGatherItem = (nextinfo: any[]) => {
  return nextinfo.find((item: any) => item.type === AI_NEXT_TYPE.GATHER);
};

const findMissingParameter = (gatherList: any[], currentArgs: any) => {
  return gatherList.find((item: any) => !(item.key in currentArgs));
};

const updateMissingParameter = (gatherList: any[], currentArgs: any, userAnswer: string) => {
  const missingParam = findMissingParameter(gatherList, currentArgs);
  if (missingParam) {
    currentArgs[missingParam.key] = userAnswer;
    console.log('📝 更新参数:', missingParam.key, '=', userAnswer);
  }
};

const askForNextParameter = (missingParam: any, gatherContext: any) => {
  console.log('🚀 仍有缺失参数，继续询问:', missingParam);

  addMessage(createAiMessage(missingParam.lable, {
    isMarkdown: false,
    gatherContext,
    typing: {
      step: 1,
      interval: 40
    },
    gatherType: AI_GATHER_TYPE.GATHER_START
  }));
};

const completeParameterGathering = (gatherItem: any, nextinfo: any[], currentArgs: any) => {
  const { toolargs, messages, resultfield } = gatherItem;
  const gatherToolname = toolargs.toolname;

  // 构建更新后的工具参数
  const updatedToolparams = createUpdatedToolParams(currentArgs);
  const newGatherItem = createNewGatherItem(gatherItem, updatedToolparams, resultfield);
  const newNextinfo = syncToolParams(nextinfo, gatherToolname, updatedToolparams);

  // 推送完成消息
  addMessage(createAiMessage(messages || '补充数据收集完成', {
    typing: true,
    nextinfo: [newGatherItem, ...newNextinfo],
    gatherType: AI_GATHER_TYPE.GATHER_END,
  }));

  // 延迟完成消息
  setTimeout(() => {
    finishCurrentMessage();
  }, 1000);
};

const createUpdatedToolParams = (currentArgs: any): string => {
  return '```json\n' + JSON.stringify(currentArgs) + '\n```';
};

const createNewGatherItem = (gatherItem: any, updatedToolparams: string, resultfield: any) => {
  return {
    type: AI_NEXT_TYPE.GATHER,
    gatherList: gatherItem.gatherList,
    toolargs: {
      ...gatherItem.toolargs,
      toolparams: updatedToolparams
    },
    resultfield
  };
};

const syncToolParams = (nextinfo: any[], gatherToolname: string, updatedToolparams: string) => {
  return nextinfo
      .filter((item: any) => item.type !== AI_NEXT_TYPE.GATHER)
      .map((item: any) => {
        // 如果 toolname 相同，同步更新 toolparams
        if (item.toolargs?.toolname === gatherToolname) {
          return {
            ...item,
            toolargs: {
              ...item.toolargs,
              toolparams: updatedToolparams
            }
          };
        }
        return { ...item };
      });
};

const handleToggleModel = () => {
  isDeepseekMode.value = !isDeepseekMode.value;
  currentModel.value = isDeepseekMode.value ? 'deepseek-r1' : 'deepseek-v3';
};

const isMarkdownContent = (content: string): boolean => {
  if (!content) return false;
  const mdPatterns = [
    /#{1,6}\s+/,
    /\*\*.+?\*\*/,
    /\*.+?\*/,
    /\[.+?]\(.+?\)/,
    /```[\s\S]*?```/,
    /`[^`]+?`/,
    /^\s*>\s+/,
    /^\s*[-*+]\s+/,
    /^\s*\d+\.\s+/,
    /\|\s+.*\s+\|/,
  ];
  return mdPatterns.some((pattern) => pattern.test(content));
};

let fullContent = '';

const fetch = new XRequest<ProcessResponse>({
  baseURL: props.Options.baseURL,
  // baseURL: '/ai-api',
  type: 'fetch',
  transformer: (e) => {
    try {
      return JSON.parse(e);
      // const temp = JSON.parse(e);
      // return {
      //     choices: [
      //         {
      //             delta: {
      //                 content: temp.message || temp
      //             }
      //         }
      //     ]
      // };
    } catch (error: any) {
      if (e === '[DONE]') {
        // 处理数据结束的情况
        // console.log('数据接收完毕')
      } else {
        console.info(e);
        // console.error(error);
      }
      return {
        choices: [
          {
            delta: {
              content: '',
              reasoning_content: ''
            }
          }
        ]
      };
    }
  },
  onMessage: (chunk) => {
    if (!chunk.choices) {
      return;
    }
    const delta = chunk.choices[0].delta;
    const lastMessage = messageList.value[messageList.value.length - 1];

    if (delta.content?.nextinfo) {
      const nextinfo = delta.content.nextinfo;
      lastMessage.nextinfo = nextinfo;
      lastMessage.completed = delta.content?.completed;

      // 流程结束了，重新生成一个sessionid
      if (delta.content.completed) {
        sessionid.value = window.crypto.randomUUID();
      }
      return;
    }

    let content = String(delta.content || '');
    let reasoningContent = '';
    // if (typeof delta.content === 'object' && delta.content?.isEnd) {
    //     content = delta.message.message || '';
    // } else {
    //     content = String(delta.content || '');
    // }
    if (content === 'isEnd:True' || content.includes('## -------- AI智能分析数据 --------')) {
      content = '';
    }

    fullContent = fullContent + content;
    // console.log(content);

    const isMarkdown = isMarkdownContent(fullContent);

    // 检查是否已有AI回复消息
    if (lastMessage && lastMessage.role === 'ai') {
      // 如果已经有AI消息，更新其内容（流式响应的情况）
      lastMessage.content = (lastMessage.content || '') + content;
      // 如果包含Markdown格式，更新标记
      if (isMarkdown) {
        lastMessage.isMarkdown = true;
      }
      if (content) {
        lastMessage.openThinking = false;
        lastMessage.thinkingStatus = 'start';
      }
      if (delta.reasoning_content) {
        lastMessage.openThinking = true;
        lastMessage.thinkingStatus = 'thinking';
        lastMessage.reasoningContent = (lastMessage.reasoningContent || '') + delta.reasoning_content;
      }
    } else {
      // 如果没有AI消息，创建新的AI消息
      addMessage(createAiMessage(content, {
        reasoningContent,
        isMarkdown: false,
        isFog: false,
        thinkingStatus: 'start',
      }));
    }
  },
  onError: (es, e) => {
    loading.value = false;
    console.log('onError:', es, e);
  },
  onOpen: () => {
    fullContent = '';
    console.log('onOpen');
  },
  onAbort: (_) => {
    console.log('onAbort');
  },
  onFinish: (_, callData) => {
    // 保存原始数据
    if (callData) {
      const lastMessage = messageList.value[messageList.value.length - 1];
      lastMessage.callData = callData;
    }
    loading.value = false;
    finishCurrentMessage();
    console.log('\n完整内容为：');
    console.log(fullContent);
    fullContent = '';
  },
});

const finishCurrentMessage = () => {
  const lastMessage = messageList.value[messageList.value.length - 1];
  if (lastMessage && lastMessage.role === 'ai') {
    lastMessage.typing = false;
    lastMessage.isFog = false;
    lastMessage.thinkingStatus = 'end';

    const isMarkdown = isMarkdownContent(fullContent);
    // 如果包含Markdown格式，更新标记
    if (isMarkdown) {
      lastMessage.isMarkdown = true;
    }

    lastMessage?.bubbleItemRef?.init();
  }
};

const handleAbort = () => {
  abort();
  console.log('请求被用户取消');
  // 可以在这里添加一条消息表示请求被用户取消
  finishCurrentMessage();
};

const {
  send,
  loading,
  abort,
  // finish,
} = useSend({
  sendHandler: startFn,
  abortHandler: fetch.abort,
});

const pushMockAiAnswer = (mockData: { answer: string, tipItems?: TipItem[], data?: any }, callback?: () => void) => {
  triggerIndices.value = 'all';
  setTimeout(() => {
    addMessage(createAiMessage(mockData.answer, {
      tipItems: mockData.tipItems,
      isMarkdown: true,
      typing: {
        step: 15,
        interval: 40
      },
      data: mockData.data,
      typingCallback: callback,
    }));
    fullContent = mockData.answer;
    // loading.value = false;
    // finishCurrentMessage();
  }, 1000);
}

async function startFn(nextContent: string, toolParams?: any) {
  // 设置生成状态为true
  try {
    const mockData = mockDataList.find((item) => item.question === nextContent);
    if (mockData) {
      pushMockAiAnswer(mockData);
      return;
    }
    triggerIndices.value = 'only-last';

    if (!sessionid.value) {
      sessionid.value = window.crypto.randomUUID();
    }
    fetch.send('/v1/chat/geoagent/', {
      // fetch.send('/api/chatgeo/', {
      method: 'POST',
      body: toolParams?.nextInfo ? JSON.stringify(
          {
            graph_model: currentModel.value,
            sessionid: sessionid.value,
            ...toolParams.nextInfo
          }
      ) : JSON.stringify({
        graph_model: currentModel.value,
        messages: nextContent,
        sessionid: sessionid.value
      }),

      // 回调里传回来
      callData: toolParams?.callData,
    } as BaseFetchOptions);
  } catch (error: any) {
    // 捕获错误，判断是否是AbortError
    if (error.name === 'AbortError') {
      console.log('请求被用户取消');
      // 可以在这里添加一条消息表示请求被用户取消
      const lastMessage = messageList.value[messageList.value.length - 1];
      if (lastMessage && lastMessage.role === 'ai') {
        lastMessage.typing = false;
        lastMessage.isFog = false;
        // 可选：添加提示信息表明响应被中断
        // lastMessage.content += "\n\n(响应已被中断)";
      }
    } else {
      console.error('请求出错:', error);
      // 处理其他类型的错误
    }
  } finally {

    // 确保最后一条消息的状态被更新
    const lastMessage = messageList.value[messageList.value.length - 1];
    if (lastMessage && lastMessage.role === 'ai') {
      lastMessage.typing = false;
      lastMessage.isFog = false;
    }
    // 无论成功还是失败，都重置状态
    // loading.value = false;
    lastMessage.thinkingStatus = 'end';
  }
}

const handleComplete = (_: any, index: number) => {
  loading.value = false;
  const lastMessage = messageList.value[index];
  lastMessage.bubbleItemRef?.init({ tipItems: lastMessage.tipItems });
  // lastMessage.typingCallback?.();
}

const triggerIndices = ref<BubbleListProps['triggerIndices']>('only-last');


// 保留这些变量，因为它们在模板中被使用
const cacheMapValue = new Map<string, any>();
const cacheMapKey = new Set<string>();
// 创建一个响应式的缓存状态对象
const cacheState = reactive<Map<string, any>>(new Map);

// 提供缓存状态给子组件
provide('cacheState', cacheState);

// 添加一个辅助函数用于处理ref
const setItemRef = (item: any) => (el: any) => {
  item.bubbleItemRef = el;
};

onUnmounted(() => {
  handleAbort();
  // cacheMapValue.clear();
});
</script>

<template>
  <div class="chat-layout">
    <!-- <div class="chat-menu"></div> -->
    <div class="chat-box">
      <div v-if="messageList.length === 0" style="flex: 1">
        <Welcome variant="borderless" title="欢迎使用 AI选址助手" description="你好，有什么可以帮助你的吗" />
        <AIPrompts :items="placeholderPromptsItems" :MapControlName="props.MapControlName"
                   @item-click="handlePromptsItemClick" />
      </div>
      <BubbleList v-else class="message-list" :showBackButton="false" :list="messageList"
                  :trigger-indices="triggerIndices" @complete="handleComplete">
        <template #header="{ item }">
          <!-- <Thinking v-if="item.role === 'ai' && thinkingStatus === 'thinking'" :status="thinkingStatus" auto-collapse :content="item.reasoningContent" button-width="250px" max-width="100%" /> -->
          <Thinking v-if="item.role === 'ai' && item.reasoningContent" v-model="item.openThinking"
                    :class="['thinking-content']" :status="item.thinkingStatus" auto-collapse
                    :content="item.reasoningContent" button-width="250px" max-width="100%" />
        </template>

        <template #footer="{ item }">
          <AIBubbleItem :ref="setItemRef(item)" :item="item" @sendFn="handleSubmit"
                        :MapControlName="props.MapControlName" :ToolData="props.ToolData" :PropStore="props.PropStore"
                        :Options="props.Options" :cacheMapValue="cacheMapValue" :cacheMapKey="cacheMapKey" />
        </template>
      </BubbleList>
      <AIPrompts v-if="messageList.length > 0" :MapControlName="props.MapControlName" :items="senderPromptsItems"
                 @item-click="handlePromptsItemClick" />
      <MentionSender class="chat-input" variant="updown" :loading="loading"
                     :auto-size="{ minRows: 2, maxRows: 5 }" :modelValue="inputContent" @submit="handleSubmit"
                     @cancel="handleAbort" @update:modelValue="(value: string) => inputContent = value">
        <template #prefix>
          <div class="action-bar">
            <div :class="['model-toggle', { 'is-active': isDeepseekMode }]" @click="handleToggleModel">
              <el-icon>
                <ElementPlus />
              </el-icon>
              <span>深度思考(R1)</span>
            </div>
            <!-- <el-button
                v-if="isGenerating"
                type="danger"
                :icon="VideoPause"
                size="small"
                @click="abortHandler"
                class="stop-btn"
            >
                停止生成
            </el-button> -->
          </div>
        </template>
      </MentionSender>
    </div>
  </div>
</template>
<style scoped lang="scss">
.chat-layout {
  width: 100%;
  // min-width: 700px;
  height: v-bind('props.Options.height');
  border-radius: 6px;
  display: flex;
  background: rgb(255, 255, 255);
  font-family: AlibabaPuHuiTi, sans-serif;
}

.chat-menu {
  background: rgba(245, 245, 245, 0.5);
  width: 280px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-box {
  height: 100%;
  width: 100%;
  // max-width: 700px;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 16px;
  // justify-content: space-between;

  :deep(.message-list) {
    flex: 1;
    max-height: none;
    --bubble-content-max-width: 400px !important;

    .el-bubble-content {
      --bubble-content-max-width: 100% !important;
    }
  }

  .bubble-actions {
    display: flex;
    align-items: center;
    margin-right: 8px;
  }

  .chat-input {
    .action-bar {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      margin-bottom: 8px;
    }

    .model-toggle {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 12px;
      border: 1px solid silver;
      border-radius: 15px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s;

      &:hover {
        border-color: #a0a6ee;
        color: #4e58e6;
      }

      &.is-active {
        color: #626aef;
        border: 1px solid #626aef !important;
        border-radius: 15px;
        font-weight: 700;
      }
    }

    .stop-btn {
      font-size: 12px;
      height: 28px;
      padding: 0 12px;
    }
  }

  .thinking-content {
    margin-bottom: 8px;
  }
}
</style>
