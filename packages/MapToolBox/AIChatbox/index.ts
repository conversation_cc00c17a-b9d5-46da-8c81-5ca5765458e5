import AIChatbox from "./AIChatbox.vue";
import AIPrompts from './AIPrompts.vue';
import type { PromptsProps, PromptItem } from './interface';

// 为组件添加install方法，使其可以被Vue.use()使用
AIChatbox.install = (app: any) => {
    app.component(AIChatbox.name || 'AIChatbox', AIChatbox);
};

AIPrompts.install = (app: any) => {
    app.component(AIPrompts.name || 'AIPrompts', AIPrompts);
};

export { AIPrompts, type PromptsProps, type PromptItem };
export default AIChatbox;
