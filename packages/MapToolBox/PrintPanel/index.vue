<!-- 截图打印 -->
<template>
  <el-container style="height: 100%">
    <el-main style="margin: 0; padding: 0">
      <button type="button" class="action-button" title="框选打印" @click="onScreenShot()">
        <Icons.kuangxuandayinIcon />
      </button>
      <button type="button" class="action-button" title="视图打印" @click="onFullScreenShot()">
        <Icons.shitudayin01Icon />
      </button>
      <button v-if="bigMapBtnVisible" class="action-button" type="button" title="大图打印" @click="onFullScreenPrint()">
        <Icons.exportImgIcon />
      </button>
    </el-main>
  </el-container>

  <!-- 视图打印 -->
  <ViewPrint ref="viewPrintRef" v-if="printVisible" :MapControlName="MapControlName" :PropStore="PropStore"
    :LayerStore="LayerStore" :Options="Options" @update:printVisible="(val: boolean) => printVisible = val">
  </ViewPrint>

  <!-- 大图界面 -->
  <BigMapPrint ref="bigMapPrintRef" v-if="bigMapPrintVisible" :MapControlName="MapControlName"
    :PropStore="PropStore" :LayerStore="LayerStore" :Options="Options"
    @update:bigMapPrintVisible="(val: boolean) => bigMapPrintVisible = val" :title="`大图打印`">
  </BigMapPrint>

  <!-- 框选打印 -->
  <BoxSelectPrint ref="boxSelectPrintRef" v-if="boxSelectPrintVisible"
    :MapControlName="MapControlName" :PropStore="PropStore" :LayerStore="LayerStore" :Options="Options"
    @update:boxSelectPrintVisible="(val: boolean) => boxSelectPrintVisible = val"
    @loading="boxSelectPrintLoading = true" @unLoading="boxSelectPrintLoading = false">
  </BoxSelectPrint>
</template>
<script lang="ts" setup>
import { nextTick, ref } from 'vue';
import { Icons } from '../../onemapkit';
import ViewPrint from './ViewPrint.vue';
import BigMapPrint from './BigMapPrint.vue';
import BoxSelectPrint from './BoxSelectPrint.vue';

defineProps({
  MapControlName: {
    type: String,
    default: 'mainMapControl',
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: {
      bottomInfoData: null,
    },
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  Options: {
    type: Object
  },
  bigMapBtnVisible: {
    type: Boolean,
    default: true
  }
});

const printVisible = ref(false);
const viewPrintRef = ref<any>(null);

const boxSelectPrintLoading = ref(false);

const onFullScreenShot = async () => {
  // 检查地图是否已准备好
  // if (!_inOnemap.isMapReady.value) {
  //   ElMessage.warning('地图尚未准备好，无法进行截图。');
  //   return; // 如果地图未准备好，直接返回
  // }
  printVisible.value = true;
  nextTick(() => {
    viewPrintRef.value?.init();
  });
};

const bigMapPrintVisible = ref(false); // 是否显示大图界面
const boxSelectPrintVisible = ref(false); // 是否显示框选界面

const bigMapPrintRef = ref<any>(null);
/**
 * 大图打印工具
 */
const onFullScreenPrint = () => {
  bigMapPrintVisible.value = true;
  nextTick(() => {
    bigMapPrintRef.value?.init();
  });
};

const boxSelectPrintRef = ref<any>(null);

/**
 * 框选打印工具
 */
const onScreenShot = () => {
  boxSelectPrintVisible.value = true;
  nextTick(() => {
    boxSelectPrintRef.value?.init();
  });
  // boxSelectPrintLoading.value = false;
}

</script>
<style lang="scss" scoped>
@import "../map-tools.scss";

.action-button {
  font-size: 16px;
  background-color: transparent;
  border: 1px solid #d3d3d3;
  color: #6e6e6e;
  height: 32px;
  width: 32px;
  text-align: center;
  outline: none;
  border-radius: 4px;
  margin-right: 10px;
  cursor: pointer;
  padding: 5px;

  &:hover {
    color: $primary-color;
    border-color: $primary-color;
  }

  &.is-active {
    background: $primary-color;
    color: #e4e4e4;
    border-color: $primary-color;
  }
}
</style>
