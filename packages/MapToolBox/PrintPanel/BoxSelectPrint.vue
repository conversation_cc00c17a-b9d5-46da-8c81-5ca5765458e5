<template>
  <Teleport to="body">
    <Dialog ref="dialogRef" v-if="visible" :title="title" :dock="DockType.top" :width="innerWidth" :height="innerHeight"
      :top="dialogStyle.top" :left="0" :margin="dialogStyle.margin" :onClose="closeHandle">
      <div class="main">
        <div class="settingPanel">
          <el-form style="margin-top: 15px" label-width="100px">
            <el-form-item label="图框模版">
              <el-select size="small" v-model="modelsize" style="width: 200px">
                <el-option label="A4横幅" value="A4"> </el-option>
                <el-option label="A4纵幅" value="A4R"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="制图标题">
              <el-input v-model="imgtitle" size="small" style="width: 200px" maxlength="100" />
            </el-form-item>
            <el-divider />

            <el-form-item label="数据来源">
              <el-switch size="small" v-model="department" />
            </el-form-item>
            <el-form-item label="时间">
              <el-switch size="small" v-model="timeinfo" />
            </el-form-item>
            <el-form-item label="指北针">
              <el-switch size="small" v-model="compass" />
            </el-form-item>
            <el-form-item label="比例尺">
              <el-switch size="small" v-model="scalebar" />
            </el-form-item>
            <el-form-item>
              <el-button style="margin-top: 20%; margin-left: 40%" size="small" type="primary"
                @click="downloadimg()">导出图片</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="picturePanel">
          <div class="picture-panel" :class="modelsize">
            <div id="imageWrapper">
              <!-- <img :src="pictureurl" alt=""> -->
              <h3>{{ imgtitle }}</h3>
              <div class="picture-panel__content">
                <div class="compass" v-if="compass">
                  <img src="@assets/images/north.png" style="opacity: 1" alt="" />
                </div>
                <div class="information">
                  <div>
                    <span v-if="department">{{
                      PropStore.bottomInfoData &&
                        PropStore.bottomInfoData.value.company
                        ? PropStore.bottomInfoData.value.company
                        : '南宁市自然资源局'
                    }}<br /></span><span v-if="timeinfo"> {{
                        shottime
                      }}</span>
                  </div>

                  <div style="position: relative;right: 0">
                    <span v-if="scalebar">比例尺 1:{{ currentRatio }}</span>
                  </div>
                </div>
                <div class="scalebar" v-if="scalebar">
                  <div class="scale-bar">
                    <span class="rulerimg">
                      <img class="scalebarimg" src="@assets/images/scalebar.jpg" alt="" /></span>
                    <span class="rulertext">
                      <span>0</span>
                      <span v-if="currentRatio < 50000">
                        {{ (currentRatio / 100).toFixed(0) }}
                      </span>
                      <span v-if="currentRatio >= 50000">
                        {{ (currentRatio / 100000).toFixed(0) }}
                      </span>
                      <!---->
                      <span v-if="currentRatio < 50000">
                        {{ ((currentRatio / 100) * 2).toFixed(0) }}m
                      </span>
                      <span v-if="currentRatio >= 50000">
                        {{ ((currentRatio / 100000) * 2).toFixed(0) }}km
                      </span>
                    </span>
                  </div>
                </div>
                <div class="sth" v-if="PropStore.bottomInfoData && PropStore.bottomInfoData.value.checkImageNumber">
                  <span>{{ PropStore.bottomInfoData.value.checkImageNumber }}</span>
                </div>
                <img ref="imgRef" :src="pictureurl" style="width: 100%; opacity: 1" alt="" />
                <!-- <img ref="imgRef" :src="pictureurl" :style="imgStyle" alt="" /> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  </Teleport>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { onMounted, ref, nextTick, watch, computed } from 'vue';
import { Dialog, DockType, getOnemap, mapType, CommonEventEnum } from '../../onemapkit';
import ScreenShot from "js-web-screen-shot";
import type { positionInfoType } from "js-web-screen-shot/dist/lib/type/ComponentType";
import dayjs from 'dayjs';
import html2canvas from "html2canvas";

const emits = defineEmits(['update:boxSelectPrintVisible', 'loading', 'unLoading']);

const props = defineProps({
  MapControlName: {
    type: String,
    default: 'mainMapControl',
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: {
      bottomInfoData: null,
    },
  },
  title: {
    type: String,
    default: '框选打印'
  },
  imgMinWidth: {
    type: Number,
    default: 256
  },
  imgMinHeight: {
    type: Number,
    default: 256
  },
  imgMaxHeight: {
    type: Number,
    default: 800
  },
  onClose: Function as any,
  Options: {
    type: Object,
    default: {
      headerHeight: 0
    },
  }
});

const visible = ref(false);
const innerWidth = ref(window.innerWidth - 2)
const innerHeight = ref(window.innerHeight - props.Options.headerHeight)

const headerHeight = props.Options.headerHeight || 0
const dialogStyle = computed(() => {
  return {
    headerHeight: headerHeight,
    top: headerHeight - 2,
    margin: {
      left: 0,
      top: headerHeight - 2,
      right: 0,
      bottom: 0,
    },
  }
})

const currentRatio = ref(0)
const modelsize = ref("A4");
const imgtitle = ref("");
const department = ref(true);
const timeinfo = ref(true);
const compass = ref(true);
const scalebar = ref(true);
const shottime = ref("");
const pictureurl = ref<string>('');

// 销毁组件函数
const destroyComponent = function () {
  console.log("截图组件销毁");
  showPopwindows()
}
// 获取裁剪区域图片信息
const getImg = function (imgInfo: { base64: string; cutInfo: positionInfoType }) {
  // console.log("截图组件传递的图片信息", imgInfo.base64);
  nextTick(() => {
    // 检查截图尺寸是否符合最小要求
    const { width, height } = imgInfo.cutInfo;
    const isValidMinSize = width >= props.imgMinWidth && height >= props.imgMinHeight;
    const isValidMaxSize = height <= props.imgMaxHeight;

    if (!isValidMinSize) {
      visible.value = false;
      ElMessage.warning(`截图区域小于${props.imgMinWidth}px * ${props.imgMinHeight}px，请重新截图`);
      showPopwindows();
      return;
    }
    if (!isValidMaxSize) {
      ElMessage.warning(`截图区域高度大于${props.imgMaxHeight}px，请重新截图`);
      showPopwindows();
      return;
    }

    // 更新截图相关数据
    visible.value = true;
    pictureurl.value = imgInfo.base64;
    shottime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    showPopwindows();
  });
}

const closeHandle = function () {
  visible.value = false;
  emits('update:boxSelectPrintVisible', false);
}

const _inOnemap = getOnemap(props.MapControlName);

const downloadimg = () => {
  var element = document.getElementById("imageWrapper") as HTMLElement;
  // console.log(imageWrapper);
  html2canvas(element).then((canvas) => {
    let dataURL = canvas.toDataURL("image/png");
    downloadImage(`一张图场景截图` + shottime.value + `.png`, dataURL);
  });
};

function downloadImage(filename: string, dataUrl: string) {
  const nav = window.navigator as any;
  if (!nav.msSaveOrOpenBlob) {
    const element = document.createElement("a");
    element.setAttribute("href", dataUrl);
    element.setAttribute("download", filename);
    element.style.display = "none";
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  } else {
    const byteString = atob(dataUrl.split(",")[1]);
    const mimeString = dataUrl.split(",")[0].split(":")[1].split(";")[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([ab], { type: mimeString });
    // download file
    nav.msSaveOrOpenBlob(blob, filename);
  }
}

const showPopwindows = () => {
  document.getElementById('popwindowslist')!.style.display = 'flex';
}

const dialogRef = ref<any>(null);

const init = async () => {
  // 如果最小化，则最大化
  if (dialogRef.value && !dialogRef.value.isMax.value) {
    return dialogRef.value.winMaxMinSize();
  }
  document.documentElement.style.setProperty('--header-height', `${dialogStyle.value.headerHeight}px`);
  // 隐藏更多工具面板
  _inOnemap.dispatchEvent(CommonEventEnum.MoreToolsPanelVisible, false);
  document.getElementById('popwindowslist')!.style.display = 'none';
  if (_inOnemap.MapType.value === mapType.cesium) {
    // 强制渲染
    _inOnemap.MapViewer.scene.render();
    // 等待一帧渲染完成
    await new Promise(resolve => {
      requestAnimationFrame(resolve);
    });
  }
  // 初始化截图组件
  console.log('初始化截图组件')

  new ScreenShot({
    enableWebRtc: false,
    level: 99999,
    hiddenToolIco: {
      square: true,
      round: true,
      rightTop: true,
      brush: true,
      mosaicPen: true,
      text: true,
      separateLine: true,
      save: true,
      undo: true,
    },
    noScroll: false,
    writeBase64: false,
    completeCallback: getImg,
    closeCallback: destroyComponent,
    useCustomImgSize: true,
    destroyContainer: true
  });
  currentRatio.value = props.PropStore.bottomInfoData.value?.bestRatio;

  console.log('初始化截图组件完成')
}

// 更新所有元素位置
const updateAllElements = () => {
  updateElementPosition('.sth', -21);
  updateElementPosition('.scalebar', -40);
  updateElementPosition('.information', 21);
};

const imgRef = ref();

// 更新元素位置的通用函数
const updateElementPosition = (selector: string, topOffset: number) => {
  const element = document.querySelector(selector);
  if (element) {
    element.setAttribute('style', `top: ${imgRef.value.height + topOffset}px;`);
  }
};

onMounted(async () => {
  console.log("框选打印组件加载");
});

watch(
  () => modelsize,
  () => {
    nextTick(updateAllElements);
  },
  { deep: true }
);

defineExpose({
  init
});
</script>

<style lang="scss" scoped>
$header-height: 50px;
$settingPanelWidth: 20%;
$maxScreenPanelHeight: 30px;
$maxScreenPanelPadding: 10px;
@import "../map-tools.scss";

#imageWrapper {
  position: relative;
  width: 100%;
  height: calc(100vh - #{$maxScreenPanelHeight} - #{$maxScreenPanelPadding} - var(--header-height));
  background: #fff;
  padding: 0 15px 15px 15px;
}

h3 {
  text-align: center;
  margin: auto;
  padding: 8px;
}

.A4 {
  width: calc(70vw - 200px);
  margin: auto;
  height: calc((70vw - 200px) * 0.7);
  left: calc((100% - (70vw - 200px)) * 0.5);
}

.A4R {
  width: calc((100vh - 120px) * 0.7);
  margin: auto;
  height: calc(100vh - 120px);
  left: calc((100% - (100vh - 120px)) * 0.5);
}

.compass {
  position: absolute;
  right: calc(5vw + 0%);
  top: 0;
  margin: 0;
  z-index: 19;

  img {
    position: absolute;
    width: 5vw;
  }
}

.scalebar {
  position: absolute;
  bottom: 1%;
  left: 1%;
  font-weight: 800;
}

.sth {
  background: rgba(255, 255, 255, 0.65);
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 2px 1%;
  font-weight: 500;

  span {
    font-size: 12px;
  }
}

.information {
  position: absolute;
  z-index: 99;
  line-height: 20px;
  color: #000;
  height: 40px;
  bottom: -45px;
  text-align: left;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  width: 100%;
}


.settingPanel {
  width: $settingPanelWidth;
  height: 100%;
  float: left;
}

.picturePanel {
  position: absolute;
  right: 0;
  background-color: #0006;
  width: calc(100% - $settingPanelWidth);
  height: calc(100vh - #{$maxScreenPanelHeight} - #{$maxScreenPanelPadding} - var(--header-height));
  float: left;
}

.screen-shot-panel {
  width: 260px;

  &__content {
    padding: 10px;
  }
}

.max-screen-panel {
  height: calc(100vh - 50px);
  top: 50px;
}

.scalebar {
  position: absolute;
  left: 1%;
  font-weight: 800;

  .scale-bar {
    height: 36px;
    width: 100px;
    font-size: 12px;

    .rulerimg {
      padding: 4px 14px 0 14px;
      display: inline-block;
      width: 100%;
      height: 16px;
    }
  }
}

// .scalebarimg {
//   height: 5px;
//   width: 100%;
// }

.rulertext {
  padding: 4px 14px 0 14px;
  display: flex;
  width: 100%;
  height: 18px;
  font-size: 11px;
  text-shadow: 0 0 0 #fff, 1px 0 0 #fff, 0px -1px 0px #fff;

  span {
    text-align: left;
    display: inline-block;
    flex: 1;
  }
}

.iconfont1 {
  font-family: "iconfont",serif !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.header_title {
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 60px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.picture-panel {
  box-shadow: #000 0 0 10px;
  margin-top: 10px;
  position: absolute;

  &__content {
    background-size: auto 100%;
    border: 2px #000 solid;
    display: table;
    text-align: center;
    vertical-align: middle;
    position: absolute;
    margin: auto;
    left: 20px;
    right: 20px;
    top: 50px;
    font-size: 0;
  }
}

.max-screen-panel {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: #e8e9eb;
  padding: 0 $maxScreenPanelPadding $maxScreenPanelPadding $maxScreenPanelPadding;
  top: 0;
  left: 0;
  right: 0;

  &.maximize {
    display: block;
    z-index: 1000;
  }

  &.minimize {
    display: none;
    z-index: -999;
  }

  &.show-header {
    height: calc(100vh - var(--header-height));
    top: #{$header-height};

    .max-screen-panel__content {
      height: calc(100vh - #{$maxScreenPanelHeight} - #{$maxScreenPanelPadding} - var(--header-height));
    }
  }

  &__header {
    background: #e8e9eb;
    height: $maxScreenPanelHeight;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__content {
    background: #fff;
    width: 100%;
    height: calc(100vh - #{$maxScreenPanelHeight} - #{$maxScreenPanelPadding});
  }

  &__title {
    color: #535353;
  }

  &__btn {
    i {
      margin-right: 10px;
      cursor: pointer;
      font-size: 20px;

      &:hover {
        color: $primary-color;
      }
    }

    &__mr-20 {
      margin-right: 20px;
    }
  }
}

.dialog-container {
  z-index: 1999;
}
</style>
