<template>
  <Teleport to="body">
    <Dialog ref="dialogRef" v-if="visible" :title="title" :dock="DockType.top" :width="innerWidth" :height="innerHeight"
      :top="dialogStyle.top" :left="0" :margin="dialogStyle.margin" :onClose="closeHandle">
      <div class="main">
        <div class="picturePanel">
          <div class="picture-panel">
            <div id="printMapView">
              <div id="print">
                文件格式
                <br />
                <el-select v-model="printParams.format" class="m-2" placeholder="请选择导出格式" @change="$forceUpdate()">
                  <el-option v-for="item in printTypes" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
                <div style="margin-top: 10px">
                  图像宽度
                  <el-input id="imageWidth" v-model="inputImageSize.width" :min="1" autosize placeholder="默认宽度为:297mm"
                    type="number">
                  </el-input>
                  <br />
                  图像高度
                  <el-input id="imageHeight" v-model="inputImageSize.height" :min="1" autosize placeholder="默认高度为:210mm"
                    type="number">
                  </el-input>
                  <div style="margin-top: 10px">
                    <el-checkbox id="checkBox" v-model="checkState" @click="checkBoxClick">
                      设置比例
                    </el-checkbox>
                  </div>
                  <el-input id="scaleInput" v-model="outScale" :min="1" autosize type="number"
                    @input="scaleInputChange">
                  </el-input>
                  <br />
                  <el-button id="printBtn" v-loading.fullscreen.lock="loading" element-loading-text="Loading..."
                    style="margin-top: 10px" type="primary" @click="exportMapBtnClick">
                    导出地图
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  </Teleport>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, nextTick, onMounted, ref, onUnmounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getOnemap, getOption, Dialog, DockType, mapType, OnemapClass } from 'onemapkit';
import type { IMapLayer } from 'onemapkit';
import { arcsetup } from '@/Map/arcsetup';
import { cloneDeep } from 'lodash';
import { getToken } from '../../utils/utils'

const emits = defineEmits(['update:bigMapPrintVisible', 'MapRealtimeEvent', 'MapReadyEvent']);

const props = defineProps({
  MapControlName: {
    type: String,
    default: 'mainMapControl',
    require: true,
  },
  PropStore: {
    type: Object as any,
    default: null,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  title: {
    type: String,
    default: '大图打印',
  },
  Options: {
    type: Object,
    default: {
      headerHeight: 0,
      printUrl: '',
      downloadImageUrl: '',
      servicePrefix: '',
      printFun: () => { },
      extentFun: () => { },
    },
  }
});

const visible = ref(false);
const headerHeight = props.Options.headerHeight || 0
const dialogStyle = computed(() => {
  return {
    headerHeight: headerHeight,
    top: headerHeight - 2,
    margin: {
      left: 0,
      top: headerHeight - 2,
      right: 0,
      bottom: 0,
    },
  }
})

const innerWidth = ref(window.innerWidth - 2)
const innerHeight = ref(window.innerHeight - dialogStyle.value.headerHeight)
const _inOemap = getOnemap(props.MapControlName);
const _inOptions = getOption(props.MapControlName);

let mapView: any = null;

onMounted(() => {
  console.log("大图打印组件加载")
});

const tempCheckedLayerids = ref<Array<string>>([]);

const dialogRef = ref<any>(null);

let _Onemap: any = null;

const addLayers = async () => {
  const layerCheck = getCurrentCheckedLayer();
  for (const layer of [...layerCheck].reverse()) {
    try {
      await _Onemap.AddLayer(layer);
    } catch (error) {
      console.error(`添加图层失败: ${layer.name}`, error);
    }
  }
  tempCheckedLayerids.value = layerCheck.map((x: any) => x.layerid);
}

const getCurrentCheckedLayer = (): Array<IMapLayer> => {
  return props.LayerStore.CheckedLayerids.map((id: string) => {
    return props.LayerStore.MapLayers.find(((x: IMapLayer) => x.layerid === id && !id.includes('tdt_') && x.visible))
  }).filter((layer: any) => layer !== undefined);
}


const init = async () => {
  // 如果最小化，则最大化
  if (dialogRef.value && !dialogRef.value.isMax) {
    dialogRef.value.winMaxMinSize();

    await reloadMap();
    return;
  }
  await showMap();
}

const isMax = computed<boolean>(() => dialogRef.value?.isMax)

watch(isMax, (newVal) => {
  if (newVal) {
    reloadMap();
  }
})

const reloadMap = async () => {
  const currentCheckedLayerids = getCurrentCheckedLayer()?.map((x: any) => x.layerid);
    // 判断两个数组是否有相同的内容
    const arraysAreDifferent = JSON.stringify(tempCheckedLayerids.value) !== JSON.stringify(currentCheckedLayerids);

    if (arraysAreDifferent && _Onemap) {
      // 移除之前的图层
      for (const tempCheckedLayerid of tempCheckedLayerids.value) {
        const _vecLayer = _Onemap.GetLayerByID({ layerid: tempCheckedLayerid });
        _Onemap.RemoveLayerById(_vecLayer);
      }
      await addLayers();
    }
}

// watch(
//   () => props.LayerStore.CheckedLayerids, 
//   (newVal, oldVal) => {
//     console.log(newVal);
//     console.log(oldVal);
//   }, 
//   {deep: true}
// )

const showMap = async () => {
  await nextTick(async () => {
    visible.value = true;
    document.documentElement.style.setProperty('--header-height', `${dialogStyle.value.headerHeight}px`);
    // 创建第一屏OmemapClass
    _Onemap = new OnemapClass(mapType.arcgis);
    // 过滤底图
    const layerGroup = _inOptions?.BaseMapLayer?.subLayers.filter((i: any) => !i.layerid.includes('tdt_'));
    // const layerGroup = _inOptions?.BaseMapLayer?.subLayers;

    const baseLayer = cloneDeep(_inOptions.BaseMapLayer);
    baseLayer.subLayers = layerGroup;

    if (!layerGroup.layers && layerGroup.length === 0) {
      return ElMessageBox({
        title: '提示',
        message: '没有可打印的地图！',
      });
    }
    const extent = _inOemap.getMapExtent();
    const _inOptionsClone = {
      BASE_URL: 'ThirdParty',
      mapContainer: 'printMapView',
      MapControlName: 'printMapViewControl',
      // BaseMapLayer: _inOptions?.BaseMapLayer,
      BaseMapLayer: baseLayer,
      ...(extent && {
        mapextent: {
          wkid: 4490,
          xmin: extent.xmin,
          ymin: extent.ymin,
          xmax: extent.xmax,
          ymax: extent.ymax,
        }
      }),
    };
    _Onemap.MapControlName = 'bigMapPrint';
    await arcsetup(_inOptionsClone, _Onemap, emits, null).then(
      async (obj: any) => {
        mapView = obj.MapViewer;

        await addLayers();
        // _Onemap.AddLayers(layerCheck)
        // _Onemap.setScale(_inOemap.getScale());

        //打开导出大图工具时直接给输入框赋值
        let scaleInput = document.getElementById('scaleInput') as HTMLInputElement;
        scaleInput.disabled = true;
        let imageWidth = document.getElementById("imageWidth") as HTMLInputElement;
        let imageHeight = document.getElementById("imageHeight") as HTMLInputElement;

        // 监听view的比例尺属性，比例尺变化，输入框属性变化
        mapView.on("extent-change", async function () {
          const currentScale = mapView.getScale().toFixed(0)

          if (checkState.value == false) {
            scaleInput.placeholder = "当前比例为:" + Math.round(currentScale) + "";
            defaultScale.value = Math.round(currentScale);
            imageSize.defaultWidth = mapView.width / 96 * 2.54;
            imageSize.defaultHeight = mapView.height / 96 * 2.54;
            imageWidth.placeholder = "默认宽度为:" + Math.round(imageSize.defaultWidth) * 10 + "mm";
            imageHeight.placeholder = "默认高度为:" + Math.round(imageSize.defaultHeight) * 10 + "mm";
          } else {
            const range = await getMapRange(mapView);
            defaultScale.value = Math.round(currentScale);
            scaleInput.placeholder = "当前比例为:" + defaultScale.value + "";
            imageSize.defaultWidth = range.lengths[0] * 100 / outScale.value;
            imageSize.defaultHeight = range.lengths[1] * 100 / outScale.value;
            imageWidth.placeholder = "默认宽度为:" + Math.round(imageSize.defaultWidth) * 10 + "mm";
            imageHeight.placeholder = "默认高度为:" + Math.round(imageSize.defaultHeight) * 10 + "mm";
          }
        });
      }
    );
  });
}

// 建议添加
onUnmounted(() => {
  // 清理地图实例和事件监听
  if (mapView) {
    mapView.destroy();
  }
});

const closeHandle = () => {
  visible.value = false;
  emits('update:bigMapPrintVisible', false);
};
// const fixedHeader = inject("getFixedHeader");
// const handlepicture = ref(true);

// const { dispatchMapEvent, store } = common();

// 已选择图层
// const selectedMapData = computed(() => store.getters.selectedMapData);

//打印地图参数
const printTypes = ref([
  {
    value: 'PDF',
    label: 'PDF',
  },
  {
    value: 'PNG32',
    label: 'PNG32',
  },
  {
    value: 'PNG8',
    label: 'PNG8',
  },
  {
    value: 'JPG',
    label: 'JPG',
  },
  {
    value: 'GIF',
    label: 'GIF',
  },
  {
    value: 'EPS',
    label: 'EPS',
  },
  {
    value: 'SVG',
    label: 'SVG',
  },
  {
    value: 'SVGZ',
    label: 'SVGZ',
  },
  {
    value: 'AIX',
    label: 'AIX',
  },
]);

const loading = ref(false);
const checkState = ref(false);
const printParams: any = ref({
  scale: null,
  format: null,
  printCondition: true,
});
const defaultScale: any = ref(null);
const outScale: any = ref(null);
const inputImageSize = ref({
  width: null,
  height: null,
});
const imageSize: any = {
  width: null,
  height: null,
  defaultWidth: 50,
  defaultHeight: 22,
};
const resolution: any = {
  width: null,
  height: null,
};

//获取当前视图范围方法
const getMapRange = (view: any) => {
  //当前视图实际距离长宽
  const polylineJson = JSON.stringify([{
    spatialReference: {
      wkid: 4490
    },
    paths: [[
      [view.extent.xmin, view.extent.ymax],
      [view.extent.xmax, view.extent.ymax],
    ]]
  },
  {
    spatialReference: {
      wkid: 4490
    },
    paths: [[
      [view.extent.xmin, view.extent.ymax],
      [view.extent.xmin, view.extent.ymin],
    ]]
  }
  ]);

  const formData = new FormData()
  formData.append("sr", "4490")
  formData.append("polylines", polylineJson)
  formData.append("lengthUnit", "")
  formData.append("calculationType", "geodesic")
  formData.append("f", "pjson")

  // return axios.post(props.Options.servicePrefix + '/proxy/query/extent/lengths', formData).then((response) => {
  //   console.log("结果", response);
  //   return response.data
  // }).catch((error) => {
  //   console.log("计算范围出错", error);
  //   ElMessage.error('计算范围出错')
  // });
  if (!props.Options.extentFun || typeof props.Options.extentFun !== 'function') {
    ElMessage.error('视图范围方法未定义');
    return;
  }
  return props.Options.extentFun(formData).then((response: any) => {
    console.log("结果", response);
    return response;
  }).catch((error: any) => {
    console.log("计算范围出错", error);
    ElMessage.error('计算范围出错')
  });
};

//点击checkbox
const checkBoxClick = async (event: any) => {
  const range = await getMapRange(mapView);
  if (event.target.tagName == 'INPUT') return

  else {
    let scaleInput = document.getElementById("scaleInput") as HTMLInputElement;
    let imageWidth = document.getElementById("imageWidth") as HTMLInputElement;
    let imageHeight = document.getElementById("imageHeight") as HTMLInputElement;
    //方法为异步时，点击按钮时checkState就已经被改变
    if (checkState.value == true) {
      scaleInput.disabled = false;
      defaultScale.value = Math.round(mapView.getScale());//点击按钮后，若为勾选状态，则获取当前最新的比例范围
      scaleInput.placeholder = "当前比例为:" + defaultScale.value + "";
      outScale.value = defaultScale.value;
      imageSize.defaultWidth = range.lengths[0] * 100 / outScale.value;
      imageSize.defaultHeight = range.lengths[1] * 100 / outScale.value;
      imageWidth.placeholder = "默认宽度为:" + Math.round(imageSize.defaultWidth) * 10 + "mm";
      imageHeight.placeholder = "默认高度为:" + Math.round(imageSize.defaultHeight) * 10 + "mm";
    } else {
      scaleInput.disabled = false;
      outScale.value = null;
      imageSize.defaultWidth = range.lengths[0] * 100 / defaultScale.value;
      imageSize.defaultHeight = range.lengths[1] * 100 / defaultScale.value;
      imageWidth.placeholder = "默认宽度为:" + Math.round(imageSize.defaultWidth) * 10 + "mm";
      imageHeight.placeholder = "默认高度为:" + Math.round(imageSize.defaultHeight) * 10 + "mm";
    }
  }
};

//打印地图方法
const printMap = (view: any) => {
  //图像分辨率上限大约为1亿左右，超出这个值会报错
  loading.value = true;
  if (resolution.width * resolution.height > 120000000) {
    ElMessage.error('图像分辨率超出最大值，无法打印');
    loading.value = false;
    return;
  }

  // 处理图层数据
  let layerJson;
  const layerIds = view.layerIds;
  if (layerIds.length === 0) {
    ElMessage.error('请加载所需打印的图层');
    loading.value = false;
    return;
  }

  // 构建图层JSON
  const operationalLayers = layerIds.map((layerId: any) => {
    const layer = view.getLayer(layerId);
    let layerUrl = layer.url;

    // 处理服务前缀
    if (layerUrl.includes(props.Options.servicePrefix)) {
      const devApiIndex = layerUrl.indexOf(props.Options.servicePrefix);
      layerUrl = props.Options.printUrl + layerUrl.substring(devApiIndex + props.Options.servicePrefix.length);
    }

    // 处理完整URL
    return {
      url: (layer.url.includes('http') ? layerUrl : location.origin + layerUrl) + '?token=' + getToken()
    };
  });

  layerJson = JSON.stringify({
    operationalLayers
  });

  const paramJson = JSON.stringify({
    mapOptions: {
      extent: {
        spatialReference: {
          wkid: view.spatialReference.wkid,
        },

        xmin: view.extent.xmin,
        ymin: view.extent.ymin,
        xmax: view.extent.xmax,
        ymax: view.extent.ymax,
      },
      spatialReference: {
        wkid: view.spatialReference.wkid,
      },
      scale: printParams.value.scale,
    },
    exportOptions: {
      dpi: 96,
      outputSize: [resolution.width, resolution.height],
    },
  });
  const Web_Map_as_JSON = layerJson.slice(0, -1) + ',' + paramJson.slice(1);

  //使用post请求，post请求需要用formdata格式
  const formData = new FormData();
  formData.append('Format', printParams.value.format);
  formData.append('Web_Map_as_JSON', Web_Map_as_JSON);
  formData.append('f', 'json');

  if (!props.Options.printFun || typeof props.Options.printFun !== 'function') {
    ElMessage.error('打印方法未定义');
    loading.value = false;
    return;
  }

  props.Options
    .printFun(formData)
    .then((response: any) => {
      if (response.results && response.results.length < 0) {
        return ElMessage.error('打印失败，打印终止');
      }
      const [{ value: { url } }] = response.results;
      console.log('地址', url);
      const fileName = url.substring(url.lastIndexOf('/') + 1);
      const proxyUrl = props.Options.downloadImageUrl + fileName;
      downloadImage(fileName, proxyUrl);
    })
    .catch((error: any) => {
      console.log('打印出错', error);
      ElMessage.error('打印异常，打印终止');
    })
    .finally(() => {
      loading.value = false;
    });
};

//下载打印地图
function downloadImage(filename: string, dataUrl: string) {
  if (typeof fetch !== 'undefined' && typeof URL !== 'undefined') {
    fetch(dataUrl)
      .then(response => response.blob())
      .then(blob => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
      })
      .catch(error => {
        console.error("Error downloading file:", error);
      });
  } else {
    window.open(dataUrl);
  }
}

//点击导出大图
const exportMapBtnClick = () => {
  nextTick(() => {
    printParams.value.printCondition = true;
    calculateResolution();
    if (printParams.value.printCondition === true) {
      printMap(mapView);
    }
  })
};

//比例尺输入框输入值改变时调用
const scaleInputChange = async () => {
  const range = await getMapRange(mapView);

  calculateImageSize(range);
};

//计算分辨率大小方法
const calculateResolution = () => {
  if (checkState.value == true) {
    if (outScale.value <= 1) {
      ElMessage.error('输入参数必须大于1');
      printParams.value.printCondition = false;
      return;
    } else {
      printParams.value.scale = outScale.value;
    }
  } else {
    printParams.value.scale = defaultScale.value;
  }
  //判断是否设定了图像大小
  if (inputImageSize.value.width) {
    if (inputImageSize.value.width <= 1) {
      ElMessage.error('输入参数必须大于1');
      printParams.value.printCondition = false;
      return;
    } else {
      imageSize.width = inputImageSize.value.width / 10;
    }
  } else {
    imageSize.width = imageSize.defaultWidth;
  }

  if (inputImageSize.value.height) {
    if (inputImageSize.value.height <= 1) {
      ElMessage.error('输入参数必须大于1');
      printParams.value.printCondition = false;
      return;
    } else {
      imageSize.height = inputImageSize.value.height / 10;
    }
  } else {
    imageSize.height = imageSize.defaultHeight;
  }

  //通过数学变换算出width,height,传给打印地图方法。
  //分辨率=纸张大小（英寸）×DPI(96)    1英寸=2.54cm
  resolution.width = (imageSize.width / 2.54) * 96;
  resolution.height = (imageSize.height / 2.54) * 96;

  if (resolution.width <= 0 || resolution.height <= 0) {
    ElMessage.error('图像分辨率超出最大值，无法打印');
    printParams.value.printCondition = false;
    return;
  }
};
//动态计算图像大小方法
const calculateImageSize = (data: any) => {
  printParams.value.scale = outScale.value;
  // console.log("params", this.printParams);
  let imageWidth = document.getElementById("imageWidth") as HTMLInputElement;
  let imageHeight = document.getElementById("imageHeight") as HTMLInputElement;
  //勾选时判断是否设定了图像大小
  //如果没有设置图像大小,就算出默认值
  //data.lengths[0]是范围长度1是宽度,纸张大小等于实际距离/比例尺，单位统一为cm
  if (printParams.value.scale) {
    if (inputImageSize.value.width) {
      imageSize.width = inputImageSize.value.width / 10;
    } else {
      imageSize.defaultWidth = data.lengths[0] * 100 / printParams.value.scale
      imageWidth.placeholder = "默认宽度为:" + Math.round(imageSize.defaultWidth * 10) + "mm";
    }

    if (inputImageSize.value.height) {
      imageSize.height = inputImageSize.value.height / 10;
    } else {
      imageSize.defaultHeight = data.lengths[1] * 100 / printParams.value.scale
      imageHeight.placeholder = "默认高度为:" + Math.round(imageSize.defaultHeight * 10) + "mm";
    }
  } else {
    imageWidth.placeholder = "请输入打印比例"
    imageHeight.placeholder = "请输入打印比例"
  }
};

defineExpose({
  init
});

</script>
<style lang="scss" scoped>
#print {
  background-color: white;
  padding: 8px;
  display: flex;
  justify-content: flex-start;
  position: absolute;
  flex-wrap: wrap;
  width: 250px;
  border-radius: 5px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2) !important;
  left: 15px;
  z-index: 9999;
  top: 15px;
}

.picturePanel {
  position: absolute;
  right: 0;
  background-color: #0006;
  width: 100vw;
  height: calc(100vh - 45px - var(--header-height));
  float: left;
}

#printMapView {
  width: 100%;
  height: 100%;
}

.picture-panel {
  position: absolute;
  width: 100vw;
  margin: auto;
  height: calc(100vh - 45px - var(--header-height));
  background-color: white;
}

.dialog-container {
  z-index: 1999;
}
</style>
