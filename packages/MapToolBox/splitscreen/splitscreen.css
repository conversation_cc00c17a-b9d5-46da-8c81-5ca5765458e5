/* 分屏 */
.SplitScreen1 .oneViewer {
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
}

.SplitScreen2 .oneViewer,
.SplitScreen2 .secondViewer {
  position: absolute !important;
  width: 50% !important;
  height: 100% !important;
}

.SplitScreen2 .secondViewer {
  left: 50%;
  top: 0;
}

.SplitScreen3 .oneViewer {
  position: absolute !important;
  width: 100% !important;
  height: 50% !important;
}

.SplitScreen3 .secondViewer,
.SplitScreen3 .thirdViewer {
  position: absolute !important;
  width: 50% !important;
  height: 50% !important;
}

.SplitScreen3 .secondViewer {
  left: 0;
  top: 50%;
}

.SplitScreen3 .thirdViewer {
  left: 50%;
  top: 50%;
}

.SplitScreen4 .oneViewer,
.SplitScreen4 .secondViewer,
.SplitScreen4 .thirdViewer,
.SplitScreen4 .fourthViewer {
  position: absolute !important;
  width: 50% !important;
  height: 50% !important;
}

.SplitScreen4 .secondViewer {
  left: 50%;
  top: 0;
}

.SplitScreen4 .thirdViewer {
  left: 0;
  top: 50%;
}

.SplitScreen4 .fourthViewer {
  left: 50%;
  top: 50%;
}