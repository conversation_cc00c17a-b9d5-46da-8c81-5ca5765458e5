<template>
  <!-- <teleport to="#app"> -->
    <div id="splitScreenContainer" class="split-container">
      <!-- 标题栏 -->
      <!-- <div class="split-header">
        <span class="split-header-title">分屏工具</span>
        <el-icon class="split-min" @click="closeHandle()">
          <SemiSelect />
        </el-icon>
        <el-icon class="split-close" @click="closeHandle()">
          <Close />
        </el-icon>
      </div> -->

      <div class="split-body">
        <!-- 侧边栏 -->
        <div :class="{ 'sidebar': true, 'collapsed': isSidebarCollapsed }">
          <!-- <div name="sidebar"> -->
            <div
              v-if="initLoaded"
              v-show="!isSidebarCollapsed"
              :class="{ 'split-sidebar': true, 'collapsed': isSidebarCollapsed }"
            >
              <!-- 分屏数按钮 选择器 （SplitImgSrc）-->
              <el-space class="split-number">
                <el-card
                  shadow="hover"
                  style="width: 80px; height: 60px"
                  v-for="(src, srcIndex) in SplitImgSrc"
                >
                  <img
                    :key="srcIndex"
                    :src="src.active ? src.activeURL : src.inactiveURL"
                    :id="'splitImg' + srcIndex"
                    style="width: 70px; height: 50px"
                    @click="splitImgClick(srcIndex, src, SplitImgSrc)"
                    @mouseover="splitImgHover(srcIndex, src)"
                    @mouseleave="splitImgLeave(srcIndex, src)"
                  />
                </el-card>
              </el-space>
              <!-- <hr> -->
              <div class="top-bar" style="z-index:99;text-align: center;">
                <el-button
                  :class="mode=='content'?'activate':'normal'"
                  type="text"
                  @click="mode='content'"
                >目录模式</el-button>
                <el-button
                  :class="mode=='list'?'activate':'normal'"
                  type="text"
                  @click="openList()"
                >列表模式</el-button>
                <el-divider style="    margin: 0;"></el-divider>
              </div>
              <div style="height: calc(100% - 150px);">
                <!--多窗口的折叠面板 mainActiveName-->
                <el-tabs
                  v-show="mode=='content'"
                  style="height: 100%"
                  v-model="tabActiveName"
                  tab-position="left"
                >
                  <el-tab-pane label="视窗1" name="window1">
                    <div :class="mode=='content'?'show':'hide'">
                      <SplitscreenTree
                        ref="SimpleTree1"
                        MapControlName="splitspreen1"
                        MapName="视窗1"
                        :MapType="splitMapType"
                        :LayerStore="_LayerStore1"
                        :PropStore="_PropStore1"
                      ></SplitscreenTree>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="视窗2" v-if="splitSpreenNum > 1" name="window2">
                    <div :class="mode=='content'?'show':'hide'">
                      <SplitscreenTree
                        ref="SimpleTree2"
                        MapControlName="splitspreen2"
                        MapName="视窗2"
                        :MapType="splitMapType"
                        :LayerStore="_LayerStore2"
                        :PropStore="_PropStore2"
                      ></SplitscreenTree>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="视窗3" v-if="splitSpreenNum > 2" name="window3">
                    <div :class="mode=='content'?'show':'hide'">
                      <SplitscreenTree
                        ref="SimpleTree3"
                        MapControlName="splitspreen3"
                        MapName="视窗3"
                        :MapType="splitMapType"
                        :LayerStore="_LayerStore3"
                        :PropStore="_PropStore3"
                      ></SplitscreenTree>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="视窗4" v-if="splitSpreenNum > 3" name="window4">
                    <div :class="mode=='content'?'show':'hide'">
                      <SplitscreenTree
                        ref="SimpleTree4"
                        MapControlName="splitspreen4"
                        MapName="视窗4"
                        :MapType="splitMapType"
                        :LayerStore="_LayerStore4"
                        :PropStore="_PropStore4"
                      ></SplitscreenTree>
                    </div>
                  </el-tab-pane>
                </el-tabs>

                <div v-if="mode=='list'">
                  <layerList
                    :data="selectedList"
                    :layerTree="layerTree"
                    :index="activeTabIndex"
                    :winNum="selectedNum"
                    :listCheckChange="layerCheckChange"
                  />
                </div>
              </div>
            </div>
          <!-- </div> -->

          <!-- 展开/收起 -->
          <div
            :class="{ 'split-sidebar-toggle': true, 'collapsed': isSidebarCollapsed }"
            @click="toggleSidebar"
          >
            <span v-if="isSidebarCollapsed" style="text-orientation: upright;">展开</span>
            <span v-else style="text-orientation: upright;">折叠</span>
          </div>
        </div>

        <!-- 地图区域 -->
        <div
          id="splitScreenMapContainer"
          :class="{ 'split-main': true, 'single-item': newSpreenNum === 1 }"
        >
          <div
            v-for="(item, index) in newSpreenNum"
            :id="`split-view-${item}`"
            :key="index"
            :class="[
						'item',
						{ 'full-width': newSpreenNum === 1 || (newSpreenNum % 2 !== 0 && index === 0), 'highlightBorder': newSpreenNum !== 1 && tabActiveName === (`window` + item) }
					]"
            :style="itemStyle(index)"
            @click="setWindow(item)"
          >
            <div class="common-right">
              <el-tooltip
                :content="getOnemap('splitspreen' + item) && getOnemap('splitspreen' + item).MapType.value === mapType.arcgis
								? '点击切换到三维'
								: '点击切换到二维'
								"
                placement="left"
                effect="light"
              >
                <div
                  class="map-transform map-switch map-righ-button"
                  style="bottom: 170px;"
                  @click="ChangeMapType()"
                >
                  <i
                    v-html="getOnemap('splitspreen' + item) && getOnemap('splitspreen' + item).MapType.value === mapType.arcgis
										? Icons.Switch2D
										: Icons.Switch3D
										"
                    :class="['user-panel__header__min']"
                  ></i>
                  <span style="font-size: 12px; caret-color: transparent">
                    {{
                    getOnemap('splitspreen' + item) && getOnemap('splitspreen' + item).MapType.value === mapType.arcgis
                    ? "二维"
                    : "三维"
                    }}
                  </span>
                </div>
              </el-tooltip>
              <el-tooltip
                :content="PropStore && PropStore.fullExtent && PropStore.fullExtent.toolbar
								? PropStore.fullExtent.toolbar
								: '定位到全幅'
								"
                placement="left"
                effect="light"
              >
                <div
                  class="map-switch map-righ-button"
                  style="bottom: 105px;"
                  @click="locationGlobalHandle()"
                >
                  <i v-html="Icons.locate"></i>
                  <span style="font-size: 12px; caret-color: transparent">全幅</span>
                </div>
              </el-tooltip>
              <!-- <MapZoom style="z-index: 99999; bottom: 40px;" MapControlName="splitspreen1" :PropStore="__PropStore[item - 1]" /> -->
			    <div class="  map-switch map-zoom map-right-button">
					<Icons.zoomAddIcon title="放大" class="zoom-icon" @click="zoomIn(item)" />
					<Icons.zoomSubIcon title="缩放" class="zoom-icon" @click="zoomOut(item)" />
				</div>
            </div>
            <div class="title">
              {{ `视窗${item}` }}
              <span style="color: blue;">
                {{ `(${getBaseMapName(item, SimpleTreeRef[item -
                1]?.PropsExt?.currentBaseMapId?.value)})`
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  <!-- </teleport> -->
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed, inject } from "vue";
import { loadEsri } from "../../utils/arcgis-tools-3";
import { loadScript, loadCss, setDefaultOptions } from "esri-loader";
import { cloneDeep } from "lodash";
import { Close,SemiSelect } from "@element-plus/icons-vue";


import {
	OnemapClass,
	type IMapLayer,
	mapType,
	SimpleTree,
	SplitscreenTree,
	Arcgis3SplitScreenExt,
	getOnemap,
	getOption,
	deleteOnemap,
	InitMapControl,
	OnemapEvent,
	PropStorage,
	LayerStorage,
	Icons,
	MapZoomEx as MapZoom,
	Utils
} from "../../onemapkit";
import * as base from "../../base/base";
import * as Cesium from "@onemapkit/cesium";

import layerList from "./layerList.vue"
const props = defineProps({
	MapControlName: {
		type: String,
		default: "splitspreen1",
		require: true,
	},
	LayerStore: {
		type: Object as any,
		default: null,
	},
	PropStore: {
		type: Object as any,
		default: null,
	},
	Options: {
		type: Object as any,
		default: null,
	},
	onClose: Function as any
});


const mode = ref("content")
const selectedList=ref([] as any[])
const selectedNum=ref(1 as number)
const onClose = inject("onClose", () => { props.onClose() });

const tabActiveName = ref("window1");
const isSidebarCollapsed = ref(false);
const initLoaded = ref(false);
let _inOnemap = getOnemap(props.MapControlName);
const _inOptions = getOption(props.MapControlName);

const SimpleTree1 = ref({ TreeRefComponent: null as any, PropsExt: {} as any, UpdateOnemap: (option: any) => {}, getBaseMap: () => {}});
const SimpleTree2 = ref({ TreeRefComponent: null as any, PropsExt: {} as any, UpdateOnemap: (option: any) => {}, getBaseMap: () => {} });
const SimpleTree3 = ref({ TreeRefComponent: null as any, PropsExt: {} as any, UpdateOnemap: (option: any) => {}, getBaseMap: () => {} });
const SimpleTree4 = ref({ TreeRefComponent: null as any, PropsExt: {} as any, UpdateOnemap: (option: any) => {}, getBaseMap: () => {} });

const SimpleTreeRef = computed(() => {
	return [SimpleTree1.value, SimpleTree2.value, SimpleTree3.value, SimpleTree4.value]
})

let _BaseMapLayerBak: string[] = [];

const _PropStore1 = new PropStorage();
const _PropStore2 = new PropStorage();
const _PropStore3 = new PropStorage();
const _PropStore4 = new PropStorage();

const _LayerStore1 = new LayerStorage();
const _LayerStore2 = new LayerStorage();
const _LayerStore3 = new LayerStorage();
const _LayerStore4 = new LayerStorage();

let splitOption: any=[]

const layerTree = ref([] as any)
const activeTabIndex =ref(1 as number)


// 地图加载完成
// watch(
//   () => _inOnemap.isMapReady.value,
//   (newVal: any) => {
// 	  console.log("地图加载完成>>>>>>>>>>")
//   }
// );


// 根据目录树勾选项设置列表数据
const setListData=(): void=>{
	console.log("获取列表数据")
	let allData=[]
	// 视窗1数据
	let data= SimpleTree1.value?.TreeRefComponent?.getCheckedNodes();
	if(!data){
		data=[]
	}
	for (var i = 0 ;i<data.length;i++){
		data[i].label=data[i].name
		data[i].checked=true
		data[i].gid="v1_"+data[i].layerid
		data[i].tag="view1"
	}
	data=data.filter((item:any)=> !item.children)
	if(data.length>0){
		data.forEach((layerInfo: any)=>{
			if(layerInfo.serviceType=="Cesium3DTiles"){
				layerInfo.disabled=splitMapType.value==mapType.cesium?false:true
			}
		})
		allData.push({
			index:1,
			label:"视窗1",
			// disabled :data.length>0?false:true,
			gid:1,
			children:data
		})
	}

	// 视窗2数据
	if(selectedNum.value<2){

		selectedList.value=allData
		return;
	}
	data= SimpleTree2.value?.TreeRefComponent?.getCheckedNodes();
	if(!data){
		data=[]
	}
	for (var i = 0 ;i<data.length;i++){
		data[i].label=data[i].name
		data[i].checked=true
		data[i].gid="v2_"+data[i].layerid
		data[i].tag="view2"
	}
	data=data.filter((item:any)=> !item.children)
	if(data.length>0){
		data.forEach((layerInfo: any)=>{
		if(layerInfo.serviceType=="Cesium3DTiles"){
			layerInfo.disabled=splitMapType.value==mapType.cesium?false:true
			}
		})
		allData.push({
			index:2,
			label:"视窗2",
			// disabled :data.length>0?false:true,
			gid:2,
			children:data
		})

	}

	// 视窗3数据
	if(selectedNum.value<3){

		selectedList.value=allData
		return;
	}
	data= SimpleTree3.value?.TreeRefComponent?.getCheckedNodes();
	if(!data){
		data=[]
	}
	for (var i = 0 ;i<data.length;i++){
		data[i].label=data[i].name
		data[i].checked=true
		data[i].gid="v3_"+data[i].layerid
		data[i].tag="view3"
	}
	data=data.filter((item:any)=> !item.children)
	if(data.length>0){
		data.forEach((layerInfo: any)=>{
		if(layerInfo.serviceType=="Cesium3DTiles"){
			layerInfo.disabled=splitMapType.value==mapType.cesium?false:true
			}
		})
		allData.push({
		index:3,
		label:"视窗3",
		// disabled :data.length>0?false:true,
		gid:3,
		children:data
		})

	}


	// 视窗4数据
	if(selectedNum.value<4){
		selectedList.value=allData
		return;
	}
	data= SimpleTree4.value?.TreeRefComponent?.getCheckedNodes();
	if(!data){
		data=[]
	}
	for (var i = 0 ;i<data.length;i++){
		data[i].label=data[i].name
		data[i].checked=true
		data[i].gid="v4_"+data[i].layerid
		data[i].tag="view4"
	}
	data=data.filter((item:any)=> !item.children)
	if(data.length>0){
		data.forEach((layerInfo: any)=>{
		if(layerInfo.serviceType=="Cesium3DTiles"){
			layerInfo.disabled=splitMapType.value==mapType.cesium?false:true
			}
		})
		allData.push({
		index:4,
		label:"视窗4",
		// disabled :data.length>0?false:true,
		gid:4,
		children:data
		})
	}
	// 列表数据
	selectedList.value=allData
}
// 将多个视窗目录树对象传给列表组件，用于同步目录树勾选
const setListTree=(): void=>{
	layerTree.value= [SimpleTree1.value,SimpleTree2.value,SimpleTree4.value,SimpleTree4.value]
}
// 列表勾选状态变化时，同步修改资源目录树的勾选状态
const layerCheckChange=(index:Number,data:any,checked:boolean): void=>{
	// console.log("图层勾选变化",index,data,checked)
	if(index==1){
		onTreeCheck("splitspreen1",data,checked,1)
	}
	else if(index==2){
		onTreeCheck("splitspreen2",data,checked,2)
	}
	else if(index==3){
		onTreeCheck("splitspreen3",data,checked,3)
	}
	else if(index==4){
		onTreeCheck("splitspreen4",data,checked,4)
	}

}

// 根据目录树勾选状态变化，触发地图图层加载或移除事件
const onTreeCheck = async (mapid:any,data: any, checked: boolean,viewIndex:number) => {
	// console.log("图层加载或移除事件")

	nextTick(async () => {
		let map = getOnemap(mapid);
		if (checked) {
		if (data && data.layerid) {
			map.AddLayer(data);
			data.checked = true;
			// layerStore?.setCheckedLayerid(true, data);
			SimpleTreeRef.value[viewIndex-1].PropsExt?.setCheckedLayerid(checked, data, "T");
			SimpleTreeRef.value[viewIndex-1].PropsExt?.onMapLayerTreeCheck(data)
		}
		} else {
		map.RemoveLayerById(data);
		data.checked = false;
		// layerStore?.setCheckedLayerid(false, data);
		SimpleTreeRef.value[viewIndex-1].PropsExt?.setCheckedLayerid(checked, data, "T");
		SimpleTreeRef.value[viewIndex-1].PropsExt?.onMapLayerTreeCheck(data)
		}
	});
};
// 显示列表
const openList=(): void=>{
	setListData()
	setListTree()
	mode.value="list"
}

/** 拷贝Store对象，防止引用主Store */
function cp(obj: any, originObj: any) {
	for (let k of Object.getOwnPropertyNames(originObj)) {
		if (obj[k] && originObj[k].value) {
			if (typeof originObj[k].value === "object") {
				try {
					obj[k].value = cloneDeep(originObj[k].value);
				} catch {
					console.log(originObj[k])
				}
			} else {
				obj[k].value = originObj[k].value;
			}
		} else {
			if (typeof originObj[k] === "object") {
				obj[k] = cloneDeep(originObj[k]);
			} else {
				obj[k] = originObj[k];
			}
		}
	}
}

cp(_PropStore1, props.PropStore);
cp(_PropStore2, props.PropStore);
cp(_PropStore3, props.PropStore);
cp(_PropStore4, props.PropStore);

cp(_LayerStore1, props.LayerStore);
cp(_LayerStore2, props.LayerStore);
cp(_LayerStore3, props.LayerStore);
cp(_LayerStore4, props.LayerStore);

_LayerStore1.MapControlName="splitspreen1";
_LayerStore2.MapControlName="splitspreen2"
_LayerStore3.MapControlName="splitspreen3"
_LayerStore4.MapControlName="splitspreen4"

const __PropStore = computed(() => [
	_PropStore1,
	_PropStore2,
	_PropStore3,
	_PropStore4,
])
const splitSpreenNum = ref(2);
const newSpreenNum = ref(2);
const splitMapType = ref(mapType.cesium);	// 默认Cesium

//#region 1 基础变量定义
//1.1 分屏对象
let splitScreen: any = undefined;

// 1.3 屏选择按钮 分屏按钮图片
const SplitImgSrc = [
	{
		inactiveURL: "assets/images/1-0.jpg",
		activeURL: "assets/images/1-1.jpg",
		active: false,
	},
	{
		inactiveURL: "assets/images/2-0.jpg",
		activeURL: "assets/images/2-1.jpg",
		active: true,
	},
	{
		inactiveURL: "assets/images/3-0.jpg",
		activeURL: "assets/images/3-1.jpg",
		active: false,
	},
	{
		inactiveURL: "assets/images/4-0.jpg",
		activeURL: "assets/images/4-1.jpg",
		active: false,
	},
];
// 1.4 分屏选择按钮 js控制css
function splitImgHover(index: any, src: any) {
	if (!src.active) {
		(<HTMLImageElement>document.getElementById("splitImg" + index)).src =
			src.activeURL;
	}
}
function splitImgLeave(index: any, src: any) {
	if (!src.active) {
		(<HTMLImageElement>document.getElementById("splitImg" + index)).src =
			src.inactiveURL;
	}
}
//#endregion

const getEsri = (): Promise<any> => {
	if (_inOptions.BASE_URL) {
		window.dojoConfig = {
			parseOnLoad: true,
			async: true,
			tlmSiblingOfDojo: false,
			has: {
				"extend-esri": 1,
			},
			locale: "zh-cn",
			baseUrl: window.ARCGIS_BASE_URL + "/dojo",
			packages: [],
		};

		setDefaultOptions({
			url: window.ARCGIS_BASE_URL + "/init.js",
			css: window.ARCGIS_BASE_URL + "/esri/css/esri.css",
		});

		loadScript({ url: window.ARCGIS_BASE_URL + "/init.js" });
		loadCss(window.ARCGIS_BASE_URL + "/esri/css/esri.css");
	} else {
		Utils.ThrowError("'BASE_URL'参数必须设置", _inOptions);
	}

	return new Promise(async (resolve, _reject) => {
		loadEsri(async (data: any) => {
			resolve(data);
		})
	});
}

const getBaseMapName = (item: number, id: string) => {
	try {
		const bs = props.LayerStore.MapLayers.find((x: any) => x.layerid == id);
		if (bs) {
			return bs.name;
		} else {
			return item;
		}
	} catch {
		return item;
	}
}

//#region 2 分屏数点击事件 ChangeSplitScreenNum
function splitImgClick(idx: number, src: any, srcArray: any) {
	// console.log("分屏数点击事件")
	nextTick(() => {
		tabActiveName.value = "window1";
	})

	if (!src.active) {
		//#region  1. 分屏按钮效果
		// 所有的item.active改为false
		nextTick(() => {
			srcArray.forEach((item: any, index: number) => {
				if (item.active) {
					item.active = false;
					(<HTMLImageElement>document.getElementById("splitImg" + index)).src =
						item.inactiveURL;
				}
			});
			// 改变图片
			(<HTMLImageElement>document.getElementById("splitImg" + idx)).src =
				src.activeURL;
			// 将当前点击item.active改为true

			src.active = true;
		})
		//#endregion

		// 2. 产生分屏对象
		let tmpIdx = idx + 1;
		newSpreenNum.value = tmpIdx;

		nextTick(() => {
			// 3. 将分屏数改为当前选中的分屏数
			ChangeSplitScreenNum(tmpIdx);
		});
	}
}
let curExtent:any=null;
const ChangeSplitScreenNum = async (num: number) => {

	setListData()
	// console.log("分屏数修改：ChangeSplitScreenNum：",num)
	selectedNum.value=Number(num)
	//#region  1.获取分屏对象
	let EsriObj;
	if (splitMapType.value == mapType.arcgis) {
		EsriObj = await getEsri();
	}
	if(!_inOnemap){
		_inOnemap=getOnemap("mainMapControl")
	}


	let extent = null;
	// let map1Option = getOption("splitspreen1");
	let map1Option =splitOption[0]?.option
	if(!map1Option){
		map1Option=_inOptions
	}
	if (map1Option && map1Option["mapextent"]) {
		extent = map1Option["mapextent"];
	} else if (_inOnemap.MapType.value == mapType.arcgis) {
		extent = _inOnemap.getMapExtent();
	}
	if(!curExtent){
		curExtent=extent
	}
	if (splitScreen == undefined) {
		try{
			if(splitMapType.value == mapType.cesium){
				let terrain= null
				try{
					terrain= await Cesium.CesiumTerrainProvider.fromUrl(map1Option.TerrainUrl.replace("layer.json", ""), {
												requestVertexNormals: true,
											})
				}
				catch{
					terrain= null
				}
				splitScreen = new base.SplitScreenExt({
						MakeBaseMapLayer: () => {
							// const tmplayer = _inOnemap.CreateGroupLayer(map1Option.BaseMapLayer);
							// Utils.addLayreIDAndType(tmplayer, map1Option.BaseMapLayer.layerid, map1Option.BaseMapLayer.layerType, false);
							// return tmplayer;
							return null
						},
						// TerrainUrl: await Cesium.CesiumTerrainProvider.fromUrl(map1Option.TerrainUrl.replace("layer.json", ""), {
						// 	requestVertexNormals: true,
						// }),
						TerrainUrl:terrain,
						mode: splitSpreenNum.value,
						cameraOptions: base.SplitScreenExt.getCameraOptions(_inOnemap.MapViewer),
					})
			}
			else{
				splitScreen = new Arcgis3SplitScreenExt(
						{
							mode: splitSpreenNum.value,
							extent: extent,
						},
						EsriObj
					);
			}

		}
		catch{
			closeHandle()
		}



		props.PropStore.updateLocationEvent = (extent: any) => { splitScreen.asyncExtent(splitScreen, extent) };

		const waitLoaded = (map: OnemapClass, _extent?: any) => {
			return new Promise((resolve, _reject) => {
				map.MapViewer.on("load", function () {
					map?.fullExtent(_extent || undefined);
					resolve(true);
				});
			});
		}

		// 创建第一屏OmemapClass
		const _Onemap = new OnemapClass(splitMapType.value);
		_Onemap.MapControlName = "splitspreen1";
		if (splitMapType.value == mapType.arcgis) {
			_Onemap.setMapViewer(
				splitScreen._viewers["sub1"],
				EsriObj
			);
			let currentBaseMap = SimpleTreeRef.value[0].PropsExt?._Options?.BaseMapLayer;
			if (!currentBaseMap) {
				currentBaseMap = map1Option.BaseMapLayer;
			}

			_Onemap.AddLayer(currentBaseMap);
			await waitLoaded(_Onemap, extent);
		} else {
			_Onemap.setMapViewer(splitScreen._viewers["sub1"]);
		}
		let tmpOptions = cloneDeep(map1Option);
		tmpOptions.MapControlName = "splitspreen1";
		nextTick(() => {
			InitMapControl(_Onemap, tmpOptions);
			SimpleTreeRef.value[0] && SimpleTreeRef.value[0].UpdateOnemap(tmpOptions);
			initLoaded.value = true;
		})
	}

	(splitScreen as any).changeMode(num);
	//#endregion


	// 2.1 分屏数小于当前的splitSpreenNum，就要删除多余的
	if (num < splitSpreenNum.value) {
		for (let i = num; i < splitSpreenNum.value; i++) {
			deleteOnemap("splitspreen" + (i + 1));

		}
	}
	//2.2 已加载第一屏，补足其他屏
	if (num > 1) {


		for (let i =1; i < num; i++) {
			let op=_inOptions;

			if(splitOption[i] && splitOption[i].option  ){
				op=splitOption[i].option
			}
			const _Onemap = new OnemapClass(splitMapType.value);
			_Onemap.MapControlName = "splitspreen" + (i + 1);
			if (splitMapType.value == mapType.arcgis) {
				_Onemap.setMapViewer(
					splitScreen._viewers["sub" + (i + 1)],
					EsriObj
				);
			} else {
				_Onemap.setMapViewer(splitScreen._viewers["sub" + (i + 1)]);
			}

			// let currentBaseMap = null;
			// try {
			// 	currentBaseMap = props.LayerStore.MapLayers.find((x: any) => x.layerid == _BaseMapLayerBak[i]);
			// 	if (!currentBaseMap) {
			// 		currentBaseMap = op.BaseMapLayer;
			// 	}
			// } catch {
			// 	currentBaseMap = op.BaseMapLayer;
			// }
			// _Onemap.AddLayer(currentBaseMap);

			let tmpOptions = cloneDeep(op);
			tmpOptions.MapControlName = "splitspreen" + (i + 1);
			nextTick(() => {

				InitMapControl(_Onemap, tmpOptions);
			})

			if (splitMapType.value == mapType.arcgis) {
				//地图加载完之后，调用相关回调
				_Onemap.MapViewer.on("load", function () {
					//保存地图加载完毕的状态
					_Onemap.isMapReady.value = true;
					_Onemap?.fullExtent(op?.mapextent || undefined);
				});
				_Onemap.changeBasemap(op.BaseMapLayer);
			}
		}
	}
	nextTick(() => {
		splitSpreenNum.value = num;
		nextTick(() => {

			for (let i = 0; i < num; i++) {
				const _onemap = getOnemap("splitspreen" + (i + 1));
				if(_onemap.MapType.value==mapType.arcgis){
					_onemap.MapViewer?.removeAllLayers();

				}
				else{
					_onemap.MapViewer?.imageryLayers?.removeAll();
				}
				let _options = splitOption[i]?.option
				if(!_options){
					_options= getOption("splitspreen" + (i + 1));
				}


				if(_onemap && _options){
					if(SimpleTreeRef.value[i]){
							SimpleTreeRef.value[i]?.PropsExt?.setTreeRef(SimpleTreeRef.value[i].TreeRefComponent,null,  null)
					}

					SimpleTreeRef.value[i].UpdateOnemap(_options);
					// 设置底图
					const tdtConfig = SimpleTreeRef.value[i]?.PropsExt?.tdtConfig?.value;
					let preBasemap: any =SimpleTreeRef.value[i]?.getBaseMap();
					let basemapConfig = null;
					if(preBasemap){
						basemapConfig = preBasemap.baesmap
					}
					else{
						basemapConfig = _options.BaseMapLayer
					}
					if(tdtConfig && basemapConfig){
						SimpleTreeRef.value[i].PropsExt?.onChangeBaseMap(basemapConfig,tdtConfig);
						SimpleTreeRef.value[i].PropsExt?.changeBasemap(tdtConfig);
						SimpleTreeRef.value[i].PropsExt?.changeLabel(tdtConfig);
					}

					// 重新加载勾选图层
					selectedList.value.forEach((element:any) => {
						if(element.index==i+1){
							let layers = element?.children
							let index = 10;
							if(layers && layers.length>0){
								layers.forEach((layer: any)=> {
									layer.layerOrder = index
									index = index+1
									SimpleTreeRef.value[i].PropsExt?.onMapLayerTreeCheck(layer)
									// _onemap.ImageryLayerClass.moveUpIndex(layer);
									// _onemap.ImageryLayerClass.moveUpIndex(layer);
									// _onemap.ImageryLayerClass.moveUpIndex(layer);

								});
							}
						}

					});



					// 定位到上次显示范围

					curExtent=splitOption[0]?.option?.mapextent
					setTimeout(() => {
						// 避免联动导致定位问题
						if(curExtent){
						_onemap?.fullExtent(curExtent);
						}
						else{
							curExtent=getOnemap("splitspreen1")?.getExtent()
							if(curExtent){
								_onemap?.fullExtent(curExtent );

							}
						}

					}, 200*i);

				}
			}

		})
	})

};




const setWindow = (win: number) => {
	tabActiveName.value = "window" + (win);
	// console.log("切换窗体",win)
	activeTabIndex.value=win
}

const closeHandle = () => {
	onClose();
}


let resizeTimer :any = null
window.addEventListener('resize',function(){
	clearTimeout(resizeTimer)
	resizeTimer = setTimeout(()=>{
		const dialog:any = document.getElementById("splitScreenContainer")?.parentNode?.parentNode?.parentNode
		if(dialog){
			dialog.style.width = window.innerWidth - 5 +'px'
			dialog.style.height= window.innerHeight- 5 +'px'
			dialog.style.top = '0px';
			dialog.style.left = '0px';
			dialog.style.right = '0px';
			dialog.style.bottom = '0px';
		}
	},500)
})
onMounted(() => {
	splitMapType.value = _inOnemap.MapType.value;
	console.log("进入分屏:",	splitMapType.value,window.innerWidth,window.innerHeight )
	// if(splitMapType.value != _inOnemap.MapType.value){
	// 	ChangeMapType()
	// }
	nextTick(() => {
		const panal = document.querySelector(".panel-content") as any;
		if (panal) {
			panal.style.height = "auto";
		}
					const dialog:any = document.getElementById("splitScreenContainer")?.parentNode?.parentNode?.parentNode
			if(dialog){
				console.log("进入分屏:",	splitMapType.value,window.innerWidth,window.innerHeight )
				dialog.style.width = window.innerWidth - 5 +'px'
				dialog.style.height= window.innerHeight - 5 +'px'
				//inset
				dialog.style.top = '0px';
				dialog.style.left = '0px';
				dialog.style.right = '0px';
				dialog.style.bottom = '0px';
			}
	})

	if (OnemapEvent.SplitSwitchVisible) OnemapEvent.SplitSwitchVisible(false);
	nextTick(() => {
		ChangeSplitScreenNum(2);
		splitSpreenNum.value = 2;
		newSpreenNum.value = 2;
		setTimeout(()=>{
			// 定位到主视图当前位置
			let extent  = getOnemap("mainMapControl")?.MapViewer?.extent
			let map = getOnemap("splitspreen1");
			if(map && extent){
				map.fullExtent(extent)
			}
			map = getOnemap("splitspreen2");
			if(map && extent){
				map.fullExtent(extent)
			}

			//TODO: 不知道什么原因导致初始化时分屏1白屏，切换点击后能显示地图
			let viewDom = document.getElementById("split-view-2")
			if(viewDom){
				viewDom.click()
			}
			viewDom = document.getElementById("split-view-1")
			if(viewDom){
				viewDom.click()
			}
			setWindow(1)


		},2000)


		
	});
});

onUnmounted(() => {
	if (OnemapEvent.SplitSwitchVisible) OnemapEvent.SplitSwitchVisible(true);

	deleteOnemap();
	if (splitScreen) {
		(splitScreen as any)?.destroy();
	}
	splitScreen = null;
});

/**
 * 视口样式控制
 * @param index
 */
const itemStyle = (index: number) => {
	if (newSpreenNum.value === 1) return {}

	const isOdd = newSpreenNum.value % 2 !== 0
	const rowCount = isOdd ? Math.ceil(newSpreenNum.value / 2) : newSpreenNum.value / 2

	return {
		height: `${100 / rowCount}%`,
		width: isOdd && index === 0 ? '100%' : '50%'
	}
}

/**
 * 展开/收起
 */
const toggleSidebar = () => {
	isSidebarCollapsed.value = !isSidebarCollapsed.value;
	console.log("折叠展开面板",isSidebarCollapsed.value)


	// const dom_sidebar= document.getElementsByClassName("sidebar")
	// const dom_split_sidebar= document.getElementsByClassName("split-sidebar")
	// const dom_toggle= document.getElementsByClassName("split-sidebar-toggle")
	// const dom_splitScreenMapContainer= document.getElementById("splitScreenMapContainer")

	// console.log("dom>>>>>>>",dom_sidebar)
	// console.log("dom>>>>>>>",dom_split_sidebar)
	// console.log("dom>>>>>>>",dom_toggle)
	// console.log("dom>>>>>>>",dom_splitScreenMapContainer)


};

/**
 * 定位到全幅
 */
const locationGlobalHandle = () => {
	for (let i = 0; i < splitSpreenNum.value; i++) {
		getOnemap('splitspreen' + (i + 1)).fullExtent(props.PropStore.fullExtent);
	}
}

/**
 * 二三维切换
 */
const ChangeMapType = () => {
	// console.log("ChangeMapType")
	setListData()
	_BaseMapLayerBak = [];
	splitOption=[]
	for (let i = 0; i < splitSpreenNum.value; i++) {
		// console.log(i+1)
		let _onemap = getOnemap("splitspreen" + (i + 1));
		// let _options = getOption("splitspreen" + (i + 1));
		let _options = SimpleTreeRef.value[i].PropsExt._Options

		// if(bm){
		// 	_options.BaseMapLayer=bm
		// }
		// if(i==0){
		// 	curExtent= _onemap.getMapExtent();
		// }
		_options.mapextent=curExtent
		splitOption.push({
			map:_onemap,
			option:_options,
			// propsExt:SimpleTreeRef.value[i].PropsExt
		})


		InitMapControl(_onemap, _options);
		// console.log("23d切换",splitOption[i].option.BaseMapLayer)
		if (_onemap) {
			_onemap.MapType.value = _onemap.MapType.value === mapType.arcgis ? mapType.cesium : mapType.arcgis;
			_BaseMapLayerBak[i] = SimpleTreeRef.value[i].PropsExt.currentBaseMapId.value;
			_options["mapextent"] = _onemap.getMapExtent();

			// SimpleTreeRef.value[i].PropsExt?.setTreeRef(SimpleTreeRef.value[0]?.TreeRefComponent,null,  null)


			_onemap.isMapReady.value = false;
			if (_onemap.MapViewer.destroy) {
				_onemap.MapViewer.destroy();
			}

			deleteOnemap("splitspreen" + (i + 1));
		}
	}

	splitMapType.value = splitMapType.value === mapType.arcgis ? mapType.cesium : mapType.arcgis;

	nextTick(() => {
		if(splitScreen){
			splitScreen.destroy();
			splitScreen = null;

		}
		newSpreenNum.value = splitSpreenNum.value;
		// splitSpreenNum.value = 1;
		ChangeSplitScreenNum(newSpreenNum.value);
	});
}


const zoomIn=(item: any): void=>{
	// console.log("放大",item)
	const map =getOnemap("splitspreen" + item);
	 map?.zoomIn();
}
const zoomOut=(item: any): void=>{
	// console.log("缩小",item)
	const map =getOnemap("splitspreen" + item);
	map?.zoomOut();
}
</script>

<style lang="scss" scoped>
@import url("./splitscreen.scss");

.div-form-container {
  white-space: nowrap;
  display: flex;
  align-items: center;
  text-align: left;
  margin: 2px 0px;
  width: 100%;
}

.div-form-item {
  display: inline-block;
  /* 水平排列 */
  margin: 0px;
  /* 添加间距 */
  width: 60px;
}

.eidt-custom-item {
  align-items: center;
  margin: 15px 5px 15px 5px;
  padding: 5px 1px 5px 2px;
  box-shadow: var(--el-box-shadow-lighter);
}

.eidt-custom-item-sub {
  display: flex;
  align-items: center;
  margin: 5px 1px 5px 1px;
  padding: 2px 1px 2px 1px;
}

.form-item {
  margin: 5px 5px;
}

.from-item-dom {
  display: flex;
  width: 100%;
  align-items: center;
}

.form-item-btn {
  margin-left: 1px;
}
/*
:deep(.el-collapse-item__header) {
  border: 1px solid #e3ebec;
  margin: 3px 1px;
  box-shadow: var(--el-box-shadow-lighter);
}*/
.el-collapse-itemcontent {
  background-color: #08b216;
}

.el-card :deep {
  --el-card-padding: 5px;
  margin: 5px;
  padding: 0px;
  border-radius: 0px;
}

.header {
  border-radius: 4px;
  background: #e1e4e6;
  display: flex;
  justify-content: space-between;
  font-size: 20px;
  align-items: center;
  height: 40px;
  /*line-height: 40px;*/
  /*font-weight: 800;*/
  padding: 0 15px;
  color: #535353;
}

.header-title {
  font-size: 14px;
  color: #000;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

.split-screen-minimize-container {
  z-index: 2000;
  position: absolute;
  /*border-radius: 4px;*/
  top: 70px;
  right: 80px;
  width: 260px;
  /*height: 200px;*/
  /*border: 1px solid red;*/
  background: white;
}

.fold_animation {
  left: 340px;
  animation: sliderIn 1s;
}

.fold_animation1 {
  left: 10px;
  animation: sliderOut 1s;
}

@keyframes sliderIn {
  0% {
    left: 10px;
  }

  50% {
    left: 170px;
  }

  100% {
    left: 340px;
  }
}

@keyframes sliderOut {
  0% {
    left: 340px;
  }

  50% {
    left: 170px;
  }

  100% {
    left: 10px;
  }
}

.custom-tree-node__more {
  position: absolute;
  right: 10px;
  margin-top: 8px;
  size: small;
}

.custom-tree-node__more:hover {
  color: #c7cbd4;
}

/*.body-right-container{*/
/*    background-color: #22ee22;*/
/*    position: absolute;*/
/*    top:30px;*/
/*    bottom: 60px;*/
/*    right: 10px;*/
/*}*/
.fold_container {
  color: #666;
}

.fold_container:hover {
  color: #66ccff;
}

.compass-container {
  background: url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.02);
  width: 52px;
  height: 52px;
  border-radius: 24px;
  overflow: hidden;
  position: relative;
}

.compass-container .pointer {
  position: absolute;
  z-index: 5;
  top: 10px;
  color: #717171;
  width: 12px;
  height: 34px;
  left: 20px;
  background: url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
}

.compass-container .arrow-left,
.compass-container .arrow-right {
  position: absolute;
  z-index: 5;
  top: 13px;
  font-size: 24px;
  color: #717171;
  cursor: pointer;
  width: 8px;
  height: 28px;
  /*image-rendering: -moz-smooth;*/
  /*image-rendering: -o-smooth;*/
  /*image-rendering: -webkit-optimize-contrast;*/
  image-rendering: smooth;
}

.compass-container .arrow-left {
  left: 3.5px;
  background: url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
}

.compass-container .arrow-right {
  right: 3px;
  background: url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
}

.compass-container .arrow-left:hover {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAADICAYAAAB4ZriWAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RTQ2NDBCRTVFODQwMTFFQkIwNUVDNjdBQjEyRDZCOUMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RTQ2NDBCRTZFODQwMTFFQkIwNUVDNjdBQjEyRDZCOUMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpFNDY0MEJFM0U4NDAxMUVCQjA1RUM2N0FCMTJENkI5QyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpFNDY0MEJFNEU4NDAxMUVCQjA1RUM2N0FCMTJENkI5QyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pomhd4wAAAasSURBVHja7J19aJVVHMfPXlomWypzztmitBKrvUmCo9aw1P7I3t+LXjTIgqBZEFkR9UdFRpQ3QukFo2aERS8QGlGYYZiSlrNtRhjZFq6caWm1tGh9f95z6bZ2d88597z8jj0/+PKM6znsc77+zrnnPHvOeYomLh0UIaOvLfe/1aRECy4XQqdADVAltB56BPU2lQqGAejrcCHNG+afqTFnoMyMUmbQ5G4KmpWn6CRoYSkj8BtweVmjysxiJuALNMEpdhQzAK/FZYVB1Q4Ozj9pWG9baPgHoSsN6u2CukPCnwrdaVh3Gcb5X0PCL4LGGNT7GlpOP4SCn03jdAGu7w0Jv8iwXmfG9VDw9GV0QQGuD4SEv8Ow3iaAL8/+wDf89dAMw7qpoR/4hr/JsN47cP3VkPCU53MM6z493IfFEbi+Aq5/EBKeVkRXGNQbGC7XfcObup6C69tCwtdB8w3q9eTKdZ/w5Hqpoet9IeFrDV3fnM91H/DXQONNhka4/mdo+MsN6rwL8HaVgi7h6Qup2cY0IAS8iesvwfX3QsNXGcI/o1O42KHrVZp1VsH1zVzgdaNdt4IL+GaD2eM6uL6aA7wX113AH2MAvx1ayQGewCdr1lmJlDnEBV4n9pmmjG14mvpeopvrcL2XA7y3juoCXnd4XCWnvsHhp8l1qjfXbcLP1v1SglbHCt9u45fagB+vme97oLe4wJPrFRrl18jxnQ280IQXMcJ/zwn+TGiKpusHOMEHSRnf8N/FDE/gA1zgZ0LVoVKmUPhWjbI7Y4Yn8D84wbeETJlC4JugsYpld3CEV42PoEFO8HUaZT8RjsIU/vSY4VWd74K6OcFTR61VLLtROIxixymzgRu8TmddG6vzXXJaEKXzTlPGtfNrucGPgybECn+SYrkvoN2xwjt33SX8Vo7wqrc5tsfsfHes8PRnmgPc4Mug47mkjC48q5TRhZ8SM7yq8yzTpiZm51VudfwA7eUIP4aT6y6cjxr+W67wKmmzO2bnE3jb8EdBoxXK9XOEV72dzdJ5lc66H/o9Vue9powOfAW3lNHtsEc0fH/ifCD4Q1zhWR6rYdP5BD6BT+AT+P85vIgZfn/M8Cr320fH7Hx5zPAVMadN4nyS80naMIEfVHCfrfMqqVPBGT5f6pT5boBNeIrjuMLvixm+N2b4nsR5xvCTEueTnP930N+b+mOFV02dhpjhG7nC9xzpzidpEwq+Wujt1mSV897cd+G8t7w3uUus8nh5c8zwLVzhOxQ77cmxOu/FfZfwZ3GE/0mo7Txj6byq+3TaUGWs8BStHOE7FMvNjdn5ORzhd8qOmy/onTh13OB13D8ngbcM36EBXxmr8/Q07PkqBWtS4lroRE7wFPMUwEfhcjdU7wOeYr0GfL6tSvdB03UWMr7gy0dKHbg+XbouOMLnS517oVHyZ+W0KSrwBXB0cLLqY7j0xOtU8d8dPfSutFeGfDaxr+3whjGnztON188Uy5YN436JdN1oAW/jMS2d1Bma9w/lmD7U+4LfoJn3mVOJ6nK47tX5Ts3yC7JcL8lRxpvztB/wS43y80X61WMjnabbgOGz3Ae8bt4fK10fKUqFwqFXtuA7NMufplCm3hd8p7AfDb7g6WCSXsvw3pynrdOf2nYenbbIB3zGfZtRka/TcoanaMw3JM2y9It+lHcUxvrqtAS/DqKl11LoYsEr6lXShu7D0MH3KWbwDTo5T29SvIsR/ASMOFN1OuxTct7Rw6QBTbqjzZvQZZpzFlfRaDJUboEuEpbOineR9/nGeRr6boQe5jjiqH5JPQDdAv0SAP4E+dLngr5hX5D9oItLp9WdHrwv0q9vX8Oh05rMbb6RC+llMcJn4nbonpCdttBZ5ePQ1VCfY/hp6LTjXEyJX5NptNF3p7U1n/8cOk+k35cTHTwFbcug10suiRE+E4uh26CDrjutq/2wz4r0TdWvLC/Ij/YBT0FHnc6VX2w2omToeO96J3KP7MjPu8h7X9uoF4rct7PZw1M8JtJvpN5ja5rgewP7GyL9IqEtNhYmIXbf06uxW+VSUzfKMeLUhYSn+E0u8pcUkvehzz1YLFdof5nkffBDG/raDq/QzpXrhKiczzSA3kVCjzJ+GJXzWQ3YBdH/wHN5ilah005mBZ/ViFvFPw9RjJg6LA8qQQOewOVS6Ofo4GUD3hbp55G3RgcvG0D3iGgjwevDdVr259ugAQehq/Djo1kf01206mgO50ED7sfl5mz3ozpZCA14EZezRfovOU3RHYuEBnws+0HT3wIMAH8xWh5Zp+98AAAAAElFTkSuQmCC)
    no-repeat;
  background-size: 100% 100%;
}

.compass-container .arrow-right:hover {
  background: url(data:image/png;base64,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)
    no-repeat;
  background-size: 100% 100%;
}

/* **********************************  分屏  ********************************** */
.SplitScreen1 {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .onedViewer {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
  }
}

.SplitScreen2 .onedViewer,
.SplitScreen2 .secondViewer {
  position: absolute !important;
  width: 50% !important;
  height: 100% !important;
}

.SplitScreen2 .secondViewer {
  left: 50%;
  top: 0;
}

.SplitScreen3 .onedViewer {
  position: absolute !important;
  width: 100% !important;
  height: 50% !important;
}

.SplitScreen3 .secondViewer,
.SplitScreen3 .thirdViewer {
  position: absolute !important;
  width: 50% !important;
  height: 50% !important;
}

.SplitScreen3 .secondViewer {
  left: 0;
  top: 50%;
}

.SplitScreen3 .thirdViewer {
  left: 50%;
  top: 50%;
}

.SplitScreen4 .onedViewer,
.SplitScreen4 .secondViewer,
.SplitScreen4 .thirdViewer,
.SplitScreen4 .fourthViewer {
  position: absolute !important;
  width: 50% !important;
  height: 50% !important;
}

.SplitScreen4 .secondViewer {
  left: 50%;
  top: 0;
}

.SplitScreen4 .thirdViewer {
  left: 0;
  top: 50%;
}

.SplitScreen4 .fourthViewer {
  left: 50%;
  top: 50%;
}
.top-bar {
  .normal {
    color: grey;
  }
}
.show {
  display: block;
  position: relative;
  height: 100%;
}
.hide {
  display: none;

}

// 侧边栏
.split-body {
  display: flex;
  height: 100vh;
}
.sidebar {
  width: 500px;
  position: relative;
  transition: width 0s ease;
}
.split-sidebar-toggle {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  // padding: 10px;
  cursor: pointer;
}
.sidebar-closed {
  width: 0;
  overflow: hidden;
}
.sidebar-closed .sidebar-content {
  display: none;
}

.map-right-button{
position: absolute;
margin-top: 8px;
width: 30px;
bottom: 25px;
margin-left: 8px;
background: #fff;
border-radius: 4px;
box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
text-align: center;
cursor: pointer;
.map-zoom {
	position: absolute;
	bottom: 0px;
	right: 0px;
	font-size: 12px;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 1px;
	cursor: pointer;
	}
	.zoom-icon {
	margin: 4px 0;
	width: 28px;
	height: 24px;
	}
}

#maplayertreeid{
	position:relative !important;
}
#splitScreenMapContainer{
	position:relative !important;
	height:calc(100% - 43px)  !important;
}
</style>
