<template>
	<div class="earth-work-calculation-container">
        <div class="content">
			<el-form class="form-content"
					 style="margin-top: 10px">
				<el-form-item label="视点设置">
					<el-button type="primary"
							   size="small"
							   @click="getClickPointInfo">图上拾取视点</el-button>
				</el-form-item>
				<el-form-item label="目标点设置">
					<el-button type="primary"
							   size="small"
							   @click="getClickPointInfoTarget">图上拾取目标点</el-button>
				</el-form-item>
				<el-form-item label="目标点高度">
					<el-input-number v-model="targetHight"
									 :min="0"
									 :step="1"
									 @change="changeTargetHight" />
				</el-form-item>
				<el-form-item label="远平面">
					<el-input-number v-model="farFloat"
									 :min="10"
									 :max="1000"
									 :step="1"
									 @change="changeTargetHight" />
				</el-form-item>
				<el-form-item label="近平面">
					<el-input-number v-model="closeFloat"
									 :min="0.1"
									 :max="10"
									 :step="0.01"
									 @change="changeTargetHight" />
				</el-form-item>
				<el-form-item label="视场角">
					<el-input-number v-model="sightAngel"
									 :min="10"
									 :max="90"
									 :step="1"
									 @change="changeTargetHight" />
				</el-form-item>
				<el-form-item label="纵横比">
					<el-input-number v-model="floatScale"
									 :min="0.5"
									 :max="2"
									 :step="0.1"
									 @change="changeTargetHight" />
				</el-form-item>
			</el-form>
		</div>
    </div>
</template>


<script setup lang="ts">
import { onUnmounted, ref, defineProps } from "vue";
// import * as CesiumTools from "cesium-tools";
import { getOnemap } from "../../onemapkit";
import {useDrawPointWithImg} from "./index";
import * as Cesium from "@onemapkit/cesium";
import {
    VisibilityAnalysis
} from "@onemapkit/cesium-tools";


const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: Object as any,
    default: null,
  },
  panelVisible: {
    type: Boolean,
    default: false,
  },
});

const targetHight = ref(80);
const farFloat = ref(1000);
const closeFloat = ref(0.1);
const sightAngel = ref(45);
const floatScale = ref(1.5);
let pointEntity: Cesium.Entity;
let pointId: string;
let targetId: string;
const _inOnemap = getOnemap(props.MapControlName);

const viewer = _inOnemap.MapViewer;
const targetX = ref(0);
const targetY = ref(0);
let position: object;
let viewCenter: object;
let visibilityAnalysis: object;


//清除绘制
const clearDraw = () => {
	if (visibilityAnalysis) {
		 // @ts-ignore
		visibilityAnalysis.destroy();
	}
	viewer.entities.values.forEach((item:any) => {
		if (item._name == "loacte" || item._name == "target") {
			item._show = false;
		}
	});
}
const getClickPointInfo = () => {
	if (visibilityAnalysis) {
		 // @ts-ignore
		visibilityAnalysis.destroy();
	}
	viewer.entities.values.forEach((item: any) => {
		if (item._name == "loacte") {
			item._show = false;
		}
	});
	useDrawPointWithImg(
		"loacte",
		viewer,
		(id: any) => {
			console.log(id,'---Id')
			pointId ? viewer.entities.removeById(pointId) : "";
			pointEntity = viewer.entities.getById(id);
			pointId = pointEntity.id;

			 console.log(pointEntity.position,'-----position')
			// @ts-ignore
			let cartesian3 = pointEntity.position._value;
			let cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
			// position.value = cartesian3
			// // 弧度转经纬度转笛卡尔坐标
			let x = Cesium.Math.toDegrees(cartographic.longitude);
			let y = Cesium.Math.toDegrees(cartographic.latitude);
			let h = cartographic.height;
			position = Cesium.Cartesian3.fromDegrees(x, y, h);
		},
		false
	);
}


const getClickPointInfoTarget = () => {
	if (visibilityAnalysis) {
		 // @ts-ignore
		visibilityAnalysis.destroy();
	}
	viewer.entities.values.forEach((item: any) => {
		if (item._name == "target") {
			item._show = false;
		}
	});
	useDrawPointWithImg(
		"target",
		viewer,
		(id:any) => {
			console.log(id,'---targetId')
			targetId ? viewer.entities.removeById(targetId) : "";
			pointEntity = viewer.entities.getById(id);
			targetId = pointEntity.id;
			 // @ts-ignore
			let cartesian3 = pointEntity.position._value;
			console.log(pointEntity.position,'-----targetposition')
			let cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
			// targetPosition.value = cartesian3
			targetX.value = Cesium.Math.toDegrees(cartographic.longitude);
			targetY.value = Cesium.Math.toDegrees(cartographic.latitude);
			 // @ts-ignore
			targetHight.value = parseInt(cartographic.height);
			viewCenter = Cesium.Cartesian3.fromDegrees(
				targetX.value,
				targetY.value,
				targetHight.value
			);
			visibilityAnalysis = new VisibilityAnalysis(viewer, {
				position,
				viewCenter,
			});
		},
		false
	);
}

const changeTargetHight = () => {
	const newViewCenter = Cesium.Cartesian3.fromDegrees(
		targetX.value,
		targetY.value,
		targetHight.value
	);
	 // @ts-ignore
	visibilityAnalysis.updateCameraFrustum({
		position,
		viewCenter: newViewCenter,
		far: farFloat.value,
		near: closeFloat.value,
		fov: sightAngel.value,
		aspectRatio: floatScale.value,
	});
};
onUnmounted(()=>{
    clearDraw()
})
</script>


<style scoped>
.earth-work-calculation-container {
	background: white;
	/* width: 340px;
	position: absolute;
	top: 20px;
	right: 80px;
	border-radius: 4px; */
}
.content {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
}
.form-content {
	padding: 15px;
}
.colorstyle {
	width: 160px;
	display: flex;
	justify-content: space-between;
	border: 1px solid rgb(220, 223, 230);
	align-items: center;
	padding: 0 15px 0 0;
	height: 24px;
	border-radius: 3px;
}
</style>