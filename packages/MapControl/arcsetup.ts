import { ref } from "vue";
import { mapType, type IPosition, OnemapClass, type mapEmitsEventType, Utils } from "../onemapkit";
import throttle from "lodash/throttle";
import { loadEsri } from "../utils/arcgis-tools-3";
// import { getCookie,getSubLayers } from "../utils/utils";
import { cloneDeep } from "lodash";
import { loadScript, loadCss, setDefaultOptions } from "esri-loader";

declare global {
	interface Window {
		dojoConfig: any;
		ARCGIS_BASE_URL: string;
	}
}

const esriObj: any = ref(null);
//记录鼠标位置
let mousePosition: IPosition = {
  x: 0,
  y: 0,
  z: 0,
  wkid: 4490,
  zoom: 0,
  scale: 0,
};

export function arcsetup(
  _inOptions: any,
  _Onemap: OnemapClass,
  emits: any, 
  _LayerStore: any  
) {
	window.dojoConfig = {
		parseOnLoad: true,
		async: true,
		tlmSiblingOfDojo: false,
		has: {
			"extend-esri": 1,
		},
		locale: "zh-cn",
		baseUrl: window.ARCGIS_BASE_URL + "/dojo",
		packages: [],
	};

	setDefaultOptions({
		url: window.ARCGIS_BASE_URL + "/init.js",
		css: window.ARCGIS_BASE_URL + "/esri/css/esri.css",
	});
		
	loadScript({ url: window.ARCGIS_BASE_URL + "/init.js" })
	loadCss(window.ARCGIS_BASE_URL + "/esri/css/esri.css")
	
  //绑定事件 --- 地图实时回调函数
  const _MapRealtimeEvent: mapEmitsEventType = (mapControlName: String, mapPoint: any) => {
    emits("MapRealtimeEvent", mapControlName, mapPoint);
  };
  const _MapClickEvent: mapEmitsEventType = (
    mapControlName: String,
    mapPoint: any,
    _options: any
  ) => {
    emits("MapClickEvent", mapControlName, mapPoint, _options);
  };

  //@ts-ignore
  const onMapEvents = (props: any, map: any) => {
    // if (_Onemap.MapRealtimeInterval != 0) {
      esriObj.value?.esriOn(map, "mouse-move", handleMousePositionThrottled);
    // }
    if (_Onemap.isMapClick) {
      esriObj.value?.esriOn(map, "click", handleMapClick);
    }
    /*
    if (props.MapRealtimeEvent.MapEvent) {
      esriObj.value?.esriOn(map, "mouse-move", handleMousePositionThrottled);
    }
    if (MapClickEvent) {
      esriObj.value?.esriOn(map, "click", handleMapClick);
    }*/
  };
  const handleMapClick = (event: any) => {
    let mapPoint = {
      x: event.mapPoint.x,
      y: event.mapPoint.y,
      wkid: event.mapPoint.spatialReference.wkid,
    };
    let mapOptions = {
      width: _Onemap.MapViewer.width,
      height: _Onemap.MapViewer.height,
      extent: {
        xmin: _Onemap.MapViewer.extent.xmin,
        xmax: _Onemap.MapViewer.extent.xmax,
        ymin: _Onemap.MapViewer.extent.ymin,
        ymax: _Onemap.MapViewer.extent.ymax,
        spatialReference: {
          wkid: _Onemap.MapViewer.extent.spatialReference.wkid,
        },
      },
    };
    _MapClickEvent(_inOptions.MapControlName, mapPoint, mapOptions);

    /*
    if (MapClickEvent) {
      MapClickEvent(
        {
          x: event.mapPoint.x,
          y: event.mapPoint.y,
          wkid: event.mapPoint.spatialReference.wkid,
        },
        {
          width: _Onemap.MapViewer.width,
          height: _Onemap.MapViewer.height,
          extent: {
            xmin: _Onemap.MapViewer.extent.xmin,
            xmax: _Onemap.MapViewer.extent.xmax,
            ymin: _Onemap.MapViewer.extent.ymin,
            ymax: _Onemap.MapViewer.extent.ymax,
            spatialReference: {
              wkid: _Onemap.MapViewer.extent.spatialReference.wkid,
            },
          },
        }
      );
    }*/
  };
  //if (_Onemap.MapRealtimeInterval == 0) 这里添加开关
  const handleMousePositionThrottled = throttle((event: any) => {
    mousePosition = {
      x: event.mapPoint.x ? Number(parseFloat(event.mapPoint.x).toFixed(5)) : 0,
      y: event.mapPoint.y ? Number(parseFloat(event.mapPoint.y).toFixed(5)) : 0,
      z: 0,
      wkid: event.mapPoint.spatialReference ? event.mapPoint.spatialReference.wkid : 4490,
    };
    // if (props.MapRealtimeEvent.MapEvent) {
    //   props.MapRealtimeEvent.MapEvent(null, mousePosition);
    // }
    _MapRealtimeEvent(_inOptions.MapControlName, mousePosition);
  }, 200);

  loadEsri((data: any) => {
    //这个是ArcGIS特有的步骤
    esriObj.value = data;
    esriObj.value.esriRequest.setRequestPreCallback(function (options: any) {
      options.headers = options.headers || {};
      options.headers["sessionid"] = Utils.getCookie("sessionid");
      return options;
    });
    //create arcgis map object
    let arcGisMap = new esriObj.value.Map("mapviewerContainer", {
      autoResize: true,
      fitExtent: true,
      logo: false,
      slider: false,
      navigationMode: "css-transforms",
      spatialReference: new esriObj.value.SpatialReference({ wkid: 4490 }),
    });
    onMapEvents(undefined, arcGisMap);

    //这里实例化与MapView相关的对象
    _Onemap.setMapViewer(arcGisMap, esriObj.value);
    //调试用
    (window as any).arcgismap = arcGisMap;

    // 1.3 从Options中 baseLayer  参数
    function innerBaseMap() {
      if (_inOptions.BaseMapLayer == false) return;
      let tmpBaseMapLayer = cloneDeep(_inOptions.BaseMapLayer);
      tmpBaseMapLayer["subLayers"] = cloneDeep(
        Utils.getSubLayers(tmpBaseMapLayer.subLayers, mapType.arcgis)
      );
      if (tmpBaseMapLayer["subLayers"].length == 0) return;
      _Onemap.changeBasemap(tmpBaseMapLayer);
    }
    innerBaseMap();

    //二三维视窗范围同步
    //地图加载完之后，调用相关回调
    const _MapReadyEvent: mapEmitsEventType = (mapControlName: String) => {
      emits("MapReadyEvent", mapControlName);
    };
    _MapReadyEvent(_inOptions.MapControlName);
    // if (props.MapReadyEvent) props.MapReadyEvent(_Onemap.MapViewer);
    _Onemap.MapViewer.on("load", function () {
      //保存地图加载完毕的状态
      console.log("arcgis map is ready");
      _Onemap.isMapReady.value = true; 
      _Onemap?.fullExtent(_inOptions?.mapextent || undefined);
      
      if (_Onemap.MapReadyHandler) _Onemap.MapReadyHandler();
      // _Onemap.Handler = this.esriObj;
    }); 
    //保存地图加载完毕的状态
    // console.log("arcgis map is ready");
    // _Onemap.isMapReady.value = true;
  });
}
