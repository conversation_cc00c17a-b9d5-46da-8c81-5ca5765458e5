import { markRaw, nextTick } from "vue";
import { cloneDeep } from "lodash";

import {
  // mapType,
  OnemapClass,
  Utils,
  mapType, 
} from "../onemapkit";
import * as base from "../base/base";
import * as Cesium from "@onemapkit/cesium";

// import { cloneDeep } from "lodash"; 
//@ts-ignore
export function czmsetup(_inOptions: any, _Onemap: OnemapClass, emits: any, _PropStore: any): any {
  /*
    Cesium.Ion.defaultAccessToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjZjViZWZjMC0zMmUxLTQ5NWEtYjZkNS0wM2VjMmM1NWE1YTgiLCJpZCI6OTEwODksImlhdCI6MTY1MDc5MjI2MH0.IhkngSXwFixfux7Z4HBWmAw-pFhqfDzmyUO0v3lLy_4";
  */
  //#region 1. 必要参数解析

  // 1.2 从MapLayer中解析 Cesium 的底图参数 BaseMapLayer
  if (_inOptions.BaseMapLayer == null) {
    Utils.ThrowError("底图参数设置有误,创建底图失败。", _inOptions);
  }

  // 1.3 从Options中 baseLayer  参数
  function innerBaseMap() {
    if (_inOptions.BaseMapLayer == false) return;
    let tmpBaseMapLayer = cloneDeep(_inOptions.BaseMapLayer);

    tmpBaseMapLayer["subLayers"] = cloneDeep(
      Utils.getSubLayers(tmpBaseMapLayer?.subLayers, mapType.cesium)
    );

    let tmplayer: any = undefined;

    if (tmpBaseMapLayer["subLayers"].length > 1) {
      tmplayer = _Onemap.CreateGroupLayer(tmpBaseMapLayer);
    } else {
      tmplayer = Cesium.ImageryLayer.fromProviderAsync(
        _Onemap.CreateLayerProvider(tmpBaseMapLayer?.subLayers[0]),
        {}
      );
    }
    Utils.addLayreIDAndType(
      tmplayer,
      tmpBaseMapLayer.layerid,
      tmpBaseMapLayer.layerType,
      tmpBaseMapLayer.isDynamicMapLayer
    );
    return tmplayer;
  }
  _Onemap?.setMapViewer(
    markRaw(
      new Cesium.Viewer(
        "mapviewerContainer",
        _inOptions?.Options
          ? Object.assign(
              base.getOptions({
                baseLayer: innerBaseMap(),
              }),
              _inOptions.Options
            )
          : base.getOptions({
              baseLayer: innerBaseMap(),
            })
      )
    )
  );

  //#endregion
  //#region 3 设置地形服务 terrainProvider
 
  // 3.1 初始化函数
  const initfun = () => {
    nextTick(() => {
      // 地图加载完毕后的回调
      _Onemap.isMapReady.value = true;
      _Onemap.setMapReadyEvent(_inOptions);
      
      //地图鼠标事件 
      // _Onemap.Handler = new Cesium.ScreenSpaceEventHandler(_Onemap.MapViewer.scene.canvas);

      if (_Onemap.MapReadyHandler) _Onemap.MapReadyHandler();
      
      if (_PropStore?.Toolset?.defaultSceneColor) {
        base.Update3DTilesColor(
          _PropStore.Toolset.defaultSceneColor[0],
          _PropStore.Toolset.defaultSceneColor[1],
          _PropStore.Toolset.defaultSceneColor[2],
          _PropStore.Toolset.defaultSceneColor[3]
        );
      } else {
        console.info("yusy>PropStore.Toolset没定义", _PropStore);
      }
    });
  };
  //  3.2 设置地形
  if (_inOptions.TerrainUrl) {
    // 加载地形
    Cesium.CesiumTerrainProvider.fromUrl(_inOptions.TerrainUrl, {
      requestVertexNormals: true,
    })
      .then((terrain) => {
        _Onemap.MapViewer.scene.terrainProvider = terrain;
        initfun();
      })
      .catch(() => {
        _Onemap.MapViewer.scene.terrainProvider = new Cesium.EllipsoidTerrainProvider();
        initfun();
      });
  } else {
    _Onemap.MapViewer.scene.terrainProvider = new Cesium.EllipsoidTerrainProvider();
    initfun();
  }
  //#endregion
  //#region 4 地图实时回调函数 地图点选事件 后期应以知道对应的按钮事件中去
  {
    // _Onemap.MapRealtimeInterval = 300;
    // _Onemap.MapRealtimeDistance = 5;
    // //地图实时回调函数
    // _Onemap._MapRealtimeEvent = (mapPoint: any, options: any) => {
    //   emits("MapRealtimeEvent", options.MapControlName, mapPoint);
    // };
    // //地图点选事件
    // _Onemap._MapClickEvent = (mapPoint: any, options: any) => {
    //   emits("MapClickEvent", mapPoint, options);
    // };
  }
  //#endregion
}
