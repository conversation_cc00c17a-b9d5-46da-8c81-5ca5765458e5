import CryptoJS from "crypto-js";

const jwlogo100x25 =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAZCAYAAADHXotLAAAACXBIWXMAABYlAAAWJQFJUiTwAAAKTWlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjanVN3WJP3Fj7f92UPVkLY8LGXbIEAIiOsCMgQWaIQkgBhhBASQMWFiApWFBURnEhVxILVCkidiOKgKLhnQYqIWotVXDjuH9yntX167+3t+9f7vOec5/zOec8PgBESJpHmomoAOVKFPDrYH49PSMTJvYACFUjgBCAQ5svCZwXFAADwA3l4fnSwP/wBr28AAgBw1S4kEsfh/4O6UCZXACCRAOAiEucLAZBSAMguVMgUAMgYALBTs2QKAJQAAGx5fEIiAKoNAOz0ST4FANipk9wXANiiHKkIAI0BAJkoRyQCQLsAYFWBUiwCwMIAoKxAIi4EwK4BgFm2MkcCgL0FAHaOWJAPQGAAgJlCLMwAIDgCAEMeE80DIEwDoDDSv+CpX3CFuEgBAMDLlc2XS9IzFLiV0Bp38vDg4iHiwmyxQmEXKRBmCeQinJebIxNI5wNMzgwAABr50cH+OD+Q5+bk4eZm52zv9MWi/mvwbyI+IfHf/ryMAgQAEE7P79pf5eXWA3DHAbB1v2upWwDaVgBo3/ldM9sJoFoK0Hr5i3k4/EAenqFQyDwdHAoLC+0lYqG9MOOLPv8z4W/gi372/EAe/tt68ABxmkCZrcCjg/1xYW52rlKO58sEQjFu9+cj/seFf/2OKdHiNLFcLBWK8ViJuFAiTcd5uVKRRCHJleIS6X8y8R+W/QmTdw0ArIZPwE62B7XLbMB+7gECiw5Y0nYAQH7zLYwaC5EAEGc0Mnn3AACTv/mPQCsBAM2XpOMAALzoGFyolBdMxggAAESggSqwQQcMwRSswA6cwR28wBcCYQZEQAwkwDwQQgbkgBwKoRiWQRlUwDrYBLWwAxqgEZrhELTBMTgN5+ASXIHrcBcGYBiewhi8hgkEQcgIE2EhOogRYo7YIs4IF5mOBCJhSDSSgKQg6YgUUSLFyHKkAqlCapFdSCPyLXIUOY1cQPqQ28ggMor8irxHMZSBslED1AJ1QLmoHxqKxqBz0XQ0D12AlqJr0Rq0Hj2AtqKn0UvodXQAfYqOY4DRMQ5mjNlhXIyHRWCJWBomxxZj5Vg1Vo81Yx1YN3YVG8CeYe8IJAKLgBPsCF6EEMJsgpCQR1hMWEOoJewjtBK6CFcJg4Qxwicik6hPtCV6EvnEeGI6sZBYRqwm7iEeIZ4lXicOE1+TSCQOyZLkTgohJZAySQtJa0jbSC2kU6Q+0hBpnEwm65Btyd7kCLKArCCXkbeQD5BPkvvJw+S3FDrFiOJMCaIkUqSUEko1ZT/lBKWfMkKZoKpRzame1AiqiDqfWkltoHZQL1OHqRM0dZolzZsWQ8ukLaPV0JppZ2n3aC/pdLoJ3YMeRZfQl9Jr6Afp5+mD9HcMDYYNg8dIYigZaxl7GacYtxkvmUymBdOXmchUMNcyG5lnmA+Yb1VYKvYqfBWRyhKVOpVWlX6V56pUVXNVP9V5qgtUq1UPq15WfaZGVbNQ46kJ1Bar1akdVbupNq7OUndSj1DPUV+jvl/9gvpjDbKGhUaghkijVGO3xhmNIRbGMmXxWELWclYD6yxrmE1iW7L57Ex2Bfsbdi97TFNDc6pmrGaRZp3mcc0BDsax4PA52ZxKziHODc57LQMtPy2x1mqtZq1+rTfaetq+2mLtcu0W7eva73VwnUCdLJ31Om0693UJuja6UbqFutt1z+o+02PreekJ9cr1Dund0Uf1bfSj9Rfq79bv0R83MDQINpAZbDE4Y/DMkGPoa5hpuNHwhOGoEctoupHEaKPRSaMnuCbuh2fjNXgXPmasbxxirDTeZdxrPGFiaTLbpMSkxeS+Kc2Ua5pmutG003TMzMgs3KzYrMnsjjnVnGueYb7ZvNv8jYWlRZzFSos2i8eW2pZ8ywWWTZb3rJhWPlZ5VvVW16xJ1lzrLOtt1ldsUBtXmwybOpvLtqitm63Edptt3xTiFI8p0in1U27aMez87ArsmuwG7Tn2YfYl9m32zx3MHBId1jt0O3xydHXMdmxwvOuk4TTDqcSpw+lXZxtnoXOd8zUXpkuQyxKXdpcXU22niqdun3rLleUa7rrStdP1o5u7m9yt2W3U3cw9xX2r+00umxvJXcM970H08PdY4nHM452nm6fC85DnL152Xlle+70eT7OcJp7WMG3I28Rb4L3Le2A6Pj1l+s7pAz7GPgKfep+Hvqa+It89viN+1n6Zfgf8nvs7+sv9j/i/4XnyFvFOBWABwQHlAb2BGoGzA2sDHwSZBKUHNQWNBbsGLww+FUIMCQ1ZH3KTb8AX8hv5YzPcZyya0RXKCJ0VWhv6MMwmTB7WEY6GzwjfEH5vpvlM6cy2CIjgR2yIuB9pGZkX+X0UKSoyqi7qUbRTdHF09yzWrORZ+2e9jvGPqYy5O9tqtnJ2Z6xqbFJsY+ybuIC4qriBeIf4RfGXEnQTJAntieTE2MQ9ieNzAudsmjOc5JpUlnRjruXcorkX5unOy553PFk1WZB8OIWYEpeyP+WDIEJQLxhP5aduTR0T8oSbhU9FvqKNolGxt7hKPJLmnVaV9jjdO31D+miGT0Z1xjMJT1IreZEZkrkj801WRNberM/ZcdktOZSclJyjUg1plrQr1zC3KLdPZisrkw3keeZtyhuTh8r35CP5c/PbFWyFTNGjtFKuUA4WTC+oK3hbGFt4uEi9SFrUM99m/ur5IwuCFny9kLBQuLCz2Lh4WfHgIr9FuxYji1MXdy4xXVK6ZHhp8NJ9y2jLspb9UOJYUlXyannc8o5Sg9KlpUMrglc0lamUycturvRauWMVYZVkVe9ql9VbVn8qF5VfrHCsqK74sEa45uJXTl/VfPV5bdra3kq3yu3rSOuk626s91m/r0q9akHV0IbwDa0b8Y3lG19tSt50oXpq9Y7NtM3KzQM1YTXtW8y2rNvyoTaj9nqdf13LVv2tq7e+2Sba1r/dd3vzDoMdFTve75TsvLUreFdrvUV99W7S7oLdjxpiG7q/5n7duEd3T8Wej3ulewf2Re/ranRvbNyvv7+yCW1SNo0eSDpw5ZuAb9qb7Zp3tXBaKg7CQeXBJ9+mfHvjUOihzsPcw83fmX+39QjrSHkr0jq/dawto22gPaG97+iMo50dXh1Hvrf/fu8x42N1xzWPV56gnSg98fnkgpPjp2Snnp1OPz3Umdx590z8mWtdUV29Z0PPnj8XdO5Mt1/3yfPe549d8Lxw9CL3Ytslt0utPa49R35w/eFIr1tv62X3y+1XPK509E3rO9Hv03/6asDVc9f41y5dn3m978bsG7duJt0cuCW69fh29u0XdwruTNxdeo94r/y+2v3qB/oP6n+0/rFlwG3g+GDAYM/DWQ/vDgmHnv6U/9OH4dJHzEfVI0YjjY+dHx8bDRq98mTOk+GnsqcTz8p+Vv9563Or59/94vtLz1j82PAL+YvPv655qfNy76uprzrHI8cfvM55PfGm/K3O233vuO+638e9H5ko/ED+UPPR+mPHp9BP9z7nfP78L/eE8/sl0p8zAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAw/SURBVHja7Jp/UFtXdsc/GGyE2GC7D3lYxQL/igI0timia2NM4gZSsWOTHUwjMu62kHYiJ8oy/WF3VAe2pQTHnQ60npIoE3locOKhRmnKNHgHGEuZSUIg3hrGa28JoS6sADsuoLpW4AmvRNQ/uI88K0iYzLbTP3xmNNK779777jvfc8/3nHMVFw6H+d+UuLg4Kw/kviUBYCAu7jTw+0BQtK2J6BcSn+XGJ0S0fSXmWQe8DQw9UPMqDDgcDjMQF+cHHgIWgDtCqXGizwKQAmiWGT8P+IF4cR0WYG4U47/Mg+PBNt58oOqVZe0RjirWLQuF/xPQIpS8RnXvGPB7ETvnK6AdaAK0qvb1QDXwfTF26WEAxcXFWrfbLdtsNiknJ0dyuVyTAG63W1buWyyWzVardWS1L9Td3b3P7/fLFovlymrHKuv6tspU3ifauru7u/fpdDqppaWlz+Fw+CLvK0arALJO7Iy/Bz4VCo5XKd4XZR0+YFQAFQcEhLtaAAqAterOAwMDpbt27Srq6Oho3b59uyE3N/cQ0FRYWJiTmZlZ9NJLL7389NNPP2o2mytNJtMFk8nUuRqF5uXl5UuSZOzu7j5bUlLSd79jBwYGSnNzcw+9++67b1gslitOp9NoMpkejda/q6vrSm1t7YS6raampkKv1+8uLCz0ZGVluSLHGI1G49atW/NbWlr6VuQQlXu6CxiAU0CyUGgQ+N4yvLIGeBbYJvotAJ8ApwW4C5H8YjKZOkdHR6Unn3yy6IMPPvDk5uaSnp6eumPHjn2Dg4MXHA6Hr6amJgfAbrd7VlJgtPtms7kyHA5XLnfvzJkzTWorbmhoMOTm5h7y+/2TTqdzBCA9PT01LS3NcOvWrSWlp6WlGfR6/W6/3z9ZW1t7j6E4nU6jXq/fPTY21l9dXX3B6XQarVbryMzMzDGlT3JysgRQX19fVV9fvzQ2NTW1aTlAFFDigE3A74q2fwOuAR7gl8u83zoBxiPAr4ud9YbYXXGRncPh8BKXPPPMM0ZFeQC5ubmHRkdHJb1evzsUCgXOnz//onrs5cuX+yOtPlK5scTpdBqff/75Y5Fuprq6usrv90+Wl5c3HThwQOrq6np1YmLiysMPP+xQuxsFtPLy8qbInfncc8/Z/H7/pNVqdTU3Nx/KzMwsKi4u7vd6vUtry8jIMGo0GkndFmuHqOWXgkMeAv4SeG+ZqGo+Yky54B9ZuLhoIfDR3t7eoj179pSGQiFZo9FIfr9/cnZ21ldZWdna3Nx8CODSpUudQ0NDE9nZ2YaCggLL4ODghffff/9zZZ6Wlpa+gYGBzwHUVhhLnn322TeAJoWzGhoaDHa7/RhAR0dHq9VqNZrN5lKAtrY2jwJYfX19lSRJRoBgMCgfP348x+1296mAtiQkJCQBnD9//kVJkoxjY2P927Zta21oaDBs2rQpCWDjxo2SJEko6waYmpoK3A8gSrQUB+QLN5YgXNengjd2AkbR9pXoRywwXC5XTmlpqSUhIUF79epVj7Irrl271rdnz57Szs7OWo1GIwHMzs4GrFbriMvl0goX9bmaCMVvX3FxsRb4Bs+kp6en5ufnF6WkpGyen5/3DQ0N9bvdbtntdi9Z582bN2VZln3BYFA2m82lKSkpm0OhUOCtt95y5OXlGUZHR4u2bt2a7/f7J8+cOdPkcrkmnU6nxWw2VwYCgUMffvjhhZKSkr7GxsbOI0eOTNy8edNXVlZWpewUgPLy8nydTmdQu6zDhw+XKmuYnp6eiOSiaIAsiO8fAX8oAAoCfwBcBP4E+B3RHlaFxAsxXMZISkrKhcbGxisHDhyQampqan0+30hdXV0/0H/27NmqTZs2aUOhkLxz584coG/nzp1GAebkcnNGKhngxo0bNr1ev3t+ft7X09MTldwdDofP4XC8YrPZpFOnTtn8fv/kiRMnHAAVFRUVwWBQVkheGbNt27ZWm83WWV9fX5WRkbFZmUev149UV1dXybLsKy8vb1KiNTW5K7wXyRnR8pDbQpm/LRT/ocgl/hOYBbYKEj8MXABOCkKfF4D8GpAK/LNIMB8DfgLE54E92Mabb806myJ9+D0Jzfy8Lykp6eXe3t6igoICy8mTJxvsdvsxWZZ969evf0XdN5abUtyLz+eL6qsVpShRH8D169f7ZFkOpKWlGcbGxj4vKCiwrFCBOKq4tddff/1V5R2UXf7JJ5+4VppDWWdqampTsI031XlIpCgh78vAvwgQ9qqy8L8C/kblosoBZ4wdh9VqHbFarUcVhQ8PD3uysrJcyvU777zTClBXV9ff1dVVarfbjyUkJCT19/d/I9qKRYzJycnS/ZAnQHZ2dn4oFJLn5uZ8iYmJWlmWA7du3Zqoq6vrd7vdnkgDWM66HQ6H78iRI67Z2dnA+Pj4jPp9RTB0zw5RgIwMclZyWYrMAf8lQFDLXZG9K9n6d1TcE1OSkpK0Pp9vZMuWLTnhcLgIIBQKBTZs2KBVkrOpqakRJdpqbGz8RpIXKz+ZmZkxajQa6X5ymKSkpJfVSV16enqqTqeTLBbL5khXGE0aGhoMQ0NDE2JdjyoR1cGDBwPqSE0tvb29RZIkSQDDw8NXVgNIvOgTrwqN44E/FdyyIHZJ6kocoojdbvccP37cl5eXl6/RaKTh4WFPYmKitqysrGr79u2e7OzspfYdO3bs6+rqevXSpUudwmrlaEnhUplg/XrD/SqypqamdqlYFwoF7ty5M3H37t2AyWTC6XR+XdJYu1arhM7qCKm2tnYiLy/PYDabK+fn531zc3M+r9c74vV6R7q6umJWC3Q6nWFwcPDC/v37PasBRIm21qjqVPFAroiylusfNZN+7733jqWkpGz2+/2T165d62tra2t1OBw+m80mNTU1GXft2lU0MTFxpbGxsVO0e+rr66sKCgoszc3N0nIZsCIXL178O+V3T0/P2ZUAEco8Oz4+PuNyuSYVsBsaGgwvvPCCJSMjY+n9gsGgHC1CKikp6SsuLr6yUtklLS3NoMwvQPCshtR7hUt6DmgFbMB24B+AYUHu+4Xr+gr4DTG2Q9S8vkHqa49w9ERV1eaHAoGkn7W3326Hmcgkbd3ly9rTP/3pPWHgXtA+XVHx8Pvt7Tc+VdXG9or6mdL2tydOPJKm1W74xc9//t8/a2+/zWKhbSZa/72gVc+3klR87QUWeUyM/RRk9dzqeZd7pvq6AlKVNa5E6pEWH+kL3xUfRX4gAImPNdnB1tYXAzD9m4vVR911+KgKegAecziyvwc//AJqlEW2gnkLmG63t4/vg3QPdLwCnwHY4bAOsvZDDYDm1Km0JMh4Ekz7YPo2jOvg49fAuxe0p+Hkl+B9arG0QyPUXIVuG3y8Ehg/gowiKNwI6RrQfQEDX4D3ETDdgM/Wg04HWVehexeUKGtqhJpp+KwMzinXAZh+Ck5XQOqfLUarNWrDWbMCEAqHJIpcI/KTKO5L92tp/w4DT8HpHjiXCSV7760U37MzMqHEAx1lcO5foedxMEettsLHZXAuBPJtGC+Dc6+Bl8U43PQleB+CjB9D1morua+BtwzO3YbxEMhlcM4GH6vBYDFP0PnFM6MGEqD7x0UPc9+lk3ihaICXgCeEW1qI0neNcFHq6xVlBKZ/ANqMKK4jA7QJoB2BaQcUfhcybojdsVrJhsLbMM4i+Zn4lvNEyjTM7IDHWaw1eb8Lmb+AgVhjrsNHW8D0BEyvBEhYKD0I3BJWnyWKhokxnnFXjJVZ3HpKrhIzBH4CskIge6P4cS/IIZCNoHsbBl4ArXj5ntUozQGFCZD8BXi1MJ0JJRXQ/asA5DJ4fwtSp2BgCryPgWl6hfXJIHugwww/jAVIWFW9vQocFL+Vk8M/BiojKrhh4Kwotyu7Y0rkLhvE9T21rQBMPwKmi4tWyjk4reyOOZDnYUZNlj8BZxGUmSE5tDjvktyB6e9EEK3yjDsq69sCWdfhI4UrOkD3fTBFrgXgx+CMRfSRz2yHmWqYmQLvf8D4YwKkaGsJwPQcyK/AZ9vhox3weKRBKlHWLVH+eFucGAZUrmcO+HNREok8MXwb+GtxdqKEyBLwR0AJMJMHf6FEEN/WEpVoZLWR0f+FqNekjppWK4qOIs/Ug+JwKaxS/oK4l7TMPAHgywdn6r+6M3UFkNN8/a+Tdcv0/db/Osl78K+TVUncg/9l/f+S/xkA7Gpc9EQqKfoAAAAASUVORK5CYII=";
const nnlogo150x25 =
  "data:image/png;base64,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";

class liced {
  public _isEnable = false;
  public _authority = false;
  public _ispermit = false;
}
export const licstate = new liced();
function getpw(input: string): string {
  const startIndex = input.length - 12;
  const validStartIndex = Math.max(startIndex, 0);
  return input.slice(validStartIndex, validStartIndex + 10);
}
const imports = {
  env: {
    //@ts-ignore
    abort: (param1, param2, param3, param4) => {
      console.error("Abort called with params:", param1, param2, param3, param4);
      throw new Error("Aborted");
    },
    "Date.now": () => {
      return Date.now(); // 返回当前时间戳，确保是 BigInt 类型
    },
  },
};
/** 默认用户 */
//@ts-ignore
let __userid = 10517;
export const readinfo =
  "A set of integrated 2D and 3D map component libraries suitable for Cesium and ArcGISJS development.";
/** 配置:prelic=time/100000 + name*100, prelic*100000+name
 * 许可时间:2030-12-23T00:00:00 = 1924185600000 20293556 2029355610517
 * 许可时间:2023-12-23T00:00:00 = 1703260800000 18084308 1808430810517
 * @param id
 */
export function _userid(id: number) {
  __userid = id;
}
let __isEnable: boolean = false;
/** 获取权限状态
 * @returns {boolean}
 */
export function isEnable(): boolean {
  return __isEnable;
}

let pathlic = "";
let pathwasm = ""; 
if (import.meta.env.MODE === "development") {
  pathlic = "/assets/omplicence.lic";
  pathwasm = "/worker/onemapkit.wasm";
} else if (import.meta.env.MODE === "production") {
  pathlic = "/ThirdParty/onemapkit/Build/assets/omplicence.lic";
  pathwasm = "/ThirdParty/onemapkit/Build/worker/onemapkit.wasm";
}

/**
 * 加载并实例化 WASM 模块
 * @returns {Promise<Object>} - WASM 模块的导出对象
 */
async function _readlic(): Promise<string | boolean> {
  try {
    const responselic = await fetch(pathlic);
    if (responselic.ok) {
      let _text = await responselic.text(); // 获取文本内容
      const _pw = getpw(_text);
      _text = _text.replace(_pw, "");
      return (
        CryptoJS.AES.decrypt(_text, _pw).toString(CryptoJS.enc.Utf8).split("sp_yusy_81517")[1] ||
        false
      );
    } else {
      console.info(`${readinfo}0`);
      return false;
    }
  } catch (error) {
    console.info(`${readinfo}0`);
    return false;
  }
}

async function _readwasm(byteslic?: string): Promise<boolean | any> {
  const fun = async (_byteslic: string) => {
    const response = await fetch(pathwasm);
    if (response.ok) {
      const bytes = await response.arrayBuffer();
      const { instance } = await WebAssembly.instantiate(bytes, imports);
      (instance.exports as any).username(_byteslic.slice(_byteslic.length - 5, _byteslic.length));
      (instance.exports as any).initonemaplic(Number(_byteslic));

      __isEnable = (instance.exports as any).isEnable();
      licstate._isEnable = __isEnable;
      licstate._authority = __isEnable;
      licstate._ispermit = __isEnable;

      console.info(`${readinfo}${__isEnable}`);
      return __isEnable;
    } else {
      console.info(`${readinfo}0`);
      return false;
    }
  };
  try {
    if (byteslic) {
      return fun(byteslic);
    } else {
      const lic: any = await _readlic();
      return lic == false ? false : fun(lic);
    }
  } catch (error) {
    console.info(`${readinfo}0`);
    return new Promise((resolve) => {
      resolve(false);
    });
  }
}

export const loadlic = _readwasm();
// loadlic.then((wasm) => {
//   console.info(`${readinfo}${wasm}`);
// });

export const _loadlogo = (isnn: boolean = true) => {
  if (__isEnable == false) {
    const __container: HTMLElement = document.getElementById("mapviewerContainer") as HTMLElement;
    //@ts-ignore
    const resizeObserver = new ResizeObserver((entries) => {
      const tmpcanvas = (__container.parentElement as HTMLElement).querySelectorAll(
        "canvas"
      ) as NodeListOf<HTMLCanvasElement>;
      let isHave = false;
      let tmpCanvas: HTMLCanvasElement = null as any;

      for (const _canvas of tmpcanvas) {
        if (_canvas.width == 150 || _canvas.width == 100) {
          tmpCanvas = _canvas;
          isHave = true;
          break;
        }
      }

      if (isHave) {
        tmpCanvas.style.top = `-30px`; // 设置距离底部的距离
        tmpCanvas.style.left = `${__container.clientWidth - (isnn ? 150 : 100) - 5}px`; // "5px"; // 可选：设置距离左侧的距离
      } else {
        funCreateCanvas();
      }
    });

    resizeObserver.observe(__container);

    const funCreateCanvas = () => {
      const canvas = document.createElement("canvas");
      canvas.width = isnn ? 150 : 100; // 设置宽度
      canvas.height = 25; // 设置高度
      canvas.style.top = `-30px`; // 设置距离底部的距离
      canvas.style.left = `${__container.clientWidth - (isnn ? 150 : 100) - 5}px`; // "5px"; // 可选：设置距离左侧的距离
      canvas.style.position = "relative"; //absolute
      canvas.style.zIndex = "10";
      (__container.parentElement as any).insertBefore(canvas, __container.nextSibling);
      const context: any = canvas.getContext("2d"); // 获取 2D 上下文

      // 1.在 canvas 上绘制内容
      // context.fillStyle = "blue"; // 设置填充颜色
      // context.fillRect(0, 0, isnn ? 150 : 100, 30); // 绘制矩形

      // 2.在 img 上绘制内容
      // const img = document.createElement("img");
      // img.src = isnn ? nnlogo150x25 : jwlogo100x25;
      // (document.getElementById("mapviewerContainer") as any).appendChild(img);

      // 3. canvas 上绘制内容
      const logoImage = new Image();
      logoImage.src = isnn ? nnlogo150x25 : jwlogo100x25; // 替换为你的商标图片路径
      logoImage.onload = function () {
        context.drawImage(
          logoImage,
          0, // 源图像起始位置 x
          0, // 源图像起始位置 y
          logoImage.width, // 源图像宽度
          logoImage.height, // 源图像高度
          0, // 目标画布起始位置 x
          0, // 目标画布起始位置 y
          isnn ? 150 : 100, // 目标画布宽度
          25 // 目标画布高度
        );
      };
    };
    funCreateCanvas();
  }
};

export const _loadlogo1 = (isnn: boolean = false) => {
  if (true) {
    /*
    const canvasContainer = document.querySelector(
      "#mapviewerContainer canvas"
    ) as HTMLCanvasElement; //webgl

    const resizeObserver = new ResizeObserver((entries) => {
      const canvasContainer1 = document.querySelectorAll(
        "#mapviewerContainer canvas"
      );

      console.log(`Canvas 尺寸发生变化: ${canvasContainer1.length}`);

      for (let entry of entries) {
        const { width } = entry.contentRect;
        canvas.style.top = "-30px"; // 设置距离底部的距离
        canvas.style.left = `${width - (isnn ? 150 : 100) - 5}px`; // "5px"; // 可选：设置距离左侧的距离
      }
    });
    resizeObserver.observe(canvasContainer);
   */
    // canvas.width =canvasContainer.width;// isnn ? 150 : 110; // 设置宽度
    // canvas.height =canvasContainer.height;// 25; // 设置高度
    // canvas.style.top =`-${canvasContainer.height}px`;// "-30px"; // 设置距离底部的距离
    // canvas.style.left = "0px"; // 可选：设置距离左侧的距离

    // 创建一个 ResizeObserver 实例
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        //@ts-ignore
        funCreateCanvas(entry);
        // 获取新的宽度和高度
        // const { width, height } = entry.contentRect;
        // console.log(`新宽度: ${width}, 新高度: ${height}`);
      }
    });

    const container: HTMLElement = document.getElementById("mapviewerContainer") as HTMLElement;
    // 开始观察目标元素
    resizeObserver.observe(container);

    //#region funCreateCanvas
    const funCreateCanvas = (__container: HTMLElement) => {
      const canvas = document.createElement("canvas");
      canvas.width = isnn ? 150 : 100; // 设置宽度
      canvas.height = 25; // 设置高度
      canvas.style.top = `-30px`; // 设置距离底部的距离
      canvas.style.left = `${__container.clientWidth - (isnn ? 150 : 100) - 5}px`; // "5px"; // 可选：设置距离左侧的距离
      canvas.style.position = "relative"; //absolute
      canvas.style.zIndex = "10";
      // (document.getElementById("mapviewerContainer") as any).parent.appendChild(canvas);
      (__container.parentElement as any).insertBefore(canvas, __container.nextSibling);
      const context: any = canvas.getContext("2d"); // 获取 2D 上下文

      // 1.在 canvas 上绘制内容
      // context.fillStyle = "blue"; // 设置填充颜色
      // context.fillRect(0, 0, isnn ? 150 : 100, 30); // 绘制矩形

      // 2.在 img 上绘制内容
      // const img = document.createElement("img");
      // img.src = isnn ? nnlogo150x25 : jwlogo100x25;
      // (document.getElementById("mapviewerContainer") as any).appendChild(img);

      // 3. canvas 上绘制内容
      const logoImage = new Image();
      logoImage.src = isnn ? nnlogo150x25 : jwlogo100x25; // 替换为你的商标图片路径
      logoImage.onload = function () {
        context.drawImage(
          logoImage,
          0, // 源图像起始位置 x
          0, // 源图像起始位置 y
          logoImage.width, // 源图像宽度
          logoImage.height, // 源图像高度
          0, // 目标画布起始位置 x
          0, // 目标画布起始位置 y
          isnn ? 150 : 100, // 目标画布宽度
          25 // 目标画布高度
        );
      };
    };

    //#endregion
  }
};
/** 备份原实现
async function _loadlicBack() {
  try {
    const responselic = await fetch("./assets/omplicence.lic");
    if (responselic.ok) {
      let _text = await responselic.text(); // 获取文本内容
      const _pw = getpw(_text);
      _text = _text.replace(_pw, "");
      const byteslic: string = CryptoJS.AES.decrypt(_text, _pw)
        .toString(CryptoJS.enc.Utf8)
        .split("sp yusy 81517")[1];

      const response = await fetch("./worker/onemapkit.wasm");
      if (response.ok) {
        const bytes = await response.arrayBuffer();
        const { instance } = await WebAssembly.instantiate(bytes, imports);
        (instance.exports as any).username(
          byteslic.slice(byteslic.length - 5, byteslic.length)
        );
        (instance.exports as any).initonemaplic(Number(byteslic));

        __isEnable = (instance.exports as any).isEnable();

        licstate._isEnable = __isEnable;
        licstate._authority = __isEnable;
        licstate._ispermit = __isEnable;

        console.info(`${readinfo}${__isEnable}`);

        return __isEnable;
      }
    }
  } catch (error) {
    console.info(`${readinfo}0`);
  }
}
*/
