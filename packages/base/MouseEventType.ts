export enum MouseEventType {
    /**
     * Represents a mouse left button down event.
     */
    LEFT_DOWN = 0,
    /**
     * Represents a mouse left button up event.
     */
    LEFT_UP = 1,
    /**
     * Represents a mouse left click event.
     */
    LEFT_CLICK = 2,
    /**
     * Represents a mouse left double click event.
     */
    LEFT_DOUBLE_CLICK = 3,
    /**
     * Represents a mouse left button down event.
     */
    RIGHT_DOWN = 5,
    /**
     * Represents a mouse right button up event.
     */
    RIGHT_UP = 6,
    /**
     * Represents a mouse right click event.
     */
    RIGHT_CLICK = 7,
    /**
     * Represents a mouse middle button down event.
     */
    MIDDLE_DOWN = 10,
    /**
     * Represents a mouse middle button up event.
     */
    MIDDLE_UP = 11,
    /**
     * Represents a mouse middle click event.
     */
    MIDDLE_CLICK = 12,
    /**
     * Represents a mouse move event.
     */
    MOUSE_MOVE = 15,
    /**
     * Represents a mouse wheel event.
     */
    WHEEL = 16,
    /**
     * Represents the start of a two-finger event on a touch surface.
     */
    PINCH_START = 17,
    /**
     * Represents the end of a two-finger event on a touch surface.
     */
    PINCH_END = 18,
    /**
     * Represents a change of a two-finger event on a touch surface.
     */
    PINCH_MOVE = 19,
    
    /**
     * 鼠标移动停下来后执行.
     */
    MOUSE_STOP = 20,
    /**
     * 鼠标移动停下来几秒后执行
     */
    MOUSE_STOP2 = 21,
    /**
     * 地图加载完毕事件
     */
    MapReady = 22
}