import * as Cesium from "@onemapkit/cesium";

export const colorCorrection = new Cesium.CustomShader({
  // lightingModel: Cesium.LightingModel.UNLIT, // 禁用光照
  uniforms: {
    saturation: {
      // 饱和度
      type: Cesium.UniformType.FLOAT,
      value: 1.0,
    },
    brightness: {
      // 亮度
      type: Cesium.UniformType.FLOAT,
      value: 1.0,
    },
    contrast: {
      // 对比度
      type: Cesium.UniformType.FLOAT,
      value: 1.0,
    },
    darkHeighLight: {
      // 暗部提亮
      type: Cesium.UniformType.BOOL,
      value: false,
    },
  },
  fragmentShaderText: `
    vec3 colorCorrection(vec3 originalColor){
      float gray = 0.2125 * originalColor.r + 0.7154 * originalColor.g + 0.0721 * originalColor.b;
      vec3 grayColor = vec3(gray, gray, gray);
      vec3 finalColor = mix(grayColor, originalColor, saturation);
    
      finalColor = finalColor * brightness;
    
      float avgValue = 0.5 * brightness;
      vec3 avgColor = vec3(avgValue, avgValue, avgValue);
      finalColor = mix(avgColor, finalColor, contrast);
    
      return finalColor;
    }
  
    void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
      vec3 color = material.diffuse;
      float gray = 0.2125 * color.r + 0.7154 * color.g + 0.0721 * color.b;
      vec3 brighten = vec3(1.0) * (1.0-gray) * 0.01;
      if(darkHeighLight){
        color += brighten;
      }
      material.diffuse = colorCorrection(color);
    }
    `,
});
/**
 * 调整3DTiles整体颜色
 * @param {Boolean} darkHeighLight 是否使用暗部提亮
 * @param {Number} saturation 饱和度
 * @param {Number} brightness 亮度
 * @param {NUmber} contrast 对比度
 */
export function Update3DTilesColor(
  darkHeighLight: boolean,
  saturation: number,
  brightness: number,
  contrast: number
) {
  colorCorrection.uniforms.darkHeighLight.value = darkHeighLight;
  colorCorrection.uniforms.saturation.value = saturation;
  colorCorrection.uniforms.brightness.value = brightness;
  colorCorrection.uniforms.contrast.value = contrast;
}
