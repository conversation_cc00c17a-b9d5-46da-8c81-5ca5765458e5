import * as Cesium from "@onemapkit/cesium";

(Cesium.Ellipsoid as any).CGCS2000 = Object.freeze(
  new Cesium.Ellipsoid(6378137.0, 6378137.0, 6356752.3141403558)
);
export const CGCS2000 = (Cesium.Ellipsoid as any).CGCS2000;
/**
 *
 * @param viewer
 * @param options
 * @param {Object} [option.depthTestAgainstTerrain =false]
 * @param {Object} [option.maximumRequestsPerServer=30]
 * @param {Object} [option.maximumRequests =100]
 * @param {Object} [option.useFlatting =false]
 * @param {Object} [option.isMapSurface = false]
 *
 */
export const initUcsmOption = (viewer: Cesium.Viewer, options?: any) => {
  //@ts-ignore
  viewer.cesiumWidget.creditContainer["style"]["display"] = "none";
  // 地形深度检测
  viewer.scene.globe.depthTestAgainstTerrain = Cesium.defaultValue(
    options?.depthTestAgainstTerrain,
    false
  );

  Cesium.RequestScheduler.maximumRequestsPerServer = Cesium.defaultValue(
    options?.maximumRequestsPerServer,
    30
  );
  Cesium.RequestScheduler.maximumRequests = Cesium.defaultValue(
    options?.maximumRequests,
    100
  );
  (Cesium.RequestScheduler as any).priorityHeapLength = Cesium.defaultValue(
    options?.priorityHeapLength,
    80
  );
  // 默认关闭地形压平
  viewer.scene.globe.useFlatting = Cesium.defaultValue(
    options?.useFlatting,
    false
  );

  if (Cesium.defaultValue(options?.isMapSurface, false)) {
    //MapSurface.Layer = (new SurfaceImageLayer(viewer)) as any;
  }
  /*
    for (let key in options) {
        switch (key) {
            case "depthTestAgainstTerrain":
                viewer.scene.globe.depthTestAgainstTerrain = options["depthTestAgainstTerrain"];
        }
    }
    */
};

export const getOptions = (options?: Cesium.Viewer.ConstructorOptions) => {
  return Object.assign(
    {
      animation: false, //是否显示动画控制面板，默认为true
      baseLayerPicker: false, // 是否显示底图选择器，默认为true
      fullscreenButton: false, //是否显示全屏按钮，默认为true。
      vrButton: false, //是否显示VR按钮，默认为false。
      geocoder: false, //控制是否显示地理编码器小部件，默认为true
      homeButton: false, //是否显示回到初始位置按钮，默认为true
      infoBox: false, //是否显示信息框，默认为true
      sceneModePicker: false, //是否显示场景模式选择器，默认为true
      selectionIndicator: false, //是否显示选择指示器，默认为true
      timeline: false, //是否显示时间轴控制面板，默认为true
      navigationHelpButton: false, //是否显示导航帮助按钮，默认为true
      navigationInstructionsInitiallyVisible: false, //导航帮助是否一开始就可见，默认为true
      scene3DOnly: true, //是否仅允许3D场景模式，默认为false
      shouldAnimate: false, //是否应该在每一帧之间循环播放场景动画。如果设置为true，则会循环播放动画，否则将保持静态不动。通过设置此属性，可以控制场景动画是否自动播放。
      // useBrowserRecommendedResolution: false, // 是否选择浏览器推荐分辨率
      automaticallyTrackDataSourceClocks: false, //是否自动跟踪数据源时钟，默认为true。当设置为true时，数据源始终与场景时钟同步，如果数据源没有时钟，则不会跟踪。
      baseLayer: false, //指定在场景中使用的初始图层
      // baseLayer: Cesium.SingleTileImageryProvider.fromUrl("/assets/earth.jpg"),
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),
      // terrain:指定一个地形提供者（TerrainProvider），用于加载和显示场景中的地形数据。
      SkyBox: false, //天空盒样式，可以是Cesium.SkyBox或者Cesium.Color类型，undefined:默认值. false, 没有天空盒子.
      SkyAtmosphere: false,
      // useDefaultRenderLoop: true, //表示是否使用渲染循环,默认的true
      // targetFrameRate: 20, //当 useDefaultRenderLoop 为 true时
      showRenderLoopErrors: true, //是否显示渲染循环错误信息，默认为true
      // contextOptions: {
      //   webgl: {
      //     //默认是false，之所以跟webGL默认的false不一样是因为这样可以提升性能
      //     alpha: false,
      //     depth: true,
      //     stencil: false,
      //     antialias: true,
      //     premultipliedAlpha: true,
      //     preserveDrawingBuffer: false,
      //     powerPreference: "high-performance",
      //     failIfMajorPerformanceCaveat: false,
      //   },
      //   requestWebgl1: false,
      //   // getWebGLStub
      //   //allowTextureFilterAnisotropic:默认设置为true，当支持WebGL扩展时启用各向异性纹理过滤。将此设置为false会提高性能，但会损害视觉质量，尤其是对于地平线视图。其他用默认就好。
      //   allowTextureFilterAnisotropic: false,
      // },
      // mapProjection:new GeographicProjection(),
      // globe: false, //new Globe(mapProjection.ellipsoid),// 地球对象，可以用于控制地球的旋转、缩放和其他属性。false:sky atmosphere hidden.

      // blurActiveElementOnCanvasFocus: false, //控制当用户点击或悬停在Cesium Viewer的画布上时，是否将焦点从当前DOM元素中移出
      // orderIndependentTranslucency: false, // 是否启用独立透明度排序，默认为true。
      // creditContainer: undefined, //显示数据源的HTML元素，默认为undefined，表示使用内置元素
      // creditViewport: undefined, //显示数据源的矩形区域，默认为undefined，表示使用整个视窗
      shadows: false, //是否启用阴影渲染，默认为false
      //默认值：ShadowMode.RECEIVE_ONLY，确定地形是否从光源投射或接收阴影，默认只接收阴影。
      //共4种模式：1. ShadowMode.DISABLED，代表物体不接收和投射阴影。
      //2. ShadowMode.ENABLED，代表物体接收和投射阴影。
      //3. ShadowMode.CAST_ONLY，代表物体只投射阴影。
      //4. ShadowMode.RECEIVE_ONLY，代表物体只接收阴影。
      terrainShadows: Cesium.ShadowMode.DISABLED, //ShadowMode类型，表示地形产生阴影的模式
      // mapMode2D: Cesium.MapMode2D.ROTATE, // 在2D场景模式下是否显示地图，默认为true。
      projectionPicker: false, // 是否显示投影方式选择器，默认为true。
      //默认值：false，如果设置为true，渲染帧只会在场景中发生变化时才会发生。
      //启用该模式可以减少应用程序的CPU/GPU使用率，并在移动设备上使用更少的电池
      requestRenderMode: false,
      // 如果requestRenderMode为true，这个值定义了在请求渲染之前允许的模拟时间的最大变化。
      // maximumRenderTimeChange: 1000,
      // depthPlaneEllipsoidOffset:0,
      //设置为0或1，将禁用MSAA，将不会进行多重采样，从而降低了渲染质量。
      //增加MSAA采样数会增加GPU的计算负担和内存消耗
      // msaaSamples: 1,
      // maximumScreenSpaceError: 32,
    },
    options
  ) as Cesium.Viewer.ConstructorOptions;
};

/**
 * {@link SplitScreenNum} 的分屏模式//
 *
 * @enum {Mumber}
 */
export const SplitScreenNum = Object.freeze({
  /**
   * 单屏模式
   *
   * @type {Mumber}
   * @constant
   */
  ONE: 1,
  /**
   * 双屏模式
   *
   * @type {Mumber}
   * @constant
   */
  TWO: 2,
  /**
   * 三屏模式
   *
   * @type {Mumber}
   * @constant
   */
  THREE: 3,
  /**
   * 三屏模式
   *
   * @type {Mumber}
   * @constant
   */

  THREEH: 5,
  /**
   * 四屏模式
   *
   * @type {Mumber}
   * @constant
   */
  FOUR: 4,
});

export function ColorFilter(_ImageryLayer: any, optins: any) {
  if (optins.isFilter) {
    _ImageryLayer.invertColor = Cesium.defaultValue(optins.invertColor, true); // 开启翻转颜色
    _ImageryLayer.filterColor = Cesium.defaultValue(
      optins.filterColor,
      Cesium.Color.fromCssColorString("#4e70a6")
    ); // 设置颜色滤镜
    _ImageryLayer.brightness = Cesium.defaultValue(optins.brightness, 0.6); // 修改图层亮度
    _ImageryLayer.contrast = Cesium.defaultValue(optins.contrast, 1.8); // 修改图层对比度
    _ImageryLayer.gamma = Cesium.defaultValue(optins.gamma, 0.3); // 修改图层灰度
    _ImageryLayer.hue = Cesium.defaultValue(optins.hue, 1.0); // 修改图层色调
    _ImageryLayer.saturation = Cesium.defaultValue(optins.saturation, 0.0); // 修改图层饱和度
  } else {
    _ImageryLayer.invertColor = Cesium.defaultValue(optins.invertColor, false); // 开启翻转颜色
    _ImageryLayer.filterColor = Cesium.defaultValue(
      optins.filterColor,
      Cesium.Color.WHITE
    ); // 设置颜色滤镜
    _ImageryLayer.brightness = Cesium.defaultValue(optins.brightness, 1.0); // 修改图层亮度
    _ImageryLayer.contrast = Cesium.defaultValue(optins.contrast, 1.0); // 修改图层对比度
    _ImageryLayer.gamma = Cesium.defaultValue(optins.gamma, 1.0); // 修改图层灰度
    _ImageryLayer.hue = Cesium.defaultValue(optins.hue, 0.0); // 修改图层色调
    _ImageryLayer.saturation = Cesium.defaultValue(optins.saturation, 1.0); // 修改图层饱和度
  }
}

  
/**
 * 根据ID获取图层 在 ImageryLayer 的索引位置，从0开始，如果没有找到就返回 -1 
 * @param layerid 
 * @param isMapTiles 是否为缓存地图资源
 * @param Viewer 
 * @returns number
 */
export function getLayerIndexByID(
  layerid: any,
  viewer: Cesium.Viewer,
  isMapTiles: boolean | string = "tiles"
): number {
  try {
    let rtn = -1;
    if(isMapTiles == "tiles"){
      for (let i = 0; i < viewer.scene.primitives.length; ++i) {
        const _primit = viewer.scene.primitives.get(i) as any;
        if (_primit?.layerid === layerid) {
          rtn = i;
          break;
        }
      }
    }
    else if (isMapTiles) {
      for (let i = 0; i < viewer.imageryLayers.length; ++i) {
        const _imager = viewer.imageryLayers.get(i) as any;
        if (_imager?.layerid === layerid) {
          rtn = i;
          break;
        }
      }
    } else {
      // for (let i = 0; i < viewer.scene.primitives.length; ++i) {
      //   const _primit = viewer.scene.primitives.get(i) as any;
      //   if (_primit?.layerid === layerid) {
      //     rtn = i;
      //     break;
      //   }
      // }
    } 
    return rtn;
  } catch (e) { 
    return -1;
  }
}
 
/**
 * 交换列表中的两个图层顺序
 * @param collection 
 * @param i 
 * @param j 
 * @returns 
 */
export function swapLayers(collection: any[], i: number, j: number) {
  i = Cesium.Math.clamp(i, 0, collection.length - 1);
  j = Cesium.Math.clamp(j, 0, collection.length - 1);
  if (i === j) {
    return;
  }
  const temp = collection[i];
  collection[i] = collection[j];
  collection[j] = temp;
}
