// import { cloneDeep } from "lodash";
import {variableType} from "../onemapkit"

/**
 * 个性化的本地化存储，变更 localStorage 或 sessionStorage 值
 * @param {string} key 关键字
 * @param {Array | object} target 数据源
 * @param {Number | string } property 变更值在 target 中的Array位置索引或object属性名称
 * @param {Array | object } value 变更值
 * @param {boolean} [isLocalStorage = true] 是localStorage 或 sessionStorage
 */
function ChangeLocalStorage(
  key: string,
  target: any,
  //@ts-ignore
  property: any,
  //@ts-ignore
  value: any,
  isLocalStorage: boolean = true
) {
  let storage = isLocalStorage ? window.localStorage : window.sessionStorage;
  // let tmp1 = storage.getItem(key);
  if (Array.isArray(target)) {
    storage.setItem(key, JSON.stringify(target));
    // let ARR = tmp1 ? JSON.parse(tmp1) : [];
    // if (target.length == property) {
    //   ARR.push(value);
    //   storage.setItem(key, JSON.stringify(ARR));
    // } else {
    //   if (isDelete) {
    //     ARR.splice(property, 1);
    //     storage.setItem(key, JSON.stringify(ARR));
    //   } else {
    //     ARR[property] = value;
    //     storage.setItem(key, JSON.stringify(ARR));
    //   }
    // }
  } else {
    storage.setItem(key, JSON.stringify(target));
    // let obj = tmp1 ? JSON.parse(tmp1) : {};
    // if (isDelete) {
    //   delete obj[property];
    //   storage.setItem(key, JSON.stringify(obj));
    // } else {
    //   obj[property] = value;
    //   storage.setItem(key, JSON.stringify(obj));
    // }
  }
}
class StorageHandler {
  private isLocalStorage: boolean = true;
  private storagekey: string = "yusytmp";
  /**
   * 变更 localStorage 或 sessionStorage 值
   * @param {string} key 关键字
   * @param {Array | object} target 数据源
   * @param {Number | string } property 变更值在 target 中的Array位置索引或object属性名称
   * @param {Array | object } value 变更值
   * @param {boolean} [isLocalStorage = true] 是localStorage 或 sessionStorage
   */
  protected CallBackChangeStorage: Function = ChangeLocalStorage;
  /**
   * 存储信息 StorageHandler
   * @param {string} _storagekey 存储对象名称
   * @param {string} [_isLocalStorage = true] 是存储为LocalStorage，还是sessionStorage
   * @param {Function} _ChangeStorage 存储的处理函数
   */
  constructor(_storagekey: string, _isLocalStorage: boolean = true, _ChangeStorage?: Function) {
    this.isLocalStorage = _isLocalStorage;
    this.storagekey = _storagekey;
    if (_ChangeStorage) this.CallBackChangeStorage = _ChangeStorage;
  }
  public get(target: any, property: any) {
    return target[property];
  }
  public set(target: any, property: any, value: any) { 
    target[property] = value;
    this.CallBackChangeStorage(this.storagekey, target, property, value, this.isLocalStorage);
    return true;
  }
  public deleteProperty(target: any, property: any) { 
    delete target[property];
    this.CallBackChangeStorage(this.storagekey, target, property, false, this.isLocalStorage);
    return true;
  }
}
export interface StorageConstructorOptions {
  /**
   * 是LocationalStorage还是SessionLocalStorage
   */
  isLocalStorage: boolean;
  /**
   * Storage名称
   */
  storagekey: string;
  /**
   * 变量初值
   */
  initStoragevalue: any;
  /** 
   Common:0-表示普通变量，内部实现方式：var variable;
   ref:1-表示 普通 ref 变量，内部实现方式：var ref(variable);
   reactive:2-表示 普通 reactive 变量，内部实现方式：var reactive(variable);
   ProxyReactive:3-表示 Proxy-reactive 变量，内部实现方式： var reactive(ProxyParameter(variable))，
   ProxyRef:4-表示 Proxy-ref 变量，内部实现方式： var ref(ProxyParameter(variable))
  */
  variableType?: variableType;
}

export class UserStorage {
  /**
   * 存放参数配置
   */
  protected StorageKeyValue = new Map<string, StorageConstructorOptions>();
  /**
   * 参数值对象 */
  public Storage: Map<string, any> = new Map<string, any>(); // {};
  /**
   * 获取参数值
   * @param {string} key 参数名称
   * @returns any
   */
  public getStorage(key: string): any {
    if (this.StorageKeyValue.has(key)) {
      let tem = this.StorageKeyValue.get(key) as StorageConstructorOptions;
      switch (tem.variableType) {
        case 0:
          return this.Storage.get(key);
        case 1:
          return this.Storage.get(key);
        case 2:
          return this.Storage.get(key);
        case 3:
          return this.Storage.get(key);
        case 4:
          return this.Storage.get(key);
        default:
          return undefined as any;
      } /**/
      /*
      switch (tem.variableType) {
        case 0:
          return (this.Storage as any)[key];
        case 1: 
          return (this.Storage as any)[key].value;
        case 2:
          return (this.Storage as any)[key];
        case 3:
          return (this.Storage as any)[key];
        case 4:
          return (this.Storage as any)[key].value;
        default:
          return undefined as any;
      }*/
    }
  }
  /**
   * 移除变量
   * @param {string} key 参数名称
   * @returns any
   */
  public removeStorage(key: string): any {
    if (this.StorageKeyValue.has(key)) {
      this.Storage.delete(key);
      this.StorageKeyValue.delete(key);
    }
  }

  /**
   * 创建代理参数
   * @param {Array<any> | Object} param 变量的初始化参数
   * @param {string} key 变量存储名称
   * @param {boolean} [isLocalStorage = true] 是存储为LocalStorage，还是sessionStorage
   * @returns
   */
  public ProxyParameter(
    param: Array<any> | Object,
    key: string,
    isLocalStorage: boolean = true
  ): any {
    let __Storage = isLocalStorage ? window.localStorage : window.sessionStorage;
    if (Array.isArray(param)) {
      if (__Storage[key]) {
        return new Proxy(
          [...new Set([...JSON.parse(__Storage[key]), ...(param || [])])],
          new StorageHandler(key, isLocalStorage)
        );
      } else {
        return new Proxy(param, new StorageHandler(key, isLocalStorage));
      }
    } else {
      if (__Storage[key]) {
        return new Proxy(
          { ...param, ...JSON.parse(__Storage[key]) },
          new StorageHandler(key, isLocalStorage)
        );
      } else {
        return new Proxy(param, new StorageHandler(key, isLocalStorage));
      }
    }
  }
}
/**
   * 创建代理参数
   * @param {Array<any> | Object} param 变量的初始化参数
   * @param {string} key 变量存储名称
   * @param {boolean} [isLocalStorage = true] 是存储为LocalStorage，还是sessionStorage
   * @returns
   */
export function ProxyParameter(
  param: Array<any> | Object,
  key: string,
  isLocalStorage: boolean = true
): any {
  let __Storage = isLocalStorage ? window.localStorage : window.sessionStorage;
  if (Array.isArray(param)) {
    if (__Storage[key]) {
      return new Proxy(
        [...new Set([...JSON.parse(__Storage[key]), ...(param || [])])],
        new StorageHandler(key, isLocalStorage)
      );
    } else {
      return new Proxy(param, new StorageHandler(key, isLocalStorage));
    }
  } else {
    if (__Storage[key]) {
      return new Proxy(
        { ...param, ...JSON.parse(__Storage[key]) },
        new StorageHandler(key, isLocalStorage)
      );
    } else {
      return new Proxy(param, new StorageHandler(key, isLocalStorage));
    }
  }
}