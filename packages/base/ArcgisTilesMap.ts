const padWithZerosIfNecessary = (key: string, value: any) => {
  if (key === "{z}") {
    if (value / 10 >= 1) {
      value = `L${value}`;
    } else {
      value = `L0${value}`;
    }
  }

  if (key === "{y}") {
    value = value.toString(16);
    const length = 8 - value.length;
    let head = "R";
    for (let i = 0; i < length; i++) {
      head += "0";
    }
    value = head + value;
  }

  if (key === "{x}") {
    value = value.toString(16);
    const length = 8 - value.length;
    let head = "C";
    for (let i = 0; i < length; i++) {
      head += "0";
    }
    value = head + value;
  }
  return value;
};

const lodinfo = [
  { level: 0, resolution: 1.406250000000238, scale: 590995186.1176 },
  { level: 1, resolution: 0.703125000000119, scale: 295497593.0588 },
  { level: 2, resolution: 0.3515625000000595, scale: 147748796.5294 },
  { level: 3, resolution: 0.17578125000002975, scale: 73874398.2647 },
  { level: 4, resolution: 0.08789062500001488, scale: 36937199.13235 },
  { level: 5, resolution: 0.04394531250000744, scale: 18468599.566175 },
  { level: 6, resolution: 0.02197265625000372, scale: 9234299.7830875 },
  { level: 7, resolution: 0.01098632812500186, scale: 4617149.89154375 },
  { level: 8, resolution: 0.00549316406250093, scale: 2308574.945771875 },
  {
    level: 9,
    resolution: 0.002746582031250465,
    scale: 1154287.4728859374,
  },
  {
    level: 10,
    resolution: 0.0013732910156252325,
    scale: 577143.7364429687,
  },
  {
    level: 11,
    resolution: 0.0006866455078126162,
    scale: 288571.86822148436,
  },
  {
    level: 12,
    resolution: 0.0003433227539063081,
    scale: 144285.93411074218,
  },
  {
    level: 13,
    resolution: 0.00017166137695315406,
    scale: 72142.96705537109,
  },
  {
    level: 14,
    resolution: 0.00008583068847657703,
    scale: 36071.483527685545,
  },
  {
    level: 15,
    resolution: 0.000042915344238288514,
    scale: 18035.741763842772,
  },
  {
    level: 16,
    resolution: 0.000021457672119144257,
    scale: 9017.870881921386,
  },
  {
    level: 17,
    resolution: 0.000010728836059572129,
    scale: 4508.935440960693,
  },
  {
    level: 18,
    resolution: 0.000005364418029786064,
    scale: 2254.4677204803465,
  },
  {
    level: 19,
    resolution: 0.000002682209014893032,
    scale: 1127.2338602401733,
  },
  {
    level: 20,
    resolution: 0.000001341104507446516,
    scale: 563.6169301200866,
  },
  {
    level: 21,
    resolution: 6.70552253723258e-7,
    scale: 281.8084650600433,
  },
  {
    level: 22,
    resolution: 3.35276126861629e-7,
    scale: 140.9042325300217,
  },
  {
    level: 23,
    resolution: 1.676380634308145e-7,
    scale: 70.45211626501083,
  },
  {
    level: 24,
    resolution: 8.381903171540725e-8,
    scale: 35.22605813250541,
  },
  {
    level: 25,
    resolution: 4.190951585770363e-8,
    scale: 17.61302906625271,
  },
  {
    level: 26,
    resolution: 2.0954757928851814e-8,
    scale: 8.806514533126353,
  },
];

/**
 *
 * @param {object} sender
 * @param {string} url
 * @param {Object} [args]
 * @param {Extent} [args.initialExtent]  initialExtent对象
 * @param {Extent} [args.fullExtent] fullExtent对象
 * @param {tileInfo} [args.tileInfo] tileInfo参数对象
 * @param {TileInfo} [TileInfo = esri.TileInfo] esri.TileInfo类
 */
export const ArcgisTilesMap = (
  sender: any,
  url: any,
  args: any,
  TileInfo: any
) => {
  sender.url = url;
  // 确保在放大到 5 级以上时只显示 5 级的瓦片 
  sender.maximumLevel = args?.maximumLevel||22;
  // Optional: 如果你想设置最小级别，可以这样做（通常可以不设置最小级别）
  sender.minimumLevel =  args?.minimumLevel||0;
  sender.spatialReference = args?.spatialReference || {
    wkid: 4490,
    latestWkid: 4490,
  };
  sender.initialExtent = args?.initialExtent || {
    xmin: -180,
    ymin: -90,
    xmax: 180,
    ymax: 90,
    spatialReference: sender?.spatialReference,
  };
  sender.fullExtent = args?.fullExtent || {
    xmin: -180,
    ymin: -90,
    xmax: 180,
    ymax: 90,
    spatialReference: sender?.spatialReference,
  };
  sender.tileInfo = new TileInfo(
    args?.tileInfo || {
      rows: 256,
      cols: 256,
      compressionQuality: 0,
      dpi: 96,
      format: "png",
      origin: {
        x: -180,
        y: 90,
      },
      spatialReference: sender?.spatialReference,
      lods: lodinfo.slice(sender.minimumLevel, sender.maximumLevel),
      maxZoom:sender.maximumLevel,
      minZoom:sender.minimumLevel
    }
  ); 
  sender.getTileUrl = (level: any, row: any, col: any) => { 
    // console.log("yusy>getTileUrl",level,sender)
    return sender.url
      .replace("{z}", padWithZerosIfNecessary("{z}",Math.max(sender.minimumLevel, Math.min(sender.maximumLevel, level)) ))
      .replace("{x}", padWithZerosIfNecessary("{x}", col))
      .replace("{y}", padWithZerosIfNecessary("{y}", row));
  };
};
