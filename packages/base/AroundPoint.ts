
import * as Cesium from "@onemapkit/cesium";

export default class AroundPoint {
  private _viewer: Cesium.Viewer;
  private _position: Cesium.Cartesian3;
  private _distance: number;
  private _viewangle: number;
  private _stepsize: number;
  private _viewheight: number;
  private _camerax: number;
  private _cameray: number;
  constructor(viewer: Cesium.Viewer, position: Cesium.Cartesian3, options: any) {
    this._viewer = viewer;
    this._position = position;
    this._stepsize = Cesium.Math.toRadians(Cesium.defaultValue(options?.stepsize, 0.2));
    this._distance = Cesium.defaultValue(options?.distance, 100);
    this._viewangle = Cesium.Math.toRadians(Cesium.defaultValue(options?.viewangle, -20));
    var cartographic = Cesium.Cartographic.fromCartesian(position)
    this._camerax = Cesium.Math.toDegrees(cartographic.latitude);
    this._cameray = Cesium.Math.toDegrees(cartographic.longitude);
    this._viewheight = cartographic.height;

    console.log("ViewHeight value ", this._cameray, this._camerax);
  }

  _bindEvent() {

    this._viewer.clock.onTick.addEventListener(this._aroundPoint, this);
  }
  _unbindEvent() {
    this._viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY);
    this._viewer.clock.onTick.removeEventListener(this._aroundPoint, this);
  }

  start() {
    this._viewer.clock.shouldAnimate = true;
    this._unbindEvent();
    this._bindEvent();
    return this;
  }

  stop() {
    this._viewer.clock.shouldAnimate = false;
    this._unbindEvent();
    return this;
  }
  set ViewHeight(value: number) {
    this._position = Cesium.Cartesian3.fromDegrees(this._cameray, this._camerax, this._viewheight + value);
    //this._viewheight = value;
  }
  get ViewHeight() {
    return this._viewheight;
  }

  set Distance(value: number) {
    this._distance = value;
  }
  get Distance() {
    return this._distance;
  }
  set ViewAngle(value: number) {
    this._viewangle = Cesium.Math.toRadians(value);
  }
  get ViewAngle() {
    return this._viewangle;
  }

  set StepSize(value: number) {
    this._stepsize = Cesium.Math.toRadians(value);
  }
  get StepSize() {
    return this._stepsize;
  }

  // 相机绕点旋转函数
  _aroundPoint() {
    let heading = this._viewer.camera.heading;
    heading += this.StepSize;
    if (heading >= Math.PI * 2 || heading <= -Math.PI * 2) {
      heading = this.StepSize;
    }

    this._viewer.camera.lookAt(
      this._position,
      new Cesium.HeadingPitchRange(heading, this.ViewAngle, this.Distance)
    );
  }

}
