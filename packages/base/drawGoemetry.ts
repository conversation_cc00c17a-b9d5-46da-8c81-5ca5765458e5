import * as Cesium from "@onemapkit/cesium";

import * as CesiumTools from "@onemapkit/cesium-tools";
// import PlaneEditableMode from "../tools/PlaneEditable/PlaneEditableMode";

/**
 * Cesium 画图实现类
 */
export class DrawGeometry {
  private Viewer: any = undefined;
  /**
   *
   * @param {Cesium.Viewer} _MapViewer
   */
  constructor(_MapViewer: Cesium.Viewer) {
    this.Viewer = _MapViewer;
  }
  /**
   * DrawPoint
   * @param {any} Options
   */
  public DrawPoint(Options?: any) {
    const key = Options?.id
      ? Options.id
      : "pt" + new Date().getTime().toString();
    this.Viewer.DrawBrush.draw(CesiumTools.PlaneEditableMode.Point, key);
  }
  /**
   * DrawPolyline2d
   * @param {any} Options
   */
  public DrawPolyline2d(Options?: any) {
    const key = Options?.id
      ? Options.id
      : "pl" + new Date().getTime().toString();
    this.Viewer.DrawBrush.draw(CesiumTools.PlaneEditableMode.Line, key);
  }
  /**
   * DrawPolygon2d
   * @param {any} Options
   */
  public DrawPolygon2d(Options?: any) {
    const key = Options?.id
      ? Options.id
      : "pg" + new Date().getTime().toString();
    this.Viewer.DrawBrush.draw(CesiumTools.PlaneEditableMode.Polygon, key);
  }
  /**
   * DrawCircle
   * @param { any}Options
   */
  public DrawCircle(Options?: any) {
    const key = Options?.id
      ? Options.id
      : "cc" + new Date().getTime().toString();
    this.Viewer.DrawBrush.draw(CesiumTools.PlaneEditableMode.Circle, key);
  }
  /**
   * DrawRectangle
   * @param {any} Options
   */
  public DrawRectangle(Options?: any) {
    const key = Options?.id
      ? Options.id
      : "rc" + new Date().getTime().toString();
    this.Viewer.DrawBrush.draw(CesiumTools.PlaneEditableMode.Rectangle, key);
  }
}
