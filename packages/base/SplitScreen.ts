import * as Cesium from "@onemapkit/cesium";
import { ILayerImpl } from "../utils/addLayer/czmAddLayer";
import {
	SplitScreenNum,
	getOptions,
	initUcsmOption,
} from "./Utils";

// 地球的颜色
const earseColor = [128, 128, 128];

export default class SplitScreenExt {
	public _viewers: any;
	public _allViewers: any;
	public _layerImpl: any = {};
	public _inOnemap: any;

	private _containers: any;
	private _mapHandlers: any;
	private _removeCallback: any;
	private _curKey: string;
	private _mode: number;

	private _BaseTerrain: any;
	private _BaseImageryProvider: any;

	/**
	 * 分屏对比工具类
	 * @constructor
	 * @alias SplitScreen
	 *
	 * @param {Cesium.Viewer} viewer 主视图对象
	 * @param {Object} [option] 包含以下参数的对象
	 * @param {Object} [option.mode = SplitScreenMode.TWO] 分屏的模式，默认为双屏
	 * @param {Object} [option.BaseImageryProvider] 初始化时子视图的影像信息
	 * @param {Object} [option.BaseTerrain] 初始化时子视图的地形信息
	 */
	constructor(option: any = {}) {
		console.log("构建分屏")
		this._mode = Cesium.defaultValue(option.mode, SplitScreenNum.ONE);

		if (option.MakeBaseMapLayer) {
			this._BaseImageryProvider = option.MakeBaseMapLayer;
		} else {
			this._BaseImageryProvider = null
			// throw ("必须添加底图才能启用三维分屏")
		}

		if (option.TerrainUrl) {
			this._BaseTerrain = option.TerrainUrl;
		} else {
			this._BaseTerrain = null
			// throw ("必须添加地形才能启用三维分屏")
		}

		this._containers = {}; // 视图container
		this._mapHandlers = {}; // 地图事件
		this._removeCallback = {}; // 移除事件回调函数
		this._curKey = "sub1"; // 当前的索引
		this._viewers = {};	// 视图
		this._allViewers = {};

		// 视图
		for (let i = 0; i < this._mode; i++) {
			const num = i + 1;
			const key = "sub" + num;
			const container = document.querySelector("#split-view-" + num);
			const mapViewer = this._createViewer(container, key, option.cameraOptions);
			mapViewer.resize();
			this._containers[key] = container;
			this._viewers[key] = mapViewer;
			this._layerImpl[key] = new ILayerImpl(mapViewer);
		}

		this._linkage();
	}
	getSubViewer(idx: number) {
		if (this._viewers) {
			return this._viewers["sub" + idx];
		} else {
			return undefined;
		}
	}

	// 获取当前视图视角
	static getCameraOptions(viewer: Cesium.Viewer) {
		if(!viewer.scene){
			return null
		}
		const position = viewer.scene.camera.position;
		const options = {
			destination: position,
			orientation: {
				heading: viewer.camera.heading,
				pitch: viewer.camera.pitch,
				roll: viewer.camera.roll,
			},
		};

		return options;
	}

	/**
	 * 更改子视图的影像
	 * @param {Number} subIndex 子视图的索引（第一个子视图索引为1）
	 * @param {Cesium.ImageryProvider} imagery 新的影像
	 */
	changeSubImagery(subIndex: number, imagery: Cesium.ImageryProvider) {
		const viewer = this._viewers["sub" + subIndex];
		if (!viewer) return;
		viewer.imageryLayers.removeAll();
		viewer.imageryLayers.addImageryProvider(imagery);
	}

	/**
	 * 更改子视图的地形
	 * @param {Number} subIndex 子视图的索引（第一个子视图索引为1）
	 * @param {Cesium.TerrainProvider} terrain 新的地形
	 */
	changeSubTerrain(subIndex: number, terrain: Cesium.TerrainProvider) {
		const viewer = this._viewers["sub" + subIndex];
		if (!viewer) return;
		viewer.terrainProvider = terrain;
	}

	/**
	 * 更改子视图的3DTiles
	 * @param {Number} subIndex 子视图的索引（第一个子视图索引为1）
	 * @param {String[]} tiles 新的3DTiles url集合
	 */
	changeSub3DTiles(subIndex: number, tiles: string[]) {
		const viewer = this._viewers["sub" + subIndex];
		if (!viewer) return;
		const primitives = viewer.scene.primitives as Cesium.PrimitiveCollection;
		primitives.removeAll();
		tiles.forEach((url: string) => {
			const tilesetPromise = Cesium.Cesium3DTileset.fromUrl(url, {
				skipLevelOfDetail: true,
			});
			tilesetPromise.then((tileset: Cesium.Cesium3DTileset) => {
				primitives.add(tileset);
			});
		});
	}

	/**
	 * 点击全幅定位，将所有视图同步到全幅坐标
	 */
	asyncExtent(self: any, extent: any) {
		for (const changeKey in self._allViewers) {
			// 创建一个 Cesium.Rectangle 对象
			const rectangle = Cesium.Rectangle.fromDegrees(extent.xmin, extent.ymin, extent.xmax, extent.ymax);

			// 飞行到矩形区域
			self._allViewers[changeKey].camera.flyTo({
				destination: rectangle,
				orientation: {
					heading: Cesium.Math.toRadians(extent.heading),   // heading
					pitch: Cesium.Math.toRadians(extent.pitch),   // pitch
					roll: Cesium.Math.toRadians(extent.roll)       // roll
				},
				duration: 0.1
			});
		}
	}

	/**
	 * 更改分屏模式
	 * @param {SplitScreenMode} mode 新的分屏类型
	 */
	changeMode(mode: number) {
		console.log("更改分屏模式")
		if (this._viewers === undefined) return;
		if (mode === this._mode) return;
		if (mode < this._mode) {
			// 新的分屏数量少于当前分屏数量
			for (let i = mode; i < this._mode; i++) {
				const key = "sub" + (i + 1);

				// 销毁多余的视图释放内存
				this._viewers[key] && this._viewers[key].destroy();
				this._viewers[key] && (delete this._viewers[key]);
				delete this._containers[key];
			}
		} else {
			// 新的分屏数量高于当前分屏数量
			console.log()

			let cameraOptions = null;
			// if(this._viewers.scene){
			// 	cameraOptions = SplitScreenExt.getCameraOptions(this._viewers["sub1"]);
			// }
			for (let i = this._mode; i < mode; i++) {
				const container = document.querySelector("#split-view-" + (i + 1));
				const key = "sub" + (i + 1);
				const mapViewer = this._createViewer(container, key, cameraOptions);
				this._containers[key] = container;
				this._viewers[key] = mapViewer;
				this._layerImpl[key] = new ILayerImpl(mapViewer);
			}
		}

		this._mode = mode;
		for (const key in this._viewers) {
			this._viewers[key].resize();
		}
		this._linkage();
		return this;
	}

	/**
	 * 销毁释放内存
	 */
	destroy() {
		console.log("销毁释放内存")
		this._clearLinkage();
		for (let key in this._viewers) {
			this._viewers[key].destroy();
			delete this._viewers[key];
		}
		for (let key in this._containers) {
			delete this._containers[key];
		}

		this._mode = 1;
		delete this._viewers;
		delete this._containers;
		return Cesium.destroyObject(this);
	}

	// 建立视图之间的同步链接
	private _linkage() {
		console.log("建立视图之间的同步链接")
		this._clearLinkage();
		this._allViewers = {
			...this._viewers,
		};

		for (const key in this._allViewers) {
			const viewer = this._allViewers[key];
			const mapHandler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
			this._mapHandlers[key] = mapHandler;
			mapHandler.setInputAction(() => {
				this._curKey = key;
			}, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

			const removeCallback = viewer.scene.camera.changed.addEventListener(
				() => {
					if (this._curKey === key) {
						// 联动其他视口地图
						const cameraOptions = SplitScreenExt.getCameraOptions(viewer);
						for (const changeKey in this._allViewers) {
							if (this._curKey !== changeKey) {
								this._allViewers[changeKey].scene.camera.setView(cameraOptions);
							}
						}
					}
				},
				this
			);
			this._removeCallback[key] = removeCallback;
		}
	}

	// 取消所有链接
	private _clearLinkage() {
		for (const key in this._mapHandlers) {
			this._removeCallback[key]();
			this._mapHandlers[key].removeInputAction(
				Cesium.ScreenSpaceEventType.MOUSE_MOVE
			);
			this._mapHandlers[key].destroy();
		}
		this._mapHandlers = {};
		this._removeCallback = {};
		this._allViewers = {};
		this._curKey = "main";
	}

	// 创建Viewer
	private _createViewer(container: any, _key: string, cameraOptions: any = {}) {
		console.log("创建Viewer")
		const mapViewer = new Cesium.Viewer(container, Object.assign(getOptions(container), {
			baseLayer: this._BaseImageryProvider(),
		}));
		mapViewer.scene.terrainProvider = Cesium.defaultValue(
			this._BaseTerrain,
			new Cesium.EllipsoidTerrainProvider()
		);

		// 地球颜色
		mapViewer.scene.globe.baseColor = new Cesium.Color(earseColor[0] / 255, earseColor[1] / 255, earseColor[2] / 255);

		initUcsmOption(mapViewer);
		(mapViewer as any)._cesiumWidget._creditContainer.style.display = "none";
		mapViewer.scene.camera.percentageChanged = 0.01;
		mapViewer.scene.globe.baseColor = Cesium.Color.BLACK;
		mapViewer.scene.globe.depthTestAgainstTerrain = true;
		if (cameraOptions) mapViewer.scene.camera.setView(cameraOptions);
		return mapViewer;
	}

	/**
	 * 设置底图
	 * @param baseImageryProvider
	 */
	public setBaseImageryProvider(baseImageryProvider: any) {
		console.log("设置底图")
		this._BaseImageryProvider = baseImageryProvider;
	}

	/**
	 * 设置地形
	 * @param baseTerrain
	 */
	public setBaseTerrain(baseTerrain: any) {
		this._BaseTerrain = baseTerrain;
	}
}
