<template>
  <div :style="'width:' + props?.width+';display: flex;'"
    
  >
    <span
      v-if="props?.label != ''"
      style="margin-right: 15px; white-space: nowrap"
      >{{ props.label }}</span
    >
    <el-input
      ref="inputRef"
      v-model="innerColor"
      :value="props.modelValue"
      @input="updateContent"
      :min="props.min"
      :max="props.max"
      :placeholder="props.placeholder"
      :disabled="props.disabled"
      :size="props.size"
      :validate-event="props.validateEvent"
      :style="'width: ' + props.inputWidth + 'px;'"
    />
    <!-- <el-checkbox
      ref="checkboxRef"
      :value="props.check"
      @change="updateChecked"
      v-model="innerCheck"
      label="RGBA"
      size="large"/> -->
    <el-color-picker
      ref="colorpickerRef"
      v-model="innerColor"
      :value="props.modelValue"
      :color-format="props.colorformat"
      show-alpha
      @active-change="updateContent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

const props = defineProps({
  width: {
    type: String,
    default: '100%',
    require: false,
  },
  colorformat: {
    type: String,
    default: "hex", //rgb hex
    require: false,
  },
  check: {
    type: Boolean,
    default: false,
    require: false,
  },
  modelValue: {
    type: String,
    default: "#FF0000FF",
    require: false,
  },
  inputWidth: {
    type: Number,
    default: 100,
    require: false,
  },
  min: {
    type: Number,
    default: 0,
    require: false,
  },
  max: {
    type: Number,
    default: 100,
    require: false,
  },
  size: {
    type: String,
    default: "default",
    require: false,
  },
  disabled: {
    type: Boolean,
    default: false,
    require: false,
  },
  label: {
    type: String,
    default: "",
    require: false,
  },
  placeholder: {
    type: String,
    default: "placeholder",
    require: false,
  },
  validateEvent: {
    type: Boolean,
    default: true,
    require: false,
  },
});

const inputRef = ref();
const colorpickerRef = ref();
const innerColor = ref("#FF0000FF");
const emit = defineEmits(["update:modelValue"]); //, "update:Checked"
const updateContent = (event: any) => {
  innerColor.value = event;
  emit("update:modelValue", event);
};

// const checkboxRef = ref();
// const innerCheck = ref(false);
// const updateChecked = (event: any) => {
//   innerCheck.value = event;
//   emit("update:Checked", event);
// };
onMounted(() => {
  
  // console.log("========",props?.width,(props?.width==undefined), (props?.width==undefined?props?.width:'100%;'));
});

/*


      :value="modelValue" 
      @input="updateValue"

      v-model="props.value"
      :min="props.min"
      :max="props.max"
      :disabled="props.disabled"
      :step="props.step"
      :size="props.size"
      :input-size="props.inputsize"
      :vertical="props.vertical"
      :label="props.label"
      :debounce="props.debounce"
      :validate-event="props.validateEvent"

      
      @input="innerValue = $event.target.value"
      :value="innerValue"
      :min="props.min"
      :max="props.max"
      :step="props.step"
      :step-strictly="props.stepStrictly"
      :precision="props.precision"
      :size="props.size"
      :disabled="props.disabled"
      :controls="props.controls"
      :controls-position="props.controlsPosition"
      :name="props.name"
      :label="props.label"
      :placeholder="props.placeholder"
      :id="props.id"
      :validate-event="props.validateEvent"



const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
    require: false,
  },
  value: {
    type: Number,
    default: 0,
    require: true,
  },
  min: {
    type: Number,
    default: 0,
    require: false,
  },
  max: {
    type: Number,
    default: 10,
    require: false,
  },
  step: {
    type: Number,
    default: 1,
    require: false,
  },
  stepStrictly: {
    type: Boolean,
    default: false,
    require: false,
  },
  precision: {
    type: Number,
    default: 0,
    require: false,
  },
  size: {
    type: String,
    default: "default",
    require: false,
  },
  disabled: {
    type: Boolean,
    default: false,
    require: false,
  },
  controls: {
    type: Boolean,
    default: true,
    require: false,
  },
  controlsPosition: {
    type: String,
    default: true,
    require: false,
  },
  name: {
    type: String,
    default: "",
    require: false,
  },
  label: {
    type: String,
    default: "",
    require: false,
  },
  placeholder: {
    type: String,
    default: "placeholder",
    require: false,
  },
  id: {
    type: String,
    default: "",
    require: false,
  },
  validateEvent: {
    type: Boolean,
    default: true,
    require: false,
  },
  inputsize: {
    type: String,
    default: "default",
    require: false,
  },
  vertical: {
    type: Boolean,
    default: false,
    require: false,
  },
  debounce: {
    type: Number,
    default: 300,
    require: false,
  },
});

const innerValue=ref(0);
 

const sliderRef = ref(null);
const inputnumberRef = ref(null);

// function handleUpdate(newVal:any) {
//   inputValue = newVal; // 更新输入值
// }

const emit = defineEmits(["update:modelValue"]);
// function sendMessageHandler() {
//   emit("Change", inputnumberRef.value);
// }
// const onChange = () => {
//   onChange;
// };


const updateValue = (event:any) => {
            emit('update:modelValue', event.target.value);
        }

 
defineEmits(['update:modelValue', 'update:textVal'])
// const close = () => {
//     emit('update:modelValue', false)
// }
// const change = (e:Event) => {
//     const target = e.target as HTMLInputElement
//     emit('update:textVal', target.value)
// }
 
*/
</script>
