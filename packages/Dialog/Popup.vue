<template>
  <div v-if="isClose" class="popup-container">
    <!-- 1.popup -->
    <div class="popup" ref="popupRef" :id="props.popupID">
      <!-- 2.1Caption -->
      <div style="display: flex">
        <!-- 2.1Caption Text -->
        <div
          id="popupheaderDivid"
          class="popupHeader"
          ref="popupHeaderRef"
          @click.stop="PopupHeaderClick"
        >
          <!-- 2.1.1 Caption Icon Text -->
          <div
            style="
              position: relative;
              display: flex;
              align-items: center;
              overflow: hidden;
              height: 35px;
              width: 100%;
            "
          >
            <el-tooltip
              :effect="
                props.captionIconTip?.effect
                  ? props.captionIconTip?.effect
                  : 'light'
              "
              :content="
                props.captionIconTip?.content
                  ? props.captionIconTip?.content
                  : props.captionText
              "
              :placement="
                props.captionIconTip?.placement
                  ? props.captionIconTip?.placement
                  : 'left'
              "
              :raw-content="
                props.captionIconTip?.rawContent
                  ? props.captionIconTip?.rawContent
                  : false
              "
              :offset="
                props.captionIconTip?.offset
                  ? props.captionIconTip?.offset
                  : props.captionIconTip.offset
              "
            >
              <div
                style="
                  margin: 2px;
                  width: 30px;
                  height: 30px;
                  min-width: 30px;
                  max-height: 30px;
                  max-width: 30px;
                "
                v-html="props.captionIcon"
              ></div>
            </el-tooltip>
            <span style="overflow: hidden; white-space: nowrap">
              {{ props.captionText }}
            </span>
          </div>
          <!-- 2.1.2 End Caption Arrow -->
          <div
            v-if="props.showCaptionArrow"
            style="
              display: flex;
              transform: rotate(270deg);
              transform-origin: center;
            "
          >
            <el-icon
              :class="
                props.isHeaderClickEnable
                  ? isPopupHeaderClick
                    ? `el-icon-transform`
                    : undefined
                  : `el-icon-transform`
              "
              :size="20"
            >
              <CaretTop />
            </el-icon>
          </div>
        </div>
        <!-- 2.2 Header slot left:props.showCaptionArrow? (isPopupHeaderClick?`35px`:`35px`):`402px` -->
        <slot name="caption"></slot>
      </div>
      <!-- 3.1 Inner Content -->
      <div
        v-if="props.isIncontent"
        v-show="
          props.isHeaderClickEnable
            ? props.showContent && isPopupHeaderClick
            : props.showContent
        "
        ref="contentRef"
        class="contentInnerDiv"
        :id="props.contentID"
      >
        <slot></slot>
      </div>
    </div>
    <!-- 3.2 Out Content -->
    <div
      v-if="!props.isIncontent"
      v-show="
        props.isHeaderClickEnable
          ? props.showContent && isPopupHeaderClick
          : props.showContent
      "
      ref="contentRef"
      class="contentOutDiv"
      :id="props.contentID"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, type PropType, type CSSProperties, onMounted, nextTick } from "vue";
import { CaretTop } from "@element-plus/icons-vue";
import { getOnemap } from "../onemapkit";

const ResCatalogIcon =
  '<svg t="1684284054562" class="curstom-icon" viewBox="0 0 1024 1024" version="1.1" p-id="2382"><path d="M852.6 462.9l12.1 7.6c24.8 15.6 32.3 48.3 16.7 73.2-4.2 6.7-9.9 12.4-16.7 16.7L540.4 764.1c-17.3 10.8-39.2 10.8-56.4 0L159.3 560c-24.8-15.6-32.3-48.3-16.7-73.2 4.2-6.7 9.9-12.4 16.7-16.7l12.1-7.6L483.9 659c17.3 10.8 39.2 10.8 56.4 0l312.2-196 0.1-0.1z m0 156.1l12.1 7.6c24.8 15.6 32.3 48.3 16.7 73.2-4.2 6.7-9.9 12.4-16.7 16.7L540.4 920.2c-17.3 10.8-39.2 10.8-56.4 0L159.3 716.1c-24.8-15.6-32.3-48.3-16.7-73.2 4.2-6.7 9.9-12.4 16.7-16.7l12.1-7.6L483.9 815c17.3 10.8 39.2 10.8 56.4 0l312.2-196h0.1zM540 106.4l324.6 204.1c24.8 15.6 32.3 48.3 16.7 73.2-4.2 6.7-9.9 12.4-16.7 16.7L540.4 604c-17.3 10.8-39.2 10.8-56.4 0L159.3 399.8c-24.8-15.6-32.3-48.3-16.7-73.2 4.2-6.7 9.9-12.4 16.7-16.7l324.4-203.7c17.3-10.8 39.2-10.8 56.4 0l-0.1 0.2z" p-id="2383"></path></svg>';
type CaptionClickHandlerType = (result: {
  popup: any;
  Header: any;
  Content: any;
}) => void;

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  // 1.1 Caption Icon图标
  captionIcon: {
    type: String,
    default: ResCatalogIcon,
  },
  //1.2 Caption 标题
  captionText: {
    type: String,
    default: "停靠面板",
  },
  //1.3 Caption Icon图标提示
  captionIconTip: {
    type: Object as PropType<{
      effect?: string;
      content?: string;
      placement?: string;
      rawContent?: boolean;
      offset?: number;
    }>,
    default: {
      effect: "light",
      placement: "left",
      rawContent: true,
      offset: 12,
    },
  },
  popupID: {
    type: String,
    default: "popupID_undefined2",
  },
  popupStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  contentID: {
    type: String,
    default: "contentID_undefined2",
  },
  //是否显示在内部 中间没有空隙
  isIncontent: {
    type: Boolean,
    default: false,
  },
  contentStyle: {
    type: Object as PropType<CSSProperties>,
    default: undefined,
  },
  /**Heade 是否可点击 */
  isHeaderClickEnable: { type: Boolean, default: true },
  /** 是否显示右边的三角图标 */
  showCaptionArrow: {
    type: Boolean,
    default: true,
  },

  showContent: {
    type: Boolean,
    default: true,
  },
  CaptionClickHandler: {
    type: Function as PropType<CaptionClickHandlerType>,
    default: undefined,
  },
  onClose: {
    type: Function,
    default: undefined,
  },
});
//@ts-ignore
const _Onemap = getOnemap(props.MapControlName);

const popupRef = ref();
const popupHeaderRef = ref();
const contentRef = ref();

//鼠标点击标题栏
/** 标题栏的鼠标点击状态标识 */
const isPopupHeaderClick = ref(false);
const PopupHeaderClick = () => {
  isPopupHeaderClick.value = !isPopupHeaderClick.value;
  nextTick(() => {
    initDomStyle();
  });
};

const initDomStyle = () => {
  if (props.isHeaderClickEnable) {
    popupHeaderRef.value.style.pointerEvents = "auto";
    popupHeaderRef.value.style.cursor = "pointer";

    //#region 1.popupRef
    popupRef.value.style.zIndex = 500;
    popupRef.value.style.position = `absolute`;
    //宽度要分情况:是否要显示箭头图标
    popupRef.value.style.width = isPopupHeaderClick.value
      ? `400px`
      : props.showCaptionArrow
      ? `110px`
      : `34px`;
    //要分情况:是否显示在内部
    popupRef.value.style.height = props.isIncontent
      ? isPopupHeaderClick.value
        ? `calc(100% - 100px)`
        : `35px`
      : `35px`;
    //输入的样式
    if (props?.popupStyle) {
      Object.keys(props.popupStyle).forEach((key: string) => {
        popupRef.value.style[key] = (props.popupStyle as any)[key];
      });
    }
    //#endregion

    //#region 2.contentRef
    contentRef.value.style.zIndex = 500;
    contentRef.value.style.position = `absolute`;
    contentRef.value.style.width = `400px`;
    contentRef.value.style.height = props.isIncontent
      ? `100%`
      : `calc(100% - 90px)`;
    // contentRef.value.style.top = props.isIncontent ? `35px` : `40px`;
    // contentRef.value.style.left = props.isIncontent ? `-1px` : `2px`;

    //输入的样式
    if (props?.contentStyle) {
      Object.keys(props.contentStyle).forEach((key: string) => {
        contentRef.value.style[key] = (props.contentStyle as any)[key];
      });
    }
    //#endregion

    if (props.CaptionClickHandler) {
      if (props.CaptionClickHandler) {
        props.CaptionClickHandler({
          popup: document.getElementById(props.popupID), // popupRef.value,
          Header: document.getElementById("popupheaderDivid"), // popupHeaderRef.value,
          Content: document.getElementById(props.contentID), //contentInnerRef.value,
        });
      }
    }
  } else {
    //#region  1. popupRef
    popupRef.value.style.zIndex = 500;
    popupRef.value.style.position = `absolute`;
    //宽度要分情况:是否要显示箭头图标
    popupRef.value.style.width = `400px`;
    //要分情况:是否显示在内部 calc(100% - 100px)
    popupRef.value.style.height = props.isIncontent
      ? `calc(100% - 90px)`
      : `35px`;
    //输入的样式
    if (props?.popupStyle) {
      Object.keys(props.popupStyle).forEach((key: string) => {
        popupRef.value.style[key] = (props.popupStyle as any)[key];
      });
    }
    //#endregion

    //#region 2.contentRef
    contentRef.value.style.zIndex = 500;
    contentRef.value.style.position = `absolute`;
    contentRef.value.style.width = `400px`;
    contentRef.value.style.height = props.isIncontent
      ? `100%`
      : `calc(100% - 90px)`;
    // contentRef.value.style.top = props.isIncontent ? `35px` : `40px`;
    // contentRef.value.style.left = props.isIncontent ? `-1px` : `2px`;

    //输入的样式
    if (props?.contentStyle) {
      Object.keys(props.contentStyle).forEach((key: string) => {
        contentRef.value.style[key] = (props.contentStyle as any)[key];
      });
    }

    //#endregion

    popupHeaderRef.value.style.pointerEvents = "none";
    popupHeaderRef.value.style.cursor = "default";
  }
};

let isClose = ref(true);
function ClosePopup() {
  isClose.value = false;
}

// const slots = useSlots();
onMounted(() => {
  nextTick(() => {
    initDomStyle();
    // const hasSlot = slots.default;
    // console.log("=======hasSlot", hasSlot);
  });
});

function popupStyle(option: [{ name: string; value: string | number }]) {
  option.forEach((itm) => {
    popupRef.value.style[itm.name] = itm.value;
  });
}
defineExpose({ ClosePopup, popupStyle });
</script>
<style lang="scss" scoped>
.popup-container {
  position: absolute; /* 改为 fixed 使对话框在视口中固定 */
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 599;
  pointer-events: none;
  /*background-color: lightgreen; */
}
.popup {
  /*position: relative;*/
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 900;
  pointer-events: auto;
  border: 1px solid rgb(255, 255, 255);
  border-radius: 2px;
}
.popupHeader {
  border-radius: 2px;
  height: 35px;
  width: 100%;
  background: #f1f1f1;
  display: flex;
  align-items: center;
  pointer-events: auto;
  z-index: 999;
}

.el-icon-transform {
  transition: 0.2s;
  transform-origin: center;
  transform: rotateZ(-90deg);
}

.contentOutDiv {
  background: rgb(255, 255, 255);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 499;
  pointer-events: auto;
  border: 1px solid rgb(255, 255, 255);
  border-radius: 2px;
  position: absolute;
}

.contentInnerDiv {
  background: rgb(255, 255, 255);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 499;
  pointer-events: auto;
  border: 1px solid rgb(255, 255, 255);
  border-radius: 2px;
  position: absolute;
}
</style>
