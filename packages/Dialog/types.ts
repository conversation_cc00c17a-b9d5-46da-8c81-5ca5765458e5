import { type Ref, type InjectionKey } from 'vue';

export interface DialogProvide {
    setDialogSize: (size: { width?: number; height?: number }) => void;
    resizeDefaultDialogSize: () => void;
    winMaxMinSize: () => void;
    winMaxSize: () => void;
    winMinSize: () => void;
    isMax: Ref<boolean>;
    registerCloseCallback: (callback: () => void) => void;
}

export const DialogProvideKey: InjectionKey<DialogProvide> = Symbol('DialogProvide');