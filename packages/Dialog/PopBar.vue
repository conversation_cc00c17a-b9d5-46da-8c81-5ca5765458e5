<template>
  <div class="dialog-header">
    <span>{{ props.title }}</span>
    <div class="boder">
      <div class="bodersize border-on-hover">
        <div ref="btnMaxMinRef" class="close-btnMaxMin buttonFontColor"></div>
      </div>
      <div class="bodersize border-on-hover">
        <button class="close-btn buttonFontColor">×</button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: "窗体",
    require: true,
  },
});
 
</script>
<style>
.dialog-header {
  padding: 10px;
  background: #f1f1f1;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  cursor: move;
} 
.boder{
    display: flex;
     height: 20px
}
.bodersize {
  width: 32px;
  height: 24px;
  margin-right: 2px;
  margin-top: -2px;
  cursor: pointer;
}
.border-on-hover {
  padding: 1px;
  border: 1px solid rgba(210, 210, 210, 0.1);
  background-color: rgba(210, 210, 210, 0.2);
}
.border-on-hover:hover {
  padding: 1px; /* 内边距 */
  border: 1px solid rgba(100, 100, 100, 0.1); /* 鼠标悬停时显示黑色边框 */
  background-color: rgba(200, 200, 200, 0.8);
}
.buttonFontColor {
  color: rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(0, 0, 0, 0.5);
}
.close-btn {
  margin: -10px 4px 6px -2px;
  font-size: 40px;
  background: none;
  border: none;
  cursor: pointer;
}
.close-btnMaxMin {
  cursor: pointer;
  margin: 10px 6px;
  padding: 0px;
  background-color: rgba(0, 0, 0,0.5);
  height: 3px;
  width: 20px;
  border:none;
}
</style>
