<template>
  <div
    class="general-utils-container"
    v-for="key in Array.from(_CustomToolGroup.keys())"
  >
    <div class="more-util-list__title">
      {{ key }}
    </div>
    <ul class="more-util-list">
      <li
        class="more-util-list-item"
        v-for="tool in _CustomToolGroup.get(key)"
        :key="tool.name"
        @click="tool.clickEvent"
      >
        <i
          :class="[
            'more-util-list-item__icon',
            tool.isActive ? 'is-active' : '',
          ]"
          v-html="tool.icon"
        ></i>
        <div
          :class="[
            'more-util-list-item__name',
            tool.isActive ? 'is-active' : '',
          ]"
        >
          {{ tool.label }}
        </div>
      </li>
    </ul>
    <div class="groupboder2" style="width: 100%">
      <div style="display: flex; flex-wrap: wrap; gap: 2px">
        <el-form-item label="名称">
          <el-input v-model="lyrName" style="width: 200px"> </el-input>
        </el-form-item>
        <el-form-item label="参数类型">
          <el-select
            v-model="isUrlModel"
            placeholder="Select"
            style="width: 150px"
          >
            <el-option
              v-for="item in [
                { label: '参数模式', value: true },
                { label: 'JSON字符串模式', value: false },
              ]"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-button @click="addOutMapLayer">添加</el-button>
      </div>

      <div v-if="isUrlModel" style="display: flex; flex-wrap: wrap; gap: 2px">
        <el-form-item label="地图模式(mapType)">
          <el-select
            v-model="lyrMapType"
            placeholder="Select"
            style="width: 90px"
          >
            <el-option
              v-for="item in ['默认', ...Object.values(mapType)]"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="图层类型(ILayerType)">
          <el-select
            v-model="lyrLayerType"
            placeholder="Select"
            style="width: 120px"
          >
            <el-option
              v-for="item in Object.values(ILayerType)"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务类型(serviceType)">
          <el-select
            v-model="lyrServiceType"
            placeholder="Select"
            style="width: 150px"
          >
            <el-option
              v-for="item in Object.values(serviceType)"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务地址">
          <el-input v-model="lyrUrlString" style="width: 400px"> </el-input>
        </el-form-item>
      </div>
      <el-input
        v-if="!isUrlModel"
        v-model="lyrJSONString"
        type="textarea"
        rows="4"
        placeholder="请输入多行文本"
      ></el-input>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onUnmounted, ref } from "vue";

// import { Help,Orange } from "@element-plus/icons-vue";
import {
  getOnemap,
  getSubOnemap,
  mapType,
  Icons,
  ILayerType,
  serviceType,
  LayerStorage,
  type IMapLayer,
} from "../onemapkit";
import * as Cesium from "@onemapkit/cesium";

const props = defineProps({
  MapControlName: {
    type: String,
    default: "mainMapControl",
    require: true,
  },
  LayerStore: {
    type: LayerStorage,
    default: null,
  },
});
const _inOnemap = getOnemap(props.MapControlName);

const isCesiumInspector = ref(false);
const isTilesInspector = ref(false);
const isVoxelInspector = ref(false);
const _CustomToolGroup = new Map<
  string,
  Array<{
    name: string;
    isActive: boolean;
    label: string;
    icon: any;
    clickEvent?: any;
  }>
>();

_CustomToolGroup.set("调试工具(3D)", [
  //帧率监听
  {
    name: "Framespersecond",
    isActive: false,
    label: "帧率监听",
    icon: Icons.aliEIP,
    clickEvent: () => {
      if (_inOnemap.MapType.value == mapType.arcgis) return;
      _inOnemap.MapViewer.scene.debugShowFramesPerSecond =
        !_inOnemap.MapViewer.scene.debugShowFramesPerSecond;
      //@ts-ignore
      for (const [key, value] of getSubOnemap()) {
        value.MapViewer.scene.debugShowFramesPerSecond =
          !value.MapViewer.scene.debugShowFramesPerSecond;
      }
    },
  },
  //性能分析
  {
    name: "CesiumInspector",
    isActive: false,
    label: "性能分析",
    icon: Icons.Cpu,
    clickEvent: () => {
      if (_inOnemap.MapType.value == mapType.arcgis) {
        isCesiumInspector.value = false;
        return;
      }
      isCesiumInspector.value = !isCesiumInspector.value;
      if (isCesiumInspector.value) {
        _inOnemap.MapViewer.extend(Cesium.viewerCesiumInspectorMixin);
        //@ts-ignore
        for (const [key, value] of getSubOnemap()) {
          value.MapViewer.extend(Cesium.viewerCesiumInspectorMixin);
        }
      } else {
        DomDestroy("cesium-viewer-cesiumInspectorContainer");
      }
    },
  },
  //模型分析
  {
    name: "TilesInspector",
    isActive: false,
    label: "模型分析",
    icon: Icons.OfficeBuilding,
    clickEvent: () => {
      if (_inOnemap.MapType.value == mapType.arcgis) {
        isTilesInspector.value = false;
        return;
      }
      isTilesInspector.value = !isTilesInspector.value;
      if (isTilesInspector.value) {
        _inOnemap.MapViewer.extend(Cesium.viewerCesium3DTilesInspectorMixin);

        //@ts-ignore
        for (const [key, value] of getSubOnemap()) {
          value.MapViewer.extend(Cesium.viewerCesium3DTilesInspectorMixin);
        }
      } else {
        DomDestroy("cesium-viewer-cesium3DTilesInspectorContainer");
      }
    },
  },
  //Voxel
  {
    name: "VoxelInspector",
    isActive: false,
    label: "Voxel",
    icon: Icons.aliECI,
    clickEvent: () => {
      if (_inOnemap.MapType.value == mapType.arcgis) {
        isVoxelInspector.value = false;
        return;
      }
      isVoxelInspector.value = !isVoxelInspector.value;
      if (isVoxelInspector.value) {
        _inOnemap.MapViewer.extend(Cesium.viewerVoxelInspectorMixin);
        //@ts-ignore
        for (const [key, value] of getSubOnemap()) {
          value.MapViewer.extend(Cesium.viewerVoxelInspectorMixin);
        }
      } else {
        DomDestroy("cesium-viewer-voxelInspectorContainer");
      }
    },
  },
]);
const DomDestroy = (domName: string) => {
  const _dom: any = document.getElementsByClassName(domName);
  _dom.forEach((element: any) => {
    let tomDom = <HTMLImageElement>element;
    if (tomDom) {
      tomDom.innerHTML = "";
      tomDom.textContent = "";
    }
  });
  // const dom = <HTMLImageElement>(
  //   document.getElementsByClassName(
  //     "cesium-viewer-cesiumInspectorContainer"
  //   )[0]
  // );
  // if (dom) {
  //   dom.innerHTML = "";
  //   dom.textContent = "";
  // }
};

//#region  输入资源参数
const isUrlModel = ref(true);
const lyrName = ref("测试");
const lyrMapType = ref();
const lyrLayerType = ref();
const lyrServiceType = ref();
const lyrUrlString = ref(
  "http://localhost/mapdata/3dtiles/wuxiang2/tileset.json"
);
const lyrJSONString = ref(`
{
  "layerid": "10146e3e-1245-47b61-8780-99bdbc285ab7",
  "name": "总部基地倾斜模型",
  "useMapModel": ["cesium"],
  "showInCatalog": true,
  "visible": true,
  "serviceType": "Cesium3DTiles",
  "subLayers": [
    {
      "layerid": "10146e3e-1245-47b61-8780-99bdbc285ab7",
      "name": "总部基地倾斜模型",
      "useMapModel": ["cesium"],
      "showInCatalog": true,
      "visible": true,
      "serviceType": "Cesium3DTiles",
      "url": "http://localhost/mapdata/3dtiles/wuxiang2/tileset.json"
    }
  ]
}
`);
function guid(): any {
  const now = new Date();

  // 获取具体的日期和时间部分
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份从0开始，+1
  const day = now.getDate();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const seconds = now.getSeconds();
  return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;
}
function addOutMapLayer() {
  if (isUrlModel.value) {
    //#region 方法1
    let _option: IMapLayer = {
      //@ts-ignore
      isUrl: true,
      name: lyrName.value,
      serviceType: lyrServiceType.value,
      layerType: lyrLayerType.value,
      url: lyrUrlString.value,
    };
    if (lyrMapType.value == "默认") {
      //@ts-ignore
      // _option["useMapModel"] = [mapType.arcgis,mapType.cesium];
    } else {
      //@ts-ignore
      _option["useMapModel"] = [lyrMapType.value];
    }
    // props.LayerStore.AddLayer(_option);
    //#endregion
    //#region 方法2
    const lyr: IMapLayer = {
      layerid: guid(),
      name: _option.name,
      showInCatalog: true,
      isBaseLayer: false,
      visible: true,
      serviceType: _option.serviceType,
    };
    if (_option?.useMapModel) {
      lyr["useMapModel"] = _option?.useMapModel;
    }
    if (_option?.layerType) {
      lyr["layerType"] = _option?.layerType;
    }
    lyr.subLayers = [{ ...lyr, url: _option?.url }];
    props.LayerStore.AddLayer({
      isUrl: false,
      JSONOBJ: lyr,
    });
    //#endregion

    props.LayerStore.CheckedLayerids.push(lyr.layerid as any);
    _inOnemap.AddLayer(lyr);
  } else {
    const lyrJSONS = JSON.parse(lyrJSONString.value);
    props.LayerStore.AddLayer({
      isUrl: false,
      JSONOBJ: lyrJSONS,
    });
    props.LayerStore.CheckedLayerids.push(lyrJSONS.layerid as any);
    _inOnemap.AddLayer(lyrJSONS);
  }
}
//#endregion

onUnmounted(() => {
  if (_inOnemap.MapType.value == mapType.arcgis) return;
  _inOnemap.MapViewer.scene.debugShowFramesPerSecond =
    !_inOnemap.MapViewer.scene.debugShowFramesPerSecond;
  //@ts-ignore
  for (const [key, value] of getSubOnemap()) {
    value.MapViewer.scene.debugShowFramesPerSecond =
      !value.MapViewer.scene.debugShowFramesPerSecond;
  }
  if (isCesiumInspector.value)
    DomDestroy("cesium-viewer-cesiumInspectorContainer");
  if (isTilesInspector.value)
    DomDestroy("cesium-viewer-cesium3DTilesInspectorContainer");
  if (isVoxelInspector.value)
    DomDestroy("cesium-viewer-voxelInspectorContainer");
});
</script>
<style lang="scss" scoped>
@import "./map-tools.scss";
:deep(.el-form-item) {
  display: flex;
  --font-size: 14px;
  margin-bottom: 1px;
}
:deep(.el-divider--horizontal) {
  display: block;
  height: 1px;
  width: 100%;
  margin: 10px 0;
  border-top: 1px var(--el-border-color) var(--el-border-style);
}

.groupboder2 {
  border: 1px solid rgb(255, 255, 255);
  margin: 2px 5px;
}
.draw-panel {
  margin: 5px 0px 5px 0px;
  .draw-btn {
    width: 50px;
    height: 30px;
    margin-left: 0px;

    .draw-icon {
      width: 22px;
      height: 22px;
      transform: scale(1);
    }
  }
}
</style>
