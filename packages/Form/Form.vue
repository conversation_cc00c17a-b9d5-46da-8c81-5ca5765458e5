<template>
  <el-form :model="props.formDataValue">
    <!-- 1.循环每行 -->
    <template v-for="(item, index) in props.formDataFrame" :key="index">
      <el-row
        :style="
          props.ustyle.rowStyle[index]
            ? props.ustyle.rowStyle[index]
            : props.ustyle.rowStyle[0]
        "
      >
        <el-col>
          <el-space
            wrap
            :size="
              (props.ustyle.colSpace[index]
                ? props.ustyle.colSpace[index]
                : props.ustyle.colSpace[0]) 
            "
          >
            <!-- 2.循环每行内的组件元素 -->
            <template v-for="sunItem in item" :key="sunItem.name">
              <el-form-item
                :style="props.ustyle.formItemStyle || ''"
                :label="sunItem.showLabel === false ? '' : sunItem.label || ''"
                :required="sunItem.required || false"
              >
                <!--0 输入框 input-->
                <template v-if="sunItem.type === 'input'">
                  <el-input
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :placeholder="sunItem.placeholder || ''"
                    :size="sunItem.size || 'default'"
                    :disabled="sunItem.disabled || false"
                    :type="sunItem.textarea || 'input'"
                    :show-password="sunItem.showpassword || false"
                    :maxlength="sunItem.maxlength || ''"
                    :minlength="sunItem.minlength || ''"
                    :readonly="sunItem.readonly || false"
                    :style="sunItem.style || ''"
                  >
                  </el-input>
                </template>
                <!--1 输入框 input-number-->
                <template v-else-if="sunItem.type === 'inputnumber'">
                  <el-input-number
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :name = "sunItem.name || 'inputnumber'"
                    :placeholder="sunItem.placeholder || ''"
                    :min="sunItem.min || 0"
                    :max="sunItem.max || 10"
                    :step="sunItem.step || 1"
                    :precision="sunItem.precision || 0"
                    :size="sunItem.size || 'default'" 
                    :disabled="sunItem.disabled || false"
                    :controls ="sunItem.controls || false"
                    :controls-position = "sunItem.controlsposition || ''"  
                    :readonly="sunItem.readonly || false"
                    :style="sunItem.style || ''"
                  >
                  </el-input-number>
                </template>
                <!--2 下拉框  select-->
                <template v-else-if="sunItem.type === 'select'">
                  <el-select
                    style="width: 100%"
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :placeholder="sunItem.placeholder || '请选择'"
                    :size="sunItem.size || 'default'"
                    :disabled="sunItem.disabled || false"
                    :border="sunItem.border || false"
                    :checked="sunItem.checked || false"
                    :style="sunItem.style || ''"
                  >
                    <el-option
                      v-for="option1 in sunItem.options"
                      :key="option1.value"
                      :label="option1.label"
                      :value="option1.value"
                      :disabled="option1.disabled"
                    >
                    </el-option>
                  </el-select>
                </template>
                <!--3.1 复选框  check-->
                <template v-else-if="sunItem.type === 'check'">
                  <el-checkbox
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :size="sunItem.size || 'default'"
                    :disabled="sunItem.disabled || false"
                    :border="sunItem.border || false"
                    :checked="sunItem.checked || false"
                    :style="sunItem.style || ''"
                  />
                </template>
                <!--3.2 复选框 check-->
                <template v-else-if="sunItem.type === 'checkboxgroup'">
                  <el-checkbox-group
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :size="sunItem.size || 'default'"
                    :border="sunItem.border || false"
                    :style="sunItem.style || ''"
                  >
                    <template v-if="sunItem.subtype === 'checkbox'">
                      <el-checkbox
                        v-for="inItem in sunItem.items"
                        :key="inItem.key"
                        :label="inItem.label"
                        :disabled="inItem.disabled || false"
                        :checked="inItem.checked || false"
                      >
                      </el-checkbox>
                    </template>
                    <template v-else>
                      <el-checkbox-button
                        v-for="inItem in sunItem.items"
                        :key="inItem.key"
                        :label="inItem.label"
                        :disabled="inItem.disabled || false"
                        :checked="inItem.checked || false"
                      >
                      </el-checkbox-button>
                    </template>
                  </el-checkbox-group>
                </template>
                <!--4 单选框 radio-->
                <template v-else-if="sunItem.type === 'radio'">
                  <el-radio-group
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :size="sunItem.size || 'default'"
                    :style="sunItem.style || ''"
                  >
                    <el-radio
                      v-for="rdoItem in sunItem.group"
                      :label="rdoItem.label || ''"
                      :disabled="rdoItem.disabled || false"
                      :style="rdoItem.style || ''"
                    ></el-radio>
                  </el-radio-group>
                </template>
                <!--5 Slider 滑块 slider-->
                <template v-else-if="sunItem.type === 'slider'">
                  <el-slider
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :min="sunItem.min || -10"
                    :max="sunItem.max || 10"
                    :step="sunItem.step || 1"
                    :disabled="sunItem.disabled || false"
                    :show-input="sunItem.showinput || false"
                    :show-input-controls="sunItem.showinputcontrols || false"
                    :size="sunItem.size || 'default'"
                    :input-size="sunItem.inputsize || 'default'"
                    :vertical="sunItem.vertical || false"
                    :height="sunItem.vertical ? sunItem.height || '100px' : ''"
                    :debounce="sunItem.debounce || 1000"
                    :style="
                      sunItem.style
                        ? sunItem.style
                        : 'width:' + (sunItem.width ? sunItem.width : '300px')
                    "
                  />
                </template>
                <!--6 Switch 开关switch -->
                <template v-else-if="sunItem.type === 'switch'">
                  <el-switch
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :size="sunItem.size || 'default'"
                    :disabled="sunItem.disabled || false"
                    :active-text="sunItem.activetext || '开启'"
                    :inactive-text="sunItem.inactivetext || '关闭'"
                    :active-value="sunItem.activetext || true"
                    :inactive-value="sunItem.inactivetext || false"
                    :inline-prompt="sunItem.inlineprompt || true"
                    :validate-event="sunItem.validateevent || true"
                    :style="sunItem.style || ''"
                  />
                </template>
                <!--7 DatePicker 日期时间选择器 -->
                <template v-else-if="sunItem.type === 'datepicker'">
                  <el-date-picker
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :readonly="sunItem.readonly || false"
                    :disabled="sunItem.disabled || false"
                    :size="sunItem.size || 'default'"
                    :editable="sunItem.editable || true"
                    :placeholder="sunItem.placeholder || '选择日期'"
                    :start-placeholder="sunItem.startplaceholder || '开始日期'"
                    :end-placeholder="sunItem.endplaceholder || '结束日期'"
                    :type="sunItem.utype || 'date'"
                    :format="sunItem.format || 'YYYY-MM-DD'"
                    :value-format="sunItem.valueformat || sunItem.format"
                    :default-time="sunItem.defaulttime || ''"
                    :style="sunItem.style || ''"
                  />
                </template>
                <!--8 TimePicker 时间选择器 -->
                <template v-else-if="sunItem.type === 'timepicker'">
                  <el-time-picker
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :readonly="sunItem.readonly || false"
                    :disabled="sunItem.disabled || false"
                    :size="sunItem.size || 'default'"
                    :editable="sunItem.editable || true"
                    :placeholder="sunItem.placeholder || '选择日期'"
                    :start-placeholder="sunItem.startplaceholder || '开始日期'"
                    :end-placeholder="sunItem.endplaceholder || '结束日期'"
                    :type="sunItem.utype || 'datetime'"
                    :format="sunItem.format || 'YYYY-MM-DD HH:mm:ss'"
                    :default-value="sunItem.defaultvalue || null"
                    :style="sunItem.style || ''"
                  />
                </template>
                <!--9 TimeSelect 时间选择 -->
                <template v-else-if="sunItem.type === 'timeselect'">
                  <el-time-select
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :disabled="sunItem.disabled || false"
                    :editable="sunItem.editable || true"
                    :size="sunItem.size || 'default'"
                    :placeholder="sunItem.placeholder || '选择时间'"
                    :start="sunItem.start || '00:00:00'"
                    :end="sunItem.end || '24:59:59'"
                    :step="sunItem.step || '00:01:00'"
                    :min-time="sunItem.mintime || '00:00:00'"
                    :max-time="sunItem.maxtime || '24:59:59'"
                    :format="sunItem.format || ''"
                    :style="sunItem.style || ''"
                  />
                </template>
                <!--10 Transfer 穿梭框 -->
                <template v-else-if="sunItem.type === 'transfer'">
                  <el-space wrap :direction="sunItem.direction || 'vertical'">
                    <div>
                      <el-text
                        v-if="sunItem.transferLabel"
                        alignment="flex-start"
                        class="mx-1"
                        :size="sunItem.labelSize || 'default'"
                        >{{ sunItem.label || "" }}</el-text
                      >
                    </div>
                    <el-transfer
                      v-model="props.formDataValue[sunItem.name]"
                      label=""
                      @change="
                        sunItem.changeEvent
                          ? sunItem.changeEvent(
                              props.formDataValue[sunItem.name]
                            )
                          : null
                      "
                      :data="sunItem.data || ''"
                      :filterable="sunItem.filterable || true"
                      :filter-placeholder="
                        sunItem.filterplaceholder || '关键字'
                      "
                      :filter-method="sunItem.filterMethod || null"
                      :titles="sunItem.titles || ['数据源', '选取项']"
                      :validate-event="sunItem.validateevent || true"
                      :style="sunItem.style || ''"
                    />
                  </el-space>
                </template>
                <!--11 color-picker 选色 -->
                <template v-else-if="sunItem.type === 'colorpicker'">
                  <el-color-picker
                    v-model="props.formDataValue[sunItem.name]"
                    @change="
                      sunItem.changeEvent
                        ? sunItem.changeEvent(props.formDataValue[sunItem.name])
                        : null
                    "
                    :label="sunItem.label || ''"
                    :disabled="sunItem.disabled || false"
                    :size="sunItem.size || 'default'"
                    :show-alpha="sunItem.showalpha || false"
                    :color-format="sunItem.colorformat || ''"
                    :validate-event="sunItem.validateevent || true"
                    :style="sunItem.style || ''"
                  />
                </template>
                <!--12 button 按钮  -->
                <template v-else-if="sunItem.type === 'button'">
                  <el-space wrap>
                    <div
                      :style="
                        sunItem.space ? 'width:' + sunItem.space : 'width:1px'
                      "
                    ></div>
                    <el-button
                      @click="
                        sunItem.changeEvent
                          ? sunItem.changeEvent(
                              props.formDataFrame,
                              props.formDataValue
                            )
                          : null
                      "
                      :size="sunItem.size || 'default'"
                      :disabled="sunItem.disabled || false"
                      :type="sunItem.utype || 'primary'"
                      :plain="sunItem.plain || false"
                      :text="sunItem.text || false"
                      :bg="sunItem.bg || false"
                      :link="sunItem.link || false"
                      :round="sunItem.round || false"
                      :circle="sunItem.circle || false"
                      :color="sunItem.color || ''"
                      :dark="sunItem.dark || false"
                      :auto-insert-space="sunItem.autoinsertspace || false"
                      :style="sunItem.style || ''"
                      >{{ sunItem.label ? sunItem.label : "按钮" }}
                    </el-button>
                  </el-space>
                </template>
              </el-form-item>
            </template>
          </el-space>
        </el-col>
      </el-row>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
const props = defineProps({
  formDataValue: {
    type: Object,
    default: {},
    require: true,
  },
  formDataFrame: {
    type: Array<any>,
    default: [],
    require: true,
  },
  ustyle: {
    type: Object,
    default: {
      formItemStyle: `margin: 1px 10px`,
      rowStyle: [`margin:0px`],
      colSpace: [20],
    },
    require: false,
  },
});

console.log("*****************props.formDataValue ", props.formDataValue);

onMounted(() => {});
</script>
<style scoped>
.spc {
  margin: 1px 150px;
}
</style>
