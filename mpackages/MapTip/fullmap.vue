<template>
    <el-tooltip
    :content="
      PropStore && PropStore.fullExtent && PropStore.fullExtent.toolbar
        ? PropStore.fullExtent.toolbar
        : '定位到全幅'
    "
    placement="left"
    effect="light"
  >
    <div  class="map-switch" @click="RestoreFullMap()">
      <i v-html="Icons.locate"></i>
      <span style="font-size: 12px; caret-color: transparent">全幅</span>
    </div>
  </el-tooltip>
  </template>
  
  <script setup lang="ts">
  import {Icons, getOnemap} from "../monemapkit";
  const props = defineProps({
    MapControlName: {
      type: String,
      default: "mainMapControl",
      require: true,
    },
  PropStore: {
    type: Object as any,
    default: null,
  },
  });
  //1.初始化 Onemap、Options
  const _Onemap = getOnemap(props.MapControlName);  
  function RestoreFullMap(){ 
    _Onemap.gotoExtent(props.PropStore.fullExtent);
    // FullMapEvent(props.MapControlName, _Onemap.MapType);
  }
  </script>
  <style lang="less" scoped>
  .common-tool-panel { 
  
    .map-switch {
      margin-top: 8px;
      width: 45px;
      margin-left: 8px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);
      text-align: center;
      cursor: pointer;
    }
  }
  </style>
  