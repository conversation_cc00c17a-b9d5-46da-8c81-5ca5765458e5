/**
 * @fileOverview 上传文件转换工具，因为Cesium可以直接识别GeoJSON格式，所以统一转换为GeoJSON格式
 */
import { convertToGeoJSONCoordinates } from "./convert";

/**
 * 坐标文本转GeoJSON，支持坐标系转换，如果不填写坐标系，则默认不转换
 * 每个图形以-----------------分割，每个图形只有x、y表示点；多个坐标组合但首尾坐标不同则为线；多个坐标但是首尾相同则为面
 * @param data 坐标文本数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid 转换后的坐标系
 */
export function textToGeoJSON(
  data: string,
  sourceWkid?: number,
  toWkid?: number
) {
  const geoJSON: any = {
    type: "FeatureCollection",
    features: [],
  };
  // 多图形将文本数据按照-----------------分割
  const geometrys = data.split("----------------");
  geometrys.forEach((geometry) => {
    let lines = geometry.split("\n");
    // 如果只有一个行，则为点
    if (lines.length === 1) {
      const feature: any = {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [],
        },
        properties: {},
      };
      const xy = lines[0].split(",");
      const x = Number(xy[1].trim());
      const y = Number(xy[2].trim());
      feature.geometry.coordinates = [x, y];
      if (sourceWkid && toWkid && sourceWkid !== toWkid) {
        let geo = convertToGeoJSONCoordinates(
          { type: "point", x: x, y: y },
          sourceWkid,
          toWkid
        );
        feature.geometry.coordinates = geo;
      }
      geoJSON.features.push(feature);
    } else {
      lines = lines.filter((x) => x.includes(","));
      // 如果首尾坐标相同，则为面
      if (lines[0] === lines[lines.length - 1]) {
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: [[]],
          },
          properties: {},
        };
        lines.forEach((line) => {
          const xy = line.split(",");
          const x = Number(xy[1].trim());
          const y = Number(xy[2].trim());
          feature.geometry.coordinates[0].push([x, y]);
        });
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "polygon", rings: feature.geometry.coordinates },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      } else {
        // 否则为线
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: [],
          },
          properties: {},
        };
        lines.forEach((line) => {
          const xy = line.split(",");
          if (xy.length < 3) return;
          const x = Number(xy[1].trim());
          const y = Number(xy[2].trim());
          feature.geometry.coordinates.push([x, y]);
        });
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "polyline", paths: feature.geometry.coordinates },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      }
    }
  });
  return geoJSON;
}

/**
 * arcgis json数据转GeoJSON，支持坐标系转换，如果不填写坐标系，则默认不转换
 * @param data arcgis json数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid  转换后的坐标系
 * @returns
 */
export function arcgisJsonToGeoJson(
  data: string,
  //@ts-ignore
  sourceWkid?: number,
  toWkid?: number
) {
  const geoJSON: any = {
    type: "FeatureCollection",
    features: [],
  };

  let arcgisJson = JSON.parse(data);

  if (arcgisJson.geometryType === "esriGeometryPoint") {
    const feature: any = {
      type: "Feature",
      geometry: {
        type: "Point",
        coordinates: [],
      },
      properties: {},
    };
    feature.geometry.coordinates = [
      arcgisJson.features[0].geometry.x,
      arcgisJson.features[0].geometry.y,
    ];
    if (arcgisJson.spatialReference.wkid !== toWkid) {
      let geo = convertToGeoJSONCoordinates(
        {
          type: "point",
          x: arcgisJson.geometry.coordinates[0],
          y: arcgisJson.geometry.coordinates[1],
        },
        Number(arcgisJson.spatialReference.wkid),
        Number(toWkid)
      );
      feature.geometry.coordinates = geo;
    }
    geoJSON.features.push(feature);
  } else if (arcgisJson.geometryType === "esriGeometryMultipoint") {
    const feature: any = {
      type: "Feature",
      geometry: {
        type: "MultiPoint",
        coordinates: [],
      },
      properties: {},
    };

    arcgisJson.features.forEach((point: any) => {
      //@ts-ignore
      let xy = [point.geometry.x, point.geometry.y];
      if (arcgisJson.spatialReference.wkid !== toWkid) {
        xy = convertToGeoJSONCoordinates(
          { type: "point", x: point.geometry.x, y: point.geometry.y },
          Number(arcgisJson.spatialReference.wkid),
          Number(toWkid)
        );
      }
      feature.geometry.coordinates.push([point.geometry.x, point.geometry.y]);
    });
    geoJSON.features.push(feature);
  } else if (arcgisJson.geometryType === "esriGeometryPolyline") {
    const feature: any = {
      type: "Feature",
      geometry: {
        type: "LineString",
        coordinates: [],
      },
      properties: {},
    };

    arcgisJson.features.forEach((item: any) => {
      let geometry = item.geometry;
      if (arcgisJson.spatialReference.wkid !== toWkid) {
        geometry = convertToGeoJSONCoordinates(
          { type: "polyline", paths: geometry.paths },
          Number(arcgisJson.spatialReference.wkid),
          Number(toWkid)
        );
      }
      feature.geometry.coordinates.push(geometry);
    });
  } else if (arcgisJson.geometryType === "esriGeometryPolygon") {
    const feature: any = {
      type: "Feature",
      geometry: {
        type: "Polygon",
        coordinates: [],
      },
      properties: {},
    };

    arcgisJson.features.forEach((item: any) => {
      let geometry = item.geometry;
      if (arcgisJson.spatialReference.wkid !== toWkid) {
        geometry = convertToGeoJSONCoordinates(
          { type: "polygon", rings: geometry.rings },
          Number(arcgisJson.spatialReference.wkid),
          Number(toWkid)
        );
      }
      feature.geometry.coordinates.push(geometry);
    });
  }

  return geoJSON;
}

/**
 * 报部坐标转换为GeoJSON坐标
 * @param data GeoJSON数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid  转换后的坐标系
 * @returns
 */
export function baoBuJsonToGeoJson(
  data: string,
  sourceWkid?: number,
  toWkid?: number
) {
  const geoJSON: any = {
    type: "FeatureCollection",
    features: [],
  };

  // 寻找报部坐标图形部分
  let coords = data.split("\n");
  ("");
  let start = coords.findIndex((x) => x.includes("地块坐标"));
  if (start === -1) return;

  coords = coords.slice(start + 1);
  if (coords.length === 0) return;
  if (coords[0].includes("@")) coords = coords.slice(1);

  // 解析出图形部分
  let geometrys = {} as any;
  coords.forEach((line) => {
    let l = line.split(",");
    l = l.splice(1);
    if (l.length < 3) return;
    if (!geometrys[l[0]]) geometrys[l[0]] = [];
    try {
      geometrys[l[0]].push([parseFloat(l[1]), parseFloat(l[2])]);
    } catch (error) {
      console.log(error);
      return;
    }
  });

  for (let key in geometrys) {
    let lines = geometrys[key];
    // 如果只有一个行，则为点
    if (lines.length === 1) {
      const feature: any = {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [],
        },
        properties: {},
      };
      feature.geometry.coordinates = lines[0];
      if (sourceWkid && toWkid && sourceWkid !== toWkid) {
        let geo = convertToGeoJSONCoordinates(
          { type: "point", x: lines[0][0], y: lines[0][1] },
          sourceWkid,
          toWkid
        );
        feature.geometry.coordinates = geo;
      }
      geoJSON.features.push(feature);
    } else {
      // 如果首尾坐标相同，则为面
      if (lines[0] === lines[lines.length - 1]) {
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: [[]],
          },
          properties: {},
        };

        feature.geometry.coordinates[0] = lines;
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "polygon", rings: [lines] },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      } else {
        // 否则为线
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: [],
          },
          properties: {},
        };

        feature.geometry.coordinates = lines;
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "polyline", paths: [lines] },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      }
    }
  }

  return geoJSON;
}

/**
 * GeoJSON数据转换为GeoJSON坐标
 * @param data GeoJSON数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid  转换后的坐标系
 * @returns
 */
export function geoJSONToGeoJSON(
  data: string,
  sourceWkid?: number,
  toWkid?: number
) {
  let geoJSON = JSON.parse(data);
  // 坐标转换
  if (sourceWkid && toWkid && sourceWkid !== toWkid) {
    // 判断是否为FeatureCollection类型，如果是FeatureCollection类型，需要遍历features
    if (geoJSON.type === "FeatureCollection") {
      geoJSON.features.forEach((feature: any) => {
        feature.geometry.coordinates = convertToGeoJSONCoordinates(
          feature.geometry.coordinates,
          sourceWkid,
          toWkid
        );
      });
    } else {
      geoJSON.features.forEach((feature: any) => {
        feature.geometry.coordinates = convertToGeoJSONCoordinates(
          feature.geometry.coordinates,
          sourceWkid,
          toWkid
        );
      });
    }
    return geoJSON;
  } else return geoJSON;
}

/**
 * shp数据转GeoJSON，支持坐标系转换，如果不填写坐标系，则默认不转换
 * @param data GeoJSON数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid  转换后的坐标系
 * @returns
 */
export function shpToGeoJSON(
  data: any[],
  sourceWkid?: number,
  toWkid?: number
) {
  const geoJSON: any = {
    type: "FeatureCollection",
    features: [],
  };
  if (data && data.length > 0) {
    data.forEach((item: any) => {
      if (item.type.toLocaleLowerCase() == "point") {
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [],
          },
          properties: {},
        };
        feature.geometry.coordinates = [item.coordinates.x, item.coordinates.y];
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "point", x: item.coordinates.x, y: item.coordinates.y },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      } else if (item.type.toLocaleLowerCase() == "polyline") {
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: [],
          },
          properties: {},
        };

        feature.geometry.coordinates = item.coordinates;
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "polyline", paths: feature.geometry.coordinates },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      } else if (item.type.toLocaleLowerCase() == "polygon") {
        const feature: any = {
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: [[]],
          },
          properties: {},
        };

        feature.geometry.coordinates = item.coordinates;
        if (sourceWkid && toWkid && sourceWkid !== toWkid) {
          let geo = convertToGeoJSONCoordinates(
            { type: "polygon", rings: feature.geometry.coordinates },
            sourceWkid,
            toWkid
          );
          feature.geometry.coordinates = geo;
        }
        geoJSON.features.push(feature);
      }
    });
  }

  return geoJSON;
}

/**
 * 手持GPS文件*.dat转GeoJSON，支持坐标系转换，如果不填写坐标系，则默认不转换
 * @param data GeoJSON数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid  转换后的坐标系
 * @returns
 */
export function datToGeoJSON(
  data: string,
  sourceWkid?: number,
  toWkid?: number
) {
  const geoJSON: any = {
    type: "FeatureCollection",
    features: [],
  };
  let lines = data.split("\r\n");
  lines = lines.filter((x) => x.includes(","));

  // 如果只有一个行，则为点
  if (lines.length === 1) {
    const feature: any = {
      type: "Feature",
      geometry: {
        type: "Point",
        coordinates: [],
      },
      properties: {},
    };
    const xyz = lines[0].split(",");
    const x = Number(xyz[2].trim());
    const y = Number(xyz[3].trim());
    feature.geometry.coordinates = [x, y];
    if (sourceWkid && toWkid && sourceWkid !== toWkid) {
      let geo = convertToGeoJSONCoordinates(
        { type: "point", x: x, y: y },
        sourceWkid,
        toWkid
      );
      feature.geometry.coordinates = geo;
    }
    geoJSON.features.push(feature);
  } else {
    lines = lines.filter((x) => x.includes(","));
    // 如果首尾坐标相同，则为面
    if (lines[0] === lines[lines.length - 1]) {
      const feature: any = {
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: [[]],
        },
        properties: {},
      };
      lines.forEach((line) => {
        const xy = line.split(",");
        const x = Number(xy[2].trim());
        const y = Number(xy[3].trim());
        feature.geometry.coordinates[0].push([x, y]);
      });
      if (sourceWkid && toWkid && sourceWkid !== toWkid) {
        let geo = convertToGeoJSONCoordinates(
          { type: "polygon", rings: [feature.geometry.coordinates] },
          sourceWkid,
          toWkid
        );
        feature.geometry.coordinates = geo;
      }
      geoJSON.features.push(feature);
    } else {
      // 否则为线
      const feature: any = {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [],
        },
        properties: {},
      };
      lines.forEach((line) => {
        const xy = line.split(",");
        if (xy.length < 3) return;
        const x = Number(xy[2].trim());
        const y = Number(xy[3].trim());
        feature.geometry.coordinates.push([x, y]);
      });
      if (sourceWkid && toWkid && sourceWkid !== toWkid) {
        let geo = convertToGeoJSONCoordinates(
          { type: "polyline", paths: [feature.geometry.coordinates] },
          sourceWkid,
          toWkid
        );
        feature.geometry.coordinates = geo;
      }
      geoJSON.features.push(feature);
    }
  }
  return geoJSON;
}

/**
 * GeoJSON数据转换ArcGIS JS对象
 * @param data GeoJSON数据
 * @param sourceWkid 坐标文本数据的坐标系
 * @param toWkid  转换后的坐标系
 * @returns
 */
export function geoJSONToArcGISJson(data: any) {
  if (data && data.type && data.type == "FeatureCollection") {
    let geometry = [] as any[];
    data.features.forEach((feature: any) => {
      if (feature.type.toLocaleLowerCase() == "feature") {
        let geo = feature.geometry;
        if (geo.type.toLocaleLowerCase() == "point") {
          geometry.push({
            type: "point",
            x: geo.coordinates[0],
            y: geo.coordinates[1],
          });
        } else if (geo.type.toLocaleLowerCase() == "polyline") {
          geometry.push({
            type: "polyline",
            paths: geo.coordinates,
          });
        } else if (geo.type.toLocaleLowerCase() == "polygon") {
          geometry.push({
            type: "polygon",
            rings: geo.coordinates,
          });
        }
      }
    });
    return geometry;
  } else if (data && data.type && data.type.toLocaleLowerCase() == "feature") {
    let geo = data.geometry;
    if (geo.type.toLocaleLowerCase() == "point") {
      return {
        type: "point",
        x: geo.coordinates[0],
        y: geo.coordinates[1],
      };
    } else if (geo.type.toLocaleLowerCase() == "polyline") {
      return {
        type: "polyline",
        paths: geo.coordinates,
      };
    } else if (geo.type.toLocaleLowerCase() == "polygon") {
      return {
        type: "polygon",
        rings: geo.coordinates,
      };
    }
  }

  return [];
}
