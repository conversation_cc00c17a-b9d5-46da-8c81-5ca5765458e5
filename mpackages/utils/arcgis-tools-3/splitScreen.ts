import { throttle } from "lodash";
import { nextTick } from "vue";
//分屏数量SplitScreenNum,枚举常量，目前支持4个分屏
enum SplitScreenNum {
  ONE = 1,
  TWO = 2,
  THREE = 3,
  FOUR = 4,
}
const defaultContainerId = ["secondViewer", "thirdViewer", "fourthViewer"];
// 创建容器
function creatContainer(viewer: any, id: string) {
  const container = document.createElement("div");
  container.id = id;
  container.className = id + " map";
  viewer.container.parentNode?.appendChild(container);
  return container;
}
// 移除容器
function removeContainer(viewer: any, container: any) {
  if (container) {
    viewer.container.parentNode?.removeChild(container);
  }
}
export default class Arcgis3SplitScreenExt {
  public _mainViewer: any;
  public _subViewers: any;
  public _allViewers: any;
  private _subContainers: any;
  // private _mapHandlers: any;
  private _removeCallback: any;
  private _curKey: string;
  private _esriObj: any;
  private _mode: number;
  private _mainViewerInfors: {
    className: string;
    parentNodeClassName: string;
  }; 

  /**
   * 分屏对比工具类
   * @constructor
   * @alias SplitScreen
   *
   * @param {any} viewer 主视图对象
   * @param {Object} [option] 包含以下参数的对象
   * @param {Object} [option.mode = SplitScreenMode.TWO] 分屏的模式
   * @param {Object} [option.BaseMapLayer] 初始化时子视图的底图信息，格式为统一的IMapLayer格式
   */
  constructor(viewer: any, option: any = {}, esriObj: any) {
    this._mainViewer = viewer;
    this._mode = option.mode || SplitScreenNum.ONE;
    console.log(
      "splitscreen, mainviewer container",
      this._mainViewer.container.parentNode,
      this._mainViewer
    );
    this._subContainers = {}; // 子视图container
    this._subViewers = {}; // 子视图viewer
    // this._mapHandlers = {}; // 地图事件
    this._removeCallback = {}; // 移除事件回调函数
    this._curKey = "main"; // 当前的索引
    this._esriObj = esriObj;
    this._allViewers = {};

    //1.存储旧的信息
    this._mainViewerInfors = {
      className: this._mainViewer.container.className,
      parentNodeClassName: (this._mainViewer.container.parentNode as any)
        .className,
    };
    
    //2.获取底图和地形
    // if (option["BaseImageryProvider"]) {
    //   this._BaseImageryProvider = option["BaseImageryProvider"];
    // } else {
    //   for (let i = 0; i < viewer.imageryLayers.length; i++) {

    //     let lyr = viewer.imageryLayers.get(i);
    //     if (lyr["_isBaseLayer"]) {
    //       this._BaseImageryProvider = lyr["_imageryProvider"];

    //       break;
    //     }
    //   }
    // }
    //3.更改主视图
    this._mainViewer.container.className = "mainViewer map";
    (this._mainViewer.container.parentNode as any).classList.add(
      "SplitScreen" + this._mode
    );
    // "SplitScreen" + this._mode;
    nextTick(() => {
      this._mainViewer.resize();
      this._mainViewer.reposition();
    });
    //4.子视图
    const len = this._mode - 1;
    for (let i = 0; i < len; i++) {
      const num = i + 1;
      const key = "sub" + num;
      const container = creatContainer(this._mainViewer, defaultContainerId[i]);
      const mapViewer = this._createViewer(container);
      nextTick(() => {
        mapViewer.resize();
      });
      // mapViewer.resize();
      this._subContainers[key] = container;
      this._subViewers[key] = mapViewer;
    }
    this._linkage();
  }
  getSubViewer(idx: number) {
    if (this._subViewers) {
      return this._subViewers["sub" + idx];
    } else {
      return undefined;
    }
  }

  /**
   * 更改分屏模式
   * @param {SplitScreenMode} mode 新的分屏类型
   */
  changeMode(mode: number) { 
    if (this._subViewers === undefined) return;
    if (mode === this._mode) return;
    (this._mainViewer.container.parentNode as any).className =
      "SplitScreen" + mode;
    if (mode < this._mode) {
      // 新的分屏数量少于当前分屏数量
      for (let i = mode; i < this._mode; i++) {
        const key = "sub" + i;
        removeContainer(this._mainViewer, this._subContainers[key]);
        // 销毁多余的视图释放内存
        this._subViewers[key].destroy();
        this._subViewers[key] = null;
        delete this._subViewers[key];
        this._subContainers[key] = null;
        delete this._subContainers[key];
      }
    } else {
      // 新的分屏数量高于当前分屏数量
      for (let i = this._mode; i < mode; i++) {
        const container = creatContainer(
          this._mainViewer,
          defaultContainerId[i - 1]
        );
        const mapViewer = this._createViewer(container);
        const key = "sub" + i;
        this._subContainers[key] = container;
        this._subViewers[key] = mapViewer;
      }
    }

    this._mode = mode;
    this._mainViewer.resize();
    for (const key in this._subViewers) {
      this._subViewers[key].resize();
    }
    this._linkage();
    return this;
  }

  /**
   * 销毁释放内存
   */
  destroy() {
    this._mainViewer.container.className = this._mainViewerInfors.className;
    (this._mainViewer.container.parentNode as any).className =
      this._mainViewerInfors.parentNodeClassName;
    this._mainViewer.resize();
    this._mainViewer.reposition();
    this._clearLinkage();
    for (let key in this._subViewers) {
      this._subViewers[key].destroy();
      delete this._subViewers[key];
    }
    for (let key in this._subContainers) {
      removeContainer(this._mainViewer, this._subContainers[key]);
      delete this._subContainers[key];
    }
    this._mode = 1;
    delete this._subViewers;
    this._subViewers = {};
    delete this._subContainers;
    this._subContainers = {};
  }
  //同步其他视图的extent
  updateOtherExtent(
    evt: any,
    curKey: any,
    extent: any,
    allViewers: any,
    globalCurKey: any
  ) {
    if (evt && curKey == globalCurKey) { 
      for (const changeKey in allViewers) {
        if (curKey !== changeKey) {
          allViewers[changeKey].setExtent(extent);
        }
      }
    }
  }
  // 建立视图之间的同步链接
  private _linkage() {
    this._clearLinkage();
    this._allViewers = {
      main: this._mainViewer,
      ...this._subViewers,
    };
    let that = this;
    const throttleUpdateOtherExtent = throttle(this.updateOtherExtent, 100);
    for (const key in this._allViewers) {
      const viewer = this._allViewers[key];
      this._esriObj.esriOn(viewer, "mouse-over", () => {
        this._curKey = key;
      });
      const removeCallback = this._esriObj.esriOn(
        viewer,
        "extent-change",
        (evt: any) => {
          throttleUpdateOtherExtent(
            evt,
            key,
            viewer.extent,
            that._allViewers,
            that._curKey
          );
        }
      );
      this._removeCallback[key] = removeCallback;
    }
  }

  // 取消所有链接
  private _clearLinkage() {
    for (const key in this._allViewers) {
      this._removeCallback[key].remove();
    }
  }

  // 创建Viewer
  private _createViewer(container: any) {
    const mapViewer = new this._esriObj.Map(container.id, {
      extent: this._mainViewer.extent,
      autoResize: true,
      fitExtent: true,
      logo: false,
      slider: false,
      navigationMode: "css-transforms",
      spatialReference: new this._esriObj.SpatialReference({ wkid: 4490 }),
    });
    return mapViewer;
  }
}
