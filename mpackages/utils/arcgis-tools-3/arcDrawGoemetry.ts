// import type { PropType } from "vue";
//@ts-ignore
import { geometryType, type IToolItemType } from "../../monemapkit";

export const DrawGeometry = {
  getType: (arcgisTools: any, _value: any): any => {
    switch (_value["geometry"].type) {
      case "point": {
        return _value;
      }
      case "polyline": {
        return _value;
      }
      case "polygon": {
        return _value;
      }
      case "circle": {
        let geo = arcgisTools._esri.Polygon({
          rings: _value["geometry"]["rings"],
          spatialReference: new arcgisTools._esri.SpatialReference({
            wkid: 4490,
          }),
        });
        let center = geo.getExtent().getCenter();
        _value["geometry"]["center"] = [center.x, center.y];
        return _value;
      }
      case "rectangle": {
        return _value;
      }
      default:
        break;
    }
  },
  pushGeometryInfo(
    sender: IToolItemType | any,
    arcgisTools: any,
    geoDataKeys: Array<string>,
    geoDataValue: Map<string, any>,
    key: string,
    value: any
  ) {
    if (geoDataKeys.includes(key) == false) {
      geoDataKeys.push(key);
    }
    this.getType(arcgisTools, value);
    geoDataValue.set(key, value);

    if (sender.ToolButtonClickEvent) {
      sender.ToolButtonClickEvent({
        GeoDataKeys: geoDataKeys,
        GeoDataValue: geoDataValue,
        Sender: sender,
        Options: {
          geometry: value,
          id: key,
          arcTools: arcgisTools,
        },
      });
    }
  },
  DrawPoint(arcgisTools: any, CallBack: any, Options?: any) {
    const key = Options?.id
      ? Options.id
      : "pt" + new Date().getTime().toString();

    arcgisTools.drawPoint(
      { ...{ id: key }, ...(Options?.attributes || {}) },
      (_geometry: any) => {
        _geometry.type = geometryType.point;
        if (CallBack)
          CallBack({
            id: key,
            geometry: _geometry,
            label: key,
            type: _geometry.type,
          });
      }
    );
  },
  DrawPolyline(arcgisTools: any, CallBack: any, Options?: any) {
    const key = Options?.id
      ? Options.id
      : "pl" + new Date().getTime().toString();

    arcgisTools.drawLine(
      { ...{ id: key }, ...(Options?.attributes || {}) },
      (_geometry: any) => {
        _geometry.type = geometryType.polyline;
        if (CallBack)
          CallBack({
            id: key,
            geometry: _geometry,
            label: key,
            type: _geometry.type,
          });
      }
    );
  },
  DrawPolygon(arcgisTools: any, CallBack: any, Options?: any) {
    const key = Options?.id
      ? Options.id
      : "pg" + new Date().getTime().toString();

    arcgisTools.drawPolygon(
      { ...{ id: key }, ...(Options?.attributes || {}) },
      (_geometry: any) => {
        _geometry.type = geometryType.polygon;
        if (CallBack)
          CallBack({
            id: key,
            geometry: _geometry,
            label: key,
            type: _geometry.type,
          });
      }
    );
  },
  DrawCircle(arcgisTools: any, CallBack: any, Options?: any) {
    const key = Options?.id
      ? Options.id
      : "cc" + new Date().getTime().toString();

    arcgisTools.drawCircle(
      { ...{ id: key }, ...(Options?.attributes || {}) },
      (_geometry: any) => {
        _geometry.type = geometryType.circle;
        if (CallBack)
          CallBack({
            id: key,
            geometry: _geometry,
            label: key,
            type: _geometry.type,
          });
      }
    );
  },
  DrawRectangle(arcgisTools: any, CallBack: any, Options?: any) {
    const key = Options?.id
      ? Options.id
      : "rc" + new Date().getTime().toString();
    arcgisTools.drawRectangle(
      { ...{ id: key }, ...(Options?.attributes || {}) },
      (_geometry: any) => {
        _geometry.type = geometryType.rectangle;
        if (CallBack)
          CallBack({
            id: key,
            geometry: _geometry,
            label: key,
            type: _geometry.type,
          });
      }
    );
  },
};
