<template>
  <div class="map"  >
    <MapControl
      ref="MapControlRef"
      :PropStore="PropStore" :LayerStore="layerStore" 
    ></MapControl> 
    
    <LayerTreePop  :PropStore="PropStore"
    MapControlName="mainMapControl"
      :LayerStore="layerStore" :MapTreeHeight="400" /> 
    <ToolsBox
      v-show="isShowMapToolBox"
      MapControlName="mainMapControl"
      :PropStore="PropStore"
      :LayerStore="layerStore"
      @ToolButotnClickEvent="_ToolButotnClickEvent"
    />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref,  defineAsyncComponent, markRaw } from "vue";
import * as Cesium from "@onemapkit/cesium";

import {
  MapControl, 
  LayerTreePop,
  ToolsBox,
  InitMapControl,
} from "../../onemapkit/Build/onemapkit.es";
import "../../onemapkit/Build/onemapkit.css";  
 
  
import "element-plus/es/components/option/style/css";

Cesium.Ion.defaultAccessToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjZjViZWZjMC0zMmUxLTQ5NWEtYjZkNS0wM2VjMmM1NWE1YTgiLCJpZCI6OTEwODksImlhdCI6MTY1MDc5MjI2MH0.IhkngSXwFixfux7Z4HBWmAw-pFhqfDzmyUO0v3lLy_4";
//第一步： 导入图层参数
import {
  _Onemap, 
  PropStore,
  layerStore,
  ServiceUrl,
  BaseMapLayerUrl,
  CustomTools,
} from "../../doc/layer";

const _Options = {
  MapControlName: "mainMapControl",
  BASE_URL: "ThirdParty",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  MapTools: {
    CustomTools: CustomTools,
    CustomToolSize: {
      width: 400,
      height: 620,
    },
  },
};
InitMapControl(_Onemap, _Options);
 

const isShowMapToolBox = ref(true);
 
const _ToolButotnClickEvent = (avg: any): any => {
  console.log("=====avg", avg);
  switch (avg.toolItem.name) {
    case "splitscreen":
      {
        // isShowMapToolBox.value = !avg.state;
      }
      break;
  }
};
//mounted
onMounted(async () => {
 
}); 
 
</script>
<style lang="scss" scoped>
.map {
  height: 100%;
  width: 100%;
  position: relative;
}
</style>
