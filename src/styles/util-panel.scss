@import './global.scss';
.util-panel {
  position: absolute;
  top: 10px;
  right: 55px;
  background: #fff;
  // overflow: hidden;
  border-radius: $map-border-radius;
  box-shadow: $map-box-shadow;
  z-index: 999;

  &__header {
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    border-bottom: $map-border;
    background: #e1e4e6;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon-guanbi {
      cursor: pointer;
    }
    .el-icon {
      cursor: pointer;
      height: 40px;
    }
    .split {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  &__content {
    padding: 10px;
    

    .action-button {
      font-size: 16px;
      background-color: transparent;
      border: 1px solid #d3d3d3;
      color: #6e6e6e;
      height: 32px;
      width: 32px;
      text-align: center;
      outline: none;
      border-radius: 4px;
      margin-right: 10px;
      cursor: pointer;

      &:hover {
        color: $map-primary-color;
        border-color: $map-primary-color;
      }

      &.is-active {
        background: $map-primary-color;
        color: #e4e4e4;
        border-color: $map-primary-color;
      }
    }
  }

  .collect__button:focus, .collect__button:hover {
    background-color: #f3f3f3;
    color: #2e2e2e;
    cursor: pointer;
    outline: none!important;
  }
  .esri-select, .collect__button {
    border-radius: 4px;
  }

  .collect__button {
    align-items: center;
    background-color: transparent;
    border: none;
    color: #6e6e6e;
    display: flex;
    font-size: 16px;
    height: 32px;
    justify-content: center;
    text-align: center;
    transition: background-color 125ms ease-in-out;
    width: 32px;
  }
}
