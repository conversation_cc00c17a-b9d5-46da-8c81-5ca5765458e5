<template>
  <headerBlock /> 
  <router-view />
</template>
<script setup>
import headerBlock from "./views/headerblock.vue";
import { LayerStorage, PropStorage, Globalvariable } from "../packages/onemapkit";
 
let istrue = true;
function _clickevent() {
  istrue = !istrue;
  console.log("===========123", testprox);
  if (istrue) {
    testprox.CheckedLayerids.value.push("555");
    console.log("===========testprox", testprox);
  } else {
    testprox.CheckedLayerids.value.splice(1, 1);
    console.log("===========testprox", testprox);
  }
}

// function test() {

//   let storage = window.localStorage;
//   storage.setItem("CheckedLayerids1", JSON.stringify(["CheckedLayerids1", "CheckedLayerids2"]));
//   let tmp1 = storage.getItem("CheckedLayerids1")
//   let ARR = tmp1 ? JSON.parse(storage.getItem("CheckedLayerids1")) : []

//   console.log("===========ARR", ARR);
//   let tmp = JSON.parse(storage.getItem("CheckedLayerids1") || '');
//   console.log("===========", tmp);
// }

// test();
</script>
<style lang="scss">
// @import "./styles/style.scss";
#app {
  height: 100%;
}
</style>
