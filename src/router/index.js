import { createRouter, createWebHashHistory } from "vue-router";
import pagesRouter from "./pages/pages.ts";
// import pagesRouterDev from "./pages/pagesdev.ts";
import buttonState from "../store/hButtonState";

const router = createRouter({
  history: createWebHashHistory(),
  scrollBehavior: (to, from, savedPosition) => {
    document.title = to.name;
    if (to.fullPath != "/") {
      let d = document.querySelector(".mzluirightView");
      if (d) {
        d.scrollTop = 0;
      }
      //document.querySelector(".mzluirightView")?.scrollTop = 0;
    }
  },
  routes: pagesRouter,
  // routes: [...pagesRouter, ...pagesRouterDev],
});
//导航守卫 buttonState()要卸载内部
router.beforeEach((to, from, naxt) => {
  const btnState = buttonState();
  btnState.name = btnState.name + "---";
  // console.log("btnState = ",btnState.name)
  naxt();
});
export default router;
