export default [
  {
    path: "/",
    name: "OnemapKit组件库",
    //@ts-ignore
    component: () => import("/src/views/index.vue"),
  },
  {
    path: "/components",
    name: "组件库",
    //@ts-ignore
    component: () => import("/src/views/home.vue"),
    children: [
      {
        index: 0,
        path: "/quickstart",
        name: "快速上手",
        isActive: false,
        children: [
          {
            index: 0,
            isActive: false,
            path: "/userguide",
            name: "使用指南",
            //@ts-ignore
            component: () => import("/doc/guide/guide.md"),
          },
          {
            index: 1,
            isActive: false,
            path: "/updatelog",
            name: "更新日志",
            //@ts-ignore
            component: () => import("/doc/updatelog/updatelog.md"),
          },
          {
            index: 2,
            isActive: false,
            path: "/install",
            name: "安装使用",
            //@ts-ignore
            component: () => import("/doc/install/install.md"),
          },
        ],
      },
      {
        index: 1,
        isActive: false,
        path: "/TechnicalvalidationMobile",
        name: "移动端技术验证",
        children: [
          {
            index: 0,
            isActive: false,
            path: "/MapMobile",
            name: "Map(二三维一体化地图MapMobile)",
            //@ts-ignore
            component: () => import("/doc/mpackages/map/map.md"),
          },
        ],
      },
      {
        index: 1,
        isActive: false,
        path: "/Technicalvalidation",
        name: "技术验证",
        children: [
          {
            index: 0,
            isActive: false,
            path: "/IonemapInterface",
            name: "IonemapInterface(主要接口)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/IonemapInterface/IonemapInterface.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/onemapclass",
            name: "onemapclass(核心类)",
            //@ts-ignore
            component: () => import("/doc/dev/onemapclass/onemapclass.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/comstoreclass",
            name: "comstoreclass(存储类)",
            //@ts-ignore
            component: () => import("/doc/dev/comstoreclass/comstoreclass.md"),
          },
          {
            //debug
            index: 0,
            isActive: false,
            path: "/debug",
            name: "debug(调试)",
            //@ts-ignore
            component: () => import("/doc/dev/debug/debug.md"),
          },
          {
            //icons
            index: 0,
            isActive: false,
            path: "/icons",
            name: "Icons(调试)",
            //@ts-ignore
            component: () => import("/doc/dev/Icons/Icons.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/MapDev",
            name: "Map(二三维一体化地图)",
            //@ts-ignore
            component: () => import("/doc/dev/Map/Map.md"),
          },

          {
            index: 0,
            isActive: false,
            path: "/MapClick",
            name: "MapClick(地图点击)",
            //@ts-ignore
            component: () => import("/doc/dev/MapClick/MapClick.md"),
          },
          {
            //LayerTree
            index: 0,
            isActive: false,
            path: "/MapTree",
            name: "MapTree(资源目录)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapTree/MapTree.md"),
          },
          {
            //LayerTree
            index: 0,
            isActive: false,
            path: "/MapContent",
            name: "MapContent(资源目录)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapTree/MapContent.md"),
          },
          {
            //LayerTree
            index: 0,
            isActive: false,
            path: "/MapContentCF",
            name: "MapContent(自定义拆分使用方法)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapTree/MapContentCaiFen.md"),
          },
          {
            //LayerTree
            index: 0,
            isActive: false,
            path: "/LayerTree",
            name: "LayerTree(资源目录)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/LayerTree/LayerTree.md"),
          },
          {
            //LayerTreePop
            index: 0,
            isActive: false,
            path: "/LayerTreePop",
            name: "LayerTreePop(资源目录)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/LayerTreePop/LayerTreePop.md"),
          },
          {
            //Map3dSet
            index: 0,
            isActive: false,
            path: "/Map3dSet",
            name: "Map3dSet(三维设置)",
            //@ts-ignore
            component: () => import("/doc/dev/Map3dSet/Map3dSet.md"),
          },
          {
            //MapLayerTree
            index: 1,
            isActive: false,
            path: "/MapLayerTree",
            name: "MapLayerTree(资源目录)",
            //@ts-ignore
            component: () => import("/doc/dev/MapLayerTree/MapLayerTree.md"),
          },
          {
            //MapLayerTreePop
            index: 0,
            isActive: false,
            path: "/MapLayerTreePop",
            name: "MapLayerTreePop(资源目录)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapLayerTreePop/MapLayerTreePop.md"),
          },
          {
            //MapToolBox
            index: 0,
            isActive: false,
            path: "/MapToolBox",
            name: "MapToolBox(工具箱)",
            //@ts-ignore
            component: () => import("/doc/dev/MapToolBox/MapToolBox.md"),
          },
          {
            //ToolsBox
            index: 0,
            isActive: false,
            path: "/ToolsBox",
            name: "ToolsBox(工具箱)",
            //@ts-ignore
            component: () => import("/doc/dev/MapToolBox/ToolsBox.md"),
          },
          {
            //DrawTools
            index: 13,
            isActive: false,
            path: "/DrawTools",
            name: "DrawTools(绘制工具)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/DrawTools/DrawTools.md"),
          },
          {
            //DrawTools
            index: 13,
            isActive: false,
            path: "/DrawToolBox",
            name: "DrawToolBox(拆分绘制工具)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/DrawToolBox/DrawToolBox.md"),
          },


          {
            //MapIdentify
            index: 13,
            isActive: false,
            path: "/MapIdentify",
            name: "MapIdentify(点选查询)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/MapIdentify/MapIdentify.md"),
          },
          {
            //SceneLight
            index: 1,
            isActive: false,
            path: "/SceneLight",
            name: "SceneLight(场景灯光)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/SceneLight/SceneLight.md"),
          },
          {
            //splitscreen
            index: 0,
            isActive: false,
            path: "/splitscreen",
            name: "splitscreen(分屏)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/splitscreen/splitscreen.md"),
          },
          // {
          //   //splitscreenPop
          //   index: 0,
          //   isActive: false,
          //   path: "/splitscreenPop",
          //   name: "splitscreenPop(分屏)",
          //   component: () =>
          //     //@ts-ignore
          //     import("/doc/dev/MapToolBox/splitscreenPop/splitscreenPop.md"),
          // },
          {
            //sweepscreen
            index: 0,
            isActive: false,
            path: "/sweepscreen",
            name: "sweepscreen(卷帘)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/sweepscreen/sweepscreen.md"),
          },
          {
            //ViewRoamer
            index: 1,
            isActive: false,
            path: "/ViewRoamer",
            name: "ViewRoamer(三维漫游)",
            component: () =>
              //@ts-ignore
              import("/doc/dev/MapToolBox/ViewRoamer/ViewRoamer.md"),
          },
          // {
          //   index: 0,
          //   isActive: false,
          //   path: "/MapControlDev",
          //   name: "MapControl(二三维一体化地图)",
          //   //@ts-ignore
          //   component: () => import("/doc/dev/MapControl/MapControl.md"),
          // },
          // {
          //   index: 0,
          //   isActive: false,
          //   path: "/videofusionDev",
          //   name: "videofusion(视频融合)",
          //   component: () =>
          //     //@ts-ignore
          //     import("/doc/dev/MapToolBox/videofusion/videofusion.md"),
          // },
          {
            index: 0,
            isActive: false,
            path: "/footInfoDev",
            name: "footInfo 底栏信息(footInfo)",
            //@ts-ignore
            component: () => import("/doc/dev/Widgets/footInfo/footInfo.md"),
          },
          {
            index: 1,
            isActive: false,
            path: "/locationDev",
            name: "Location(定位)",
            //@ts-ignore
            component: () => import("/doc/dev/MapToolBox/Location/Location.md"),
          },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/MeasureDev",
          //   name: "Measure 量算工具",
          //   //@ts-ignore
          //   component: () => import("/doc/dev/MapToolBox/Measure/Measure.md"),
          // },
          // {
          //   index: 11,
          //   isActive: false,
          //   path: "/BatchAnalysis",
          //   name: "BatchAnalysis(批量分析)",
          //   component: () =>
          //     //@ts-ignore
          //     import("/doc/dev/MapToolBox/BatchAnalysis/BatchAnalysis.md"),
          // },
          {
            index: 0,
            isActive: false,
            path: "/DialogDev",
            name: "Dialog 弹窗(Dialog)",
            //@ts-ignore
            component: () => import("/doc/dev/others/Dialog/dialog.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/DrawTip",
            name: "绘制提示气泡(DrawTip)",
            //@ts-ignore
            component: () => import("/doc/dev/others/DrawTip/drawTip.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/PopwindowDev",
            name: "Popwindow 弹窗(Popwindow)",
            //@ts-ignore
            component: () => import("/doc/dev/others/Popwindow/popwindow.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/MessageBoxDev",
            name: "确认弹窗(MessageBox)",
            //@ts-ignore
            component: () => import("/doc/dev/others/MessageBox/MessageBox.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/ToolButtonDev",
            name: "工具按钮(ToolButton)",
            //@ts-ignore
            component: () => import("/doc/dev/others/ToolButton/ToolButton.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/popupDev",
            name: "停靠面板(popup)",
            //@ts-ignore
            component: () => import("/doc/dev/others/popup/popup.md"),
          },

          {
            index: 1,
            isActive: false,
            path: "/SearchDev",
            name: "Search 通用搜索",
            //@ts-ignore
            component: () => import("/doc/dev/Widgets/search/Search.md"),
          },
        ],
      },
      {
        index: 1,
        isActive: false,
        path: "/mapcomponent",
        name: "地图组件",
        children: [
          // {
          //   index: 0,
          //   isActive: false,
          //   path: "/MapControl",
          //   name: "MapControl 二三维一体化地图",
          //   component: () => import("/doc/MapControl/MapControl.md"),
          // },
          // {
          //   index: 0,
          //   isActive: false,
          //   path: "/map",
          //   name: "Map 二三维一体化地图",
          //   component: () => import("/doc/Map/Map.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/MultipleLight",
          //   name: "MultipleLight 三维多光源组件",
          //   component: () => import("/doc/MultipleLight/MultipleLight.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/ViewPointRoaming",
          //   name: "ViewPointRoaming 三维视点漫游组件",
          //   component: () =>
          //     import("/doc/ViewPointRoaming/ViewPointRoaming.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/PathRoaming",
          //   name: "PathRoaming 三维路径漫游组件",
          //   component: () => import("/doc/PathRoaming/PathRoaming.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/LightBar",
          //   name: "LightBar 灯带组件",
          //   component: () => import("/doc/LightBar/LightBar.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/FirstPersionControl",
          //   name: "FirstPersionControl 三维第一人称漫游组件",
          //   component: () =>
          //     import("/doc/FirstPersionControl/FirstPersionControl.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/SkylineAnalysis",
          //   name: "SkylineAnalysis 天际线分析组件",
          //   component: () => import("/doc/SkylineAnalysis/SkylineAnalysis.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/VisualAnalysis",
          //   name: "VisualAnalysis 可视域分析组件",
          //   component: () => import("/doc/VisualAnalysis/VisualAnalysis.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/SplitScreenArcgis",
          //   name: "SplitScreen 分屏Arcgis",
          //   //@ts-ignore
          //   component: () => import("/doc/MapTools/SplitScreen/SplitScreen.md"),
          // },
        ],
      },
      {
        index: 1,
        isActive: false,
        path: "/mapwidgets",
        name: "地图部件",
        children: [
          {
            index: 0,
            isActive: false,
            path: "/bottominfo",
            name: "BottomInfo 地图底栏信息",
            component: () => import("/doc/Widgets/BottomInfo/BottomInfo.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/SearchBar",
            name: "SearchBar 通用搜索",
            component: () => import("/doc/Widgets/searchBar/SearchBar.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/RegionSearch",
            name: "RegionSearch 区域搜索",
            component: () => import("/doc/Widgets/RegionSearch/RegionSearch.md"),
          },
          {
            index: 1,
            isActive: false,
            path: "/resourcecatalog",
            name: "ResourceCatalog 资源目录面板",
            component: () =>
              import("/doc/Widgets/ResourceCatalog/ResourceCatalog.md"),
          },
          {
            index: 0,
            isActive: false,
            path: "/splitscreen",
            name: "splitscreen 分屏(splitscreen)",
            component: () => import("/doc/MapTools/SplitScreen/SplitScreen.md"),
          },
        ],
      },
      {
        index: 1,
        isActive: false,
        path: "/maptools",
        name: "地图工具栏",
        children: [
          // {
          //   index: 0,
          //   isActive: false,
          //   path: "/maptools",
          //   name: "MapTools 工具箱",
          //   component: () => import("/doc/MapTools/MapTools.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/locationpanel",
          //   name: "LocationPanel 定位工具",
          //   component: () =>
          //     import("/doc/MapTools/LocationPanel/LocationPanel.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/UploadLayerPanel",
          //   name: "UploadLayerPanel 在线服务工具",
          //   component: () =>
          //     import("/doc/MapTools/UploadLayerPanel/UploadLayerPanel.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/drawpanel",
          //   name: "DrawPanel 绘制工具",
          //   component: () => import("/doc/MapTools/DrawPanel/DrawPanel.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/AnalysisPanel",
          //   name: "AnalysisPanel(批量分析)",
          //   component: () =>
          //     import("/doc/MapTools/AnalysisPanel/AnalysisPanel.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/measurepanel",
          //   name: "MeasureTool 量算工具",
          //   component: () => import("/doc/MapTools/MeasureTool/MeasureTool.md"),
          // },
          // {
          //   index: 1,
          //   isActive: false,
          //   path: "/TimeSliderPanel",
          //   name: "TimeSliderPanel 时间轴工具",
          //   component: () =>
          //     import("/doc/MapTools/TimeSliderPanel/TimeSliderPanel.md"),
          // },
        ],
      },
      {
        index: 2,
        isActive: false,
        path: "/othercomponent",
        name: "其他组件",
        children: [
          {
            index: 0,
            isActive: false,
            path: "/button",
            name: "Button 按钮",
            component: () => import("/doc/button/Button.md"),
          },
          {
            index: 2,
            isActive: false,
            path: "/table",
            name: "table 表格",
            component: () => import("/doc/table/table.md"),
          },
          {
            index: 3,
            isActive: false,
            path: "/poppanel",
            name: "PopPanel 弹出面板",
            component: () => import("/doc/PopPanel/PopPanel.md"),
          },
          {
            index: 3,
            isActive: false,
            path: "/Form",
            name: "Form 组件",
            component: () => import("/doc/Form/Form.md"),
          },
        ],
      },
    ],
  },
  {
    path: "/test/onemap",
    name: "一张图",
    component: () => import("/src/onemap/index.vue"),
    children: [],
  },
  {
    path: "/test/onemapczm",
    name: "一张图(Onemap1)",
    component: () => import("/src/onemap/czmtest.vue"),
    children: [],
  },
  {
    path: "/appmap",
    name: "APP应用",
    component: () => import("/src/appmap/index.vue"),
    children: [],
  },
  {
    path: "/appmap2",
    name: "APP应用(2)",
    component: () => import("/src/appmap2/index.vue"),
    children: [],
  },
];
