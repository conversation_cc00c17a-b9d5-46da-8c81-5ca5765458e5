<template>
  <div class="pre-code-box" >
    <div class="buttoncontainer">
      <el-button link @click="showOrhideCode"
        >{{ showCode ? "隐藏代码" : "显示代码" }}
      </el-button>
      <el-button
        v-if="showCode"
        style="margin-left: 20px"
        link
        @click="copyCode"
        >拷贝代码</el-button
      >
    </div>
    <transition name="slide-fade" >
      <pre class="language-html" v-if="showCode" v-highlight>
        <code class="language-html">{{ sourceCode }}</code>
      </pre>
    </transition>

    <div class="buttoncontainer">
      <el-button v-if="showCode" link @click="showOrhideCode"
        >{{ showCode ? "隐藏代码" : "显示代码" }}
      </el-button>
    </div>
    <textarea id="inputCopy" style="background-color: rgb(52, 52, 52);"></textarea>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
const props = defineProps({
  compName: {
    type: String,
    default: "",
    require: true,
  },
  demoName: {
    type: String,
    default: "",
    require: true,
  },
  sourceCodePath: {
    type: String,
    default: "",
  },
});

const showCode = ref(false);
const border = ref("1px solid rgba(0,0,0,.06)");
const showOrhideCode = () => {
  showCode.value = !showCode.value;
  if (showCode.value) {
    border.value = "0";
  } else {
    border.value = "1px solid rgba(0,0,0,.06)";
  }
};
const sourceCode = ref("");
async function getSourceCode() {
  //@ts-ignore
  const isDev = import.meta.env.MODE === "development";
  if (isDev) {
    sourceCode.value = (
      await import(`/doc/${props.sourceCodePath}?raw`)
    ).default;
  } else {
    const url = location.origin;

    console.log("not dev", url);
    sourceCode.value = await fetch(`${url}/doc/${props.sourceCodePath}`).then(
      (res) => res.text()
    );
  }
}
const copyCode = () => {
  const input = document.getElementById("inputCopy");
  //@ts-ignore
  input.value = sourceCode.value;
  //@ts-ignore
  input.select();
  ElMessage({
    type: "success",
    message: "代码复制成功",
  });
};
onMounted(() => {
  getSourceCode();
});
</script>

<style scoped lang="scss">
.buttoncontainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}
#inputCopy {
  opacity: 0;
  border: 0;
  outline: none;
  height: 0;
  position: fixed;
  z-index: -99999999;
}
.slide-fade-enter-active {
  transition: all 0.1s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.1s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0.5;
}
.pre-code-box {
  width: 100%;
  height: auto;
  overflow: hidden;
  border-top: 0;
  margin: 0px 0 15px 0;
  position: relative;
  transition: all 0.15s ease-out;
  .m-copy {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    i {
      font-size: 22px;
      color: #b7b3b3;
      &:hover {
        color: #000;
      }
    }
  }
  .showCode {
    width: 100%;
    line-height: 40px;
    font-size: 14px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #111111;
    box-shadow: 0px 16px 15px -16px rgb(0 0 0 / 10%);
    color: #505050;
    cursor: pointer;
    i {
      margin-left: 10px;
    }
    i.rotate {
      transform: rotate(180deg);
    }
    &:hover {
      background: #f9f9f9;
      color: #0e80eb;
    }
  }
  &:hover {
    box-shadow: 0px 16px 15px -16px rgb(0 0 0 / 10%);
  }
}
</style>
