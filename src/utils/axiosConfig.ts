import axios from "axios";

// 获取 token（首先检查 Cookie，然后检查 localStorage）  
const getToken = () => {
	const cookieToken = document.cookie
		.split('; ')
		.find((row) => row.startsWith('sessionid='))
		?.split('=')[1];

	if (cookieToken) {
		return cookieToken;
	}

	return localStorage.getItem('sessionid');
};

// 设置请求拦截器  
axios.interceptors.request.use((config) => {
	const token = getToken();
	if (token) {
		config.headers.Authorization = `Bearer ${token}`;
	}
	return config;
}, (error) => {
	// 处理请求拦截器发生的错误（可选）  
	return Promise.reject(error);
});

export default axios;