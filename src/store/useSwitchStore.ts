import {defineStore} from 'pinia' ;
//存储位置显示开关的状态
export const useSwitchStore = defineStore('switchStore',{
    state: ()=>({
      switchtip:true,
    }),
    actions:{
      setSwitchTip(value:boolean){
        this.switchtip=value;
        localStorage.setItem('switchStore',JSON.stringify(value));
      },

    //   //从localStorage中加载switchtip的值
    //   loadSwitchtipFromStorage(){
    //     const storedValue=localStorage.getItem('switchStore');
    //     if(storedValue!=null){
    //       this.switchtip=JSON.parse(storedValue);
          
    //     }
    //     return this.switchtip;
    //   }
 },
       
  })