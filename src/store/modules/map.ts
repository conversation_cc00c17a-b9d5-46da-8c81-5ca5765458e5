import { defineStore } from 'pinia';
export const mapStore = defineStore({
  id: 'mapStore',
  state: () => ({
    mapViewType: '2d',
    mapObj: {},
    arcgisVersion: '4',
    gotConfig: false,
  }),

  actions: {
    setMapViewType(data: string) {
      this.mapViewType = data;
    },
    setMapObj(data: any) {
      this.mapObj = data;
    },
    setArcgisVersion(data: string) {
      this.arcgisVersion = data ? data : '4';
    },
    setGotConfig(data = true) {
      this.gotConfig = data;
    },
  },
});
