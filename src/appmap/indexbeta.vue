<!-- html -->
<template>
    <div class="container" id="parentContainer">
      <Map
        ref="MapControlRef"
        :PropStore="PropStore"
        :LayerStore="layerStore"
        @MapReadyEvent="_MapReadyEvent"
      ></Map>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref } from "vue";
  
  import "../../onemapkit/Build/onemapkit.css";
  
  import "element-plus/es/components/option/style/css";
  
  import "../../onemapkit/Build/onemapkit.css";
  
  import { 
    mapType,
    map as Map,
    InitMapControl,
    getOptions,
  } from "../../../onemapkit/mpackages/onemapkit";
  
  /**第一步：　导入图层参数*/
  import {
    _Onemap,
    layerStore,
    PropStore,
    ServiceUrl,
    BaseMapLayerUrl,
  } from "../../doc/layermobile";
  
  InitMapControl(_Onemap, {
    BASE_URL: "ThirdParty",
    MapControlName: "mainMapControl",
    BaseMapLayer: BaseMapLayerUrl(),
    TerrainUrl: ServiceUrl.TerrainUrl,
  });
  
  //MapControl 组件的地图加载完毕的回调方法,是获取MapViewer的一种方式
  const _MapReadyEvent:any = (mapviewer: any,layer: any): any => { 
    console.log(
      "==cesium===yusy>BaseMapLayer加载完毕。",
      _Onemap.MapViewer.scene.mode,
      _Onemap.MapType.value,
      mapviewer,
      layer,
    );
    if (_Onemap.MapType.value === mapType.scene2d) {
      // _Onemap.MapViewer.scene.requestRenderMode = false;
    }
  };
  
  const MapControlRef = ref();
    
  </script>
  <style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
    position: relative;
  }
  </style>
  