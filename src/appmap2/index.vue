<template>
  <div class="appmap-root">
    <!-- <Map
      ref="MapControlRef"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      :TipTools="[
        { Control: AttributeQuery, isPropStore: true, isLayerStore: false },
        { Control: mapswitch, isPropStore: false, isLayerStore: false },
        { Control: fullmap, isPropStore: true, isLayerStore: false },
        { Control: mapzoom, isPropStore: false, isLayerStore: false },
      ]"
      @MapClickEvent="_MapClickEvent"
      @MapReadyEvent="_MapReadyEvent"
    ></Map> -->
    <SlidePanel
      ref="slidePanelRef"
      :offsetTop="65"
      :minHeight="50"
      @scrolltolower="scrolltolowerHandler"
    >
      <template #="{ isMaxHeight }">
        <div></div>
      </template>
    </SlidePanel>
    <Map
      ref="MapControlRef"
      :LayerStore="layerStore"
      :PropStore="PropStore"
      @MapClickEvent="_MapClickEvent"
      @MapReadyEvent="_MapReadyEvent"
    >
    </Map>

    <MapProvider :LayerStore="layerStore" :PropStore="PropStore">
      <MapToolBox>
        <LayerExplorer>
          <div class="layers-list-basemap-title">
            当前底图：<span class="layer-name">{{ curBasemap?.name }}</span>
          </div>

          <van-tabs v-model:active="activeBasemapGroup">
            <van-tab v-for="name in basemapAlias" :title="name" :key="name">
              <LayerList
                :data="basemapLayers"
                type="radio"
                :show-action="false"
                :show-opacity="false"
                @checkedChange="handleCheckedChange"
              >
              </LayerList>
            </van-tab>
          </van-tabs>

          <van-tabs v-model:active="activeLayerTab">
            <van-tab title="专题">
              <LayerTree :data="layerStore.Layers" />
            </van-tab>
            <van-tab title="图层">
              <LayerTree :data="layerStore.Layers" />
            </van-tab>
            <van-tab title="收藏">
              <LayerList :data="favsLayers"> </LayerList>
            </van-tab>
          </van-tabs>
        </LayerExplorer>

        <MapToolButton name="定位2" :icon="LocationArrow" loading>
          <div>内容</div>
        </MapToolButton>
        <MapToolButton name="定位3" :icon="LocationArrow" disabled>
          <div>内容</div>
        </MapToolButton>
      </MapToolBox>
    </MapProvider>
    <!-- <LayerTreePop
      :PropStore="PropStore"
      :LayerStore="layerStore"
      :MapTreeHeight="700"
    /> -->
  </div>
</template>

<script lang="ts" setup>
// import "../../onemapkit/Build/onemapkit.css";
// import "cesium/Build/Cesium/Widgets/widgets.css";
// import "element-plus/es/components/option/style/css";

// import "../../onemapkit/Build/onemapkit.css";
import { ref, watch, computed } from "vue";
import SlidePanel, {
  type SlidePanelInstance,
} from "../../mpackages/SlidePanel";
import MapToolBox from "../../mpackages/MapToolBox";
import MapToolButton from "../../mpackages/MapToolButton";
import LocationArrow from "@iconify-icons/gis/location-arrow";
import LayersIcon from "@iconify-icons/gis/layer-stack";
import LayerExplorer, {
  useLayerStore,
} from "../../mpackages/MapTools/LayerExplorer";
import LayerTree from "../../mpackages/MapTools/LayerExplorer/LayerTree";
import LayerList from "../../mpackages/MapTools/LayerExplorer/LayerList";
import MapProvider from "../../mpackages/MapProvider";

import {
  // LayerTreePop,
  // MapToolBox,
  map as Map,
  defaultParameter,
  // mapswitch,
  // fullmap,
  // mapzoom,
  // BottomBar,
  // SearchBar,
  // AttributeQuery,
  Utils,
  InitMapControl,
  OnemapClass,
  mapType,
} from "../../mpackages/onemapkit";
import {
  //   _Onemap,
  PropStore,
  BaseMapLayerUrl,
  layerStore,
  ServiceUrl,
  CustomTools,
  initShowMapLayer,
} from "../../doc/layermobile";

window.layerStore = layerStore;
window.propStore = PropStore;

const _Onemap = new OnemapClass(mapType.cesium);
InitMapControl(_Onemap, {
  BASE_URL: "ThirdParty",
  MapControlName: "mainMapControl",
  BaseMapLayer: BaseMapLayerUrl(),
  TerrainUrl: ServiceUrl.TerrainUrl,
  // MapLayerTree: Object.assign(
  //   defaultParameter.defaultLayerContentTab,
  //   defaultParameter.defaultMapTreeTab
  // ),
  // MapTools: {
  //   Toolset: CustomTools,
  //   CustomToolSize: {
  //     width: 400,
  //     height: 420,
  //   },
  // },
});

const _MapClickEvent = (mapControlName: String, _point: any, _options: any) => {
  // if (PropertyVisible.value == false) PropertyVisible.value = true;
  // console.log("=======avg", mapControlName, _point, _options);
};
const _MapReadyEvent = (mapControlName: String) => {
  console.log(12);
  initShowMapLayer();
};

const slidePanelRef = ref<SlidePanelInstance>();
watch(slidePanelRef, (e) => {
  e && console.log(231, e);
  if (e) {
    console.log(989, e.pullDistance);
  }
});

const scrolltolowerHandler = () => {
  console.log("scrolltolowerHandler");
};

const activeLayerTab = ref("专题目录");
const activeBasemapGroup = ref("航片"); //["航片", "矢量", "卫片"];
const basemapAlias = ref({ 航片: "航飞", 矢量: "地图", 卫片: "卫星" });

const { withProvider } = useLayerStore();
const { favs } = withProvider(layerStore);
const basemapLayers = computed(() => {
  return layerStore.Layers.find((l) => l.name == "底图数据")?.children ?? [];
});
const curBasemap = ref();
const handleCheckedChange = (e) => {
  curBasemap.value = e;
};

const favsLayers = computed(() => {
  return layerStore._MapLayers.filter((l) => favs.has(l.layerid));
});
</script>

<style lang="scss" scoped>
.appmap-root {
  position: fixed;
  inset: 0;
  z-index: 1;
  background-color: #fff;
  overflow: hidden;
}
</style>
