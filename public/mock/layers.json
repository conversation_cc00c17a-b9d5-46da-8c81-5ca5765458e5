[{"layerid": "08d9a80b-a21f-4d33-89123ab1-95c89663552f12", "name": "测试数据", "showInCatalog": true, "children": [{"layerid": "t7xaXcBsj", "name": "本机良庆自然幢", "useMapModel": ["cesium"], "layerOrder": 1, "visible": false, "serviceType": "Cesium3DTiles", "url": "http://localhost:9003/model/t7xaXcBsj/tileset.json"}, {"layerid": "1111test2222", "name": "本地测试2", "useMapModel": ["<PERSON><PERSON>"], "visible": false, "serviceType": "<PERSON><PERSON><PERSON>ile<PERSON><PERSON><PERSON>", "showInCatalog": true, "url": "http://22977v69j4.iok.la:6080//arcgis/rest/services/%E8%A1%8C%E6%94%BF%E5%8C%BA%E5%88%92/%E8%A1%8C%E6%94%BF%E5%8C%BA/MapServer", "options": {"layerTagId": "1111test2222"}}, {"layerid": "1111test3333", "name": "本地测试2(V)", "useMapModel": ["<PERSON><PERSON>", "cesium"], "visible": false, "serviceType": "ArcgisMapImageLayer", "showInCatalog": true, "url": "http://22977v69j4.iok.la:6080//arcgis/rest/services/%E8%A1%8C%E6%94%BF%E5%8C%BA%E5%88%92/%E8%A1%8C%E6%94%BF%E5%8C%BAv/MapServer", "options": {"layerTagId": "1111test3333"}}, {"layerid": "1111test4444", "name": "多子图层", "useMapModel": ["<PERSON><PERSON>", "cesium"], "visible": false, "serviceType": "ArcgisMapImageLayer", "showInCatalog": true, "url": "http://22977v69j4.iok.la:6080//arcgis/rest/services/%E5%9C%B0%E5%90%8D%E5%9C%B0%E5%9D%80/%E5%90%84%E7%B1%BB%E4%B8%93%E9%A2%98/MapServer", "options": {"layerTagId": "1111test4444"}}]}, {"layerid": "08d9a80b-a21f-4d33-89ab-95c89663552f123", "name": "底图数据", "visible": false, "showInCatalog": false, "children": [{"layerid": "czm_singlemap", "isBaseLayer": true, "useMapModel": ["cesium"], "visible": true, "name": "单张图片", "showInCatalog": false, "url": "/assets/earth.jpg", "serviceType": "SingleImage"}, {"layerid": "电子地图tdt_vector", "isBaseLayer": true, "name": "电子地图(czm)", "baseLayerType": "vector", "useMapModel": ["cesium"], "visible": false, "url": "http://{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=4267820f43926eaf808d61dc07269beb", "serviceType": "tiandiMap", "options": {"subdomains": ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"]}}, {"layerid": "天地图影像tdt_satellite1", "isBaseLayer": true, "useMapModel": ["cesium"], "visible": true, "name": "天地图影像(czm)", "serviceType": "tiandiMap", "baseLayerType": "satellite", "url": "http://{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=4267820f43926eaf808d61dc07269beb", "options": {"subdomains": ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"]}}, {"layerid": "2023年卫星影像basemapSatilite2023", "isBaseLayer": true, "baseLayerType": "satellite", "useMapModel": ["<PERSON><PERSON>"], "visible": false, "name": "2023年卫星影像", "subLayers": [{"layerid": "天地图影像aatdt_image", "isBaseLayer": true, "useMapModel": ["<PERSON><PERSON>"], "visible": true, "name": "天地图影像", "url": "https://{subDomain}.tianditu.gov.cn/img_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=c&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=tile&tk=4267820f43926eaf808d61dc07269beb", "serviceType": "tiandiMap"}, {"layerid": "tdt_image_anno", "isBaseLayer": true, "useMapModel": ["<PERSON><PERSON>"], "visible": true, "name": "天地图影像注记", "url": "https://{subDomain}.tianditu.gov.cn/cia_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=c&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=tile&tk=4267820f43926eaf808d61dc07269beb", "serviceType": "tiandiMap"}], "url": ""}, {"layerid": "basemapVector", "isBaseLayer": true, "baseLayerType": "vector", "useMapModel": ["<PERSON><PERSON>"], "visible": true, "name": "电子地图", "subLayers": [{"layerid": "tdt_vector", "isBaseLayer": true, "useMapModel": ["<PERSON><PERSON>"], "visible": true, "name": "电子地图", "url": "https://{subDomain}.tianditu.gov.cn/vec_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=c&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&FORMAT=tile&tk=4267820f43926eaf808d61dc07269beb", "serviceType": "tiandiMap"}]}]}, {"layerid": "08d9a7dc-4b01-4766-8e44-f69d3c9f1a4d54", "name": "三维数据", "showInCatalog": true, "useMapModel": ["cesium"], "children": [{"layerid": "895bcac2-8e16-4154-9140-5250705b551f", "name": "czm三维数据", "showInCatalog": true, "useMapModel": ["cesium"], "children": [{"layerid": "10146e3e-1245-47b6-8780-99bdbc285ab7", "name": "南宁市倾斜模型", "useMapModel": ["cesium"], "layerOrder": 1, "visible": false, "serviceType": "Cesium3DTiles", "url": "https://cesium-develop.nnland.cn/mapdata/qingxie/tileset.json", "legendUrl": "/dev-api/onemap/proxy/map/10146e3e-1233-47b6-8780-99bdbc285ab7/legend?f=json"}, {"layerid": "t7xaXcBsj", "name": "本机良庆自然幢", "useMapModel": ["cesium"], "layerOrder": 1, "visible": false, "serviceType": "Cesium3DTiles", "url": "http://localhost:9003/model/t7xaXcBsj/tileset.json"}, {"layerid": "t7xaXcBsj1", "name": "66良庆自然幢", "useMapModel": ["cesium"], "layerOrder": 1, "visible": false, "serviceType": "Cesium3DTiles", "url": "http://22977v69j4.iok.la:8050/data3d/liangqingzrz1/tileset.json"}, {"layerid": "qtest", "name": "钦州测试", "useMapModel": ["cesium"], "layerOrder": 1, "visible": false, "serviceType": "Cesium3DTiles", "url": "http://22977v69j4.iok.la:8050/data3d/qtest/cesium2/tileset.json"}]}]}]